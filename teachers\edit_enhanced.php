<?php
/**
 * صفحة تعديل المعلم - نسخة محسنة
 */

require_once "../includes/config.php";
require_once "../includes/database.php";
require_once "../includes/functions.php";
require_once "../includes/security.php";

// التحقق من الصلاحيات
check_session();
if (!check_permission("admin")) {
    header("Location: ../dashboard/");
    exit();
}

$teacher_id = intval($_GET["id"] ?? 0);
$page_title = "تعديل معلم";
$error_message = "";
$success_message = "";

if (empty($teacher_id)) {
    $_SESSION["error_message"] = "معرف المعلم غير صحيح";
    header("Location: index.php");
    exit();
}

// جلب بيانات المعلم
$teacher_stmt = $conn->prepare("
    SELECT t.*, u.full_name, u.username, u.email, u.status as user_status
    FROM teachers t
    INNER JOIN users u ON t.user_id = u.id
    WHERE t.id = ?
");
$teacher_stmt->bind_param("i", $teacher_id);
$teacher_stmt->execute();
$teacher = $teacher_stmt->get_result()->fetch_assoc();

if (!$teacher) {
    $_SESSION["error_message"] = "المعلم غير موجود";
    header("Location: index.php");
    exit();
}

// معالجة تحديث البيانات
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // جمع البيانات
    $full_name = clean_input($_POST["full_name"] ?? "");
    $email = clean_input($_POST["email"] ?? "");
    $phone = clean_input($_POST["phone"] ?? "");
    $employee_id = clean_input($_POST["employee_id"] ?? "");
    $hire_date = clean_input($_POST["hire_date"] ?? "");
    $department = clean_input($_POST["department"] ?? "");
    $specialization = clean_input($_POST["specialization"] ?? "");
    $salary = floatval($_POST["salary"] ?? 0);
    $notes = clean_input($_POST["notes"] ?? "");
    $status = clean_input($_POST["status"] ?? "active");

    // التحقق من صحة البيانات
    $errors = [];

    if (empty($full_name)) {
        $errors[] = "الاسم الكامل مطلوب";
    }

    if (empty($email)) {
        $errors[] = "البريد الإلكتروني مطلوب";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "صيغة البريد الإلكتروني غير صحيحة";
    }

    if (empty($employee_id)) {
        $errors[] = "رقم الموظف مطلوب";
    }

    if (!empty($hire_date) && !validate_date($hire_date)) {
        $errors[] = "تاريخ التوظيف غير صحيح";
    }

    if (!empty($phone) && !validate_phone($phone)) {
        $errors[] = "رقم الهاتف غير صحيح";
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    if (!empty($email)) {
        $email_check = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $email_check->bind_param("si", $email, $teacher["user_id"]);
        $email_check->execute();
        if ($email_check->get_result()->num_rows > 0) {
            $errors[] = "البريد الإلكتروني مستخدم من قبل";
        }
    }

    // التحقق من عدم تكرار رقم الموظف
    if (!empty($employee_id)) {
        $employee_check = $conn->prepare("SELECT id FROM teachers WHERE employee_id = ? AND id != ?");
        $employee_check->bind_param("si", $employee_id, $teacher_id);
        $employee_check->execute();
        if ($employee_check->get_result()->num_rows > 0) {
            $errors[] = "رقم الموظف مستخدم من قبل";
        }
    }

    if (empty($errors)) {
        $conn->begin_transaction();

        try {
            // تحديث بيانات المستخدم
            $update_user_stmt = $conn->prepare("
                UPDATE users SET
                    full_name = ?, email = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $update_user_stmt->bind_param("sssi", $full_name, $email, $status, $teacher["user_id"]);
            $update_user_stmt->execute();

            // تحديث بيانات المعلم
            $update_teacher_stmt = $conn->prepare("
                UPDATE teachers SET
                    employee_id = ?, phone = ?, hire_date = ?, department = ?,
                    specialization = ?, salary = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $update_teacher_stmt->bind_param("sssssdsi",
                $employee_id, $phone, $hire_date, $department,
                $specialization, $salary, $status, $teacher_id
            );
            $update_teacher_stmt->execute();

            $conn->commit();

            $_SESSION["success_message"] = "تم تحديث بيانات المعلم بنجاح";
            header("Location: view.php?id=" . $teacher_id);
            exit();

        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "حدث خطأ أثناء التحديث: " . $e->getMessage();
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
}

include_once "../includes/header.php";
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>تعديل معلم: <?php echo htmlspecialchars($teacher["full_name"]); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($teacher[\"full_name\"]); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($teacher[\"email\"]); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label">رقم الموظف *</label>
                                    <input type="text" class="form-control" id="employee_id" name="employee_id" 
                                           value="<?php echo htmlspecialchars($teacher[\"employee_id\"]); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($teacher[\"phone\"]); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department" class="form-label">القسم</label>
                                    <input type="text" class="form-control" id="department" name="department" 
                                           value="<?php echo htmlspecialchars($teacher[\"department\"]); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="specialization" class="form-label">التخصص</label>
                                    <input type="text" class="form-control" id="specialization" name="specialization" 
                                           value="<?php echo htmlspecialchars($teacher[\"specialization\"]); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hire_date" class="form-label">تاريخ التوظيف</label>
                                    <input type="date" class="form-control" id="hire_date" name="hire_date" 
                                           value="<?php echo $teacher[\"hire_date\"]; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="salary" class="form-label">الراتب</label>
                                    <input type="number" class="form-control" id="salary" name="salary" step="0.01"
                                           value="<?php echo $teacher[\"salary\"]; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo ($teacher[\"status\"] == \"active\") ? \"selected\" : \"\"; ?>>نشط</option>
                                <option value="inactive" <?php echo ($teacher[\"status\"] == \"inactive\") ? \"selected\" : \"\"; ?>>غير نشط</option>
                            </select>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once "../includes/footer.php"; ?>