-- إصلاح نظام الغياب بالخصم الشامل
-- Comprehensive Absence with Deduction System Fix

USE school_management;

-- 1. إصلاح مشكلة التكرار في الحالات
-- Fix duplicate status issue

-- تنظيف البيانات المكررة
DELETE t1 FROM staff_absences_with_deduction t1
INNER JOIN staff_absences_with_deduction t2 
WHERE t1.id > t2.id 
AND t1.user_id = t2.user_id 
AND t1.absence_date = t2.absence_date;

-- 2. إنشاء جدول موحد للغياب والإجازات
-- Create unified table for absences and leaves

CREATE TABLE IF NOT EXISTS unified_staff_absences (
    id INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    user_id INT(10) UNSIGNED NOT NULL,
    absence_date DATE NOT NULL,
    end_date DATE NULL,
    absence_type ENUM('sick','personal','emergency','unauthorized','annual','maternity','paternity','study','unpaid') NOT NULL DEFAULT 'unauthorized',
    absence_category ENUM('absence','leave') NOT NULL DEFAULT 'absence',
    days_count INT(10) UNSIGNED DEFAULT 1,
    hours_count DECIMAL(4,2) DEFAULT NULL,
    reason TEXT,
    medical_certificate VARCHAR(255) DEFAULT NULL,
    
    -- معلومات الخصم
    has_deduction TINYINT(1) DEFAULT 0,
    deduction_type ENUM('fixed','percentage','daily_rate','hourly_rate') DEFAULT 'daily_rate',
    deduction_amount DECIMAL(10,2) DEFAULT 0.00,
    
    -- معلومات الموافقة
    status ENUM('pending','approved','rejected','cancelled') DEFAULT 'pending',
    applied_by INT(10) UNSIGNED NOT NULL,
    approved_by INT(10) UNSIGNED DEFAULT NULL,
    approved_at TIMESTAMP NULL DEFAULT NULL,
    rejected_by INT(10) UNSIGNED DEFAULT NULL,
    rejected_at TIMESTAMP NULL DEFAULT NULL,
    rejection_reason TEXT DEFAULT NULL,
    
    -- معلومات إضافية
    replacement_user_id INT(10) UNSIGNED DEFAULT NULL,
    replacement_notes TEXT DEFAULT NULL,
    hr_notes TEXT DEFAULT NULL,
    manager_notes TEXT DEFAULT NULL,
    
    -- تواريخ النظام
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY unique_user_date (user_id, absence_date),
    KEY idx_user_id (user_id),
    KEY idx_absence_date (absence_date),
    KEY idx_absence_type (absence_type),
    KEY idx_absence_category (absence_category),
    KEY idx_status (status),
    KEY idx_applied_by (applied_by),
    KEY idx_approved_by (approved_by),
    KEY idx_date_range (absence_date, end_date),
    KEY idx_user_status (user_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. نقل البيانات من الجداول القديمة
-- Migrate data from old tables

-- نقل من staff_absences_with_deduction
INSERT IGNORE INTO unified_staff_absences (
    user_id, absence_date, absence_type, absence_category, days_count,
    reason, has_deduction, deduction_type, deduction_amount, status,
    applied_by, approved_by, approved_at, rejected_by, rejected_at,
    rejection_reason, created_at, updated_at
)
SELECT 
    user_id, 
    absence_date, 
    absence_type,
    'absence' as absence_category,
    COALESCE(days_count, 1) as days_count,
    reason,
    CASE WHEN deduction_amount > 0 THEN 1 ELSE 0 END as has_deduction,
    COALESCE(deduction_type, 'daily_rate') as deduction_type,
    COALESCE(deduction_amount, 0.00) as deduction_amount,
    COALESCE(status, 'pending') as status,
    COALESCE(recorded_by, created_by, 1) as applied_by,
    approved_by,
    approved_at,
    rejected_by,
    rejected_at,
    rejection_reason,
    created_at,
    updated_at
FROM staff_absences_with_deduction;

-- نقل من staff_leaves
INSERT IGNORE INTO unified_staff_absences (
    user_id, absence_date, end_date, absence_type, absence_category, days_count,
    reason, has_deduction, deduction_amount, status, applied_by, approved_by, 
    approved_at, medical_certificate, replacement_user_id, replacement_notes,
    created_at, updated_at
)
SELECT 
    sl.user_id,
    sl.start_date as absence_date,
    sl.end_date,
    CASE 
        WHEN lt.code = 'sick' THEN 'sick'
        WHEN lt.code = 'annual' THEN 'annual'
        WHEN lt.code = 'emergency' THEN 'emergency'
        WHEN lt.code = 'maternity' THEN 'maternity'
        WHEN lt.code = 'paternity' THEN 'paternity'
        WHEN lt.code = 'study' THEN 'study'
        WHEN lt.code = 'unpaid' THEN 'unpaid'
        ELSE 'personal'
    END as absence_type,
    'leave' as absence_category,
    COALESCE(sl.working_days, sl.total_days, 1) as days_count,
    sl.reason,
    CASE WHEN lt.code IN ('unpaid', 'personal') THEN 1 ELSE 0 END as has_deduction,
    CASE WHEN lt.code IN ('unpaid', 'personal') THEN 50.00 ELSE 0.00 END as deduction_amount,
    COALESCE(sl.status, 'pending') as status,
    COALESCE(sl.applied_by, 1) as applied_by,
    sl.approved_by,
    sl.approved_at,
    sl.medical_certificate,
    sl.replacement_user_id,
    sl.replacement_notes,
    sl.created_at,
    sl.updated_at
FROM staff_leaves sl
LEFT JOIN leave_types lt ON sl.leave_type_id = lt.id
WHERE NOT EXISTS (
    SELECT 1 FROM unified_staff_absences usa 
    WHERE usa.user_id = sl.user_id 
    AND usa.absence_date = sl.start_date
);

-- 4. تحديث جدول staff_attendance ليتوافق مع النظام الموحد
-- Update staff_attendance to match unified system

-- إضافة عمود للربط مع النظام الموحد
ALTER TABLE staff_attendance 
ADD COLUMN IF NOT EXISTS absence_id INT(10) UNSIGNED DEFAULT NULL AFTER user_id,
ADD KEY idx_absence_id (absence_id);

-- ربط سجلات الحضور مع الغياب الموحد
UPDATE staff_attendance sa
JOIN unified_staff_absences usa ON sa.user_id = usa.user_id AND sa.attendance_date = usa.absence_date
SET sa.absence_id = usa.id,
    sa.status = CASE 
        WHEN usa.has_deduction = 1 THEN 'absent'
        WHEN usa.absence_category = 'leave' THEN usa.absence_type
        ELSE 'absent'
    END,
    sa.check_in_time = CASE WHEN usa.has_deduction = 1 THEN NULL ELSE sa.check_in_time END,
    sa.check_out_time = CASE WHEN usa.has_deduction = 1 THEN NULL ELSE sa.check_out_time END,
    sa.notes = CONCAT(COALESCE(sa.notes, ''), ' - ', usa.reason)
WHERE sa.absence_id IS NULL;

-- 5. إنشاء views محسنة
-- Create improved views

-- View شاملة للغياب والإجازات
CREATE OR REPLACE VIEW staff_absences_comprehensive AS
SELECT 
    usa.*,
    u.full_name as user_name,
    u.role as user_role,
    u.email as user_email,
    applier.full_name as applied_by_name,
    approver.full_name as approved_by_name,
    rejector.full_name as rejected_by_name,
    replacement.full_name as replacement_name,
    
    -- ترجمة الحقول للعربية
    CASE 
        WHEN usa.absence_type = 'sick' THEN 'إجازة مرضية'
        WHEN usa.absence_type = 'personal' THEN 'إجازة شخصية'
        WHEN usa.absence_type = 'emergency' THEN 'إجازة طارئة'
        WHEN usa.absence_type = 'unauthorized' THEN 'غياب غير مبرر'
        WHEN usa.absence_type = 'annual' THEN 'إجازة سنوية'
        WHEN usa.absence_type = 'maternity' THEN 'إجازة أمومة'
        WHEN usa.absence_type = 'paternity' THEN 'إجازة أبوة'
        WHEN usa.absence_type = 'study' THEN 'إجازة دراسية'
        WHEN usa.absence_type = 'unpaid' THEN 'إجازة بدون راتب'
        ELSE usa.absence_type
    END as absence_type_ar,
    
    CASE 
        WHEN usa.absence_category = 'absence' THEN 'غياب'
        WHEN usa.absence_category = 'leave' THEN 'إجازة'
        ELSE usa.absence_category
    END as absence_category_ar,
    
    CASE 
        WHEN usa.status = 'pending' THEN 'في الانتظار'
        WHEN usa.status = 'approved' THEN 'مُعتمد'
        WHEN usa.status = 'rejected' THEN 'مرفوض'
        WHEN usa.status = 'cancelled' THEN 'ملغي'
        ELSE usa.status
    END as status_ar,
    
    -- حساب المدة
    CASE 
        WHEN usa.end_date IS NOT NULL THEN 
            DATEDIFF(usa.end_date, usa.absence_date) + 1
        ELSE usa.days_count
    END as calculated_days,
    
    -- معلومات الخصم
    CASE WHEN usa.has_deduction = 1 THEN 'نعم' ELSE 'لا' END as has_deduction_ar,
    
    -- معلومات الحضور المرتبطة
    sa.check_in_time,
    sa.check_out_time,
    sa.total_hours
    
FROM unified_staff_absences usa
LEFT JOIN users u ON usa.user_id = u.id
LEFT JOIN users applier ON usa.applied_by = applier.id
LEFT JOIN users approver ON usa.approved_by = approver.id
LEFT JOIN users rejector ON usa.rejected_by = rejector.id
LEFT JOIN users replacement ON usa.replacement_user_id = replacement.id
LEFT JOIN staff_attendance sa ON usa.id = sa.absence_id;

-- View للتقارير
CREATE OR REPLACE VIEW staff_absence_reports AS
SELECT 
    DATE_FORMAT(usa.absence_date, '%Y-%m') as month_year,
    u.id as user_id,
    u.full_name as user_name,
    u.role as user_role,
    usa.absence_type,
    usa.absence_category,
    COUNT(*) as total_absences,
    SUM(usa.days_count) as total_days,
    SUM(CASE WHEN usa.has_deduction = 1 THEN usa.deduction_amount ELSE 0 END) as total_deductions,
    COUNT(CASE WHEN usa.status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN usa.status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN usa.status = 'rejected' THEN 1 END) as rejected_count
FROM unified_staff_absences usa
LEFT JOIN users u ON usa.user_id = u.id
GROUP BY DATE_FORMAT(usa.absence_date, '%Y-%m'), u.id, usa.absence_type, usa.absence_category;

-- 6. إنشاء إجراءات مخزنة محسنة
-- Create improved stored procedures

DELIMITER //

-- إجراء تسجيل غياب/إجازة موحد
CREATE PROCEDURE IF NOT EXISTS record_unified_absence(
    IN p_user_id INT,
    IN p_absence_date DATE,
    IN p_end_date DATE,
    IN p_absence_type VARCHAR(50),
    IN p_absence_category VARCHAR(20),
    IN p_days_count INT,
    IN p_reason TEXT,
    IN p_has_deduction TINYINT,
    IN p_applied_by INT
)
BEGIN
    DECLARE v_deduction_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_deduction_type VARCHAR(20) DEFAULT 'daily_rate';
    DECLARE v_absence_id INT;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- حساب مبلغ الخصم إذا كان مطلوباً
    IF p_has_deduction = 1 THEN
        SELECT ds.deduction_value, ds.deduction_type 
        INTO v_deduction_amount, v_deduction_type
        FROM deduction_settings ds 
        WHERE ds.absence_type = p_absence_type AND ds.is_active = 1 
        LIMIT 1;
        
        IF v_deduction_type = 'daily_rate' THEN
            SET v_deduction_amount = v_deduction_amount * p_days_count;
        END IF;
    END IF;
    
    -- إدراج في الجدول الموحد
    INSERT INTO unified_staff_absences (
        user_id, absence_date, end_date, absence_type, absence_category,
        days_count, reason, has_deduction, deduction_type, deduction_amount,
        status, applied_by
    ) VALUES (
        p_user_id, p_absence_date, p_end_date, p_absence_type, p_absence_category,
        p_days_count, p_reason, p_has_deduction, v_deduction_type, v_deduction_amount,
        'pending', p_applied_by
    );
    
    SET v_absence_id = LAST_INSERT_ID();
    
    -- تحديث/إدراج في جدول الحضور
    INSERT INTO staff_attendance (
        user_id, attendance_date, status, absence_id, notes, recorded_by
    ) VALUES (
        p_user_id, p_absence_date, 
        CASE WHEN p_has_deduction = 1 THEN 'absent' ELSE p_absence_type END,
        v_absence_id, p_reason, p_applied_by
    )
    ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        absence_id = VALUES(absence_id),
        notes = VALUES(notes),
        updated_at = CURRENT_TIMESTAMP;
    
    COMMIT;
    
    SELECT v_absence_id as absence_id, 'تم تسجيل الغياب/الإجازة بنجاح' as message;
END//

-- إجراء الموافقة على الغياب/الإجازة
CREATE PROCEDURE IF NOT EXISTS approve_absence(
    IN p_absence_id INT,
    IN p_approved_by INT,
    IN p_notes TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- تحديث حالة الغياب
    UPDATE unified_staff_absences 
    SET status = 'approved',
        approved_by = p_approved_by,
        approved_at = CURRENT_TIMESTAMP,
        manager_notes = p_notes
    WHERE id = p_absence_id;
    
    -- تحديث سجل الحضور
    UPDATE staff_attendance sa
    JOIN unified_staff_absences usa ON sa.absence_id = usa.id
    SET sa.status = CASE 
            WHEN usa.has_deduction = 1 THEN 'absent'
            ELSE usa.absence_type
        END,
        sa.check_in_time = NULL,
        sa.check_out_time = NULL
    WHERE usa.id = p_absence_id;
    
    COMMIT;
    
    SELECT 'تم اعتماد الغياب/الإجازة بنجاح' as message;
END//

DELIMITER ;

-- 7. إضافة قيود خارجية (بشكل منفصل لتجنب الأخطاء)
-- Add foreign key constraints (separately to avoid errors)

-- تنظيف البيانات أولاً
SET FOREIGN_KEY_CHECKS = 0;

-- حذف القيود الموجودة إذا كانت موجودة
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_user_id_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_applied_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_approved_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_rejected_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_replacement_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_absence_id_foreign;

-- تنظيف البيانات غير الصحيحة
DELETE FROM unified_staff_absences WHERE user_id NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET applied_by = 1 WHERE applied_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- إضافة القيود الجديدة
ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_user_id_foreign
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_applied_by_foreign
FOREIGN KEY (applied_by) REFERENCES users (id) ON DELETE RESTRICT;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_approved_by_foreign
FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_rejected_by_foreign
FOREIGN KEY (rejected_by) REFERENCES users (id) ON DELETE SET NULL;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_replacement_foreign
FOREIGN KEY (replacement_user_id) REFERENCES users (id) ON DELETE SET NULL;

-- تنظيف جدول staff_attendance
UPDATE staff_attendance SET absence_id = NULL WHERE absence_id IS NOT NULL AND absence_id NOT IN (SELECT id FROM unified_staff_absences);

ALTER TABLE staff_attendance
ADD CONSTRAINT staff_attendance_absence_id_foreign
FOREIGN KEY (absence_id) REFERENCES unified_staff_absences (id) ON DELETE SET NULL;

SET FOREIGN_KEY_CHECKS = 1;

-- 8. إنشاء دوال مساعدة
-- Create helper functions

DELIMITER //

-- دالة للحصول على إحصائيات الغياب
CREATE FUNCTION IF NOT EXISTS get_user_absence_stats(
    p_user_id INT,
    p_start_date DATE,
    p_end_date DATE,
    p_absence_type VARCHAR(50)
) RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_total_days INT DEFAULT 0;
    DECLARE v_total_deductions DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_approved_count INT DEFAULT 0;
    DECLARE v_pending_count INT DEFAULT 0;
    DECLARE v_result JSON;

    SELECT
        COALESCE(SUM(days_count), 0),
        COALESCE(SUM(CASE WHEN has_deduction = 1 THEN deduction_amount ELSE 0 END), 0.00),
        COUNT(CASE WHEN status = 'approved' THEN 1 END),
        COUNT(CASE WHEN status = 'pending' THEN 1 END)
    INTO v_total_days, v_total_deductions, v_approved_count, v_pending_count
    FROM unified_staff_absences
    WHERE user_id = p_user_id
    AND absence_date BETWEEN p_start_date AND p_end_date
    AND (p_absence_type IS NULL OR absence_type = p_absence_type);

    SET v_result = JSON_OBJECT(
        'total_days', v_total_days,
        'total_deductions', v_total_deductions,
        'approved_count', v_approved_count,
        'pending_count', v_pending_count
    );

    RETURN v_result;
END//

DELIMITER ;

SELECT 'Absence system fixed successfully!' as message;
