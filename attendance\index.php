<?php
/**
 * صفحة نظام الحضور والغياب الرئيسية - إعادة توجيه إلى الحضور الذكي
 * Attendance Management Main Page - Redirect to Smart Attendance
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

// إعادة التوجيه إلى صفحة الحضور الذكي مع الحفاظ على المعاملات
$tab = $_GET['tab'] ?? 'students';
$date = $_GET['date'] ?? '';
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';

// بناء رابط إعادة التوجيه
$redirect_url = 'smart_attendance.php?tab=' . urlencode($tab);
if (!empty($date)) {
    $redirect_url .= '&date=' . urlencode($date);
}
if (!empty($search)) {
    $redirect_url .= '&search=' . urlencode($search);
}
if (!empty($status)) {
    $redirect_url .= '&status=' . urlencode($status);
}

header('Location: ' . $redirect_url);
exit();
