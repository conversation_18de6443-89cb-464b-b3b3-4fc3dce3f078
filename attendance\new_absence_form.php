<?php
/**
 * نموذج تسجيل الغياب بالخصم الجديد - مع الترابط التلقائي
 * New Absence with Deduction Form - With Auto Integration
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$error_message = '';
$success_message = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $staff_id = intval($_POST['staff_id']);
        $absence_date = clean_input($_POST['absence_date']);
        $reason = clean_input($_POST['reason']);
        $deduction_amount = floatval($_POST['deduction_amount']);
        $deduction_type = clean_input($_POST['deduction_type']);
        $absence_type = clean_input($_POST['absence_type'] ?? 'unauthorized');
        $days_count = intval($_POST['days_count'] ?? 1);

        if (empty($staff_id) || empty($absence_date)) {
            $error_message = 'الحقول المطلوبة: الموظف وتاريخ الغياب';
        } elseif ($absence_date > date('Y-m-d')) {
            $error_message = 'لا يمكن تسجيل غياب لتاريخ مستقبلي';
        } elseif ($deduction_amount <= 0) {
            $error_message = 'مبلغ الخصم يجب أن يكون أكبر من صفر';
        } else {
            try {
                // التحقق من وجود النظام
                $system_check = $conn->query("SHOW TABLES LIKE 'staff_absences_with_deduction'");
                if ($system_check->num_rows === 0) {
                    throw new Exception('نظام الغياب بالخصم غير مُعد. <a href="../rebuild_absence_system.php">انقر هنا لإعداد النظام</a>');
                }

                $conn->begin_transaction();

                // التحقق من عدم وجود تسجيل مسبق لنفس التاريخ
                $check_stmt = $conn->prepare("
                    SELECT id FROM staff_absences_with_deduction 
                    WHERE user_id = ? AND absence_date = ?
                ");
                $check_stmt->bind_param("is", $staff_id, $absence_date);
                $check_stmt->execute();
                $existing = $check_stmt->get_result();

                if ($existing->num_rows > 0) {
                    throw new Exception('يوجد تسجيل غياب بالخصم مسبق لهذا التاريخ');
                }

                // إدراج الغياب بالخصم
                $absence_stmt = $conn->prepare("
                    INSERT INTO staff_absences_with_deduction
                    (user_id, absence_date, absence_type, days_count, reason, deduction_amount, deduction_type, status, recorded_by, processed_by, processed_at, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'processed', ?, ?, NOW(), ?)
                ");
                $absence_stmt->bind_param("isisdsisii", $staff_id, $absence_date, $absence_type, $days_count, $reason, $deduction_amount, $deduction_type, $user_id, $user_id, $user_id);

                if (!$absence_stmt->execute()) {
                    throw new Exception('فشل في تسجيل الغياب بالخصم: ' . $absence_stmt->error);
                }

                $absence_id = $conn->insert_id;

                // تحديث أو إدراج سجل الحضور ليظهر كغياب بالخصم
                $attendance_stmt = $conn->prepare("
                    INSERT INTO staff_attendance
                    (user_id, attendance_date, status, check_in_time, check_out_time, notes, created_at)
                    VALUES (?, ?, 'absent_with_deduction', NULL, NULL, NULL, NOW())
                    ON DUPLICATE KEY UPDATE
                    status = 'absent_with_deduction',
                    check_in_time = NULL,
                    check_out_time = NULL,
                    notes = NULL,
                    updated_at = NOW()
                ");
                $attendance_stmt->bind_param("is", $staff_id, $absence_date);

                if (!$attendance_stmt->execute()) {
                    throw new Exception('فشل في تحديث سجل الحضور: ' . $attendance_stmt->error);
                }
                $conn->commit();

                // تسجيل النشاط
                log_activity($user_id, 'record_absence_with_deduction', 'staff_absences_with_deduction', $absence_id);

                $success_message = 'تم تسجيل الغياب بالخصم وتفعيله مباشرة في نظام الحضور. مبلغ الخصم: ' . $deduction_amount . ' ج.م';

            } catch (Exception $e) {
                $conn->rollback();
                $error_message = $e->getMessage();
            }
        }
    }
}

// جلب إعدادات الخصم
$deduction_settings = [];
$settings_query = $conn->query("SELECT absence_type, deduction_value FROM deduction_settings WHERE is_active = 1 ORDER BY id");
if ($settings_query && $settings_query->num_rows > 0) {
    while ($setting = $settings_query->fetch_assoc()) {
        $deduction_settings[$setting['absence_type']] = $setting['deduction_value'];
    }
}

// القيم الافتراضية للخصم
$default_daily = $deduction_settings['unauthorized'] ?? 100;
$default_hourly = $deduction_settings['personal'] ?? 15;

// جلب قائمة الموظفين
$staff_query = $conn->query("
    SELECT id, full_name, role
    FROM users
    WHERE role IN ('teacher', 'staff') AND status = 'active'
    AND role NOT IN ('admin', 'super_admin', 'system_admin')
    ORDER BY full_name
");
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-danger text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-times me-2"></i>
                        تسجيل غياب بالخصم - النظام الجديد
                    </h4>
                </div>
                
                <div class="card-body">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>تحقق من الترابط:</h5>
                            <ul class="mb-0">
                                <li><a href="smart_attendance.php?tab=teachers" class="alert-link">الحضور الذكي - المعلمين</a></li>
                                <li><a href="smart_attendance.php?tab=admins" class="alert-link">الحضور الذكي - الإداريين</a></li>
                                <li><a href="manage_absences.php" class="alert-link">إدارة الغياب بالخصم</a></li>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- تحذير مهم -->
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> تسجيل الغياب بالخصم سيؤثر على راتب الموظف ويتم تفعيله مباشرة في نظام الحضور. يرجى التأكد من صحة البيانات قبل الحفظ.
                    </div>

                    <form method="POST" id="absenceForm" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="staff_id" class="form-label">الموظف *</label>
                                    <select class="form-select" id="staff_id" name="staff_id" required>
                                        <option value="">اختر الموظف</option>
                                        <?php if ($staff_query): ?>
                                            <?php while ($staff = $staff_query->fetch_assoc()): ?>
                                                <option value="<?php echo $staff['id']; ?>" data-role="<?php echo $staff['role']; ?>">
                                                    <?php echo htmlspecialchars($staff['full_name']); ?> 
                                                    (<?php echo $staff['role'] === 'teacher' ? 'معلم' : 'إداري'; ?>)
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الموظف</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="absence_date" class="form-label">تاريخ الغياب *</label>
                                    <input type="date" class="form-control" id="absence_date" name="absence_date" 
                                           max="<?php echo date('Y-m-d'); ?>" required>
                                    <div class="invalid-feedback">يرجى تحديد تاريخ الغياب</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deduction_type" class="form-label">نوع الخصم *</label>
                                    <select class="form-select" id="deduction_type" name="deduction_type" required>
                                        <option value="daily_wage">خصم يومي كامل</option>
                                        <option value="hourly_wage">خصم بالساعة</option>
                                        <option value="fixed_amount">مبلغ ثابت</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deduction_amount" class="form-label">مبلغ الخصم (ج.م) *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="deduction_amount" name="deduction_amount" 
                                               step="0.01" min="0" value="<?php echo $default_daily; ?>" required>
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                    <small class="text-muted">سيتم تحديث المبلغ تلقائياً حسب نوع الخصم</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">سبب الغياب (اختياري)</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" 
                                      placeholder="اكتب سبب الغياب بالخصم (اختياري)..."></textarea>
                            <small class="text-muted">يمكنك ترك هذا الحقل فارغاً إذا لم يكن هناك سبب محدد</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-save me-2"></i>تسجيل الغياب بالخصم
                            </button>
                            <a href="smart_attendance.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>العودة للحضور الذكي
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const deductionTypeSelect = document.getElementById('deduction_type');
    const deductionAmountInput = document.getElementById('deduction_amount');
    const absenceForm = document.getElementById('absenceForm');
    
    // تحديث مبلغ الخصم حسب النوع
    deductionTypeSelect.addEventListener('change', function() {
        const type = this.value;
        let amount = <?php echo $default_daily; ?>;
        
        switch(type) {
            case 'daily_wage':
                amount = <?php echo $default_daily; ?>;
                break;
            case 'hourly_wage':
                amount = <?php echo $default_hourly; ?>;
                break;
            case 'fixed_amount':
                amount = 0;
                break;
        }
        
        deductionAmountInput.value = amount;
        if (type === 'fixed_amount') {
            deductionAmountInput.focus();
        }
    });
    
    // تأكيد إضافي قبل الإرسال
    absenceForm.addEventListener('submit', function(e) {
        const staffSelect = document.getElementById('staff_id');
        const dateInput = document.getElementById('absence_date');
        const amountInput = document.getElementById('deduction_amount');
        
        const staffName = staffSelect.options[staffSelect.selectedIndex].text;
        const date = dateInput.value;
        const amount = amountInput.value;
        
        if (!confirm(`هل أنت متأكد من تسجيل غياب بالخصم؟\n\nالموظف: ${staffName}\nالتاريخ: ${date}\nمبلغ الخصم: ${amount} ج.م\n\nسيتم تفعيل الغياب مباشرة في نظام الحضور.`)) {
            e.preventDefault();
        }
    });
    
    // Bootstrap validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>

<?php require_once '../includes/footer.php'; ?>
