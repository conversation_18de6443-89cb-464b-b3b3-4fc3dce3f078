<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('add_subject');

// الحصول على المراحل الدراسية
$stages_query = "SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = [];
if ($stages_result) {
    while ($row = $stages_result->fetch_assoc()) {
        $stages[] = $row;
    }
}

// الحصول على الصفوف الدراسية
$grades_query = "
    SELECT g.id, g.grade_name, g.grade_code, g.stage_id, es.stage_name
    FROM grades g
    INNER JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order, g.sort_order
";
$grades_result = $conn->query($grades_query);
$grades = [];
if ($grades_result) {
    while ($row = $grades_result->fetch_assoc()) {
        $grades[] = $row;
    }
}

// الحصول على المعاملات من URL
$selected_stage_id = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;
$selected_grade_id = isset($_GET['grade_id']) ? intval($_GET['grade_id']) : 0;

$error = '';
$success = '';
$validation_errors = [];

// متغيرات النموذج
$subject_name = '';
$subject_name_en = '';
$subject_code = '';
$stage_id = $selected_stage_id;
$description = '';
$credit_hours = 1;
$department = '';
$status = 'active';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // تنظيف البيانات
    $subject_name = trim($_POST['subject_name'] ?? '');
    $subject_name_en = trim($_POST['subject_name_en'] ?? '');
    $subject_code = trim($_POST['subject_code'] ?? '');
    $stage_id = intval($_POST['stage_id'] ?? 0);
    $grade_id = intval($_POST['grade_id'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $credit_hours = intval($_POST['credit_hours'] ?? 1);
    $department = trim($_POST['department'] ?? '');
    $status = $_POST['status'] ?? 'active';

    // التحقق من صحة البيانات
    if (empty($subject_name)) {
        $validation_errors['subject_name'] = __('subject_name') . ' ' . __('required_field');
    }

    if (empty($subject_code)) {
        $validation_errors['subject_code'] = __('subject_code') . ' ' . __('required_field');
    } elseif (!preg_match('/^[A-Z0-9_]+$/', $subject_code)) {
        $validation_errors['subject_code'] = __('subject_code_invalid_format');
    }

    if ($stage_id <= 0) {
        $validation_errors['stage_id'] = __('educational_stage') . ' ' . __('required_field');
    }

    if ($grade_id <= 0) {
        $validation_errors['grade_id'] = __('school_grade') . ' ' . __('required_field');
    }

    if ($credit_hours <= 0) {
        $validation_errors['credit_hours'] = __('credit_hours') . ' ' . __('required_field');
    }

    // التحقق من عدم تكرار رمز المادة
    if (empty($validation_errors['subject_code'])) {
        $check_query = "SELECT id FROM subjects WHERE subject_code = ?";
        $stmt = $conn->prepare($check_query);
        if ($stmt) {
            $stmt->bind_param('s', $subject_code);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $validation_errors['subject_code'] = __('subject_code_exists');
            }
            $stmt->close();
        }
    }

    // إذا لم توجد أخطاء، قم بإدراج البيانات
    if (empty($validation_errors)) {
        $insert_query = "
            INSERT INTO subjects
            (subject_name, subject_name_en, subject_code, stage_id, grade_id, description, credit_hours, department, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";

        $stmt = $conn->prepare($insert_query);
        if ($stmt) {
            $stmt->bind_param('sssiisiss',
                $subject_name, $subject_name_en, $subject_code, $stage_id, $grade_id,
                $description, $credit_hours, $department, $status
            );

            if ($stmt->execute()) {
                $subject_id = $conn->insert_id;

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'add_subject', 'subjects', $subject_id, null, [
                    'subject_name' => $subject_name,
                    'subject_code' => $subject_code
                ]);

                $success = __('subject_added_successfully');

                // إعادة تعيين المتغيرات
                $subject_name = $subject_name_en = $subject_code = $description = $department = '';
                $stage_id = $selected_stage_id;
                $credit_hours = 1;
                $status = 'active';

            } else {
                $error = __('database_error') . ': ' . $conn->error;
            }
            $stmt->close();
        } else {
            $error = __('database_error') . ': ' . $conn->error;
        }
    } else {
        $error = __('please_fix_errors');
    }
}
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-plus text-primary me-2"></i>
                <?php echo __('add_subject'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('add_new_subject'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_subjects'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Form Section -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('subject_information'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        <div class="row">
                            <!-- اسم المادة بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label for="subject_name" class="form-label">
                                    <?php echo __('subject_name'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control <?php echo isset($validation_errors['subject_name']) ? 'is-invalid' : ''; ?>"
                                       id="subject_name"
                                       name="subject_name"
                                       value="<?php echo htmlspecialchars($subject_name); ?>"
                                       placeholder="<?php echo __('enter_subject_name'); ?>"
                                       required>
                                <?php if (isset($validation_errors['subject_name'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['subject_name']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- اسم المادة بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label for="subject_name_en" class="form-label">
                                    <?php echo __('subject_name_en'); ?>
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="subject_name_en"
                                       name="subject_name_en"
                                       value="<?php echo htmlspecialchars($subject_name_en); ?>"
                                       placeholder="<?php echo __('enter_subject_name_en'); ?>">
                            </div>
                        </div>

                        <div class="row">
                            <!-- رمز المادة -->
                            <div class="col-md-4 mb-3">
                                <label for="subject_code" class="form-label">
                                    <?php echo __('subject_code'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control <?php echo isset($validation_errors['subject_code']) ? 'is-invalid' : ''; ?>"
                                       id="subject_code"
                                       name="subject_code"
                                       value="<?php echo htmlspecialchars($subject_code); ?>"
                                       placeholder="<?php echo __('enter_subject_code'); ?>"
                                       style="text-transform: uppercase;"
                                       pattern="[A-Z0-9_]+"
                                       required>
                                <div class="form-text"><?php echo __('subject_code_help'); ?></div>
                                <?php if (isset($validation_errors['subject_code'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['subject_code']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- المرحلة الدراسية -->
                            <div class="col-md-4 mb-3">
                                <label for="stage_id" class="form-label">
                                    <?php echo __('educational_stage'); ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?php echo isset($validation_errors['stage_id']) ? 'is-invalid' : ''; ?>"
                                        id="stage_id" name="stage_id" required onchange="updateGrades()">
                                    <option value=""><?php echo __('select_stage'); ?></option>
                                    <?php foreach ($stages as $stage): ?>
                                        <option value="<?php echo $stage['id']; ?>"
                                                <?php echo $stage_id == $stage['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($stage['stage_name']); ?> (<?php echo htmlspecialchars($stage['stage_code']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($validation_errors['stage_id'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['stage_id']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- الصف الدراسي -->
                            <div class="col-md-4 mb-3">
                                <label for="grade_id" class="form-label">
                                    <?php echo __('school_grade'); ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?php echo isset($validation_errors['grade_id']) ? 'is-invalid' : ''; ?>"
                                        id="grade_id" name="grade_id" required>
                                    <option value=""><?php echo __('select_grade_first'); ?></option>
                                    <?php foreach ($grades as $grade): ?>
                                        <option value="<?php echo $grade['id']; ?>"
                                                data-stage="<?php echo $grade['stage_id']; ?>"
                                                <?php echo (isset($grade_id) ? $grade_id : $selected_grade_id) == $grade['id'] ? 'selected' : ''; ?>
                                                style="display: none;">
                                            <?php echo htmlspecialchars($grade['grade_name']); ?> (<?php echo htmlspecialchars($grade['grade_code']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($validation_errors['grade_id'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['grade_id']; ?>
                                    </div>
                                <?php endif; ?>
                                <div class="form-text"><?php echo __('select_stage_first'); ?></div>
                            </div>

                            <!-- الساعات المعتمدة -->
                            <div class="col-md-4 mb-3">
                                <label for="credit_hours" class="form-label">
                                    <?php echo __('credit_hours'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="number"
                                       class="form-control <?php echo isset($validation_errors['credit_hours']) ? 'is-invalid' : ''; ?>"
                                       id="credit_hours"
                                       name="credit_hours"
                                       value="<?php echo $credit_hours; ?>"
                                       min="1"
                                       max="10"
                                       required>
                                <?php if (isset($validation_errors['credit_hours'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['credit_hours']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <!-- القسم -->
                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">
                                    <?php echo __('department'); ?>
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="department"
                                       name="department"
                                       value="<?php echo htmlspecialchars($department); ?>"
                                       placeholder="<?php echo __('enter_department'); ?>">
                            </div>

                            <!-- حالة المادة -->
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">
                                    <?php echo __('status'); ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>
                                        <?php echo __('active'); ?>
                                    </option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>
                                        <?php echo __('inactive'); ?>
                                    </option>
                                </select>
                            </div>
                        </div>

                        <!-- الوصف -->
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <?php echo __('description'); ?>
                            </label>
                            <textarea class="form-control"
                                      id="description"
                                      name="description"
                                      rows="4"
                                      placeholder="<?php echo __('enter_subject_description'); ?>"><?php echo htmlspecialchars($description); ?></textarea>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('save_subject'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar with Help -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        <?php echo __('help_and_tips'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary"><?php echo __('subject_code'); ?></h6>
                        <p class="small text-muted"><?php echo __('subject_code_tip'); ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary"><?php echo __('educational_stage'); ?></h6>
                        <p class="small text-muted"><?php echo __('stage_subject_tip'); ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary"><?php echo __('credit_hours'); ?></h6>
                        <p class="small text-muted"><?php echo __('credit_hours_tip'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateGrades() {
    const stageSelect = document.getElementById('stage_id');
    const gradeSelect = document.getElementById('grade_id');
    const selectedStage = stageSelect.value;

    // إخفاء جميع الخيارات
    const gradeOptions = gradeSelect.querySelectorAll('option[data-stage]');
    gradeOptions.forEach(option => {
        option.style.display = 'none';
        option.selected = false;
    });

    // إعادة تعيين القيمة
    gradeSelect.value = '';

    if (selectedStage) {
        // إظهار الصفوف المرتبطة بالمرحلة المختارة
        const relevantOptions = gradeSelect.querySelectorAll(`option[data-stage="${selectedStage}"]`);
        relevantOptions.forEach(option => {
            option.style.display = 'block';
        });
    }
}

// تشغيل التحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrades();
});

// تحويل رمز المادة إلى أحرف كبيرة
document.getElementById('subject_code').addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9_]/g, '');
});
</script>

<?php require_once '../includes/footer.php'; ?>