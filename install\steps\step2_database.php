<h3 class="mb-4">
    <i class="fas fa-database text-primary me-2"></i>
    Database Configuration
</h3>

<p class="text-muted mb-4">
    Please provide your database connection details. The installer will create the database if it doesn't exist.
</p>

<form method="POST" action="?step=2" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="db_host" class="form-label">Database Host <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control" 
                       id="db_host" 
                       name="db_host" 
                       value="<?php echo htmlspecialchars($_POST['db_host'] ?? 'localhost'); ?>"
                       required>
                <div class="form-text">Usually 'localhost' for local servers</div>
                <div class="invalid-feedback">
                    Please provide a database host.
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label for="db_name" class="form-label">Database Name <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control" 
                       id="db_name" 
                       name="db_name" 
                       value="<?php echo htmlspecialchars($_POST['db_name'] ?? 'school_management'); ?>"
                       required>
                <div class="form-text">Will be created if it doesn't exist</div>
                <div class="invalid-feedback">
                    Please provide a database name.
                </div>
            </div>
        </div>
    </div>
    
    <div class="form-check mb-3">
        <input type="checkbox" 
               class="form-check-input" 
               id="cleanup" 
               name="cleanup" 
               value="1">
        <label class="form-check-label" for="cleanup">
            تنظيف قاعدة البيانات (حذف الجداول الموجودة إذا كانت موجودة)
        </label>
        <div class="form-text text-danger">
            تنبيه: سيتم حذف جميع البيانات الموجودة في قاعدة البيانات إذا قمت بتحديد هذا الخيار
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="db_user" class="form-label">Database Username <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control" 
                       id="db_user" 
                       name="db_user" 
                       value="<?php echo htmlspecialchars($_POST['db_user'] ?? 'root'); ?>"
                       required>
                <div class="invalid-feedback">
                    Please provide a database username.
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label for="db_pass" class="form-label">Database Password</label>
                <input type="password" 
                       class="form-control" 
                       id="db_pass" 
                       name="db_pass" 
                       value="">
                <div class="form-text">Leave empty if no password is required</div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Note:</strong> Make sure your database user has the following privileges:
        <ul class="mb-0 mt-2">
            <li>CREATE - To create the database</li>
            <li>SELECT, INSERT, UPDATE, DELETE - For data operations</li>
            <li>CREATE, ALTER, DROP - For table operations</li>
            <li>INDEX - For creating indexes</li>
        </ul>
    </div>
    
    <div class="d-flex justify-content-between">
        <a href="?step=1" class="btn btn-secondary btn-lg">
            <i class="fas fa-arrow-left me-2"></i>Back
        </a>
        <button type="submit" class="btn btn-primary btn-lg">
            Test Connection & Continue <i class="fas fa-arrow-right ms-2"></i>
        </button>
    </div>
</form>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
