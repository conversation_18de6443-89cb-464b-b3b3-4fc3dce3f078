<?php
/**
 * تقارير الإجازات
 * Leave Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

// فلاتر التقرير
$year = $_GET['year'] ?? date('Y');
$month = $_GET['month'] ?? '';
$leave_type = $_GET['leave_type'] ?? '';
$status = $_GET['status'] ?? '';

// بناء الاستعلام
$where_conditions = ["YEAR(sl.created_at) = ?"];
$params = [$year];
$types = "s";

if (!empty($month)) {
    $where_conditions[] = "MONTH(sl.created_at) = ?";
    $params[] = $month;
    $types .= "s";
}

if (!empty($leave_type)) {
    $where_conditions[] = "sl.leave_type = ?";
    $params[] = $leave_type;
    $types .= "s";
}

if (!empty($status)) {
    $where_conditions[] = "sl.status = ?";
    $params[] = $status;
    $types .= "s";
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

// جلب البيانات
$query = "
    SELECT sl.*, u.full_name, u.role,
           approver.full_name as approved_by_name
    FROM staff_leaves sl
    JOIN users u ON sl.user_id = u.id
    LEFT JOIN users approver ON sl.approved_by = approver.id
    $where_clause
    ORDER BY sl.created_at DESC
";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$leaves_result = $stmt->get_result();

// حساب الإحصائيات
$stats_query = "
    SELECT 
        COUNT(*) as total_leaves,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_leaves,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_leaves,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_leaves,
        SUM(CASE WHEN status = 'approved' THEN total_days ELSE 0 END) as total_approved_days,
        SUM(CASE WHEN leave_type = 'sick' AND status = 'approved' THEN total_days ELSE 0 END) as sick_days,
        SUM(CASE WHEN leave_type = 'regular' AND status = 'approved' THEN total_days ELSE 0 END) as regular_days
    FROM staff_leaves sl
    $where_clause
";

$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->bind_param($types, ...$params);
$stats_stmt->execute();
$stats_result = $stats_stmt->get_result();
$stats = $stats_result->fetch_assoc();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقارير الإجازات
                        </h5>
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="year" class="form-label">السنة</label>
                                <select class="form-select" id="year" name="year">
                                    <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                        <option value="<?php echo $y; ?>" <?php echo $year == $y ? 'selected' : ''; ?>>
                                            <?php echo $y; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="month" class="form-label">الشهر</label>
                                <select class="form-select" id="month" name="month">
                                    <option value="">جميع الشهور</option>
                                    <?php for ($m = 1; $m <= 12; $m++): ?>
                                        <option value="<?php echo $m; ?>" <?php echo $month == $m ? 'selected' : ''; ?>>
                                            <?php echo date('F', mktime(0, 0, 0, $m, 1)); ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="leave_type" class="form-label">نوع الإجازة</label>
                                <select class="form-select" id="leave_type" name="leave_type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="sick" <?php echo $leave_type === 'sick' ? 'selected' : ''; ?>>إجازة مرضية</option>
                                    <option value="regular" <?php echo $leave_type === 'regular' ? 'selected' : ''; ?>>إجازة اعتيادية</option>
                                    <option value="emergency" <?php echo $leave_type === 'emergency' ? 'selected' : ''; ?>>إجازة طارئة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                    <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>موافق عليها</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>مرفوضة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                </button>
                                <a href="reports.php" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- إحصائيات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['total_leaves']; ?></h4>
                                    <p class="mb-0">إجمالي الإجازات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['pending_leaves']; ?></h4>
                                    <p class="mb-0">في الانتظار</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['approved_leaves']; ?></h4>
                                    <p class="mb-0">موافق عليها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['rejected_leaves']; ?></h4>
                                    <p class="mb-0">مرفوضة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['sick_days']; ?></h4>
                                    <p class="mb-0">أيام مرضية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['regular_days']; ?></h4>
                                    <p class="mb-0">أيام اعتيادية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول التقرير -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الموافق عليها</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($leaves_result->num_rows > 0): ?>
                                    <?php while ($leave = $leaves_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($leave['full_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo $leave['role']; ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $leave_types = [
                                                    'sick' => 'إجازة مرضية',
                                                    'regular' => 'إجازة اعتيادية',
                                                    'emergency' => 'إجازة طارئة'
                                                ];
                                                echo $leave_types[$leave['leave_type']] ?? $leave['leave_type'];
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['start_date'])); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['end_date'])); ?></td>
                                            <td><?php echo $leave['total_days']; ?> يوم</td>
                                            <td>
                                                <?php
                                                $status_badges = [
                                                    'pending' => 'warning',
                                                    'approved' => 'success',
                                                    'rejected' => 'danger'
                                                ];
                                                $status_text = [
                                                    'pending' => 'في الانتظار',
                                                    'approved' => 'موافق عليها',
                                                    'rejected' => 'مرفوضة'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_badges[$leave['status']]; ?>">
                                                    <?php echo $status_text[$leave['status']]; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['created_at'])); ?></td>
                                            <td><?php echo htmlspecialchars($leave['approved_by_name'] ?? '-'); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center">لا توجد إجازات تطابق المعايير المحددة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
