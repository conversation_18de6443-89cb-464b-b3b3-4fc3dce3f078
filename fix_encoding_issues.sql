-- إصلاح مشاكل ترميز النصوص العربية
-- Fix Arabic text encoding issues

USE school_management;

-- تحديث ترميز قاعدة البيانات
ALTER DATABASE school_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح ترميز جدول users
ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحديث البيانات المُشوهة في جدول users
UPDATE users SET full_name = 'مدير النظام' WHERE id = 1 AND (full_name LIKE '%Ù…%' OR full_name = 'مدير النظام');
UPDATE users SET full_name = 'معلم أول' WHERE id = 2 AND (full_name LIKE '%Ù…%' OR full_name = 'معلم تجريبي');
UPDATE users SET full_name = 'موظف إداري' WHERE id = 3 AND (full_name LIKE '%Ù…%' OR full_name = 'موظف إداري');

-- تحديث أي بيانات أخرى مُشوهة
UPDATE users SET full_name = 
    CASE 
        WHEN full_name LIKE '%Ù…ÙˆØ¸Ù%' THEN 'موظف إداري'
        WHEN full_name LIKE '%Ù…Ø¯ÙŠØ±%' THEN 'مدير النظام'
        WHEN full_name LIKE '%Ù…Ø¹Ù„Ù…%' THEN 'معلم'
        WHEN full_name LIKE '%Ø·Ø§Ù„Ø¨%' THEN 'طالب'
        ELSE full_name
    END
WHERE full_name LIKE '%Ù%' OR full_name LIKE '%Ø%';

-- إصلاح ترميز الجداول الأخرى
ALTER TABLE staff_attendance CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE staff_absences_with_deduction CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE deduction_settings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE leave_types CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE system_settings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحديث البيانات في جدول leave_types
UPDATE leave_types SET 
    name = CASE 
        WHEN id = 1 THEN 'إجازة سنوية'
        WHEN id = 2 THEN 'إجازة مرضية'
        WHEN id = 3 THEN 'إجازة طارئة'
        WHEN id = 4 THEN 'إجازة أمومة'
        WHEN id = 5 THEN 'إجازة أبوة'
        WHEN id = 6 THEN 'إجازة حج/عمرة'
        WHEN id = 7 THEN 'إجازة بدون راتب'
        WHEN id = 8 THEN 'إجازة دراسية'
        ELSE name
    END
WHERE name LIKE '%Ù%' OR name LIKE '%Ø%';

-- تحديث البيانات في جدول deduction_settings
UPDATE deduction_settings SET 
    description = CASE 
        WHEN absence_type = 'sick' THEN 'إجازة مرضية - عادة بدون خصم مع تقرير طبي'
        WHEN absence_type = 'personal' THEN 'إجازة شخصية - خصم 50 ريال لليوم الواحد'
        WHEN absence_type = 'emergency' THEN 'إجازة طارئة - خصم 25 ريال لليوم الواحد'
        WHEN absence_type = 'unauthorized' THEN 'غياب غير مبرر - خصم 100 ريال لليوم الواحد'
        ELSE description
    END
WHERE description LIKE '%Ù%' OR description LIKE '%Ø%';

-- تحديث البيانات في جدول system_settings
UPDATE system_settings SET 
    setting_value = CASE 
        WHEN setting_key = 'system_name' THEN 'نظام إدارة المدارس'
        WHEN setting_key = 'currency' THEN 'ريال سعودي'
        WHEN setting_key = 'currency_symbol' THEN 'ر.س'
        ELSE setting_value
    END,
    description = CASE 
        WHEN setting_key = 'system_name' THEN 'اسم النظام'
        WHEN setting_key = 'system_version' THEN 'إصدار النظام'
        WHEN setting_key = 'timezone' THEN 'المنطقة الزمنية'
        WHEN setting_key = 'language' THEN 'اللغة الافتراضية'
        WHEN setting_key = 'currency' THEN 'العملة الافتراضية'
        WHEN setting_key = 'currency_symbol' THEN 'رمز العملة'
        WHEN setting_key = 'academic_year_start' THEN 'بداية العام الدراسي'
        WHEN setting_key = 'academic_year_end' THEN 'نهاية العام الدراسي'
        ELSE description
    END
WHERE (setting_value LIKE '%Ù%' OR setting_value LIKE '%Ø%') 
   OR (description LIKE '%Ù%' OR description LIKE '%Ø%');

-- إنشاء دالة لتنظيف النصوص المُشوهة
DELIMITER //

CREATE FUNCTION IF NOT EXISTS clean_arabic_text(input_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci)
RETURNS TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
DETERMINISTIC
BEGIN
    DECLARE cleaned_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    
    SET cleaned_text = input_text;
    
    -- استبدال النصوص المُشوهة الشائعة
    SET cleaned_text = REPLACE(cleaned_text, 'Ù…ÙˆØ¸Ù Ø¥Ø¯Ø§Ø±ÙŠ', 'موظف إداري');
    SET cleaned_text = REPLACE(cleaned_text, 'Ù…Ø¯ÙŠØ± Ø§Ù„Ù†Ø¸Ø§Ù…', 'مدير النظام');
    SET cleaned_text = REPLACE(cleaned_text, 'Ù…Ø¹Ù„Ù…', 'معلم');
    SET cleaned_text = REPLACE(cleaned_text, 'Ø·Ø§Ù„Ø¨', 'طالب');
    SET cleaned_text = REPLACE(cleaned_text, 'Ø¥Ø¬Ø§Ø²Ø©', 'إجازة');
    SET cleaned_text = REPLACE(cleaned_text, 'ØºÙŠØ§Ø¨', 'غياب');
    SET cleaned_text = REPLACE(cleaned_text, 'Ø­Ø¶ÙˆØ±', 'حضور');
    
    RETURN cleaned_text;
END//

DELIMITER ;

-- تطبيق التنظيف على جميع الجداول
UPDATE users SET full_name = clean_arabic_text(full_name) WHERE full_name LIKE '%Ù%' OR full_name LIKE '%Ø%';
UPDATE leave_types SET name = clean_arabic_text(name) WHERE name LIKE '%Ù%' OR name LIKE '%Ø%';
UPDATE deduction_settings SET description = clean_arabic_text(description) WHERE description LIKE '%Ù%' OR description LIKE '%Ø%';
UPDATE system_settings SET setting_value = clean_arabic_text(setting_value), description = clean_arabic_text(description) 
WHERE (setting_value LIKE '%Ù%' OR setting_value LIKE '%Ø%') OR (description LIKE '%Ù%' OR description LIKE '%Ø%');

-- تحديث أي سجلات في staff_absences_with_deduction
UPDATE staff_absences_with_deduction SET reason = clean_arabic_text(reason) WHERE reason LIKE '%Ù%' OR reason LIKE '%Ø%';
UPDATE staff_absences_with_deduction SET notes = clean_arabic_text(notes) WHERE notes LIKE '%Ù%' OR notes LIKE '%Ø%';

-- إضافة مستخدمين جدد بأسماء صحيحة إذا لم يكونوا موجودين
INSERT IGNORE INTO users (id, username, email, password, full_name, role, status) VALUES
(10, 'admin_main', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام الرئيسي', 'admin', 'active'),
(11, 'teacher_arabic', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'معلم اللغة العربية', 'teacher', 'active'),
(12, 'staff_admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف إداري أول', 'staff', 'active'),
(13, 'staff_finance', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف الشؤون المالية', 'staff', 'active');

-- التأكد من صحة الترميز لجميع الجداول
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء view لعرض البيانات بترميز صحيح
CREATE OR REPLACE VIEW users_clean AS
SELECT 
    id,
    username,
    email,
    full_name,
    role,
    status,
    CASE 
        WHEN role = 'admin' THEN 'مدير'
        WHEN role = 'teacher' THEN 'معلم'
        WHEN role = 'staff' THEN 'موظف إداري'
        WHEN role = 'student' THEN 'طالب'
        WHEN role = 'parent' THEN 'ولي أمر'
        ELSE role
    END as role_ar,
    CASE 
        WHEN status = 'active' THEN 'نشط'
        WHEN status = 'inactive' THEN 'غير نشط'
        WHEN status = 'suspended' THEN 'موقوف'
        WHEN status = 'pending' THEN 'في الانتظار'
        ELSE status
    END as status_ar,
    created_at,
    updated_at
FROM users;

SELECT 'Arabic text encoding issues fixed successfully!' as message;
