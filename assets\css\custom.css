/**
 * School Management System - Custom Styles
 * نظام إدارة المدارس - التنسيقات المخصصة
 */

/* ===================================
   CSS VARIABLES & ROOT STYLES
   ================================== */
:root {
    /* Primary Colors */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #7c8ef7;
    
    /* Secondary Colors */
    --secondary-color: #764ba2;
    --secondary-dark: #6b4190;
    --secondary-light: #8b5fb4;
    
    /* Status Colors */
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #f56565;
    --info-color: #4299e1;
    
    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Typography */
    --font-family-ar: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-en: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===================================
   GLOBAL STYLES
   ================================== */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-ar);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    transition: all var(--transition-normal);
}

/* Arabic Font */
html[lang="ar"] body {
    font-family: var(--font-family-ar);
}

/* English Font */
html[lang="en"] body {
    font-family: var(--font-family-en);
}

/* ===================================
   TYPOGRAPHY
   ================================== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--gray-800);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===================================
   LAYOUT COMPONENTS
   ================================== */

/* Header */
.main-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1030;
}

.main-header .navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: white !important;
}

/* Sidebar */
.main-sidebar {
    background: white;
    box-shadow: var(--shadow-lg);
    border-right: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.sidebar-nav {
    padding: var(--spacing-md) 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    margin: 0 var(--spacing-md);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
    transform: translateX(4px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-link i {
    width: 20px;
    margin-left: var(--spacing-md);
    text-align: center;
}

/* Main Content */
.main-content {
    background-color: var(--gray-50);
    min-height: calc(100vh - 70px);
    padding: var(--spacing-xl);
    transition: all var(--transition-normal);
}

/* ===================================
   CARD COMPONENTS
   ================================== */
.card {
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--gray-50), white);
    border-bottom: 1px solid var(--gray-200);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0 !important;
    padding: var(--spacing-lg);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-stats {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    position: relative;
}

.card-stats::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

/* ===================================
   BUTTON COMPONENTS
   ================================== */
.btn {
    border-radius: var(--border-radius-lg);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: all var(--transition-fast);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #38a169);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #dd6b20);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #e53e3e);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #3182ce);
}

/* ===================================
   FORM COMPONENTS
   ================================== */
.form-control {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    transition: all var(--transition-fast);
    background-color: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background-color: white;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.input-group {
    position: relative;
}

.input-group-text {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-50));
    border: 2px solid var(--gray-200);
    color: var(--gray-600);
}

/* ===================================
   TABLE COMPONENTS
   ================================== */
.table {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-50));
    border: none;
    font-weight: 600;
    color: var(--gray-700);
    padding: var(--spacing-lg);
}

.table tbody td {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-100);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--gray-50);
}

/* ===================================
   BADGE COMPONENTS
   ================================== */
.badge {
    border-radius: var(--border-radius-lg);
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-md);
}

.badge-success {
    background: linear-gradient(135deg, var(--success-color), #38a169);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-color), #dd6b20);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-color), #e53e3e);
}

.badge-info {
    background: linear-gradient(135deg, var(--info-color), #3182ce);
}

/* ===================================
   ALERT COMPONENTS
   ================================== */
.alert {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
}

.alert-success::before {
    background: var(--success-color);
}

.alert-warning::before {
    background: var(--warning-color);
}

.alert-danger::before {
    background: var(--danger-color);
}

.alert-info::before {
    background: var(--info-color);
}

/* ===================================
   MODAL COMPONENTS
   ================================== */
.modal-content {
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: linear-gradient(135deg, var(--gray-50), white);
    border-bottom: 1px solid var(--gray-200);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.modal-title {
    font-weight: 600;
    color: var(--gray-800);
}

/* إصلاح مشاكل المودال والطبقة الخلفية */
.modal-backdrop {
    opacity: 0.5 !important;
    z-index: 2050 !important;
}
.modal.show, .modal.fade.show {
    opacity: 1 !important;
    z-index: 2100 !important;
    pointer-events: auto !important;
}
.modal-dialog {
    pointer-events: auto !important;
}

/* ===================================
   LOADING COMPONENTS
   ================================== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
}

/* ===================================
   NOTIFICATION COMPONENTS
   ================================== */
.notification-toast {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1050;
    min-width: 300px;
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* ===================================
   RESPONSIVE DESIGN
   ================================== */
@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .table-responsive {
        border-radius: var(--border-radius-lg);
    }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}

/* ===================================
   DARK THEME
   ================================== */
[data-theme="dark"] {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
}

[data-theme="dark"] body {
    background-color: var(--gray-50);
    color: var(--gray-700);
}

[data-theme="dark"] .card {
    background-color: var(--gray-100);
}

[data-theme="dark"] .form-control {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-700);
}

[data-theme="dark"] .table {
    background-color: var(--gray-100);
}

/* ===================================
   PRINT STYLES
   ================================== */
@media print {
    .no-print {
        display: none !important;
    }
    
    .main-sidebar {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .btn {
        display: none !important;
    }
}

/* ===================================
   UTILITY CLASSES
   ================================== */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-secondary { border-color: var(--secondary-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-danger { border-color: var(--danger-color) !important; }
.border-info { border-color: var(--info-color) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-2xl { border-radius: var(--border-radius-2xl) !important; }

/* تصحيح قوي جدًا لتفعيل النوافذ المنبثقة دومًا فوق كل شيء وقبول التفاعل */
body.modal-open,
body.modal-open .main-content,
body.modal-open .container-fluid {
    filter: none !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    background: none !important;
}
.modal,
.modal.show,
.modal.fade.show,
.modal-dialog,
.modal-content {
    z-index: 99999 !important;
    pointer-events: auto !important;
    opacity: 1 !important;
    background: #fff !important;
    filter: none !important;
}
.modal-backdrop {
    z-index: 99998 !important;
    opacity: 0.5 !important;
    pointer-events: none !important;
}

#loading-overlay {
    pointer-events: none !important;
    background: transparent !important;
    opacity: 0 !important;
}
