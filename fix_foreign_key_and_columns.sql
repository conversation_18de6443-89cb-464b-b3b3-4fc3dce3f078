-- إصلاح مشاكل القيود الخارجية والأعمدة المفقودة
-- Fix foreign key constraints and missing columns

USE school_management;

-- إضافة العمود المفقود processed_at
ALTER TABLE staff_absences_with_deduction 
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP NULL DEFAULT NULL AFTER approved_at;

-- إضافة أعمدة إضافية مفيدة إذا لم تكن موجودة
ALTER TABLE staff_absences_with_deduction 
ADD COLUMN IF NOT EXISTS rejection_reason TEXT NULL DEFAULT NULL AFTER processed_at;

ALTER TABLE staff_absences_with_deduction 
ADD COLUMN IF NOT EXISTS rejected_by INT(10) UNSIGNED NULL DEFAULT NULL AFTER rejection_reason;

ALTER TABLE staff_absences_with_deduction 
ADD COLUMN IF NOT EXISTS rejected_at TIMESTAMP NULL DEFAULT NULL AFTER rejected_by;

-- التأكد من وجود مستخدمين للاختبار
INSERT IGNORE INTO users (id, username, email, password, full_name, role, status) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 'active'),
(2, 'teacher1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'معلم أول', 'teacher', 'active'),
(3, 'staff1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف إداري', 'staff', 'active');

-- حذف القيود الخارجية المُشكلة مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- حذف القيود الخارجية الموجودة
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_user_id_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_approved_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_processed_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_recorded_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_created_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_rejected_by_foreign;

-- تنظيف البيانات الخاطئة (المراجع لمستخدمين غير موجودين)
DELETE FROM staff_absences_with_deduction 
WHERE user_id NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET processed_by = NULL 
WHERE processed_by IS NOT NULL AND processed_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET recorded_by = NULL 
WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET created_by = NULL 
WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET rejected_by = NULL 
WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود الخارجية بشكل صحيح
ALTER TABLE staff_absences_with_deduction
  ADD CONSTRAINT staff_absences_user_id_foreign 
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  
  ADD CONSTRAINT staff_absences_approved_by_foreign 
  FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL,
  
  ADD CONSTRAINT staff_absences_processed_by_foreign 
  FOREIGN KEY (processed_by) REFERENCES users (id) ON DELETE SET NULL,
  
  ADD CONSTRAINT staff_absences_recorded_by_foreign 
  FOREIGN KEY (recorded_by) REFERENCES users (id) ON DELETE SET NULL,
  
  ADD CONSTRAINT staff_absences_created_by_foreign 
  FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL,
  
  ADD CONSTRAINT staff_absences_rejected_by_foreign 
  FOREIGN KEY (rejected_by) REFERENCES users (id) ON DELETE SET NULL;

-- إعادة تفعيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- تحديث الإجراء المخزن ليستخدم مستخدمين موجودين
DROP PROCEDURE IF EXISTS record_absence_with_deduction;

DELIMITER //

CREATE PROCEDURE record_absence_with_deduction(
    IN p_user_id INT,
    IN p_absence_date DATE,
    IN p_absence_type VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    IN p_days_count INT,
    IN p_reason TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    IN p_recorded_by INT
)
BEGIN
    DECLARE deduction_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE deduction_type_val VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'daily_rate';
    DECLARE user_exists INT DEFAULT 0;
    DECLARE recorder_exists INT DEFAULT 0;
    
    -- التحقق من وجود المستخدم
    SELECT COUNT(*) INTO user_exists FROM users WHERE id = p_user_id;
    SELECT COUNT(*) INTO recorder_exists FROM users WHERE id = p_recorded_by;
    
    -- إذا كان المستخدم غير موجود، استخدم مستخدم افتراضي
    IF user_exists = 0 THEN
        SET p_user_id = 1; -- admin user
    END IF;
    
    -- إذا كان المُسجل غير موجود، استخدم مستخدم افتراضي
    IF recorder_exists = 0 THEN
        SET p_recorded_by = 1; -- admin user
    END IF;
    
    -- حساب مبلغ الخصم
    SET deduction_amount = get_deduction_amount(p_absence_type, p_days_count);
    
    -- جلب نوع الخصم
    SELECT ds.deduction_type INTO deduction_type_val
    FROM deduction_settings ds 
    WHERE ds.absence_type COLLATE utf8mb4_unicode_ci = p_absence_type COLLATE utf8mb4_unicode_ci
    AND ds.is_active = 1 
    LIMIT 1;
    
    -- تسجيل الغياب في جدول staff_attendance
    INSERT INTO staff_attendance (
        user_id, attendance_date, status, notes, recorded_by
    ) VALUES (
        p_user_id, p_absence_date, p_absence_type, p_reason, p_recorded_by
    )
    ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        notes = VALUES(notes),
        updated_at = CURRENT_TIMESTAMP;
    
    -- تسجيل الخصم
    INSERT INTO staff_absences_with_deduction (
        user_id, absence_date, absence_type, days_count, 
        deduction_type, deduction_amount, reason, recorded_by, created_by
    ) VALUES (
        p_user_id, p_absence_date, p_absence_type, p_days_count,
        deduction_type_val, deduction_amount, p_reason, p_recorded_by, p_recorded_by
    )
    ON DUPLICATE KEY UPDATE
        days_count = VALUES(days_count),
        deduction_type = VALUES(deduction_type),
        deduction_amount = VALUES(deduction_amount),
        reason = VALUES(reason),
        updated_at = CURRENT_TIMESTAMP;
    
END//

DELIMITER ;

-- إنشاء إجراء مخزن آمن لتسجيل الغياب
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS safe_record_absence(
    IN p_user_id INT,
    IN p_absence_date DATE,
    IN p_absence_type VARCHAR(50),
    IN p_days_count INT,
    IN p_reason TEXT,
    IN p_recorded_by INT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدم
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = p_user_id) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'User does not exist';
    END IF;
    
    -- التحقق من وجود المُسجل
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = p_recorded_by) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Recorder does not exist';
    END IF;
    
    -- استدعاء الإجراء الأساسي
    CALL record_absence_with_deduction(p_user_id, p_absence_date, p_absence_type, p_days_count, p_reason, p_recorded_by);
    
    COMMIT;
END//

DELIMITER ;

-- تحديث الـ view لتتضمن الأعمدة الجديدة
DROP VIEW IF EXISTS staff_absences_detailed;

CREATE VIEW staff_absences_detailed AS
SELECT 
    sad.*,
    u.full_name as user_name,
    u.role as user_role,
    u.email as user_email,
    recorder.full_name as recorded_by_name,
    approver.full_name as approved_by_name,
    processor.full_name as processed_by_name,
    rejector.full_name as rejected_by_name,
    creator.full_name as created_by_name,
    ds.description as absence_type_description,
    CASE 
        WHEN sad.absence_type = 'sick' THEN 'إجازة مرضية'
        WHEN sad.absence_type = 'personal' THEN 'إجازة شخصية'
        WHEN sad.absence_type = 'emergency' THEN 'إجازة طارئة'
        WHEN sad.absence_type = 'unauthorized' THEN 'غياب غير مبرر'
        ELSE sad.absence_type
    END as absence_type_ar,
    CASE 
        WHEN sad.deduction_type = 'fixed' THEN 'مبلغ ثابت'
        WHEN sad.deduction_type = 'percentage' THEN 'نسبة مئوية'
        WHEN sad.deduction_type = 'daily_rate' THEN 'معدل يومي'
        ELSE sad.deduction_type
    END as deduction_type_ar,
    CASE 
        WHEN sad.status = 'pending' THEN 'في الانتظار'
        WHEN sad.status = 'approved' THEN 'مُعتمد'
        WHEN sad.status = 'rejected' THEN 'مرفوض'
        ELSE sad.status
    END as status_ar
FROM staff_absences_with_deduction sad
LEFT JOIN users u ON sad.user_id = u.id
LEFT JOIN users recorder ON sad.recorded_by = recorder.id
LEFT JOIN users approver ON sad.approved_by = approver.id
LEFT JOIN users processor ON sad.processed_by = processor.id
LEFT JOIN users rejector ON sad.rejected_by = rejector.id
LEFT JOIN users creator ON sad.created_by = creator.id
LEFT JOIN deduction_settings ds ON sad.absence_type = ds.absence_type AND ds.is_active = 1;

-- إضافة فهارس جديدة
CREATE INDEX IF NOT EXISTS idx_staff_absences_rejected ON staff_absences_with_deduction(rejected_by, rejected_at);

-- إدراج بيانات تجريبية آمنة
INSERT IGNORE INTO staff_absences_with_deduction (
    user_id, absence_date, absence_type, days_count, 
    deduction_type, deduction_amount, reason, status, recorded_by, created_by
) VALUES 
(1, CURDATE(), 'personal', 1, 'daily_rate', 50.00, 'اختبار النظام', 'pending', 1, 1),
(2, CURDATE(), 'sick', 1, 'fixed', 0.00, 'إجازة مرضية', 'approved', 1, 1);

SELECT 'Foreign key constraints and missing columns fixed successfully!' as message;
