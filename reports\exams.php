<?php
/**
 * تقارير الامتحانات
 * Exam Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

// فلاتر التقرير
$filter_subject = clean_input($_GET['subject'] ?? '');
$filter_class = clean_input($_GET['class'] ?? '');
$filter_status = clean_input($_GET['status'] ?? '');

// بناء الاستعلام
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($filter_subject)) {
    $where_conditions[] = "e.subject_id = ?";
    $params[] = $filter_subject;
    $param_types .= 'i';
}

if (!empty($filter_class)) {
    $where_conditions[] = "e.class_id = ?";
    $params[] = $filter_class;
    $param_types .= 'i';
}

if (!empty($filter_status)) {
    $where_conditions[] = "e.status = ?";
    $params[] = $filter_status;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب تقارير الامتحانات
$exams_query = "
    SELECT e.*, s.subject_name, c.class_name, u.full_name as teacher_name
    FROM exams e
    LEFT JOIN subjects s ON e.subject_id = s.id
    LEFT JOIN classes c ON e.class_id = c.id
    LEFT JOIN users u ON e.teacher_id = u.id
    $where_clause
    ORDER BY e.exam_date DESC
";

$exams_stmt = $conn->prepare($exams_query);
if (!empty($params)) {
    $exams_stmt->bind_param($param_types, ...$params);
}
$exams_stmt->execute();
$exams_result = $exams_stmt->get_result();

// إحصائيات الامتحانات
$stats_query = "
    SELECT 
        COUNT(*) as total_exams,
        COUNT(CASE WHEN e.status = 'published' THEN 1 END) as published_exams,
        COUNT(CASE WHEN e.status = 'draft' THEN 1 END) as draft_exams,
        COUNT(CASE WHEN e.status = 'completed' THEN 1 END) as completed_exams,
        AVG(e.total_marks) as avg_total_marks
    FROM exams e
    $where_clause
";

$stats_stmt = $conn->prepare($stats_query);
if (!empty($params)) {
    $stats_stmt->bind_param($param_types, ...$params);
}
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// جلب قائمة المواد
$subjects_query = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);

// جلب قائمة الفصول
$classes_query = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_query);

include_once '../includes/header.php';
?>

<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i><?php echo __('exam_reports'); ?>
                        </h5>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back_to_reports'); ?>
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i><?php echo __('report_filters'); ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="subject" class="form-label"><?php echo __('subject'); ?></label>
                                    <select class="form-select" id="subject" name="subject">
                                        <option value=""><?php echo __('all_subjects'); ?></option>
                                        <?php while ($subject = $subjects_result->fetch_assoc()): ?>
                                            <option value="<?php echo $subject['id']; ?>" <?php echo $filter_subject == $subject['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($subject['subject_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="class" class="form-label"><?php echo __('class'); ?></label>
                                    <select class="form-select" id="class" name="class">
                                        <option value=""><?php echo __('all_classes'); ?></option>
                                        <?php while ($class = $classes_result->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo $filter_class == $class['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                                    <select class="form-select" id="status" name="status">
                                        <option value=""><?php echo __('all_statuses'); ?></option>
                                        <option value="draft" <?php echo $filter_status === 'draft' ? 'selected' : ''; ?>><?php echo __('draft'); ?></option>
                                        <option value="published" <?php echo $filter_status === 'published' ? 'selected' : ''; ?>><?php echo __('published'); ?></option>
                                        <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>><?php echo __('completed'); ?></option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i><?php echo __('generate_report'); ?>
                                        </button>
                                        <a href="?export=excel&<?php echo http_build_query($_GET); ?>" class="btn btn-success">
                                            <i class="fas fa-file-excel me-2"></i><?php echo __('export_excel'); ?>
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- إحصائيات الامتحانات -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['total_exams'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('total_exams'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['published_exams'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('published_exams'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['draft_exams'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('draft_exams'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['completed_exams'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('completed_exams'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الامتحانات -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><?php echo __('exam_title'); ?></th>
                                    <th><?php echo __('subject'); ?></th>
                                    <th><?php echo __('class'); ?></th>
                                    <th><?php echo __('teacher'); ?></th>
                                    <th><?php echo __('exam_date'); ?></th>
                                    <th><?php echo __('duration'); ?></th>
                                    <th><?php echo __('total_marks'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($exams_result->num_rows > 0): ?>
                                    <?php while ($exam = $exams_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($exam['exam_title']); ?></td>
                                            <td><?php echo htmlspecialchars($exam['subject_name'] ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($exam['class_name'] ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($exam['teacher_name'] ?? '-'); ?></td>
                                            <td><?php echo date('Y/m/d', strtotime($exam['exam_date'])); ?></td>
                                            <td><?php echo $exam['duration']; ?> <?php echo __('minutes'); ?></td>
                                            <td><?php echo $exam['total_marks']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $exam['status'] === 'published' ? 'success' : 
                                                        ($exam['status'] === 'completed' ? 'info' : 'warning'); 
                                                ?>">
                                                    <?php echo __($exam['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center"><?php echo __('no_exams_found'); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
