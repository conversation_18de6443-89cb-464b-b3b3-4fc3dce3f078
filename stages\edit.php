<?php
/**
 * صفحة تعديل المرحلة الدراسية
 * Edit Educational Stage Page
 */

require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('edit_stage');

// متغيرات الرسائل والأخطاء
$success_message = '';
$error_message = '';
$validation_errors = [];

// الحصول على معرف المرحلة
$stage_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($stage_id <= 0) {
    redirect_to('index.php');
}

// جلب بيانات المرحلة
$stage_query = "SELECT * FROM educational_stages WHERE id = ?";
$stmt = $conn->prepare($stage_query);
if (!$stmt) {
    die(__('database_error') . ': ' . $conn->error);
}

$stmt->bind_param('i', $stage_id);
$stmt->execute();
$result = $stmt->get_result();
$stage = $result->fetch_assoc();
$stmt->close();

if (!$stage) {
    redirect_to('index.php');
}

// متغيرات النموذج
$stage_name = $stage['stage_name'];
$stage_name_en = $stage['stage_name_en'];
$stage_code = $stage['stage_code'];
$description = $stage['description'];
$sort_order = $stage['sort_order'];
$min_age = $stage['min_age'];
$max_age = $stage['max_age'];
$duration_years = $stage['duration_years'];
$status = $stage['status'];

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // تنظيف البيانات
    $stage_name = trim($_POST['stage_name'] ?? '');
    $stage_name_en = trim($_POST['stage_name_en'] ?? '');
    $stage_code = trim($_POST['stage_code'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $sort_order = intval($_POST['sort_order'] ?? 0);
    $min_age = !empty($_POST['min_age']) ? intval($_POST['min_age']) : null;
    $max_age = !empty($_POST['max_age']) ? intval($_POST['max_age']) : null;
    $duration_years = intval($_POST['duration_years'] ?? 1);
    $status = $_POST['status'] ?? 'active';

    // التحقق من صحة البيانات
    if (empty($stage_name)) {
        $validation_errors['stage_name'] = __('stage_name_required');
    } elseif (strlen($stage_name) > 100) {
        $validation_errors['stage_name'] = __('stage_name_too_long');
    }

    if (empty($stage_code)) {
        $validation_errors['stage_code'] = __('stage_code_required');
    } elseif (strlen($stage_code) > 20) {
        $validation_errors['stage_code'] = __('stage_code_too_long');
    } elseif (!preg_match('/^[A-Z0-9_]+$/', $stage_code)) {
        $validation_errors['stage_code'] = __('stage_code_invalid_format');
    }

    if (!empty($stage_name_en) && strlen($stage_name_en) > 100) {
        $validation_errors['stage_name_en'] = __('stage_name_en_too_long');
    }

    if ($sort_order <= 0) {
        $validation_errors['sort_order'] = __('sort_order_required');
    }

    if ($duration_years <= 0) {
        $validation_errors['duration_years'] = __('duration_years_required');
    }

    if ($min_age !== null && ($min_age < 0 || $min_age > 25)) {
        $validation_errors['min_age'] = __('min_age_invalid');
    }

    if ($max_age !== null && ($max_age < 0 || $max_age > 25)) {
        $validation_errors['max_age'] = __('max_age_invalid');
    }

    if ($min_age !== null && $max_age !== null && $min_age >= $max_age) {
        $validation_errors['age_range'] = __('age_range_invalid');
    }

    // التحقق من عدم تكرار رمز المرحلة (باستثناء المرحلة الحالية)
    if (empty($validation_errors['stage_code'])) {
        $check_query = "SELECT id FROM educational_stages WHERE stage_code = ? AND id != ?";
        $stmt = $conn->prepare($check_query);
        if ($stmt) {
            $stmt->bind_param('si', $stage_code, $stage_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $validation_errors['stage_code'] = __('stage_code_exists');
            }
            $stmt->close();
        }
    }

    // التحقق من عدم تكرار ترتيب المرحلة (باستثناء المرحلة الحالية)
    if (empty($validation_errors['sort_order'])) {
        $check_query = "SELECT id FROM educational_stages WHERE sort_order = ? AND id != ?";
        $stmt = $conn->prepare($check_query);
        if ($stmt) {
            $stmt->bind_param('ii', $sort_order, $stage_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $validation_errors['sort_order'] = __('sort_order_exists');
            }
            $stmt->close();
        }
    }

    // إذا لم توجد أخطاء، قم بتحديث البيانات
    if (empty($validation_errors)) {
        // حفظ البيانات القديمة للسجل
        $old_data = [
            'stage_name' => $stage['stage_name'],
            'stage_code' => $stage['stage_code'],
            'sort_order' => $stage['sort_order']
        ];

        $update_query = "
            UPDATE educational_stages 
            SET stage_name = ?, stage_name_en = ?, stage_code = ?, description = ?, 
                sort_order = ?, min_age = ?, max_age = ?, duration_years = ?, status = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ";
        
        $stmt = $conn->prepare($update_query);
        if ($stmt) {
            $stmt->bind_param('ssssiiiisi', 
                $stage_name, $stage_name_en, $stage_code, $description, 
                $sort_order, $min_age, $max_age, $duration_years, $status, $stage_id
            );
            
            if ($stmt->execute()) {
                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'edit_stage', 'educational_stages', $stage_id, $old_data, [
                    'stage_name' => $stage_name,
                    'stage_code' => $stage_code,
                    'sort_order' => $sort_order
                ]);
                
                $success_message = __('stage_updated_successfully');
                
                // تحديث البيانات المحلية
                $stage['stage_name'] = $stage_name;
                $stage['stage_name_en'] = $stage_name_en;
                $stage['stage_code'] = $stage_code;
                $stage['description'] = $description;
                $stage['sort_order'] = $sort_order;
                $stage['min_age'] = $min_age;
                $stage['max_age'] = $max_age;
                $stage['duration_years'] = $duration_years;
                $stage['status'] = $status;
                
            } else {
                $error_message = __('database_error') . ': ' . $conn->error;
            }
            $stmt->close();
        } else {
            $error_message = __('database_error') . ': ' . $conn->error;
        }
    }
}

// الحصول على إحصائيات المرحلة
$stats = ['classes_count' => 0, 'subjects_count' => 0, 'students_count' => 0];

// عدد الفصول
$classes_count_query = "SELECT COUNT(*) as count FROM classes WHERE stage_id = ? AND status = 'active'";
$stmt = $conn->prepare($classes_count_query);
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stats['classes_count'] = $result->fetch_assoc()['count'];
    $stmt->close();
}

// عدد المواد
$subjects_count_query = "SELECT COUNT(*) as count FROM subjects WHERE stage_id = ? AND status = 'active'";
$stmt = $conn->prepare($subjects_count_query);
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stats['subjects_count'] = $result->fetch_assoc()['count'];
    $stmt->close();
}

// عدد الطلاب
$students_count_query = "
    SELECT COUNT(DISTINCT s.id) as count
    FROM students s
    INNER JOIN classes c ON s.class_id = c.id
    WHERE c.stage_id = ?
";
$stmt = $conn->prepare($students_count_query);
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stats['students_count'] = $result->fetch_assoc()['count'];
    $stmt->close();
}
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-edit text-primary me-2"></i>
                <?php echo __('edit_stage'); ?>
            </h2>
            <p class="text-muted mb-0">
                <?php echo __('editing_stage'); ?>: <strong><?php echo htmlspecialchars($stage['stage_name']); ?></strong>
            </p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $stage_id; ?>" class="btn btn-outline-info me-2">
                <i class="fas fa-eye me-2"></i><?php echo __('view_stage'); ?>
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_stages'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Form Section -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('stage_information'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        <div class="row">
                            <!-- اسم المرحلة بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label for="stage_name" class="form-label">
                                    <?php echo __('stage_name'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?php echo isset($validation_errors['stage_name']) ? 'is-invalid' : ''; ?>" 
                                       id="stage_name" 
                                       name="stage_name" 
                                       value="<?php echo htmlspecialchars($stage_name); ?>"
                                       placeholder="<?php echo __('enter_stage_name'); ?>"
                                       maxlength="100"
                                       required>
                                <?php if (isset($validation_errors['stage_name'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['stage_name']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- اسم المرحلة بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label for="stage_name_en" class="form-label">
                                    <?php echo __('stage_name_en'); ?>
                                </label>
                                <input type="text" 
                                       class="form-control <?php echo isset($validation_errors['stage_name_en']) ? 'is-invalid' : ''; ?>" 
                                       id="stage_name_en" 
                                       name="stage_name_en" 
                                       value="<?php echo htmlspecialchars($stage_name_en); ?>"
                                       placeholder="<?php echo __('enter_stage_name_en'); ?>"
                                       maxlength="100">
                                <?php if (isset($validation_errors['stage_name_en'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['stage_name_en']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رمز المرحلة -->
                            <div class="col-md-4 mb-3">
                                <label for="stage_code" class="form-label">
                                    <?php echo __('stage_code'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?php echo isset($validation_errors['stage_code']) ? 'is-invalid' : ''; ?>" 
                                       id="stage_code" 
                                       name="stage_code" 
                                       value="<?php echo htmlspecialchars($stage_code); ?>"
                                       placeholder="<?php echo __('enter_stage_code'); ?>"
                                       maxlength="20"
                                       style="text-transform: uppercase;"
                                       pattern="[A-Z0-9_]+"
                                       required>
                                <div class="form-text"><?php echo __('stage_code_help'); ?></div>
                                <?php if (isset($validation_errors['stage_code'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['stage_code']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- ترتيب المرحلة -->
                            <div class="col-md-4 mb-3">
                                <label for="sort_order" class="form-label">
                                    <?php echo __('sort_order'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="number" 
                                       class="form-control <?php echo isset($validation_errors['sort_order']) ? 'is-invalid' : ''; ?>" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="<?php echo $sort_order; ?>"
                                       min="1"
                                       max="100"
                                       required>
                                <div class="form-text"><?php echo __('sort_order_help'); ?></div>
                                <?php if (isset($validation_errors['sort_order'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['sort_order']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- مدة المرحلة بالسنوات -->
                            <div class="col-md-4 mb-3">
                                <label for="duration_years" class="form-label">
                                    <?php echo __('duration_years'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="number" 
                                       class="form-control <?php echo isset($validation_errors['duration_years']) ? 'is-invalid' : ''; ?>" 
                                       id="duration_years" 
                                       name="duration_years" 
                                       value="<?php echo $duration_years; ?>"
                                       min="1"
                                       max="10"
                                       required>
                                <div class="form-text"><?php echo __('duration_years_help'); ?></div>
                                <?php if (isset($validation_errors['duration_years'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['duration_years']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <!-- العمر الأدنى -->
                            <div class="col-md-4 mb-3">
                                <label for="min_age" class="form-label">
                                    <?php echo __('min_age'); ?>
                                </label>
                                <input type="number" 
                                       class="form-control <?php echo isset($validation_errors['min_age']) ? 'is-invalid' : ''; ?>" 
                                       id="min_age" 
                                       name="min_age" 
                                       value="<?php echo $min_age; ?>"
                                       min="0"
                                       max="25"
                                       placeholder="<?php echo __('optional'); ?>">
                                <div class="form-text"><?php echo __('min_age_help'); ?></div>
                                <?php if (isset($validation_errors['min_age'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['min_age']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- العمر الأقصى -->
                            <div class="col-md-4 mb-3">
                                <label for="max_age" class="form-label">
                                    <?php echo __('max_age'); ?>
                                </label>
                                <input type="number" 
                                       class="form-control <?php echo isset($validation_errors['max_age']) ? 'is-invalid' : ''; ?>" 
                                       id="max_age" 
                                       name="max_age" 
                                       value="<?php echo $max_age; ?>"
                                       min="0"
                                       max="25"
                                       placeholder="<?php echo __('optional'); ?>">
                                <div class="form-text"><?php echo __('max_age_help'); ?></div>
                                <?php if (isset($validation_errors['max_age'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['max_age']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- حالة المرحلة -->
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">
                                    <?php echo __('status'); ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>
                                        <?php echo __('active'); ?>
                                    </option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>
                                        <?php echo __('inactive'); ?>
                                    </option>
                                </select>
                                <?php if ($stats['classes_count'] > 0 || $stats['subjects_count'] > 0): ?>
                                    <div class="form-text text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <?php echo __('stage_has_dependencies_warning'); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- رسالة خطأ النطاق العمري -->
                        <?php if (isset($validation_errors['age_range'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $validation_errors['age_range']; ?>
                            </div>
                        <?php endif; ?>

                        <!-- الوصف -->
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <?php echo __('description'); ?>
                            </label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="4"
                                      placeholder="<?php echo __('enter_stage_description'); ?>"><?php echo htmlspecialchars($description); ?></textarea>
                            <div class="form-text"><?php echo __('description_help'); ?></div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('update_stage'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar with Statistics -->
        <div class="col-lg-4">
            <!-- Statistics Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo __('stage_statistics'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h4 class="text-info mb-0"><?php echo $stats['classes_count']; ?></h4>
                                <small class="text-muted"><?php echo __('classes'); ?></small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h4 class="text-warning mb-0"><?php echo $stats['subjects_count']; ?></h4>
                                <small class="text-muted"><?php echo __('subjects'); ?></small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success mb-0"><?php echo $stats['students_count']; ?></h4>
                            <small class="text-muted"><?php echo __('students'); ?></small>
                        </div>
                    </div>
                    
                    <?php if ($stats['classes_count'] > 0 || $stats['subjects_count'] > 0): ?>
                        <hr>
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <small><?php echo __('stage_dependencies_info'); ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Stage Info Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('stage_details'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted"><?php echo __('created_at'); ?>:</small><br>
                        <span><?php echo date('Y-m-d H:i', strtotime($stage['created_at'])); ?></span>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted"><?php echo __('updated_at'); ?>:</small><br>
                        <span><?php echo date('Y-m-d H:i', strtotime($stage['updated_at'])); ?></span>
                    </div>
                    <div>
                        <small class="text-muted"><?php echo __('stage_id'); ?>:</small><br>
                        <code>#<?php echo $stage['id']; ?></code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحويل رمز المرحلة إلى أحرف كبيرة
document.getElementById('stage_code').addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9_]/g, '');
});

// التحقق من النطاق العمري
document.getElementById('min_age').addEventListener('change', validateAgeRange);
document.getElementById('max_age').addEventListener('change', validateAgeRange);

function validateAgeRange() {
    const minAge = parseInt(document.getElementById('min_age').value);
    const maxAge = parseInt(document.getElementById('max_age').value);
    
    if (minAge && maxAge && minAge >= maxAge) {
        document.getElementById('max_age').setCustomValidity('<?php echo __('max_age_must_be_greater'); ?>');
    } else {
        document.getElementById('max_age').setCustomValidity('');
    }
}

// تحذير عند تغيير الحالة إذا كانت هناك تبعيات
<?php if ($stats['classes_count'] > 0 || $stats['subjects_count'] > 0): ?>
document.getElementById('status').addEventListener('change', function() {
    if (this.value === 'inactive') {
        if (!confirm('<?php echo __('deactivate_stage_confirmation'); ?>')) {
            this.value = 'active';
        }
    }
});
<?php endif; ?>
</script>

<?php require_once '../includes/footer.php'; ?>
