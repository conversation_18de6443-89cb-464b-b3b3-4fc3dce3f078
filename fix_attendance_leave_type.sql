-- إصلاح مشاكل leave_type في ملفات الحضور والغياب
-- Fix leave_type issues in attendance files

USE school_management;

-- التأكد من وجود أنواع الإجازات الأساسية
-- Ensure basic leave types exist
INSERT IGNORE INTO leave_types (id, name, name_en, code, description, max_days_per_year, max_days_per_request, min_days_notice, requires_medical_certificate, requires_replacement, approval_levels, is_paid, affects_salary, can_be_carried_forward, color_code, icon, sort_order, is_active) VALUES
(1, 'إجازة سنوية', 'Annual Leave', 'annual', 'الإجازة السنوية المستحقة للموظف', 30, 15, 7, 0, 1, 2, 1, 0, 1, '#28a745', 'fas fa-calendar-check', 1, 1),
(2, 'إجازة مرضية', 'Sick Leave', 'sick', 'إجازة مرضية بتقرير طبي', 90, 30, 0, 1, 0, 1, 1, 0, 0, '#dc3545', 'fas fa-user-injured', 2, 1),
(3, 'إجازة طارئة', 'Emergency Leave', 'emergency', 'إجازة طارئة لظروف استثنائية', 7, 3, 0, 0, 0, 1, 1, 0, 0, '#ffc107', 'fas fa-exclamation-triangle', 3, 1),
(4, 'إجازة أمومة', 'Maternity Leave', 'maternity', 'إجازة أمومة للموظفات', 90, 90, 30, 1, 0, 2, 1, 0, 0, '#e83e8c', 'fas fa-baby', 4, 1),
(5, 'إجازة أبوة', 'Paternity Leave', 'paternity', 'إجازة أبوة للموظفين', 7, 7, 7, 0, 0, 1, 1, 0, 0, '#6f42c1', 'fas fa-male', 5, 1),
(6, 'إجازة حج/عمرة', 'Pilgrimage Leave', 'pilgrimage', 'إجازة أداء فريضة الحج أو العمرة', 15, 15, 30, 0, 1, 2, 1, 0, 0, '#17a2b8', 'fas fa-kaaba', 6, 1),
(7, 'إجازة بدون راتب', 'Unpaid Leave', 'unpaid', 'إجازة بدون راتب لظروف خاصة', 30, 30, 14, 0, 1, 3, 0, 1, 0, '#6c757d', 'fas fa-ban', 7, 1),
(8, 'إجازة دراسية', 'Study Leave', 'study', 'إجازة للدراسة والتطوير المهني', 365, 365, 60, 0, 1, 3, 1, 0, 0, '#fd7e14', 'fas fa-graduation-cap', 8, 1);

-- إنشاء view لتسهيل الاستعلامات
-- Create view to simplify queries
CREATE OR REPLACE VIEW staff_leaves_with_types AS
SELECT 
    sl.*,
    lt.name as leave_type_name,
    lt.code as leave_type_code,
    lt.color_code,
    lt.icon,
    u.full_name as user_name,
    u.role as user_role
FROM staff_leaves sl
LEFT JOIN leave_types lt ON sl.leave_type_id = lt.id
LEFT JOIN users u ON sl.user_id = u.id;

-- إنشاء دالة مساعدة للحصول على leave_type_id من النص
-- Create helper function to get leave_type_id from text
DELIMITER //

CREATE FUNCTION IF NOT EXISTS get_leave_type_id(leave_type_text VARCHAR(100))
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE type_id INT DEFAULT 3; -- Default to emergency leave
    
    -- البحث بالكود أولاً
    SELECT id INTO type_id FROM leave_types 
    WHERE code = leave_type_text AND is_active = 1 
    LIMIT 1;
    
    -- إذا لم يتم العثور عليه، البحث بالاسم
    IF type_id IS NULL THEN
        SELECT id INTO type_id FROM leave_types 
        WHERE (name = leave_type_text OR name_en = leave_type_text) AND is_active = 1 
        LIMIT 1;
    END IF;
    
    -- إذا لم يتم العثور عليه، البحث بالتشابه
    IF type_id IS NULL THEN
        SELECT id INTO type_id FROM leave_types 
        WHERE (name LIKE CONCAT('%', leave_type_text, '%') OR 
               name_en LIKE CONCAT('%', leave_type_text, '%') OR
               code LIKE CONCAT('%', leave_type_text, '%')) AND is_active = 1 
        LIMIT 1;
    END IF;
    
    -- إذا لم يتم العثور على أي شيء، استخدم الإجازة الطارئة
    IF type_id IS NULL THEN
        SET type_id = 3;
    END IF;
    
    RETURN type_id;
END//

DELIMITER ;

-- تحديث أي سجلات قديمة قد تحتوي على بيانات غير صحيحة
-- Update any old records that might contain incorrect data
UPDATE staff_leaves 
SET leave_type_id = 3 
WHERE leave_type_id IS NULL OR leave_type_id = 0;

-- إضافة فهارس لتحسين الأداء
-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_staff_leaves_user_date ON staff_leaves(user_id, start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_staff_leaves_status_date ON staff_leaves(status, start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_leave_types_code ON leave_types(code);
CREATE INDEX IF NOT EXISTS idx_leave_types_active ON leave_types(is_active);

SELECT 'Leave types fix completed successfully!' as message;
