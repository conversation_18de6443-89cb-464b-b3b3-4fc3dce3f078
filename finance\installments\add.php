<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة تغيير اللغة من URL
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $_GET['lang'];
}

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة إضافة القسط الجديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات وتنظيفها
        $student_id = intval($_POST['student_id'] ?? 0);
        $installment_type = clean_input($_POST['installment_type'] ?? '');
        $bank_account_id = intval($_POST['bank_account_id'] ?? 0);
        $amount_requested = floatval($_POST['amount_requested'] ?? 0);
        $receipt_date = clean_input($_POST['receipt_date'] ?? '');
        $fee_type = clean_input($_POST['fee_type'] ?? 'tuition');
        $description = clean_input($_POST['description'] ?? '');

        // توليد رقم الإيصال تلقائياً (4 أرقام فقط)
        $receipt_number = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        
        // التحقق من صحة البيانات
        if ($student_id <= 0) {
            $error_message = 'يجب اختيار الطالب';
        } elseif (empty($installment_type)) {
            $error_message = 'يجب اختيار نوع القسط';
        } elseif ($bank_account_id <= 0) {
            $error_message = 'يجب اختيار رقم الحساب البنكي';
        } elseif ($amount_requested <= 0) {
            $error_message = 'يجب إدخال مبلغ صحيح';
        } elseif (empty($receipt_date)) {
            $error_message = 'يجب إدخال تاريخ الإيصال';
        } else {
            // تحديد رقم القسط بناءً على النوع المختار
            $installment_number = 0;
            switch ($installment_type) {
                case 'first':
                    $installment_number = 1;
                    break;
                case 'second':
                    $installment_number = 2;
                    break;
                case 'third':
                    $installment_number = 3;
                    break;
                case 'complete':
                    $installment_number = 0; // إجمالي
                    break;
                default:
                    $installment_number = 1;
            }

            // التحقق من عدم تكرار البيانات (الطالب + رقم القسط + نوع الرسوم)
            $duplicate_check_stmt = $conn->prepare("
                SELECT si.id, u.full_name as student_name, ft.type_name as fee_type_name
                FROM student_installments si
                JOIN students s ON si.student_id = s.id
                JOIN users u ON s.user_id = u.id
                LEFT JOIN fee_types ft ON si.fee_type_id = ft.id
                WHERE si.student_id = ? AND si.installment_number = ? AND si.fee_type_id = ?
            ");
            $duplicate_check_stmt->bind_param("iii", $student_id, $installment_number, $fee_type);
            $duplicate_check_stmt->execute();
            $duplicate_result = $duplicate_check_stmt->get_result()->fetch_assoc();

            if ($duplicate_result) {
                $installment_text = '';
                switch ($installment_number) {
                    case 0:
                        $installment_text = 'إجمالي';
                        break;
                    case 1:
                        $installment_text = 'القسط الأول';
                        break;
                    case 2:
                        $installment_text = 'القسط الثاني';
                        break;
                    case 3:
                        $installment_text = 'القسط الثالث';
                        break;
                    default:
                        $installment_text = 'القسط رقم ' . $installment_number;
                }

                $error_message = "لا يمكن حفظ البيانات المدخلة وذلك لتكرار البيانات.<br>";
                $error_message .= "برجاء التأكد بأن اسم الطالب غير مدخل من قبل.<br><br>";
                $error_message .= "<strong>البيانات المكررة:</strong><br>";
                $error_message .= "• الطالب: " . htmlspecialchars($duplicate_result['student_name']) . "<br>";
                $error_message .= "• نوع القسط: " . htmlspecialchars($installment_text) . "<br>";
                $error_message .= "• نوع الرسوم: " . htmlspecialchars($duplicate_result['fee_type_name'] ?? 'غير محدد');
            } else {
                // التحقق من عدم تكرار رقم الإيصال
                $check_stmt = $conn->prepare("SELECT id FROM student_installments WHERE receipt_number = ?");
                $check_stmt->bind_param("s", $receipt_number);
                $check_stmt->execute();
                $existing = $check_stmt->get_result()->fetch_assoc();

                // إذا كان رقم الإيصال موجود، أنشئ رقم جديد
                while ($existing) {
                    $receipt_number = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
                    $check_stmt = $conn->prepare("SELECT id FROM student_installments WHERE receipt_number = ?");
                    $check_stmt->bind_param("s", $receipt_number);
                    $check_stmt->execute();
                    $existing = $check_stmt->get_result()->fetch_assoc();
                }

                // إضافة القسط الجديد مع الحساب البنكي
                $status = 'pending'; // حالة افتراضية
                $notes = $fee_type . ': ' . $description;
                $paid_amount = 0; // المبلغ المدفوع افتراضياً صفر
                $total_installments = 1; // عدد الأقساط الإجمالي

                $stmt = $conn->prepare("INSERT INTO student_installments (student_id, installment_number, receipt_number, amount, total_amount, paid_amount, due_date, status, notes, bank_account_id, total_installments) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

                if ($stmt) {
                    $stmt->bind_param("iisdddsssii", $student_id, $installment_number, $receipt_number, $amount_requested, $amount_requested, $paid_amount, $receipt_date, $status, $notes, $bank_account_id, $total_installments);

                    if ($stmt->execute()) {
                        $success_message = 'تم إضافة القسط بنجاح';
                        // إعادة توجيه لصفحة الأقساط
                        header("Location: index.php?success=1");
                        exit();
                    } else {
                        $error_message = 'خطأ في قاعدة البيانات: ' . $conn->error;
                    }
                } else {
                    $error_message = 'خطأ في قاعدة البيانات: ' . $conn->error;
                }
            }
        }
    }
}

// جلب قائمة الطلاب
$students = $conn->query("SELECT s.id, u.full_name, s.student_id, c.class_name FROM students s JOIN users u ON s.user_id = u.id LEFT JOIN classes c ON s.class_id = c.id WHERE s.status = 'active' ORDER BY u.full_name");

// جلب قائمة الحسابات البنكية النشطة مع أنواع الرسوم المرتبطة
$bank_accounts_query = "
    SELECT
        ba.id,
        ba.bank_name,
        ba.account_number,
        GROUP_CONCAT(baft.fee_type_id) as linked_fee_type_ids,
        GROUP_CONCAT(ft.type_name SEPARATOR '|') as linked_fee_type_names
    FROM bank_accounts ba
    LEFT JOIN bank_account_fee_types baft ON ba.id = baft.bank_account_id
    LEFT JOIN fee_types ft ON baft.fee_type_id = ft.id
    WHERE ba.is_active = 1
    GROUP BY ba.id
    ORDER BY ba.bank_name
";
$bank_accounts = $conn->query($bank_accounts_query);

// جلب جميع أنواع الرسوم من جدول fee_types
$fee_types = $conn->query("SELECT id, type_name, description FROM fee_types WHERE status = 'active' ORDER BY type_name");

// تحديد عنوان الصفحة وتضمين الهيدر بعد معالجة النموذج
$page_title = __('add_installment');
include_once '../../includes/header.php';
?>

<style>
    /* تنسيق اقتراحات البحث */
    #student_suggestions {
        border-top: none !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .suggestion-item:hover {
        background-color: #f8f9fa;
    }

    .suggestion-item:last-child {
        border-bottom: none !important;
    }

    #student_search:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
</style>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-plus me-2"></i><?php echo __('add_installment'); ?></h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div><?php echo $error_message; ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- اختيار الطالب -->
                        <div class="mb-4">
                            <label class="form-label"><?php echo __('student'); ?> <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="student_search" placeholder="ابحث عن الطالب بالاسم..." autocomplete="off">
                                <input type="hidden" name="student_id" id="student_id" required>
                                <div id="student_suggestions" class="position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm" style="display: none; z-index: 1000; max-height: 300px; overflow-y: auto;"></div>
                            </div>
                            <small class="text-muted">اكتب اسم الطالب للبحث واختيار الطالب المطلوب</small>
                        </div>
                        
                        <!-- بيانات القسط -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('installment_details'); ?></h6>
                            </div>
                            <div class="card-body">
                                <!-- الصف الأول: رقم الإيصال ورقم القسط -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الإيصال</label>
                                            <input type="text" class="form-control" value="سيتم توليده تلقائياً" readonly style="background-color: #f8f9fa;">
                                            <small class="text-muted">سيتم توليد رقم الإيصال تلقائياً عند الحفظ</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم القسط <span class="text-danger">*</span></label>
                                            <select class="form-select" name="installment_type" required>
                                                <option value="">اختر نوع القسط</option>
                                                <option value="complete" <?php if(isset($_POST['installment_type']) && $_POST['installment_type'] == 'complete') echo 'selected'; ?>>إجمالي</option>
                                                <option value="first" <?php if(isset($_POST['installment_type']) && $_POST['installment_type'] == 'first') echo 'selected'; ?>>القسط الأول</option>
                                                <option value="second" <?php if(isset($_POST['installment_type']) && $_POST['installment_type'] == 'second') echo 'selected'; ?>>القسط الثاني</option>
                                                <option value="third" <?php if(isset($_POST['installment_type']) && $_POST['installment_type'] == 'third') echo 'selected'; ?>>القسط الثالث</option>
                                            </select>
                                            <small class="text-muted">اختر نوع القسط المطلوب</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الثاني: رقم الحساب البنكي -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الحساب البنكي <span class="text-danger">*</span></label>
                                            <select class="form-select" name="bank_account_id" id="bank_account_id" required>
                                                <option value="">اختر رقم الحساب</option>
                                                <?php if ($bank_accounts && $bank_accounts->num_rows > 0): ?>
                                                    <?php
                                                    // إعادة تعيين المؤشر لقراءة البيانات مرة أخرى
                                                    $bank_accounts->data_seek(0);
                                                    while ($account = $bank_accounts->fetch_assoc()): ?>
                                                        <option value="<?php echo $account['id']; ?>" <?php if(isset($_POST['bank_account_id']) && $_POST['bank_account_id'] == $account['id']) echo 'selected'; ?>>
                                                            <?php echo htmlspecialchars($account['account_number']); ?> - <?php echo htmlspecialchars($account['bank_name']); ?>
                                                            <?php if (!empty($account['linked_fee_type_names'])): ?>
                                                                - (<?php echo htmlspecialchars(str_replace('|', ', ', $account['linked_fee_type_names'])); ?>)
                                                            <?php endif; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                            <small class="text-muted">سيتم اختيار نوع الرسوم تلقائياً حسب الحساب المختار</small>
                                        </div>
                                    </div>
                                </div>



                                <!-- الصف الثالث: نوع الرسوم وبرجاء قبول مبلغ -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نوع الرسوم</label>
                                            <select class="form-select" name="fee_type" id="fee_type">
                                                <option value="">اختر طالب أولاً لعرض الرسوم المتاحة</option>
                                            </select>
                                            <small class="text-muted">سيتم عرض الرسوم المتاحة للصف الدراسي للطالب المختار</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">برجاء قبول مبلغ <span class="text-danger">*</span></label>
                                            <div class="input-group" style="width: 100%;">
                                                <input type="number" class="form-control" name="amount_requested" id="amount_requested" min="0" step="0.01" value="<?php echo $_POST['amount_requested'] ?? ''; ?>" required style="flex: 1;" readonly>
                                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                            </div>
                                            <small class="text-muted">سيتم تحديد المبلغ تلقائياً حسب نوع الرسم والصف</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الرابع: تاريخ الإيصال -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ الإيصال <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" name="receipt_date" value="<?php echo $_POST['receipt_date'] ?? date('Y-m-d'); ?>" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- تنبيه عدم التطابق -->
                                <div class="alert alert-warning" id="mismatch_warning" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <div>
                                            <strong>تنبيه هام:</strong> رقم الحساب البنكي المختار غير مربوط بنوع الرسوم المحدد.
                                            <br><small>يمكنك المتابعة والحفظ على مسؤوليتك الشخصية.</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الخامس: الوصف -->
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3" placeholder="وصف تفصيلي للقسط..."><?php echo $_POST['description'] ?? ''; ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary" onclick="return confirmSubmit()">
                                <i class="fas fa-save me-2"></i><?php echo __('add_installment'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<style>
/* إصلاح مشكلة عرض حقل المبلغ */
.input-group {
    display: flex !important;
    width: 100% !important;
}

.input-group .form-control {
    flex: 1 !important;
    min-width: 0 !important;
}

.input-group-text {
    flex-shrink: 0 !important;
    white-space: nowrap !important;
}

/* تحسين عرض الحقول على الشاشات الصغيرة */
@media (max-width: 768px) {
    .col-md-6 {
        margin-bottom: 1rem;
    }

    .input-group {
        flex-wrap: nowrap !important;
    }
}

/* تنسيق التنبيه */
#mismatch_warning {
    border-left: 4px solid #ffc107;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<script>
// بيانات الحسابات البنكية وأنواع الرسوم المرتبطة
const bankAccountsData = {
    <?php
    if ($bank_accounts && $bank_accounts->num_rows > 0) {
        $bank_accounts->data_seek(0);
        $accounts_js = [];
        while ($account = $bank_accounts->fetch_assoc()) {
            $fee_type_ids = !empty($account['linked_fee_type_ids']) ? explode(',', $account['linked_fee_type_ids']) : [];
            $fee_type_names = !empty($account['linked_fee_type_names']) ? explode('|', $account['linked_fee_type_names']) : [];
            $accounts_js[] = $account['id'] . ': {
                feeTypeIds: [' . implode(',', array_map('intval', $fee_type_ids)) . '],
                feeTypeNames: ["' . implode('","', array_map('addslashes', $fee_type_names)) . '"],
                bankName: "' . addslashes($account['bank_name']) . '",
                accountNumber: "' . addslashes($account['account_number']) . '"
            }';
        }
        echo implode(",\n    ", $accounts_js);
    }
    ?>
};

document.addEventListener('DOMContentLoaded', function() {
    const studentSearch = document.getElementById('student_search');
    const studentIdInput = document.getElementById('student_id');
    const studentSuggestions = document.getElementById('student_suggestions');
    const bankAccountSelect = document.getElementById('bank_account_id');
    const feeTypeSelect = document.getElementById('fee_type');
    const amountInput = document.getElementById('amount_requested');
    const mismatchWarning = document.getElementById('mismatch_warning');

    // بيانات الطلاب
    const students = [
        <?php
        $students->data_seek(0); // إعادة تعيين المؤشر
        $student_data = [];
        while ($student = $students->fetch_assoc()) {
            $student_data[] = '{
                id: ' . $student['id'] . ',
                name: "' . addslashes($student['full_name']) . '",
                student_id: "' . addslashes($student['student_id']) . '",
                class_name: "' . addslashes($student['class_name'] ?? 'غير محدد') . '"
            }';
        }
        echo implode(',', $student_data);
        ?>
    ];

    // البحث في الطلاب
    studentSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();

        if (searchTerm.length < 1) {
            studentSuggestions.style.display = 'none';
            return;
        }

        const filteredStudents = students.filter(student =>
            student.name.toLowerCase().includes(searchTerm) ||
            student.student_id.toLowerCase().includes(searchTerm)
        );

        if (filteredStudents.length > 0) {
            let suggestionsHtml = '';
            filteredStudents.slice(0, 10).forEach(student => {
                suggestionsHtml += `
                    <div class="suggestion-item p-2 border-bottom cursor-pointer"
                         data-student-id="${student.id}"
                         data-student-name="${student.name}"
                         style="cursor: pointer;">
                        <div class="fw-bold">${student.name}</div>
                        <small class="text-muted">(${student.student_id}) - ${student.class_name}</small>
                    </div>
                `;
            });
            studentSuggestions.innerHTML = suggestionsHtml;
            studentSuggestions.style.display = 'block';
        } else {
            studentSuggestions.innerHTML = '<div class="p-2 text-muted">لا توجد نتائج</div>';
            studentSuggestions.style.display = 'block';
        }
    });

    // اختيار طالب من الاقتراحات
    studentSuggestions.addEventListener('click', function(e) {
        const suggestionItem = e.target.closest('.suggestion-item');
        if (suggestionItem) {
            const studentId = suggestionItem.getAttribute('data-student-id');
            const studentName = suggestionItem.getAttribute('data-student-name');

            studentSearch.value = studentName;
            studentIdInput.value = studentId;
            studentSuggestions.style.display = 'none';

            // تحميل رسوم الطالب
            loadStudentClassFees(studentId);
        }
    });

    // إخفاء الاقتراحات عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!studentSearch.contains(e.target) && !studentSuggestions.contains(e.target)) {
            studentSuggestions.style.display = 'none';
        }
    });

    // عند تغيير الحساب البنكي
    bankAccountSelect.addEventListener('change', function() {
        const selectedBankId = this.value;

        if (selectedBankId && bankAccountsData[selectedBankId]) {
            const accountData = bankAccountsData[selectedBankId];

            // إذا كان هناك نوع رسوم واحد فقط مرتبط، اختره تلقائياً
            if (accountData.feeTypeIds.length === 1) {
                feeTypeSelect.value = accountData.feeTypeIds[0];
                hideMismatchWarning();
            } else if (accountData.feeTypeIds.length === 0) {
                // إذا لم يكن هناك أنواع رسوم مرتبطة، أظهر تنبيه
                showMismatchWarning();
            } else {
                // إذا كان هناك عدة أنواع رسوم، تحقق من التطابق
                checkMismatch();
            }
        } else {
            hideMismatchWarning();
        }
    });

    // عند تغيير نوع الرسوم
    feeTypeSelect.addEventListener('change', checkMismatch);

    function checkMismatch() {
        const selectedBankId = bankAccountSelect.value;
        const selectedFeeTypeId = parseInt(feeTypeSelect.value);

        if (selectedBankId && selectedFeeTypeId && bankAccountsData[selectedBankId]) {
            const accountData = bankAccountsData[selectedBankId];

            if (accountData.feeTypeIds.length > 0 && !accountData.feeTypeIds.includes(selectedFeeTypeId)) {
                showMismatchWarning();
            } else {
                hideMismatchWarning();
            }
        } else if (selectedBankId && selectedFeeTypeId && bankAccountsData[selectedBankId] && bankAccountsData[selectedBankId].feeTypeIds.length === 0) {
            showMismatchWarning();
        } else {
            hideMismatchWarning();
        }
    }

    function showMismatchWarning() {
        if (mismatchWarning) {
            mismatchWarning.style.display = 'block';
            mismatchWarning.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    function hideMismatchWarning() {
        if (mismatchWarning) {
            mismatchWarning.style.display = 'none';
        }
    }

    // تحقق أولي عند تحميل الصفحة
    if (bankAccountSelect.value || feeTypeSelect.value) {
        checkMismatch();
    }
});

// دالة تأكيد الإرسال
function confirmSubmit() {
    const studentSearch = document.getElementById('student_search');
    const studentIdInput = document.getElementById('student_id');
    const installmentTypeSelect = document.querySelector('select[name="installment_type"]');
    const feeTypeSelect = document.querySelector('select[name="fee_type"]');

    if (studentIdInput.value && installmentTypeSelect.value && feeTypeSelect.value) {
        const studentName = studentSearch.value;
        const installmentType = installmentTypeSelect.options[installmentTypeSelect.selectedIndex].text;
        const feeTypeName = feeTypeSelect.options[feeTypeSelect.selectedIndex].text;

        return confirm(
            'هل أنت متأكد من إضافة هذا القسط؟\n\n' +
            'الطالب: ' + studentName + '\n' +
            'نوع القسط: ' + installmentType + '\n' +
            'نوع الرسوم: ' + feeTypeName + '\n\n' +
            'تأكد من عدم وجود هذه البيانات مسبقاً لتجنب التكرار.'
        );
    }

    return true;
}

// جلب الرسوم المرتبطة بصف الطالب
function loadStudentClassFees(studentId) {
    const feeTypeSelect = document.getElementById('fee_type');
    const amountInput = document.getElementById('amount_requested');

    // عرض رسالة تحميل
    feeTypeSelect.innerHTML = '<option value="">جاري تحميل الرسوم المتاحة...</option>';

    fetch('../get_student_class_fees.php?student_id=' + studentId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                feeTypeSelect.innerHTML = '<option value="">اختر نوع الرسوم</option>';

                data.fees.forEach(fee => {
                    const option = document.createElement('option');
                    option.value = fee.fee_type_id;
                    option.textContent = fee.type_name + ' - ' + parseFloat(fee.amount).toLocaleString() + ' <?php echo get_currency_symbol(); ?>';
                    option.dataset.amount = fee.amount;
                    feeTypeSelect.appendChild(option);
                });

                // إضافة مستمع لتغيير نوع الرسم
                feeTypeSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.dataset.amount) {
                        amountInput.value = parseFloat(selectedOption.dataset.amount).toFixed(2);
                        amountInput.readOnly = false;
                    } else {
                        amountInput.value = '';
                        amountInput.readOnly = true;
                    }
                });

            } else {
                feeTypeSelect.innerHTML = '<option value="">خطأ في تحميل الرسوم</option>';
                console.error('Error loading fees:', data.message);
            }
        })
        .catch(error => {
            feeTypeSelect.innerHTML = '<option value="">خطأ في الاتصال</option>';
            console.error('Fetch error:', error);
        });
}
</script>

<?php include_once '../../includes/footer.php'; ?>
