/**
 * نظام الفلاتر الديناميكية للمواد
 */

class DynamicFilters {
    constructor() {
        this.apiUrl = 'api/filters.php';
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
    }

    bindEvents() {
        // تغيير المرحلة
        document.getElementById('stage').addEventListener('change', (e) => {
            this.onStageChange(e.target.value);
        });

        // تغيير الصف
        document.getElementById('grade').addEventListener('change', (e) => {
            this.onGradeChange(e.target.value);
        });

        // تغيير الفصل
        if (document.getElementById('class')) {
            document.getElementById('class').addEventListener('change', (e) => {
                this.onClassChange(e.target.value);
            });
        }

        // تغيير القسم
        document.getElementById('department').addEventListener('change', (e) => {
            this.onDepartmentChange(e.target.value);
        });

        // تغيير الحالة
        document.getElementById('status').addEventListener('change', (e) => {
            this.onStatusChange(e.target.value);
        });

        // البحث
        document.getElementById('search').addEventListener('input', (e) => {
            this.onSearchChange(e.target.value);
        });

        // زر البحث
        document.getElementById('searchBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.performSearch();
        });

        // زر مسح الفلاتر
        document.getElementById('clearBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.clearFilters();
        });
    }

    async loadInitialData() {
        try {
            // تحميل الصفوف الأولية
            const stageId = document.getElementById('stage').value;
            if (stageId) {
                await this.loadGrades(stageId);
            }

            // تحميل الفصول الأولية
            const gradeId = document.getElementById('grade').value;
            if (gradeId) {
                await this.loadClasses(gradeId);
            }

            // تحميل الأقسام الأولية
            await this.loadDepartments();

        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
        }
    }

    async onStageChange(stageId) {
        try {
            // إعادة تعيين الصفوف والفصول
            this.resetSelect('grade', 'جميع الصفوف');
            this.resetSelect('class', 'جميع الفصول');

            if (stageId) {
                await this.loadGrades(stageId);
            }

            await this.loadDepartments();
            this.performSearch();

        } catch (error) {
            console.error('خطأ في تغيير المرحلة:', error);
            this.showError('خطأ في تحميل الصفوف');
        }
    }

    async onGradeChange(gradeId) {
        try {
            // إعادة تعيين الفصول
            this.resetSelect('class', 'جميع الفصول');

            if (gradeId) {
                await this.loadClasses(gradeId);
            }

            await this.loadDepartments();
            this.performSearch();

        } catch (error) {
            console.error('خطأ في تغيير الصف:', error);
            this.showError('خطأ في تحميل الفصول');
        }
    }

    async onClassChange(classId) {
        try {
            await this.loadDepartments();
            this.performSearch();
        } catch (error) {
            console.error('خطأ في تغيير الفصل:', error);
        }
    }

    async onDepartmentChange(department) {
        this.performSearch();
    }

    async onStatusChange(status) {
        this.performSearch();
    }

    onSearchChange(searchTerm) {
        // تأخير البحث لتجنب الطلبات المتكررة
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.performSearch();
        }, 500);
    }

    async loadGrades(stageId) {
        try {
            const response = await fetch(`${this.apiUrl}?action=get_grades&stage_id=${stageId}`);
            const data = await response.json();

            if (data.success) {
                this.populateSelect('grade', data.data, 'id', 'grade_name', 'جميع الصفوف');
            } else {
                throw new Error(data.error || 'خطأ في تحميل الصفوف');
            }
        } catch (error) {
            console.error('خطأ في تحميل الصفوف:', error);
            this.showError('خطأ في تحميل الصفوف');
        }
    }

    async loadClasses(gradeId) {
        try {
            const response = await fetch(`${this.apiUrl}?action=get_classes&grade_id=${gradeId}`);
            const data = await response.json();

            if (data.success) {
                this.populateSelect('class', data.data, 'id', 'class_name', 'جميع الفصول');
            } else {
                throw new Error(data.error || 'خطأ في تحميل الفصول');
            }
        } catch (error) {
            console.error('خطأ في تحميل الفصول:', error);
            this.showError('خطأ في تحميل الفصول');
        }
    }

    async loadDepartments() {
        try {
            const stageId = document.getElementById('stage').value;
            const gradeId = document.getElementById('grade').value;
            
            const response = await fetch(`${this.apiUrl}?action=get_departments&stage_id=${stageId}&grade_id=${gradeId}`);
            const data = await response.json();

            if (data.success) {
                this.populateSelect('department', data.data, 'department', 'department', 'جميع الأقسام');
            } else {
                throw new Error(data.error || 'خطأ في تحميل الأقسام');
            }
        } catch (error) {
            console.error('خطأ في تحميل الأقسام:', error);
            this.showError('خطأ في تحميل الأقسام');
        }
    }

    async performSearch() {
        try {
            this.showLoading(true);

            const filters = this.getFilterValues();
            const queryString = new URLSearchParams(filters).toString();
            
            const response = await fetch(`${this.apiUrl}?action=search_subjects&${queryString}`);
            const data = await response.json();

            if (data.success) {
                this.updateSubjectsTable(data.data);
                this.updateResultsCount(data.data.length);
            } else {
                throw new Error(data.error || 'خطأ في البحث');
            }

        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showError('خطأ في البحث عن المواد');
        } finally {
            this.showLoading(false);
        }
    }

    getFilterValues() {
        return {
            stage_id: document.getElementById('stage').value || '',
            grade_id: document.getElementById('grade').value || '',
            class_id: document.getElementById('class')?.value || '',
            department: document.getElementById('department').value || '',
            status: document.getElementById('status').value || '',
            search: document.getElementById('search').value || ''
        };
    }

    populateSelect(selectId, data, valueField, textField, defaultText) {
        const select = document.getElementById(selectId);
        if (!select) return;

        // حفظ القيمة المحددة حالياً
        const currentValue = select.value;

        // مسح الخيارات الحالية
        select.innerHTML = `<option value="">${defaultText}</option>`;

        // إضافة الخيارات الجديدة
        data.forEach(item => {
            const option = document.createElement('option');
            option.value = item[valueField];
            option.textContent = item[textField];
            select.appendChild(option);
        });

        // استعادة القيمة المحددة إذا كانت ما زالت متاحة
        if (currentValue && [...select.options].some(opt => opt.value === currentValue)) {
            select.value = currentValue;
        }
    }

    resetSelect(selectId, defaultText) {
        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = `<option value="">${defaultText}</option>`;
        }
    }

    updateSubjectsTable(subjects) {
        const tbody = document.querySelector('#subjectsTable tbody');
        if (!tbody) return;

        if (subjects.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <i class="fas fa-search fa-2x text-muted mb-2"></i>
                        <p class="text-muted">لا توجد مواد تطابق معايير البحث</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = subjects.map(subject => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-book me-2 text-primary"></i>
                        <strong>${this.escapeHtml(subject.subject_name)}</strong>
                    </div>
                </td>
                <td><code>${this.escapeHtml(subject.subject_code || 'غير محدد')}</code></td>
                <td>${this.escapeHtml(subject.department || 'غير محدد')}</td>
                <td>${this.escapeHtml(subject.stage_name || 'غير محدد')}</td>
                <td>${this.escapeHtml(subject.grade_name || 'غير محدد')}</td>
                <td>${this.escapeHtml(subject.class_name || 'غير محدد')}</td>
                <td><span class="badge bg-info">${subject.credit_hours || 1} ساعة</span></td>
                <td>
                    <span class="badge bg-${subject.status === 'active' ? 'success' : 'secondary'}">
                        ${subject.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td><span class="badge bg-primary">${subject.teacher_count}</span></td>
                <td><span class="badge bg-warning">${subject.student_count}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="view.php?id=${subject.id}" class="btn btn-outline-primary" title="عرض">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="edit.php?id=${subject.id}" class="btn btn-outline-warning" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" class="btn btn-outline-danger" title="حذف"
                                onclick="confirmDelete(${subject.id}, '${this.escapeHtml(subject.subject_name)}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updateResultsCount(count) {
        const countElement = document.getElementById('resultsCount');
        if (countElement) {
            countElement.textContent = `عرض ${count} مادة`;
        }
    }

    clearFilters() {
        document.getElementById('search').value = '';
        document.getElementById('stage').value = '';
        document.getElementById('grade').value = '';
        if (document.getElementById('class')) {
            document.getElementById('class').value = '';
        }
        document.getElementById('department').value = '';
        document.getElementById('status').value = '';

        // إعادة تعيين القوائم
        this.resetSelect('grade', 'جميع الصفوف');
        this.resetSelect('class', 'جميع الفصول');
        this.resetSelect('department', 'جميع الأقسام');

        // تحديث البحث
        this.performSearch();
    }

    showLoading(show) {
        const loadingElement = document.getElementById('loadingIndicator');
        if (loadingElement) {
            loadingElement.style.display = show ? 'block' : 'none';
        }

        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.disabled = show;
            searchBtn.innerHTML = show ? 
                '<i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...' : 
                '<i class="fas fa-search me-2"></i>بحث';
        }
    }

    showError(message) {
        // يمكن تحسين هذا لاحقاً لإظهار رسائل خطأ أفضل
        console.error(message);
        
        // إظهار تنبيه بسيط
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertBefore(alertDiv, container.firstChild);
            
            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.dynamicFilters = new DynamicFilters();
});
