-- إصلاح شامل لجميع القيود الخارجية
-- Complete foreign key constraints fix

USE school_management;

-- تعطيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 0;

-- 1. حذ<PERSON> جميع القيود الخارجية المرتبطة بجدول users
-- الحصول على قائمة بجميع القيود الخارجية
SELECT CONCAT('ALTER TABLE ', TABLE_NAME, ' DROP FOREIGN KEY ', CONSTRAINT_NAME, ';') as drop_statements
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users';

-- حذف القيود يدوياً
-- unified_staff_absences
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_user_id_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_applied_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_approved_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_rejected_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_replacement_foreign;

-- staff_absences_with_deduction
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_user_id_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_approved_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_recorded_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_processed_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_created_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_rejected_by_foreign;

-- staff_leaves
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_user_id_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_applied_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_approved_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_rejected_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_cancelled_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_replacement_user_id_foreign;

-- staff_attendance
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_user_id_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_recorded_by_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_approved_by_foreign;

-- admin_attendance
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_user_id_foreign;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_recorded_by_foreign;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_ibfk_1;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_ibfk_2;

-- 2. تنظيف البيانات غير الصحيحة
-- unified_staff_absences
DELETE FROM unified_staff_absences WHERE user_id NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by IS NOT NULL AND applied_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- staff_absences_with_deduction
DELETE FROM staff_absences_with_deduction WHERE user_id NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by IS NOT NULL AND processed_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);

-- staff_leaves
DELETE FROM staff_leaves WHERE user_id NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET applied_by = NULL WHERE applied_by IS NOT NULL AND applied_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by IS NOT NULL AND cancelled_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- staff_attendance
DELETE FROM staff_attendance WHERE user_id NOT IN (SELECT id FROM users);
UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);
UPDATE staff_attendance SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

-- admin_attendance
DELETE FROM admin_attendance WHERE admin_id NOT IN (SELECT id FROM users);
UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

-- 3. إعادة إضافة القيود الخارجية بدون قيود صارمة
-- unified_staff_absences - فقط القيود الأساسية
ALTER TABLE unified_staff_absences
ADD CONSTRAINT fk_unified_absences_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;

-- staff_absences_with_deduction - فقط القيود الأساسية
ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT fk_staff_absences_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;

-- staff_leaves - فقط القيود الأساسية
ALTER TABLE staff_leaves
ADD CONSTRAINT fk_staff_leaves_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;

-- staff_attendance - فقط القيود الأساسية
ALTER TABLE staff_attendance
ADD CONSTRAINT fk_staff_attendance_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;

-- admin_attendance - فقط القيود الأساسية
ALTER TABLE admin_attendance
ADD CONSTRAINT fk_admin_attendance_user 
FOREIGN KEY (admin_id) REFERENCES users (id) ON DELETE CASCADE;

-- إعادة تفعيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- 4. إنشاء إجراء مبسط لحذف المستخدمين
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS simple_delete_user(IN p_user_id INT)
BEGIN
    DECLARE user_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدم
    SELECT COUNT(*) INTO user_exists FROM users WHERE id = p_user_id;
    
    IF user_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'User does not exist';
    END IF;
    
    -- تنظيف المراجع يدوياً
    UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by = p_user_id;
    UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by = p_user_id;
    
    UPDATE staff_leaves SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE staff_leaves SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by = p_user_id;
    UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_attendance SET approved_by = NULL WHERE approved_by = p_user_id;
    
    UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    
    -- حذف المستخدم (سيحذف السجلات المرتبطة تلقائياً بسبب CASCADE)
    DELETE FROM users WHERE id = p_user_id;
    
    COMMIT;
    
    SELECT CONCAT('User ', p_user_id, ' deleted successfully') as message;
END//

DELIMITER ;

-- 5. إنشاء view لعرض القيود الخارجية الحالية
CREATE OR REPLACE VIEW current_foreign_keys AS
SELECT 
    TABLE_NAME as table_name,
    COLUMN_NAME as column_name,
    CONSTRAINT_NAME as constraint_name,
    REFERENCED_TABLE_NAME as referenced_table,
    REFERENCED_COLUMN_NAME as referenced_column
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users'
ORDER BY TABLE_NAME, COLUMN_NAME;

-- عرض النتائج
SELECT 'All foreign key constraints have been simplified and fixed!' as status;

-- عرض القيود الحالية
SELECT * FROM current_foreign_keys;

-- إحصائيات
SELECT 
    COUNT(*) as total_foreign_keys,
    COUNT(DISTINCT TABLE_NAME) as tables_with_foreign_keys
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users';
