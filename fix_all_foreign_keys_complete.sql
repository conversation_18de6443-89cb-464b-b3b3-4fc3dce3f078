-- إصلاح شامل لجميع القيود الخارجية في النظام
-- Complete fix for all foreign key constraints in the system

USE school_management;

-- تعطيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 0;

-- 1. البح<PERSON> عن جميع القيود الخارجية المرتبطة بجدول users
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users'
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 2. حذف جميع القيود الخارجية المرتبطة بجدول users

-- student_grades
ALTER TABLE student_grades DROP FOREIGN KEY IF EXISTS fk_student_grades_graded_by;
ALTER TABLE student_grades DROP FOREIGN KEY IF EXISTS student_grades_graded_by_foreign;
ALTER TABLE student_grades DROP FOREIGN KEY IF EXISTS student_grades_teacher_id_foreign;
ALTER TABLE student_grades DROP FOREIGN KEY IF EXISTS student_grades_created_by_foreign;
ALTER TABLE student_grades DROP FOREIGN KEY IF EXISTS student_grades_updated_by_foreign;

-- unified_staff_absences
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_user_id_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_applied_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_approved_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_rejected_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_replacement_foreign;

-- staff_absences_with_deduction
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_user_id_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_approved_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_recorded_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_processed_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_created_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_rejected_by_foreign;

-- staff_leaves
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_user_id_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_applied_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_approved_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_rejected_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_cancelled_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_replacement_user_id_foreign;

-- staff_attendance
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_user_id_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_recorded_by_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_approved_by_foreign;

-- admin_attendance
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_user_id_foreign;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_recorded_by_foreign;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_ibfk_1;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_ibfk_2;

-- teacher_subjects (إذا كان موجوداً)
ALTER TABLE teacher_subjects DROP FOREIGN KEY IF EXISTS teacher_subjects_teacher_id_foreign;
ALTER TABLE teacher_subjects DROP FOREIGN KEY IF EXISTS teacher_subjects_assigned_by_foreign;

-- class_teachers (إذا كان موجوداً)
ALTER TABLE class_teachers DROP FOREIGN KEY IF EXISTS class_teachers_teacher_id_foreign;
ALTER TABLE class_teachers DROP FOREIGN KEY IF EXISTS class_teachers_assigned_by_foreign;

-- student_attendance (إذا كان موجوداً)
ALTER TABLE student_attendance DROP FOREIGN KEY IF EXISTS student_attendance_teacher_id_foreign;
ALTER TABLE student_attendance DROP FOREIGN KEY IF EXISTS student_attendance_recorded_by_foreign;

-- announcements (إذا كان موجوداً)
ALTER TABLE announcements DROP FOREIGN KEY IF EXISTS announcements_created_by_foreign;
ALTER TABLE announcements DROP FOREIGN KEY IF EXISTS announcements_updated_by_foreign;

-- system_logs (إذا كان موجوداً)
ALTER TABLE system_logs DROP FOREIGN KEY IF EXISTS system_logs_user_id_foreign;

-- 3. تنظيف البيانات غير الصحيحة

-- student_grades
DELETE FROM student_grades WHERE graded_by NOT IN (SELECT id FROM users);
UPDATE student_grades SET teacher_id = NULL WHERE teacher_id IS NOT NULL AND teacher_id NOT IN (SELECT id FROM users);
UPDATE student_grades SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);
UPDATE student_grades SET updated_by = NULL WHERE updated_by IS NOT NULL AND updated_by NOT IN (SELECT id FROM users);

-- unified_staff_absences
DELETE FROM unified_staff_absences WHERE user_id NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by IS NOT NULL AND applied_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- staff_absences_with_deduction
DELETE FROM staff_absences_with_deduction WHERE user_id NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by IS NOT NULL AND processed_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);
UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);

-- staff_leaves
DELETE FROM staff_leaves WHERE user_id NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET applied_by = NULL WHERE applied_by IS NOT NULL AND applied_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by IS NOT NULL AND cancelled_by NOT IN (SELECT id FROM users);
UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- staff_attendance
DELETE FROM staff_attendance WHERE user_id NOT IN (SELECT id FROM users);
UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);
UPDATE staff_attendance SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

-- admin_attendance
DELETE FROM admin_attendance WHERE admin_id NOT IN (SELECT id FROM users);
UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

-- teacher_subjects
UPDATE teacher_subjects SET teacher_id = NULL WHERE teacher_id IS NOT NULL AND teacher_id NOT IN (SELECT id FROM users);
UPDATE teacher_subjects SET assigned_by = NULL WHERE assigned_by IS NOT NULL AND assigned_by NOT IN (SELECT id FROM users);

-- class_teachers
UPDATE class_teachers SET teacher_id = NULL WHERE teacher_id IS NOT NULL AND teacher_id NOT IN (SELECT id FROM users);
UPDATE class_teachers SET assigned_by = NULL WHERE assigned_by IS NOT NULL AND assigned_by NOT IN (SELECT id FROM users);

-- student_attendance
UPDATE student_attendance SET teacher_id = NULL WHERE teacher_id IS NOT NULL AND teacher_id NOT IN (SELECT id FROM users);
UPDATE student_attendance SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

-- announcements
UPDATE announcements SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);
UPDATE announcements SET updated_by = NULL WHERE updated_by IS NOT NULL AND updated_by NOT IN (SELECT id FROM users);

-- system_logs
DELETE FROM system_logs WHERE user_id IS NOT NULL AND user_id NOT IN (SELECT id FROM users);

-- 4. إعادة إضافة القيود الخارجية الأساسية فقط (CASCADE للحذف التلقائي)

-- student_grades - فقط القيد الأساسي
ALTER TABLE student_grades
ADD CONSTRAINT fk_student_grades_graded_by 
FOREIGN KEY (graded_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- unified_staff_absences - فقط القيد الأساسي
ALTER TABLE unified_staff_absences
ADD CONSTRAINT fk_unified_absences_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- staff_absences_with_deduction - فقط القيد الأساسي
ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT fk_staff_absences_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- staff_leaves - فقط القيد الأساسي
ALTER TABLE staff_leaves
ADD CONSTRAINT fk_staff_leaves_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- staff_attendance - فقط القيد الأساسي
ALTER TABLE staff_attendance
ADD CONSTRAINT fk_staff_attendance_user 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- admin_attendance - فقط القيد الأساسي
ALTER TABLE admin_attendance
ADD CONSTRAINT fk_admin_attendance_user 
FOREIGN KEY (admin_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- إعادة تفعيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- 5. إنشاء إجراء شامل لحذف المستخدمين
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS delete_user_completely(IN p_user_id INT)
BEGIN
    DECLARE user_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدم
    SELECT COUNT(*) INTO user_exists FROM users WHERE id = p_user_id;
    
    IF user_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'User does not exist';
    END IF;
    
    -- تنظيف جميع المراجع يدوياً
    UPDATE student_grades SET graded_by = NULL WHERE graded_by = p_user_id;
    UPDATE student_grades SET teacher_id = NULL WHERE teacher_id = p_user_id;
    UPDATE student_grades SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE student_grades SET updated_by = NULL WHERE updated_by = p_user_id;
    
    UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by = p_user_id;
    UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by = p_user_id;
    
    UPDATE staff_leaves SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE staff_leaves SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by = p_user_id;
    UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_attendance SET approved_by = NULL WHERE approved_by = p_user_id;
    
    UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    
    UPDATE teacher_subjects SET teacher_id = NULL WHERE teacher_id = p_user_id;
    UPDATE teacher_subjects SET assigned_by = NULL WHERE assigned_by = p_user_id;
    
    UPDATE class_teachers SET teacher_id = NULL WHERE teacher_id = p_user_id;
    UPDATE class_teachers SET assigned_by = NULL WHERE assigned_by = p_user_id;
    
    UPDATE student_attendance SET teacher_id = NULL WHERE teacher_id = p_user_id;
    UPDATE student_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    
    UPDATE announcements SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE announcements SET updated_by = NULL WHERE updated_by = p_user_id;
    
    -- حذف المستخدم (سيحذف السجلات المرتبطة تلقائياً بسبب CASCADE)
    DELETE FROM users WHERE id = p_user_id;
    
    COMMIT;
    
    SELECT CONCAT('User ', p_user_id, ' deleted completely with all references cleaned') as message;
END//

DELIMITER ;

-- عرض النتائج
SELECT 'All foreign key constraints have been completely fixed!' as status;

-- عرض القيود الخارجية المتبقية
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users'
ORDER BY TABLE_NAME, COLUMN_NAME;
