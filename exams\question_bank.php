<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

// جلب الأسئلة
$questions = $conn->query("SELECT * FROM exam_questions");
?>
<div class="container">
    <h2>بنك الأسئلة</h2>
    <a href="add_question.php" class="btn btn-primary mb-3">إضافة سؤال</a>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>السؤال</th>
                <th>الدرجة</th>
                <th>الامتحان</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php while($q = $questions->fetch_assoc()): ?>
            <tr>
                <td><?= htmlspecialchars($q['question_text']) ?></td>
                <td><?= htmlspecialchars($q['marks']) ?></td>
                <td><?= htmlspecialchars($q['exam_id']) ?></td>
                <td>
                    <a href="view_question.php?id=<?= $q['id'] ?>" class="btn btn-info btn-sm">عرض</a>
                    <a href="edit_question.php?id=<?= $q['id'] ?>" class="btn btn-warning btn-sm">تعديل</a>
                    <a href="delete_question.php?id=<?= $q['id'] ?>" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</a>
                </td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>
<?php require_once '../includes/footer.php'; ?> 