<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$page_title = __('view_class');
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$class_id = intval($_GET['id'] ?? 0);
if (!$class_id) {
    $_SESSION['error_message'] = __('invalid_class_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات الفصل
$class = null;
global $conn;
$stmt = $conn->prepare("SELECT * FROM classes WHERE id = ?");
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows === 0) {
    $_SESSION['error_message'] = __('class_not_found');
    header('Location: index.php');
    exit();
}
$class = $result->fetch_assoc();

// حذف الفصل
if (isset($_POST['delete_class']) && check_permission('admin')) {
    // تحقق من عدم وجود طلاب في الفصل
    $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM students WHERE class_id = ?");
    $check_stmt->bind_param("i", $class_id);
    $check_stmt->execute();
    $student_count = $check_stmt->get_result()->fetch_assoc()['count'];
    if ($student_count > 0) {
        $_SESSION['error_message'] = __('cannot_delete_class_with_students');
        header('Location: view.php?id=' . $class_id);
        exit();
    }
    $stmt = $conn->prepare("DELETE FROM classes WHERE id = ?");
    $stmt->bind_param("i", $class_id);
    if ($stmt->execute()) {
        $_SESSION['success_message'] = __('deleted_successfully');
        header('Location: index.php');
        exit();
    } else {
        $_SESSION['error_message'] = __('error_occurred');
        header('Location: view.php?id=' . $class_id);
        exit();
    }
}
?>
<link rel="stylesheet" href="../assets/css/modern-style.css">
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('view_class'); ?></h1>
            <p class="text-muted"><?php echo htmlspecialchars($class['class_name']); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
            <?php if (check_permission('admin')): ?>
            <a href="edit.php?id=<?php echo $class_id; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
            </a>
            <form method="POST" style="display:inline;" onsubmit="return confirm('<?php echo __('are_you_sure_delete_class'); ?>');">
                <button type="submit" name="delete_class" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>
                </button>
            </form>
            <?php endif; ?>
        </div>
    </div>
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-school me-2"></i><?php echo __('class_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong><?php echo __('class_name'); ?>:</strong> <?php echo htmlspecialchars($class['class_name']); ?>
                </div>
                <div class="col-md-6">
                    <strong><?php echo __('grade_level'); ?>:</strong> <?php echo htmlspecialchars($class['grade_level']); ?>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong><?php echo __('capacity'); ?>:</strong> <?php echo htmlspecialchars($class['capacity']); ?>
                </div>
                <div class="col-md-6">
                    <strong><?php echo __('status'); ?>:</strong> <?php echo ($class['status'] == 'active') ? __('active') : __('inactive'); ?>
                </div>
            </div>
            <?php if (isset($class['description']) && !empty($class['description'])): ?>
                <div class="mb-3">
                    <strong><?php echo __('description'); ?>:</strong><br>
                    <span><?php echo nl2br(htmlspecialchars($class['description'])); ?></span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- جدول الطلاب المسجلين في الفصل -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-users me-2"></i><?php echo __('students'); ?>
            </h5>
        </div>
        <div class="card-body">
            <?php
            // جلب الطلاب المسجلين في هذا الفصل
            $students = [];
            $stmt = $conn->prepare("SELECT s.id, u.full_name, u.email, u.status, s.student_id FROM students s JOIN users u ON s.user_id = u.id WHERE s.class_id = ? ORDER BY u.full_name ASC");
            $stmt->bind_param("i", $class_id);
            $stmt->execute();
            $result = $stmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $students[] = $row;
            }
            ?>
            <?php if (count($students) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-bordered align-middle">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th><?php echo __('student_id'); ?></th>
                                <th><?php echo __('full_name'); ?></th>
                                <th><?php echo __('email'); ?></th>
                                <th><?php echo __('status'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $i => $student): ?>
                                <tr>
                                    <td><?php echo $i + 1; ?></td>
                                    <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                    <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($student['email']); ?></td>
                                    <td><?php echo ($student['status'] == 'active') ? __('active') : __('inactive'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info text-center mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('no_students_found'); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php require_once '../includes/footer.php'; ?> 