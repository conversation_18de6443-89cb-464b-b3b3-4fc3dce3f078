<?php
/**
 * API لحفظ تفضيلات المستخدم
 * User Preferences API
 */

define('SYSTEM_INIT', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['key']) || !isset($input['value'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid input']);
    exit();
}

$key = clean_input($input['key']);
$value = clean_input($input['value']);

// التحقق من صحة المفاتيح المسموحة
$allowed_keys = ['language', 'theme', 'timezone', 'items_per_page'];

if (!in_array($key, $allowed_keys)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid preference key']);
    exit();
}

// التحقق من صحة القيم
switch ($key) {
    case 'language':
        if (!in_array($value, ['ar', 'en'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid language']);
            exit();
        }
        break;
    case 'theme':
        if (!in_array($value, ['light', 'dark', 'auto'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid theme']);
            exit();
        }
        break;
    case 'timezone':
        if (!in_array($value, timezone_identifiers_list())) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid timezone']);
            exit();
        }
        break;
    case 'items_per_page':
        if (!is_numeric($value) || $value < 5 || $value > 100) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid items per page']);
            exit();
        }
        break;
}

try {
    // حفظ التفضيل في الجلسة
    if ($key === 'language') {
        $_SESSION['system_language'] = $value;
    } else {
        $_SESSION['system_' . $key] = $value;
    }
    
    // إذا كان المستخدم مسجلاً، احفظ في قاعدة البيانات
    if (is_logged_in()) {
        global $conn;
        
        // التحقق من وجود التفضيل
        $check_stmt = $conn->prepare("SELECT id FROM user_preferences WHERE user_id = ? AND preference_key = ?");
        $check_stmt->bind_param("is", $_SESSION['user_id'], $key);
        $check_stmt->execute();
        $exists = $check_stmt->get_result()->num_rows > 0;
        
        if ($exists) {
            // تحديث التفضيل الموجود
            $update_stmt = $conn->prepare("UPDATE user_preferences SET preference_value = ?, updated_at = NOW() WHERE user_id = ? AND preference_key = ?");
            $update_stmt->bind_param("sis", $value, $_SESSION['user_id'], $key);
            $update_stmt->execute();
        } else {
            // إنشاء تفضيل جديد
            $insert_stmt = $conn->prepare("INSERT INTO user_preferences (user_id, preference_key, preference_value, created_at) VALUES (?, ?, ?, NOW())");
            $insert_stmt->bind_param("iss", $_SESSION['user_id'], $key, $value);
            $insert_stmt->execute();
        }
        
        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'update_preference', 'user_preferences', null, null, [
            'preference_key' => $key,
            'preference_value' => $value
        ]);
    }
    
    echo json_encode(['success' => true, 'message' => 'Preference saved successfully']);
    
} catch (Exception $e) {
    log_error("Error saving preference: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
