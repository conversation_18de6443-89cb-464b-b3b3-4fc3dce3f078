<?php
/**
 * ملف التكوين الأساسي لنظام إدارة المدارس
 * School Management System Configuration
 */

// تفعيل عرض جميع أخطاء PHP أثناء التطوير
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// منع الوصول المباشر
if (!defined('SYSTEM_INIT')) {
    define('SYSTEM_INIT', true);
}

// إعدادات قاعدة البيانات
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_NAME')) define('DB_NAME', 'school_management');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASSWORD')) define('DB_PASSWORD', '');
if (!defined('DB_PASS')) define('DB_PASS', DB_PASSWORD);
if (!defined('DB_CHARSET')) define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام الأساسية
if (!defined('SYSTEM_NAME')) define('SYSTEM_NAME', 'نظام إدارة المدارس');
if (!defined('SYSTEM_VERSION')) define('SYSTEM_VERSION', '2.0.0');
if (!defined('SYSTEM_URL')) define('SYSTEM_URL', 'http://localhost/school_system_v2');
if (!defined('ADMIN_EMAIL')) define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات الأمان
if (!defined('SESSION_TIMEOUT')) define('SESSION_TIMEOUT', 3600); // ساعة واحدة
if (!defined('MAX_LOGIN_ATTEMPTS')) define('MAX_LOGIN_ATTEMPTS', 5);
if (!defined('LOGIN_LOCKOUT_TIME')) define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة
if (!defined('PASSWORD_MIN_LENGTH')) define('PASSWORD_MIN_LENGTH', 6);
if (!defined('CSRF_TOKEN_EXPIRE')) define('CSRF_TOKEN_EXPIRE', 1800); // 30 دقيقة

// إعدادات الملفات
if (!defined('UPLOAD_PATH')) define('UPLOAD_PATH', __DIR__ . '/../uploads/');
if (!defined('MAX_FILE_SIZE')) define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 ميجابايت
if (!defined('ALLOWED_IMAGE_TYPES')) define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
if (!defined('ALLOWED_DOCUMENT_TYPES')) define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'txt']);
if (!defined('ALLOWED_AUDIO_TYPES')) define('ALLOWED_AUDIO_TYPES', ['mp3', 'wav', 'ogg']);
if (!defined('ALLOWED_VIDEO_TYPES')) define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov']);

// إعدادات اللغة والمنطقة الزمنية
if (!defined('DEFAULT_LANGUAGE')) define('DEFAULT_LANGUAGE', 'ar');
if (!defined('DEFAULT_TIMEZONE')) define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
if (!defined('DEFAULT_CURRENCY')) define('DEFAULT_CURRENCY', 'ريال سعودي');
if (!defined('CURRENCY_SYMBOL')) define('CURRENCY_SYMBOL', 'ر.س');

// إعدادات التطبيق
if (!defined('ITEMS_PER_PAGE')) define('ITEMS_PER_PAGE', 20);
if (!defined('DEBUG_MODE')) define('DEBUG_MODE', true);
if (!defined('LOG_ERRORS')) define('LOG_ERRORS', true);
if (!defined('LOG_PATH')) define('LOG_PATH', __DIR__ . '/../logs/');

// إعدادات البريد الإلكتروني
if (!defined('SMTP_HOST')) define('SMTP_HOST', 'smtp.gmail.com');
if (!defined('SMTP_PORT')) define('SMTP_PORT', 587);
if (!defined('SMTP_USERNAME')) define('SMTP_USERNAME', '');
if (!defined('SMTP_PASSWORD')) define('SMTP_PASSWORD', '');
if (!defined('SMTP_ENCRYPTION')) define('SMTP_ENCRYPTION', 'tls');

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إعداد معالج الأخطاء
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// إعداد المنطقة الزمنية الافتراضية
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعداد الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

/**
 * دالة لحفظ إعداد في قاعدة البيانات
 */
function set_system_setting($key, $value, $description = '') {
    global $conn;
    
    // التحقق من وجود الاتصال بقاعدة البيانات
    if (!isset($conn) || $conn === null) {
        return false;
    }
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO system_settings (setting_key, setting_value, description, updated_at) 
            VALUES (?, ?, ?, NOW()) 
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value), 
            description = VALUES(description), 
            updated_at = NOW()
        ");
        if (!$stmt) {
            return false;
        }
        $stmt->bind_param("sss", $key, $value, $description);
        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Error setting system setting: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على جميع الإعدادات
 */
function get_all_system_settings() {
    global $conn;
    
    // التحقق من وجود الاتصال بقاعدة البيانات
    if (!isset($conn) || $conn === null) {
        return [];
    }
    
    try {
        $result = $conn->query("SELECT setting_key, setting_value, description FROM system_settings");
        if (!$result) {
            return [];
        }
        $settings = [];
        
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = [
                'value' => $row['setting_value'],
                'description' => $row['description']
            ];
        }
        
        return $settings;
    } catch (Exception $e) {
        error_log("Error getting all system settings: " . $e->getMessage());
        return [];
    }
}

/**
 * دالة لتطبيق إعدادات النظام بعد تحميل قاعدة البيانات
 */
function apply_system_settings() {
    // تطبيق المنطقة الزمنية من قاعدة البيانات
    $timezone = get_system_setting('timezone', DEFAULT_TIMEZONE);
    date_default_timezone_set($timezone);
    
    // تطبيق إعدادات أخرى حسب الحاجة
    $language = get_system_setting('language', DEFAULT_LANGUAGE);
    if (isset($_SESSION) && !isset($_SESSION['system_language'])) {
        $_SESSION['system_language'] = $language;
    }
}

// دالة log_activity معرفة في includes/functions.php

/**
 * دالة لتنظيف البيانات المدخلة
 */
// تم حذف أو تعليق تعريف الدالة clean_input() من هنا لتجنب التكرار

/**
 * دالة للحصول على قيمة آمنة من المصفوفة
 */
function safe_get($array, $key, $default = '') {
    return isset($array[$key]) ? $array[$key] : $default;
}

/**
 * دالة لتحويل التاريخ إلى التنسيق العربي
 */
function format_arabic_date($date, $format = 'Y/m/d') {
    if (!$date) return '';
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    if (!$timestamp) return '';
    
    return date($format, $timestamp);
}

/**
 * دالة لتحويل الأرقام إلى العربية
 */
function arabic_numbers($str) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($english_numbers, $arabic_numbers, $str);
}

/**
 * دالة لإنشاء كلمة مرور عشوائية
 */
function generate_password($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

// تحديد مسارات النظام
if (!defined('ROOT_PATH')) define('ROOT_PATH', dirname(__DIR__));
if (!defined('INCLUDES_PATH')) define('INCLUDES_PATH', ROOT_PATH . '/includes');
if (!defined('UPLOADS_PATH')) define('UPLOADS_PATH', ROOT_PATH . '/uploads');
if (!defined('ASSETS_PATH')) define('ASSETS_PATH', ROOT_PATH . '/assets');

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
$required_dirs = [
    UPLOADS_PATH,
    UPLOADS_PATH . '/profiles',
    UPLOADS_PATH . '/exams',
    UPLOADS_PATH . '/documents',
    LOG_PATH
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// تحميل ملفات الترجمة
$language_file = INCLUDES_PATH . '/languages/' . DEFAULT_LANGUAGE . '.php';
if (file_exists($language_file)) {
    include_once $language_file;
}
?>
