<h3 class="mb-4">
    <i class="fas fa-school text-primary me-2"></i>
    School Configuration
</h3>

<p class="text-muted mb-4">
    Configure your school's basic information and system settings.
</p>

<form method="POST" action="?step=4" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="school_name" class="form-label">School Name (Arabic) <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control" 
                       id="school_name" 
                       name="school_name" 
                       value="<?php echo htmlspecialchars($_POST['school_name'] ?? 'مدرسة المستقبل'); ?>"
                       required>
                <div class="invalid-feedback">
                    Please provide the school name in Arabic.
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label for="school_name_en" class="form-label">School Name (English)</label>
                <input type="text" 
                       class="form-control" 
                       id="school_name_en" 
                       name="school_name_en" 
                       value="<?php echo htmlspecialchars($_POST['school_name_en'] ?? 'Future School'); ?>">
                <div class="form-text">Optional - for bilingual support</div>
            </div>
        </div>
    </div>
    
    <div class="mb-3">
        <label for="school_address" class="form-label">School Address</label>
        <textarea class="form-control" 
                  id="school_address" 
                  name="school_address" 
                  rows="3"><?php echo htmlspecialchars($_POST['school_address'] ?? ''); ?></textarea>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <label for="school_phone" class="form-label">Phone Number</label>
                <input type="tel" 
                       class="form-control" 
                       id="school_phone" 
                       name="school_phone" 
                       value="<?php echo htmlspecialchars($_POST['school_phone'] ?? ''); ?>">
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="mb-3">
                <label for="school_email" class="form-label">School Email</label>
                <input type="email" 
                       class="form-control" 
                       id="school_email" 
                       name="school_email" 
                       value="<?php echo htmlspecialchars($_POST['school_email'] ?? ''); ?>">
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="mb-3">
                <label for="currency_symbol" class="form-label">Currency Symbol</label>
                <input type="text" 
                       class="form-control" 
                       id="currency_symbol" 
                       name="currency_symbol" 
                       value="<?php echo htmlspecialchars($_POST['currency_symbol'] ?? 'ر.س'); ?>"
                       maxlength="10">
                <div class="form-text">e.g., $, €, ر.س</div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="academic_year" class="form-label">Current Academic Year <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control" 
                       id="academic_year" 
                       name="academic_year" 
                       value="<?php echo htmlspecialchars($_POST['academic_year'] ?? date('Y') . '-' . (date('Y') + 1)); ?>"
                       pattern="[0-9]{4}-[0-9]{4}"
                       placeholder="2024-2025"
                       required>
                <div class="form-text">Format: YYYY-YYYY (e.g., 2024-2025)</div>
                <div class="invalid-feedback">
                    Please provide the academic year in YYYY-YYYY format.
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label for="semester" class="form-label">Current Semester</label>
                <select class="form-select" id="semester" name="semester">
                    <option value="first" <?php echo ($_POST['semester'] ?? 'first') === 'first' ? 'selected' : ''; ?>>
                        First Semester
                    </option>
                    <option value="second" <?php echo ($_POST['semester'] ?? '') === 'second' ? 'selected' : ''; ?>>
                        Second Semester
                    </option>
                    <option value="summer" <?php echo ($_POST['semester'] ?? '') === 'summer' ? 'selected' : ''; ?>>
                        Summer Semester
                    </option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Note:</strong> You can modify all these settings later from the system settings page.
    </div>
    
    <div class="d-flex justify-content-between">
        <a href="?step=3" class="btn btn-secondary btn-lg">
            <i class="fas fa-arrow-left me-2"></i>Back
        </a>
        <button type="submit" class="btn btn-primary btn-lg">
            Save Configuration & Continue <i class="fas fa-arrow-right ms-2"></i>
        </button>
    </div>
</form>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
