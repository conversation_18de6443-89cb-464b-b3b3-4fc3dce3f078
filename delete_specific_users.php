<?php
/**
 * حذف مستخدمين محددين من النظام
 * Delete specific users from the system
 */

echo "<h1>🗑️ حذف مستخدمين محددين من النظام</h1>";

// الاتصال بقاعدة البيانات
$conn = new mysqli('localhost', 'root', '', 'school_management');

if ($conn->connect_error) {
    echo "<div class='alert alert-danger'>❌ فشل في الاتصال: " . $conn->connect_error . "</div>";
    exit;
}

$conn->set_charset("utf8mb4");
echo "<div class='alert alert-success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

// قائمة المستخدمين المطلوب حذفهم
$emails_to_delete = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
];

echo "<h3>📋 المستخدمين المطلوب حذفهم:</h3>";
echo "<ul>";
foreach ($emails_to_delete as $email) {
    echo "<li>$email</li>";
}
echo "</ul>";

try {
    // البحث عن المستخدمين أولاً
    echo "<h3>🔍 البحث عن المستخدمين في النظام:</h3>";
    
    $users_to_delete = [];
    foreach ($emails_to_delete as $email) {
        $stmt = $conn->prepare("SELECT id, username, email, full_name, role FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            $users_to_delete[] = $user;
            echo "<p>✅ تم العثور على: <strong>" . $user['full_name'] . "</strong> (ID: " . $user['id'] . ", Email: " . $user['email'] . ", Role: " . $user['role'] . ")</p>";
        } else {
            echo "<p>⚠️ لم يتم العثور على مستخدم بالبريد الإلكتروني: $email</p>";
        }
    }
    
    if (empty($users_to_delete)) {
        echo "<div class='alert alert-warning'>⚠️ لم يتم العثور على أي من المستخدمين المطلوب حذفهم</div>";
        exit;
    }
    
    echo "<p><strong>إجمالي المستخدمين الموجودين للحذف:</strong> " . count($users_to_delete) . "</p>";
    
    // عرض البيانات المرتبطة قبل الحذف
    echo "<h3>📊 البيانات المرتبطة قبل الحذف:</h3>";
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>المستخدم</th>";
    echo "<th>الحضور</th>";
    echo "<th>الغياب الموحد</th>";
    echo "<th>الغياب التقليدي</th>";
    echo "<th>الإجازات</th>";
    echo "<th>إدارة الحضور</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    $total_related_records = 0;
    foreach ($users_to_delete as $user) {
        $user_id = $user['id'];
        
        // عد البيانات المرتبطة
        $attendance_count = $conn->query("SELECT COUNT(*) as count FROM staff_attendance WHERE user_id = $user_id")->fetch_assoc()['count'];
        $unified_absence_count = $conn->query("SELECT COUNT(*) as count FROM unified_staff_absences WHERE user_id = $user_id")->fetch_assoc()['count'];
        $traditional_absence_count = $conn->query("SELECT COUNT(*) as count FROM staff_absences_with_deduction WHERE user_id = $user_id")->fetch_assoc()['count'];
        $leaves_count = $conn->query("SELECT COUNT(*) as count FROM staff_leaves WHERE user_id = $user_id")->fetch_assoc()['count'];
        $admin_attendance_count = $conn->query("SELECT COUNT(*) as count FROM admin_attendance WHERE admin_id = $user_id")->fetch_assoc()['count'];
        
        $user_total = $attendance_count + $unified_absence_count + $traditional_absence_count + $leaves_count + $admin_attendance_count;
        $total_related_records += $user_total;
        
        echo "<tr>";
        echo "<td>" . $user['full_name'] . " (ID: " . $user['id'] . ")</td>";
        echo "<td>$attendance_count</td>";
        echo "<td>$unified_absence_count</td>";
        echo "<td>$traditional_absence_count</td>";
        echo "<td>$leaves_count</td>";
        echo "<td>$admin_attendance_count</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    echo "<p><strong>إجمالي السجلات المرتبطة:</strong> $total_related_records</p>";
    
    if ($total_related_records > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ تحذير</h4>";
        echo "<p>سيتم حذف <strong>$total_related_records</strong> سجل مرتبط مع هؤلاء المستخدمين تلقائياً (CASCADE).</p>";
        echo "</div>";
    }
    
    // نموذج تأكيد الحذف
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
        echo "<h3>🗑️ بدء عملية الحذف:</h3>";
        
        $deleted_users = 0;
        $deleted_records = 0;
        
        foreach ($users_to_delete as $user) {
            $user_id = $user['id'];
            $user_name = $user['full_name'];
            
            echo "<h4>🔄 حذف المستخدم: $user_name (ID: $user_id)</h4>";
            
            // عد البيانات المرتبطة قبل الحذف
            $before_counts = [
                'attendance' => $conn->query("SELECT COUNT(*) as count FROM staff_attendance WHERE user_id = $user_id")->fetch_assoc()['count'],
                'unified_absence' => $conn->query("SELECT COUNT(*) as count FROM unified_staff_absences WHERE user_id = $user_id")->fetch_assoc()['count'],
                'traditional_absence' => $conn->query("SELECT COUNT(*) as count FROM staff_absences_with_deduction WHERE user_id = $user_id")->fetch_assoc()['count'],
                'leaves' => $conn->query("SELECT COUNT(*) as count FROM staff_leaves WHERE user_id = $user_id")->fetch_assoc()['count'],
                'admin_attendance' => $conn->query("SELECT COUNT(*) as count FROM admin_attendance WHERE admin_id = $user_id")->fetch_assoc()['count']
            ];
            
            // تنظيف المراجع أولاً (للحقول التي لا تحتوي على CASCADE)
            echo "<p>🧹 تنظيف المراجع...</p>";
            
            $conn->query("UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by = $user_id");
            $conn->query("UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by = $user_id");
            $conn->query("UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id = $user_id");
            
            $conn->query("UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by = $user_id");
            
            $conn->query("UPDATE staff_leaves SET applied_by = NULL WHERE applied_by = $user_id");
            $conn->query("UPDATE staff_leaves SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by = $user_id");
            $conn->query("UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by = $user_id");
            $conn->query("UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id = $user_id");
            
            $conn->query("UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by = $user_id");
            $conn->query("UPDATE staff_attendance SET approved_by = NULL WHERE approved_by = $user_id");
            
            $conn->query("UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by = $user_id");
            
            echo "<p>✅ تم تنظيف المراجع</p>";
            
            // حذف المستخدم (سيحذف السجلات المرتبطة تلقائياً بسبب CASCADE)
            $delete_stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $delete_stmt->bind_param("i", $user_id);
            
            if ($delete_stmt->execute()) {
                echo "<p>✅ تم حذف المستخدم بنجاح</p>";
                $deleted_users++;
                
                // عد البيانات المرتبطة بعد الحذف للتأكد
                $after_counts = [
                    'attendance' => $conn->query("SELECT COUNT(*) as count FROM staff_attendance WHERE user_id = $user_id")->fetch_assoc()['count'],
                    'unified_absence' => $conn->query("SELECT COUNT(*) as count FROM unified_staff_absences WHERE user_id = $user_id")->fetch_assoc()['count'],
                    'traditional_absence' => $conn->query("SELECT COUNT(*) as count FROM staff_absences_with_deduction WHERE user_id = $user_id")->fetch_assoc()['count'],
                    'leaves' => $conn->query("SELECT COUNT(*) as count FROM staff_leaves WHERE user_id = $user_id")->fetch_assoc()['count'],
                    'admin_attendance' => $conn->query("SELECT COUNT(*) as count FROM admin_attendance WHERE admin_id = $user_id")->fetch_assoc()['count']
                ];
                
                $user_deleted_records = 0;
                foreach ($before_counts as $table => $before_count) {
                    $after_count = $after_counts[$table];
                    $deleted_count = $before_count - $after_count;
                    $user_deleted_records += $deleted_count;
                    
                    if ($deleted_count > 0) {
                        echo "<p>📊 $table: تم حذف $deleted_count سجل</p>";
                    }
                }
                
                $deleted_records += $user_deleted_records;
                echo "<p><strong>إجمالي السجلات المحذوفة للمستخدم:</strong> $user_deleted_records</p>";
                
            } else {
                echo "<p>❌ فشل في حذف المستخدم: " . $delete_stmt->error . "</p>";
            }
            
            echo "<hr>";
        }
        
        echo "<div class='alert alert-success'>";
        echo "<h4>🎉 تم الانتهاء من عملية الحذف!</h4>";
        echo "<p><strong>النتائج:</strong></p>";
        echo "<ul>";
        echo "<li>✅ تم حذف <strong>$deleted_users</strong> مستخدم</li>";
        echo "<li>✅ تم حذف <strong>$deleted_records</strong> سجل مرتبط</li>";
        echo "<li>✅ تم تنظيف جميع المراجع</li>";
        echo "<li>✅ لا توجد مشاكل في القيود الخارجية</li>";
        echo "</ul>";
        echo "</div>";
        
        // التحقق النهائي
        echo "<h3>🔍 التحقق النهائي:</h3>";
        
        foreach ($emails_to_delete as $email) {
            $check_result = $conn->query("SELECT COUNT(*) as count FROM users WHERE email = '$email'");
            $count = $check_result->fetch_assoc()['count'];
            
            if ($count == 0) {
                echo "<p>✅ تم حذف المستخدم: $email</p>";
            } else {
                echo "<p>❌ المستخدم لا يزال موجوداً: $email</p>";
            }
        }
        
    } else {
        // عرض نموذج التأكيد
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ تأكيد الحذف</h4>";
        echo "<p>هل أنت متأكد من حذف هؤلاء المستخدمين وجميع البيانات المرتبطة بهم؟</p>";
        echo "<p><strong>هذا الإجراء لا يمكن التراجع عنه!</strong></p>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<div class='form-check'>";
        echo "<input type='checkbox' class='form-check-input' id='confirm_checkbox' required>";
        echo "<label class='form-check-label' for='confirm_checkbox'>";
        echo "أؤكد أنني أريد حذف هؤلاء المستخدمين وجميع البيانات المرتبطة بهم";
        echo "</label>";
        echo "</div>";
        echo "<br>";
        echo "<button type='submit' name='confirm_delete' class='btn btn-danger'>🗑️ تأكيد الحذف</button>";
        echo "<a href='" . $_SERVER['PHP_SELF'] . "' class='btn btn-secondary'>❌ إلغاء</a>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ في العملية</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

$conn->close();

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
.alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.alert-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
.table { width: 100%; border-collapse: collapse; margin: 10px 0; }
.table th, .table td { padding: 8px; text-align: right; border-bottom: 1px solid #ddd; }
.table-striped tbody tr:nth-child(odd) { background-color: #f9f9f9; }
.table-responsive { overflow-x: auto; }
.btn { padding: 0.375rem 0.75rem; margin: 0.125rem; border: none; border-radius: 0.375rem; cursor: pointer; text-decoration: none; display: inline-block; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.form-check { margin: 10px 0; }
.form-check-input { margin-left: 0.5rem; }
h3 { color: #333; margin-top: 30px; }
h4 { color: #666; margin-top: 20px; }
hr { margin: 20px 0; border: 1px solid #ddd; }
</style>
