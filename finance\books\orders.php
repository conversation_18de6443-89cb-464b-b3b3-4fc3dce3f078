<?php
/**
 * صفحة إدارة طلبات الكتب
 * Book Orders Management Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// Fetch book orders
$orders_query = "
    SELECT 
        sbo.id, sbo.order_date, sbo.total_amount, sbo.status,
        s.national_id, u.full_name as student_name,
        c.class_name
    FROM student_book_orders sbo
    JOIN students s ON sbo.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    ORDER BY sbo.order_date DESC
";
$orders_result = $conn->query($orders_query);

// Check for query errors
if ($orders_result === false) {
    die('Error executing query: ' . htmlspecialchars($conn->error));
}

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('book_orders'); ?></h1>
            <p class="text-muted"><?php echo __('manage_student_book_orders'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_books'); ?>
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th><?php echo __('order_id'); ?></th>
                            <th><?php echo __('student_name'); ?></th>
                            <th><?php echo __('national_id'); ?></th>
                            <th><?php echo __('class'); ?></th>
                            <th><?php echo __('order_date'); ?></th>
                            <th><?php echo __('total_amount'); ?></th>
                            <th><?php echo __('status'); ?></th>
                            <th><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($orders_result->num_rows > 0): ?>
                            <?php while($order = $orders_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $order['id']; ?></td>
                                    <td><?php echo htmlspecialchars($order['student_name']); ?></td>
                                    <td><?php echo htmlspecialchars($order['national_id']); ?></td>
                                    <td><?php echo htmlspecialchars($order['class_name']); ?></td>
                                    <td><?php echo date_format(date_create($order['order_date']), 'Y-m-d'); ?></td>
                                    <td><?php echo number_format($order['total_amount'], 2); ?></td>
                                    <td><span class="badge bg-<?php echo get_status_badge($order['status']); ?>"><?php echo __($order['status']); ?></span></td>
                                    <td>
                                        <a href="order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-info"><i class="fas fa-eye"></i></a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center"><?php echo __('no_orders_found'); ?></td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
