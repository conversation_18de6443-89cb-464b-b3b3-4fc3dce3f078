<?php
/**
 * API للحصول على عدد الإشعارات غير المقروءة
 * Unread Notifications Count API
 */

define('SYSTEM_INIT', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    $user_id = $_SESSION['user_id'];
    
    global $conn;
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    
    $result = $stmt->get_result()->fetch_assoc();
    $count = intval($result['count']);
    
    echo json_encode(['count' => $count]);
    
} catch (Exception $e) {
    log_error("Error fetching unread notifications count: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
