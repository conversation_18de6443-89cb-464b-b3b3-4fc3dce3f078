<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$error_message = '';
$success_message = '';

// جلب قائمة الفصول النشطة (يجب أن يكون متاحًا دائمًا للنموذج)
$classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");

// معالجة إضافة المعلم
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
        unset($_SESSION['success_message']);
    } else {
        // جمع البيانات وتنظيفها
        $full_name = clean_input($_POST['full_name'] ?? '');
        $email = clean_input($_POST['email'] ?? '');
        $username = clean_input($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $phone = clean_input($_POST['phone'] ?? '');
        $national_id = clean_input($_POST['national_id'] ?? '');
        $date_of_birth = clean_input($_POST['date_of_birth'] ?? '');
        $gender = clean_input($_POST['gender'] ?? '');
        $address = clean_input($_POST['address'] ?? '');
        $employee_id = clean_input($_POST['employee_id'] ?? '');
        $hire_date = clean_input($_POST['hire_date'] ?? '');
        $department = clean_input($_POST['department'] ?? '');
        $qualification = clean_input($_POST['qualification'] ?? '');
        $specialization = clean_input($_POST['specialization'] ?? '');
        $experience_years = intval($_POST['experience_years'] ?? 0);
        $salary = floatval($_POST['salary'] ?? 0);
        $status = clean_input($_POST['status'] ?? 'active');

        // التحقق من صحة البيانات
        $errors = [];
        if (empty($full_name)) {
            $errors[] = __('full_name') . ' ' . __('required_field');
        }
        if (empty($username)) {
            $errors[] = __('username') . ' ' . __('required_field');
        } elseif (get_user_by_username($username)) {
            $errors[] = __('username_exists');
        }
        if (empty($email)) {
            $errors[] = __('email') . ' ' . __('required_field');
        } elseif (!validate_email($email)) {
            $errors[] = __('invalid_email');
        } elseif (get_user_by_email($email)) {
            $errors[] = __('email_exists');
        }
        if (empty($password)) {
            $errors[] = __('password') . ' ' . __('required_field');
        } elseif (!validate_password($password)) {
            $errors[] = __('password_too_short');
        } elseif ($password !== $confirm_password) {
            $errors[] = __('passwords_not_match');
        }
        if (empty($employee_id)) {
            $errors[] = __('employee_id') . ' ' . __('required_field');
        } else {
            $stmt = $conn->prepare("SELECT id FROM teachers WHERE employee_id = ?");
            $stmt->bind_param("s", $employee_id);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $errors[] = __('employee_id_already_exists');
            }
        }
        if (!empty($national_id) && !validate_national_id($national_id)) {
            $errors['national_id'] = __('invalid_national_id');
        }
        if (!empty($phone) && !validate_phone($phone)) {
            $errors['phone'] = __('invalid_phone');
        }
        if (!empty($date_of_birth) && !validate_date($date_of_birth)) {
            $errors[] = __('invalid_date_of_birth');
        }
        if (!empty($hire_date) && !validate_date($hire_date)) {
            $errors[] = __('invalid_hire_date');
        }
        if (empty($gender) || !in_array($gender, ['male', 'female'])) {
            $errors[] = __('gender') . ' ' . __('required_field');
        }
        // التحقق من اختيار الفصول
        $class_ids = $_POST['class_ids'] ?? [];
        if (empty($class_ids) || !is_array($class_ids)) {
            $errors[] = __('class') . ' ' . __('required_field');
        }
        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();
            try {
                // إنشاء المستخدم
                $hashed_password = hash_password($password);
                $user_stmt = $conn->prepare("
                    INSERT INTO users (username, email, password, full_name, phone, role, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, 'teacher', ?, NOW())
                ");
                if (!$user_stmt) throw new Exception($conn->error);
                $user_stmt->bind_param("ssssss", $username, $email, $hashed_password, $full_name, $phone, $status);
                if (!$user_stmt->execute()) throw new Exception($user_stmt->error);
                $user_id = $conn->insert_id;
                // إنشاء سجل المعلم
                $teacher_stmt = $conn->prepare("
                    INSERT INTO teachers (
                        user_id, employee_id, national_id, date_of_birth, gender, address, hire_date, department, qualification, specialization, experience_years, salary, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                if (!$teacher_stmt) throw new Exception($conn->error);
                $teacher_stmt->bind_param(
                    "isssssssssdss",
                    $user_id, $employee_id, $national_id, $date_of_birth, $gender, $address, $hire_date, $department, $qualification, $specialization, $experience_years, $salary, $status
                );
                if (!$teacher_stmt->execute()) throw new Exception($teacher_stmt->error);
                $teacher_row_id = $conn->insert_id;
                // معالجة رفع الصورة الشخصية
                if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = upload_file($_FILES['profile_picture'], 'profiles', ALLOWED_IMAGE_TYPES);
                    if ($upload_result['success']) {
                        $update_stmt = $conn->prepare("UPDATE users SET profile_picture = ? WHERE id = ?");
                        $update_stmt->bind_param("si", $upload_result['filename'], $user_id);
                        $update_stmt->execute();
                    }
                }
                // ربط المعلم بالفصول المختارة
                foreach ($class_ids as $class_id) {
                    $class_id = intval($class_id);
                    // تحقق من عدم وجود ربط مسبق
                    $check_stmt = $conn->prepare("SELECT id FROM teacher_assignments WHERE teacher_id = ? AND class_id = ? AND status = 'active'");
                    $check_stmt->bind_param("ii", $teacher_row_id, $class_id);
                    $check_stmt->execute();
                    if ($check_stmt->get_result()->num_rows == 0) {
                        $assign_stmt = $conn->prepare("INSERT INTO teacher_assignments (teacher_id, class_id, status, assigned_at) VALUES (?, ?, 'active', NOW())");
                        $assign_stmt->bind_param("ii", $teacher_row_id, $class_id);
                        $assign_stmt->execute();
                    }
                }
                $conn->commit();
                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'add_teacher', 'teachers', $teacher_row_id, null, [
                    'teacher_name' => $full_name,
                    'employee_id' => $employee_id
                ]);
                // إرسال إشعار
                add_notification($user_id, __('welcome_to_system'), __('your_account_created_successfully'), 'success');
                $_SESSION['success_message'] = __('teacher_added_successfully');
                header('Location: index.php');
                exit();
            } catch (Exception $e) {
                $conn->rollback();
                log_error("Error adding teacher: " . $e->getMessage());
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    $error_message = __('error_occurred') . '<br>' . htmlspecialchars($e->getMessage());
                } else {
                    $error_message = __('error_occurred');
                }
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

$page_title = __('add_teacher');
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}
?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('add_teacher'); ?></h1>
            <p class="text-muted"><?php echo __('add_new_teacher_info'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>
    <!-- Add Teacher Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-chalkboard-teacher me-2"></i><?php echo __('teacher_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-lg-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user me-2"></i><?php echo __('personal_information'); ?>
                        </h6>
                        <div class="mb-3">
                            <label for="full_name" class="form-label"><?php echo __('full_name'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>
                        <div class="mb-3">
                            <label for="username" class="form-label"><?php echo __('username'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label"><?php echo __('password'); ?> <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label"><?php echo __('confirm_password'); ?> <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="national_id" class="form-label"><?php echo __('national_id'); ?></label>
                            <input type="text" class="form-control" id="national_id" name="national_id" value="<?php echo htmlspecialchars($_POST['national_id'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="date_of_birth" class="form-label"><?php echo __('birth_date'); ?></label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="<?php echo htmlspecialchars($_POST['date_of_birth'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="gender" class="form-label"><?php echo __('gender'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value=""><?php echo __('select'); ?></option>
                                <option value="male" <?php echo (($_POST['gender'] ?? '') === 'male') ? 'selected' : ''; ?>><?php echo __('male'); ?></option>
                                <option value="female" <?php echo (($_POST['gender'] ?? '') === 'female') ? 'selected' : ''; ?>><?php echo __('female'); ?></option>
                            </select>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label"><?php echo __('address'); ?></label>
                            <input type="text" class="form-control" id="address" name="address" value="<?php echo htmlspecialchars($_POST['address'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="profile_picture" class="form-label"><?php echo __('profile_picture'); ?></label>
                            <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                        </div>
                    </div>
                    <!-- Professional Information -->
                    <div class="col-lg-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-briefcase me-2"></i><?php echo __('professional_information'); ?>
                        </h6>
                        <div class="mb-3">
                            <label for="employee_id" class="form-label"><?php echo __('employee_id'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" value="<?php echo htmlspecialchars($_POST['employee_id'] ?? ''); ?>" required>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>
                        <div class="mb-3">
                            <label for="hire_date" class="form-label"><?php echo __('hire_date'); ?></label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date" value="<?php echo htmlspecialchars($_POST['hire_date'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="department" class="form-label"><?php echo __('department'); ?></label>
                            <input type="text" class="form-control" id="department" name="department" value="<?php echo htmlspecialchars($_POST['department'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="qualification" class="form-label"><?php echo __('qualification'); ?></label>
                            <input type="text" class="form-control" id="qualification" name="qualification" value="<?php echo htmlspecialchars($_POST['qualification'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="specialization" class="form-label"><?php echo __('specialization'); ?></label>
                            <input type="text" class="form-control" id="specialization" name="specialization" value="<?php echo htmlspecialchars($_POST['specialization'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="experience_years" class="form-label"><?php echo __('experience_years'); ?></label>
                            <input type="number" class="form-control" id="experience_years" name="experience_years" value="<?php echo htmlspecialchars($_POST['experience_years'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="salary" class="form-label"><?php echo __('salary'); ?></label>
                            <input type="number" step="0.01" class="form-control" id="salary" name="salary" value="<?php echo htmlspecialchars($_POST['salary'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                            <textarea class="form-control" id="notes" name="notes" rows="4"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label"><?php echo __('status'); ?></label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo (($_POST['status'] ?? 'active') === 'active') ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                                <option value="inactive" <?php echo (($_POST['status'] ?? '') === 'inactive') ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                                <option value="suspended" <?php echo (($_POST['status'] ?? '') === 'suspended') ? 'selected' : ''; ?>><?php echo __('suspended'); ?></option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="class_ids" class="form-label"><?php echo __('class'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="class_ids" name="class_ids[]" multiple required>
                                <?php if ($classes) while ($class = $classes->fetch_assoc()): ?>
                                    <option value="<?php echo $class['id']; ?>" <?php echo (isset($_POST['class_ids']) && in_array($class['id'], (array)$_POST['class_ids'])) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['grade_level']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من فصل بالضغط على Ctrl أو Shift</div>
                        </div>
                    </div>
                </div>
                <!-- Form Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('save'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
function togglePassword(id) {
    var x = document.getElementById(id);
    if (x.type === "password") {
        x.type = "text";
    } else {
        x.type = "password";
    }
}
</script>
<?php require_once '../includes/footer.php'; ?> 