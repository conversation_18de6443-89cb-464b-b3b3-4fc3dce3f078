<?php
/**
 * إدارة فئات المصروفات - بدون نوافذ منبثقة
 * Expense Categories Management - No Modals
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة إضافة فئة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    $category_name = clean_input($_POST['category_name'] ?? '');
    $description = clean_input($_POST['description'] ?? '');
    $icon = clean_input($_POST['icon'] ?? 'fas fa-money-bill');
    $color = clean_input($_POST['color'] ?? '#007bff');
    $daily_limit = floatval($_POST['daily_limit'] ?? 0);
    $monthly_limit = floatval($_POST['monthly_limit'] ?? 0);
    
    if (empty($category_name)) {
        $error_message = 'يرجى إدخال اسم الفئة';
    } else {
        $insert_stmt = $conn->prepare("
            INSERT INTO expense_categories 
            (category_name, description, icon, color, daily_limit, monthly_limit, created_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $user_id = $_SESSION['user_id'];
        $insert_stmt->bind_param("ssssddi", 
            $category_name, $description, $icon, $color, 
            $daily_limit, $monthly_limit, $user_id
        );
        
        if ($insert_stmt->execute()) {
            $success_message = 'تم إضافة الفئة بنجاح';
        } else {
            $error_message = 'فشل في إضافة الفئة';
        }
    }
}

// معالجة تبديل الحالة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'toggle_status') {
    $category_id = intval($_POST['category_id'] ?? 0);
    $new_status = intval($_POST['new_status'] ?? 0);
    
    $update_stmt = $conn->prepare("UPDATE expense_categories SET is_active = ? WHERE id = ?");
    $update_stmt->bind_param("ii", $new_status, $category_id);
    
    if ($update_stmt->execute()) {
        $success_message = 'تم تحديث حالة الفئة بنجاح';
    } else {
        $error_message = 'فشل في تحديث حالة الفئة';
    }
}

// جلب الفئات
$categories_query = "
    SELECT 
        ec.*,
        COUNT(de.id) as expenses_count,
        COALESCE(SUM(de.amount), 0) as total_amount
    FROM expense_categories ec
    LEFT JOIN daily_expenses de ON ec.id = de.category_id AND de.status != 'rejected'
    GROUP BY ec.id
    ORDER BY ec.category_name
";
$categories_result = $conn->query($categories_query);

$page_title = 'إدارة فئات المصروفات';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>إدارة فئات المصروفات
            </h1>
            <p class="text-muted mb-0">إنشاء وإدارة فئات المصروفات المختلفة</p>
        </div>
        <div>
            <a href="add_category.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة فئة جديدة
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للمصروفات
            </a>
        </div>
    </div>

    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- قائمة الفئات -->
    <div class="row">
        <?php while ($category = $categories_result->fetch_assoc()): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center" 
                         style="background-color: <?php echo $category['color']; ?>; color: white;">
                        <div class="d-flex align-items-center">
                            <i class="<?php echo $category['icon']; ?> me-2"></i>
                            <h6 class="mb-0"><?php echo htmlspecialchars($category['category_name']); ?></h6>
                        </div>
                        <div class="form-check form-switch">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_status">
                                <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                <input type="hidden" name="new_status" value="<?php echo $category['is_active'] ? 0 : 1; ?>">
                                <input class="form-check-input" type="checkbox" 
                                       <?php echo $category['is_active'] ? 'checked' : ''; ?>
                                       onchange="this.form.submit()">
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="card-text text-muted small">
                            <?php echo htmlspecialchars($category['description'] ?: 'لا يوجد وصف'); ?>
                        </p>
                        
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <h5 class="text-primary mb-0"><?php echo $category['expenses_count']; ?></h5>
                                <small class="text-muted">مصروف</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-success mb-0"><?php echo format_currency($category['total_amount']); ?></h5>
                                <small class="text-muted">إجمالي</small>
                            </div>
                        </div>

                        <?php if ($category['daily_limit'] || $category['monthly_limit']): ?>
                            <div class="alert alert-info py-2">
                                <small>
                                    <?php if ($category['daily_limit']): ?>
                                        <i class="fas fa-calendar-day me-1"></i>يومي: <?php echo format_currency($category['daily_limit']); ?>
                                    <?php endif; ?>
                                    <?php if ($category['monthly_limit']): ?>
                                        <br><i class="fas fa-calendar-alt me-1"></i>شهري: <?php echo format_currency($category['monthly_limit']); ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <div class="d-grid gap-2">
                            <div class="btn-group" role="group">
                                <a href="edit_category.php?id=<?php echo $category['id']; ?>" 
                                   class="btn btn-outline-primary btn-sm" title="تعديل الفئة">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="index.php?category=<?php echo $category['id']; ?>"
                                   class="btn btn-outline-info btn-sm" title="عرض مصروفات هذه الفئة">
                                    <i class="fas fa-eye"></i> عرض المصروفات
                                </a>
                                <a href="delete_category.php?id=<?php echo $category['id']; ?>" 
                                   class="btn btn-outline-danger btn-sm" title="حذف الفئة"
                                   <?php echo ($category['expenses_count'] > 0) ? 'onclick="return false;" style="opacity: 0.5; cursor: not-allowed;"' : 'onclick="return confirm(\'هل أنت متأكد من حذف هذه الفئة؟\')"'; ?>>
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                            <?php if ($category['expenses_count'] > 0): ?>
                                <small class="text-muted text-center">
                                    <i class="fas fa-info-circle"></i> لا يمكن الحذف (<?php echo $category['expenses_count']; ?> مصروف مرتبط)
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<style>
.card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-header {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.btn-group .btn {
    flex: 1;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
        border-radius: 0.375rem !important;
    }
}
</style>
