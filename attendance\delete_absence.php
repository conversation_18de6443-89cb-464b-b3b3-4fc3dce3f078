<?php
/**
 * حذف الغياب بالخصم
 * Delete Absence with Deduction
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

// التحقق من وجود معرف السجل
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: manage_absences.php');
    exit();
}

$absence_id = intval($_GET['id']);

// جلب بيانات السجل
$stmt = $conn->prepare("
    SELECT 
        sad.id,
        sad.user_id,
        sad.absence_date,
        sad.reason,
        sad.deduction_amount,
        sad.deduction_type,
        u.full_name,
        u.role
    FROM staff_absences_with_deduction sad
    JOIN users u ON sad.user_id = u.id
    WHERE sad.id = ?
");

$stmt->bind_param("i", $absence_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: manage_absences.php');
    exit();
}

$absence = $result->fetch_assoc();

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    try {
        $conn->begin_transaction();
        
        // حذف سجل الحضور المرتبط من الجدول الموحد
        $delete_staff_attendance = $conn->prepare("
            DELETE FROM staff_attendance
            WHERE user_id = ? AND attendance_date = ? AND status = 'absent_with_deduction'
        ");
        $delete_staff_attendance->bind_param("is", $absence['user_id'], $absence['absence_date']);
        $delete_staff_attendance->execute();

        // تسجيل عدد السجلات المحذوفة للتأكد
        $deleted_attendance_rows = $delete_staff_attendance->affected_rows;
        error_log("Deleted $deleted_attendance_rows attendance record(s) for user {$absence['user_id']} on {$absence['absence_date']}");
        
        // حذف سجل الغياب بالخصم
        $delete_absence = $conn->prepare("DELETE FROM staff_absences_with_deduction WHERE id = ?");
        $delete_absence->bind_param("i", $absence_id);
        
        if ($delete_absence->execute()) {
            $conn->commit();
            $success_message = 'تم حذف سجل الغياب بالخصم بنجاح';
            
            // إعادة توجيه بعد 3 ثوان
            header("refresh:3;url=manage_absences.php");
        } else {
            throw new Exception('فشل في حذف السجل');
        }
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = 'خطأ في الحذف: ' . $e->getMessage();
    }
}

// تضمين الهيدر بعد معالجة جميع عمليات إعادة التوجيه
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-trash text-danger me-2"></i>
                    حذف الغياب بالخصم
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="manage_absences.php">إدارة الغياب</a></li>
                        <li class="breadcrumb-item active">حذف</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <?php if (!empty($success_message)): ?>
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            تم الحذف بنجاح
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                        </div>
                        <div class="text-center mt-3">
                            <div class="spinner-border text-success me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span>سيتم إعادة توجيهك إلى قائمة الغياب خلال 3 ثوان...</span>
                        </div>
                        <div class="text-center mt-3">
                            <a href="manage_absences.php" class="btn btn-success">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة الآن
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تأكيد الحذف
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    تفاصيل السجل المراد حذفه:
                                </h6>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-4"><strong>الموظف:</strong></div>
                                            <div class="col-sm-8"><?php echo htmlspecialchars($absence['full_name']); ?></div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-4"><strong>الدور:</strong></div>
                                            <div class="col-sm-8"><?php echo $absence['role'] === 'teacher' ? 'معلم' : 'إداري'; ?></div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-4"><strong>تاريخ الغياب:</strong></div>
                                            <div class="col-sm-8"><?php echo date('Y-m-d', strtotime($absence['absence_date'])); ?></div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-4"><strong>مبلغ الخصم:</strong></div>
                                            <div class="col-sm-8 text-danger"><strong><?php echo number_format($absence['deduction_amount'], 2); ?> ج.م</strong></div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-4"><strong>نوع الخصم:</strong></div>
                                            <div class="col-sm-8">
                                                <?php 
                                                $deduction_types = [
                                                    'daily_wage' => 'خصم يومي كامل',
                                                    'hourly_wage' => 'خصم بالساعة',
                                                    'fixed_amount' => 'مبلغ ثابت'
                                                ];
                                                echo $deduction_types[$absence['deduction_type']] ?? $absence['deduction_type'];
                                                ?>
                                            </div>
                                        </div>
                                        <?php if (!empty($absence['reason'])): ?>
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-4"><strong>السبب:</strong></div>
                                            <div class="col-sm-8"><?php echo htmlspecialchars($absence['reason']); ?></div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-danger mb-3">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    ما سيتم حذفه:
                                </h6>
                                <div class="list-group">
                                    <div class="list-group-item">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        <strong>سجل الغياب بالخصم</strong>
                                        <br><small class="text-muted">من جدول staff_absences_with_deduction</small>
                                    </div>
                                    <div class="list-group-item">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        <strong>سجل الحضور المرتبط</strong>
                                        <br><small class="text-muted">من الجدول الموحد staff_attendance</small>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    <strong>نصيحة:</strong> إذا كنت تريد تعديل البيانات بدلاً من الحذف، يمكنك 
                                    <a href="edit_absence.php?id=<?php echo $absence_id; ?>" class="alert-link">الضغط هنا للتعديل</a>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center">
                            <a href="manage_absences.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                إلغاء والعودة
                            </a>
                            
                            <form method="POST" class="d-inline">
                                <button type="submit" name="confirm_delete" class="btn btn-danger" 
                                        onclick="return confirm('هل أنت متأكد تماماً من حذف هذا السجل؟\n\nلن تتمكن من استرداده بعد الحذف!')">
                                    <i class="fas fa-trash me-2"></i>
                                    تأكيد الحذف نهائياً
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
