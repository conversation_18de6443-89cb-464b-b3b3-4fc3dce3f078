# ===================================
# School Management System Environment Configuration
# نظام إدارة المدارس - إعدادات البيئة
# ===================================

# Application Settings
APP_NAME="School Management System"
APP_VERSION="2.0.0"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com
APP_TIMEZONE=Asia/Riyadh

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=school_management
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci

# Security Settings
APP_KEY=base64:your-32-character-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_SECURE=true
SESSION_SAME_SITE=strict

# CSRF Protection
CSRF_TOKEN_EXPIRE=3600

# Password Security
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# Login Security
LOGIN_MAX_ATTEMPTS=5
LOGIN_LOCKOUT_TIME=900

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="School Management System"

# File Upload Settings
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt
PROFILE_MAX_SIZE=2097152
DOCUMENT_MAX_SIZE=10485760

# Cache Configuration
CACHE_DRIVER=file
CACHE_PREFIX=sms_
CACHE_LIFETIME=3600

# Session Configuration
SESSION_DRIVER=file
SESSION_COOKIE_NAME=sms_session
SESSION_COOKIE_PATH=/
SESSION_COOKIE_DOMAIN=
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true

# Logging Configuration
LOG_CHANNEL=daily
LOG_LEVEL=info
LOG_MAX_FILES=10
LOG_MAX_SIZE=10485760

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION=true
BACKUP_INCLUDE_FILES=true
BACKUP_STORAGE=local

# Notification Settings
NOTIFICATIONS_ENABLED=true
EMAIL_NOTIFICATIONS=true
SMS_NOTIFICATIONS=false
PUSH_NOTIFICATIONS=false
NOTIFICATION_CLEANUP_DAYS=90

# API Configuration
API_ENABLED=true
API_VERSION=v1
API_RATE_LIMIT=100
API_TOKEN_EXPIRE=86400

# Academic Settings
ACADEMIC_YEAR_START_MONTH=9
ACADEMIC_YEAR_END_MONTH=6
DEFAULT_SEMESTER=first
SEMESTERS=first,second,summer

# Grading System
PASSING_GRADE=60
HONOR_ROLL_GRADE=85
DEAN_LIST_GRADE=90

# Attendance Settings
ATTENDANCE_REQUIRED_PERCENTAGE=75
ATTENDANCE_WARNING_PERCENTAGE=80
LATE_ARRIVAL_MINUTES=15
ABSENCE_EXCUSE_DAYS=3

# Financial Settings
DEFAULT_CURRENCY=SAR
CURRENCY_SYMBOL=ر.س
CURRENCY_POSITION=after
DECIMAL_PLACES=2
THOUSANDS_SEPARATOR=,
DECIMAL_SEPARATOR=.

LATE_FEE_PERCENTAGE=5
LATE_FEE_GRACE_DAYS=7
INSTALLMENT_FREQUENCIES=monthly,quarterly,semester,annual
PAYMENT_METHODS=cash,bank_transfer,check,card,online

# Language Settings
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
RTL_LANGUAGES=ar

# Theme Settings
DEFAULT_THEME=light
AVAILABLE_THEMES=light,dark

# System Limits
MAX_STUDENTS_PER_CLASS=35
MAX_SUBJECTS_PER_TEACHER=10
MAX_CLASSES_PER_TEACHER=8
MAX_EXAM_DURATION_MINUTES=180
MAX_QUESTION_LENGTH=1000
MAX_ANSWER_LENGTH=500

# Pagination
ITEMS_PER_PAGE=20
MAX_ITEMS_PER_PAGE=100
PAGINATION_LINKS=5

# External Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
GOOGLE_ANALYTICS_ID=your-google-analytics-id
RECAPTCHA_SITE_KEY=your-recaptcha-site-key
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key

# SMS Service (Optional)
SMS_PROVIDER=twilio
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret
SMS_FROM_NUMBER=+**********

# Cloud Storage (Optional)
CLOUD_STORAGE_ENABLED=false
CLOUD_STORAGE_PROVIDER=aws
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-s3-bucket

# Redis Configuration (Optional)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DATABASE=0

# Memcached Configuration (Optional)
MEMCACHED_HOST=127.0.0.1
MEMCACHED_PORT=11211

# LDAP Configuration (Optional)
LDAP_ENABLED=false
LDAP_HOST=ldap.your-domain.com
LDAP_PORT=389
LDAP_BASE_DN=dc=your-domain,dc=com
LDAP_USERNAME=cn=admin,dc=your-domain,dc=com
LDAP_PASSWORD=your-ldap-password

# Social Login (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Monitoring and Analytics
MONITORING_ENABLED=false
ANALYTICS_ENABLED=false
ERROR_REPORTING_ENABLED=true
PERFORMANCE_MONITORING=false

# Development Settings (Only for development environment)
# APP_ENV=development
# APP_DEBUG=true
# LOG_LEVEL=debug
# CACHE_DRIVER=array
# SESSION_DRIVER=array

# Testing Settings (Only for testing environment)
# APP_ENV=testing
# DB_DATABASE=school_management_test
# CACHE_DRIVER=array
# SESSION_DRIVER=array
# MAIL_MAILER=array

# ===================================
# IMPORTANT SECURITY NOTES:
# ===================================
# 1. Never commit this file with real credentials to version control
# 2. Copy this file to .env and fill in your actual values
# 3. Make sure .env is in your .gitignore file
# 4. Use strong, unique passwords and keys
# 5. Enable HTTPS in production
# 6. Regularly rotate your secrets and keys
# 7. Use environment-specific configurations
# ===================================
