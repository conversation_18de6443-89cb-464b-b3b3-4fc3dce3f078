<?php
/**
 * صفحة عرض تفاصيل المعلم
 * Teacher Details View Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// دالة مساعدة لتجنب مشكلة htmlspecialchars مع null
function safe_html($value, $default = '-') {
    return htmlspecialchars($value ?? $default);
}

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    redirect_to('../dashboard/');
}

// الحصول على معرف المعلم
$teacher_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($teacher_id <= 0) {
    $_SESSION['error_message'] = 'معرف المعلم غير صحيح';
    redirect_to('index.php');
}

// جلب بيانات المعلم
$teacher_query = "
    SELECT
        t.*,
        u.username,
        u.email,
        u.status as user_status,
        u.last_login,
        u.created_at as user_created_at,
        COALESCE(u.full_name, u.username) as full_name,
        COUNT(DISTINCT ta.subject_id) as subjects_count,
        COUNT(DISTINCT c.id) as classes_count
    FROM teachers t
    INNER JOIN users u ON t.user_id = u.id
    LEFT JOIN teacher_assignments ta ON t.id = ta.teacher_id
    LEFT JOIN classes c ON t.id = c.teacher_id
    WHERE t.id = ?
    GROUP BY t.id
";

$stmt = $conn->prepare($teacher_query);
if (!$stmt) {
    $_SESSION['error_message'] = 'خطأ في قاعدة البيانات';
    redirect_to('index.php');
}

$stmt->bind_param('i', $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$stmt->close();

if (!$teacher) {
    $_SESSION['error_message'] = 'المعلم غير موجود';
    redirect_to('index.php');
}

// جلب المواد المُدرسة
$subjects_query = "
    SELECT 
        s.subject_name,
        s.subject_code,
        s.credit_hours,
        ta.assigned_at,
        g.grade_name
    FROM teacher_assignments ta
    INNER JOIN subjects s ON ta.subject_id = s.id
    LEFT JOIN grades g ON s.grade_id = g.id
    WHERE ta.teacher_id = ?
    ORDER BY s.subject_name
";

$stmt = $conn->prepare($subjects_query);
$subjects = [];
if ($stmt) {
    $stmt->bind_param('i', $teacher_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $subjects[] = $row;
    }
    $stmt->close();
}

// جلب الفصول المُدرسة
$classes_query = "
    SELECT
        c.class_name,
        c.grade_level,
        c.capacity,
        g.grade_name,
        COUNT(s.id) as students_count
    FROM classes c
    LEFT JOIN grades g ON c.grade_id = g.id
    LEFT JOIN students s ON c.id = s.class_id
    WHERE c.teacher_id = ?
    GROUP BY c.id
    ORDER BY c.class_name
";

$stmt = $conn->prepare($classes_query);
$classes = [];
if ($stmt) {
    $stmt->bind_param('i', $teacher_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $classes[] = $row;
    }
    $stmt->close();
}

$page_title = 'تفاصيل المعلم: ' . ($teacher['full_name'] ?? $teacher['username']);
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-tie text-primary me-2"></i>
                تفاصيل المعلم
            </h2>
            <p class="text-muted mb-0"><?php echo htmlspecialchars($teacher['full_name'] ?? $teacher['username']); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للمعلمين
            </a>
            <a href="edit.php?id=<?php echo $teacher_id; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>تعديل البيانات
            </a>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المعلم الأساسية -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-lg mx-auto mb-3">
                            <i class="fas fa-user-tie fa-4x text-primary"></i>
                        </div>
                        <h5 class="mb-1"><?php echo htmlspecialchars($teacher['full_name'] ?? $teacher['username']); ?></h5>
                        <p class="text-muted mb-0"><?php echo htmlspecialchars($teacher['employee_id'] ?? '-'); ?></p>
                    </div>
                    
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="fw-bold text-muted">رقم الهوية:</td>
                            <td><?php echo htmlspecialchars($teacher['national_id'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">الهاتف:</td>
                            <td><?php echo htmlspecialchars($teacher['phone'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">البريد الإلكتروني:</td>
                            <td><?php echo htmlspecialchars($teacher['email'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">التخصص:</td>
                            <td><?php echo htmlspecialchars($teacher['specialization'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">القسم:</td>
                            <td><?php echo htmlspecialchars($teacher['department'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">تاريخ التوظيف:</td>
                            <td><?php echo $teacher['hire_date'] ? date('Y-m-d', strtotime($teacher['hire_date'])) : '-'; ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">الراتب:</td>
                            <td><?php echo $teacher['salary'] ? number_format($teacher['salary'], 2) . ' ريال' : '-'; ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">الحالة:</td>
                            <td>
                                <?php if ($teacher['status'] === 'active'): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- الإحصائيات والمعلومات الإضافية -->
        <div class="col-lg-8">
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-book fa-2x mb-2"></i>
                            <h4 class="mb-0"><?php echo $teacher['subjects_count']; ?></h4>
                            <small>المواد المُدرسة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h4 class="mb-0"><?php echo $teacher['classes_count']; ?></h4>
                            <small>الفصول المُدرسة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar fa-2x mb-2"></i>
                            <h4 class="mb-0">
                                <?php 
                                $years = floor((time() - strtotime($teacher['hire_date'])) / (365*24*60*60));
                                echo $years;
                                ?>
                            </h4>
                            <small>سنوات الخبرة</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المواد المُدرسة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>المواد المُدرسة
                        <span class="badge bg-primary"><?php echo count($subjects); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($subjects)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مواد مُدرسة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المادة</th>
                                        <th>رمز المادة</th>
                                        <th>الصف</th>
                                        <th>الساعات</th>
                                        <th>تاريخ التكليف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subjects as $subject): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($subject['subject_name'] ?? '-'); ?></td>
                                            <td><code><?php echo htmlspecialchars($subject['subject_code'] ?? '-'); ?></code></td>
                                            <td><?php echo htmlspecialchars($subject['grade_name'] ?? '-'); ?></td>
                                            <td><?php echo $subject['credit_hours']; ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($subject['assigned_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- الفصول المُدرسة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>الفصول المُدرسة
                        <span class="badge bg-success"><?php echo count($classes); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($classes)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد فصول مُدرسة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الفصل</th>
                                        <th>المستوى</th>
                                        <th>الصف</th>
                                        <th>عدد الطلاب</th>
                                        <th>السعة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($classes as $class): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($class['class_name'] ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($class['grade_level'] ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($class['grade_name'] ?? '-'); ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $class['students_count']; ?></span>
                                            </td>
                                            <td><?php echo $class['capacity']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
