<?php
/**
 * نموذج طلب الإجازة
 * Leave Request Form
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$leave_type = $_GET['type'] ?? 'regular';

// تعريف أسماء أنواع الإجازات
$leave_type_names = [
    'sick' => 'إجازة مرضية',
    'regular' => 'إجازة اعتيادية',
    'emergency' => 'إجازة طارئة'
];

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $staff_id = intval($_POST['staff_id']);
        $leave_type = clean_input($_POST['leave_type']);
        $start_date = clean_input($_POST['start_date']);
        $end_date = clean_input($_POST['end_date']);
        $reason = clean_input($_POST['reason']);

        if (empty($staff_id) || empty($leave_type) || empty($start_date) || empty($end_date)) {
            $error_message = 'جميع الحقول مطلوبة';
        } elseif ($leave_type === 'sick' && empty($reason)) {
            $error_message = 'يجب تقديم تفاصيل الحالة المرضية للإجازة المرضية';
        } elseif ($start_date > $end_date) {
            $error_message = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
        } else {
            try {
                // حساب عدد الأيام
                $start = new DateTime($start_date);
                $end = new DateTime($end_date);
                $interval = $start->diff($end);
                $total_days = $interval->days + 1;

                // إدراج طلب الإجازة (مفعل مباشرة)
                $stmt = $conn->prepare("
                    INSERT INTO staff_leaves (user_id, leave_type, start_date, end_date, total_days, reason, status, applied_by, approved_by, approved_at, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 'approved', ?, ?, NOW(), NOW())
                ");
                $stmt->bind_param("isssssii", $staff_id, $leave_type, $start_date, $end_date, $total_days, $reason, $user_id, $user_id);
                
                if ($stmt->execute()) {
                    $success_message = 'تم تسجيل ' . ($leave_type_names[$leave_type] ?? 'الإجازة') . ' بنجاح وتم تفعيلها مباشرة';

                    // تسجيل النشاط
                    log_activity($user_id, 'submit_leave_request', 'staff_leaves', $conn->insert_id);
                } else {
                    $error_message = 'حدث خطأ أثناء إرسال الطلب';
                }
            } catch (Exception $e) {
                $error_message = __('error_occurred') . ': ' . $e->getMessage();
            }
        }
    }
}

// جلب قائمة الموظفين (باستثناء مديري النظام)
$staff_query = "
    SELECT u.id, u.username, u.full_name, u.role
    FROM users u
    WHERE u.role IN ('teacher', 'staff')
    AND u.role NOT IN ('admin', 'system_admin')
    AND u.status = 'active'
    ORDER BY u.full_name
";
$staff_result = $conn->query($staff_query);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-<?php echo $leave_type === 'sick' ? 'user-injured' : ($leave_type === 'regular' ? 'calendar-times' : 'exclamation-triangle'); ?> me-2"></i>
                        نموذج طلب <?php echo $leave_type_names[$leave_type] ?? 'الإجازة'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <!-- معلومات مفيدة بناءً على نوع الإجازة -->
                    <?php if ($leave_type === 'sick'): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> للإجازة المرضية، يرجى تقديم تفاصيل واضحة عن الحالة المرضية. سيتم تفعيل الإجازة مباشرة عند التسجيل.
                        </div>
                    <?php elseif ($leave_type === 'regular'): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم تفعيل الإجازة الاعتيادية مباشرة عند التسجيل دون الحاجة لانتظار الموافقة.
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم تفعيل الإجازة مباشرة عند التسجيل.
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="staff_id" class="form-label">الموظف *</label>
                                    <select class="form-select" id="staff_id" name="staff_id" required>
                                        <option value="">اختر الموظف</option>
                                        <?php while ($staff = $staff_result->fetch_assoc()): ?>
                                            <option value="<?php echo $staff['id']; ?>">
                                                <?php echo htmlspecialchars($staff['full_name'] . ' (' . $staff['role'] . ')'); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="leave_type" class="form-label">نوع الإجازة *</label>
                                    <?php $selected_leave_name = $leave_type_names[$leave_type] ?? 'إجازة اعتيادية'; ?>
                                    <div class="form-control-plaintext bg-light p-2 rounded border">
                                        <i class="fas fa-<?php echo $leave_type === 'sick' ? 'user-injured text-danger' : ($leave_type === 'regular' ? 'calendar-times text-primary' : 'exclamation-triangle text-warning'); ?> me-2"></i>
                                        <strong><?php echo $selected_leave_name; ?></strong>
                                    </div>
                                    <input type="hidden" id="leave_type" name="leave_type" value="<?php echo htmlspecialchars($leave_type); ?>">
                                    <small class="text-muted">نوع الإجازة محدد بناءً على اختيارك السابق</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية *</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">تاريخ النهاية *</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">
                                <?php echo $leave_type === 'sick' ? 'تفاصيل الحالة المرضية' : 'سبب الإجازة'; ?>
                                <?php echo $leave_type === 'sick' ? ' *' : ''; ?>
                            </label>
                            <textarea class="form-control" id="reason" name="reason" rows="3"
                                      placeholder="<?php
                                      echo $leave_type === 'sick' ? 'اكتب تفاصيل الحالة المرضية والأعراض...' :
                                           ($leave_type === 'regular' ? 'اكتب سبب طلب الإجازة الاعتيادية...' :
                                            'اكتب سبب الإجازة...');
                                      ?>"
                                      <?php echo $leave_type === 'sick' ? 'required' : ''; ?>></textarea>
                            <?php if ($leave_type === 'sick'): ?>
                                <small class="text-muted">يرجى تقديم تفاصيل واضحة عن الحالة المرضية</small>
                            <?php endif; ?>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="smart_attendance.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>رجوع
                            </a>
                            <button type="submit" class="btn btn-<?php echo $leave_type === 'sick' ? 'danger' : 'primary'; ?>">
                                <i class="fas fa-save me-2"></i>تسجيل <?php echo $leave_type_names[$leave_type] ?? 'الإجازة'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const leaveType = '<?php echo $leave_type; ?>';

    // التحقق من صحة النموذج
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            // تحقق إضافي للإجازة المرضية
            if (leaveType === 'sick') {
                const reason = document.getElementById('reason');
                if (!reason.value.trim()) {
                    event.preventDefault();
                    event.stopPropagation();
                    reason.setCustomValidity('يجب تقديم تفاصيل الحالة المرضية');
                } else {
                    reason.setCustomValidity('');
                }
            }

            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // التحقق من تواريخ الإجازة
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');

    startDate.addEventListener('change', function() {
        endDate.min = this.value;
        if (endDate.value && endDate.value < this.value) {
            endDate.value = this.value;
        }
    });

    // تحسينات خاصة بنوع الإجازة
    if (leaveType === 'sick') {
        // للإجازة المرضية: تعيين تاريخ البداية لليوم الحالي افتراضياً
        if (!startDate.value) {
            startDate.value = new Date().toISOString().split('T')[0];
            startDate.dispatchEvent(new Event('change'));
        }
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
