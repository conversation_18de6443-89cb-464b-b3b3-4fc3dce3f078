<?php
/**
 * صفحة عرض المراحل الدراسية
 * Educational Stages Management Page
 */

require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('educational_stages');

// معالجة البحث والفلترة
$selected_stage = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'sort_order';
$sort_order = isset($_GET['order']) && $_GET['order'] === 'desc' ? 'DESC' : 'ASC';

// جلب جميع المراحل للقائمة المنسدلة
$all_stages_query = "SELECT id, stage_name, stage_code FROM educational_stages ORDER BY sort_order";
$all_stages_result = $conn->query($all_stages_query);
$all_stages = [];
if ($all_stages_result) {
    while ($row = $all_stages_result->fetch_assoc()) {
        $all_stages[] = $row;
    }
}

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if ($selected_stage > 0) {
    $where_conditions[] = "es.id = ?";
    $params[] = $selected_stage;
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد السجلات
$count_query = "SELECT COUNT(*) as total FROM educational_stages es $where_clause";
$stmt = $conn->prepare($count_query);
if ($stmt) {
    if (!empty($params)) {
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
    }
    $stmt->execute();
    $total_records = $stmt->get_result()->fetch_assoc()['total'];
    $stmt->close();
} else {
    $total_records = 0;
}

// إعدادات الصفحات
$records_per_page = 10;
$total_pages = ceil($total_records / $records_per_page);
$current_page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($current_page - 1) * $records_per_page;

// جلب المراحل الدراسية
$query = "
    SELECT 
        es.*,
        COUNT(DISTINCT c.id) as classes_count,
        COUNT(DISTINCT s.id) as subjects_count
    FROM educational_stages es
    LEFT JOIN classes c ON es.id = c.stage_id AND c.status = 'active'
    LEFT JOIN subjects s ON es.id = s.stage_id AND s.status = 'active'
    $where_clause
    GROUP BY es.id
    ORDER BY $sort_by $sort_order
    LIMIT ? OFFSET ?
";

$stages = [];
$stmt = $conn->prepare($query);
if ($stmt) {
    $all_params = array_merge($params, [$records_per_page, $offset]);
    $types = str_repeat('s', count($params)) . 'ii';
    $stmt->bind_param($types, ...$all_params);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $stages[] = $row;
    }
    $stmt->close();
}

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_stages,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_stages,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_stages
    FROM educational_stages
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result ? $stats_result->fetch_assoc() : ['total_stages' => 0, 'active_stages' => 0, 'inactive_stages' => 0];
?>

<div class="container-fluid">
    <!-- Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-layer-group text-primary me-2"></i>
                <?php echo __('educational_stages'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('manage_educational_stages'); ?></p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('add_stage'); ?>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['total_stages']; ?></h4>
                            <p class="mb-0"><?php echo __('total_stages'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['active_stages']; ?></h4>
                            <p class="mb-0"><?php echo __('active_stages'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['inactive_stages']; ?></h4>
                            <p class="mb-0"><?php echo __('inactive_stages'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo array_sum(array_column($stages, 'classes_count')); ?></h4>
                            <p class="mb-0"><?php echo __('total_classes'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-school fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i><?php echo __('search_and_filter'); ?>
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3" id="searchForm">
                <div class="col-md-4">
                    <label for="stage_id" class="form-label">
                        <i class="fas fa-layer-group me-1"></i><?php echo __('select_stage'); ?>
                    </label>
                    <select class="form-select" id="stage_id" name="stage_id" onchange="handleStageSelection()">
                        <option value=""><?php echo __('all_stages'); ?></option>
                        <?php foreach ($all_stages as $stage): ?>
                            <option value="<?php echo $stage['id']; ?>"
                                    <?php echo $selected_stage == $stage['id'] ? 'selected' : ''; ?>
                                    data-stage-name="<?php echo htmlspecialchars($stage['stage_name']); ?>"
                                    data-stage-code="<?php echo htmlspecialchars($stage['stage_code']); ?>">
                                <?php echo htmlspecialchars($stage['stage_name']); ?>
                                (<?php echo htmlspecialchars($stage['stage_code']); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div id="stageInfo" class="form-text" style="display: none;">
                        <i class="fas fa-info-circle me-1"></i>
                        <span id="stageInfoText"></span>
                    </div>
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label">
                        <i class="fas fa-toggle-on me-1"></i><?php echo __('status'); ?>
                    </label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>
                            <?php echo __('active'); ?>
                        </option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>
                            <?php echo __('inactive'); ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="sort" class="form-label">
                        <i class="fas fa-sort me-1"></i><?php echo __('sort_by'); ?>
                    </label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="sort_order" <?php echo $sort_by === 'sort_order' ? 'selected' : ''; ?>>
                            <?php echo __('sort_order'); ?>
                        </option>
                        <option value="stage_name" <?php echo $sort_by === 'stage_name' ? 'selected' : ''; ?>>
                            <?php echo __('stage_name'); ?>
                        </option>
                        <option value="created_at" <?php echo $sort_by === 'created_at' ? 'selected' : ''; ?>>
                            <?php echo __('created_at'); ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="order" class="form-label">
                        <i class="fas fa-sort-amount-down me-1"></i><?php echo __('order'); ?>
                    </label>
                    <select class="form-select" id="order" name="order">
                        <option value="asc" <?php echo $sort_order === 'ASC' ? 'selected' : ''; ?>>
                            <?php echo __('ascending'); ?>
                        </option>
                        <option value="desc" <?php echo $sort_order === 'DESC' ? 'selected' : ''; ?>>
                            <?php echo __('descending'); ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="searchBtn">
                            <i class="fas fa-search me-1"></i><?php echo __('search'); ?>
                        </button>

                        <button type="button" class="btn btn-outline-info btn-sm" id="viewStageBtn"
                                onclick="viewSelectedStage()" style="display: none;">
                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                        </button>

                        <?php if ($selected_stage > 0 || !empty($status_filter)): ?>
                            <a href="index.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Results Section -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <?php echo __('stages_list'); ?> 
                <span class="badge bg-secondary"><?php echo $total_records; ?></span>
            </h5>
            <?php if ($selected_stage > 0 || !empty($status_filter)): ?>
                <div class="d-flex align-items-center">
                    <?php if ($selected_stage > 0): ?>
                        <?php
                        $selected_stage_name = '';
                        foreach ($all_stages as $stage) {
                            if ($stage['id'] == $selected_stage) {
                                $selected_stage_name = $stage['stage_name'];
                                break;
                            }
                        }
                        ?>
                        <span class="badge bg-primary me-2">
                            <i class="fas fa-layer-group me-1"></i>
                            <?php echo htmlspecialchars($selected_stage_name); ?>
                        </span>
                    <?php endif; ?>

                    <?php if (!empty($status_filter)): ?>
                        <span class="badge bg-info me-2">
                            <i class="fas fa-toggle-on me-1"></i>
                            <?php echo __($status_filter); ?>
                        </span>
                    <?php endif; ?>

                    <a href="index.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
        <div class="card-body p-0">
            <?php if (empty($stages)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo __('no_stages_found'); ?></h5>
                    <p class="text-muted"><?php echo __('no_stages_message'); ?></p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_first_stage'); ?>
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th><?php echo __('sort_order'); ?></th>
                                <th><?php echo __('stage_name'); ?></th>
                                <th><?php echo __('stage_code'); ?></th>
                                <th><?php echo __('age_range'); ?></th>
                                <th><?php echo __('duration'); ?></th>
                                <th><?php echo __('classes'); ?></th>
                                <th><?php echo __('subjects'); ?></th>
                                <th><?php echo __('status'); ?></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($stages as $stage): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $stage['sort_order']; ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($stage['stage_name']); ?></strong>
                                            <?php if (!empty($stage['stage_name_en'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($stage['stage_name_en']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <code><?php echo htmlspecialchars($stage['stage_code']); ?></code>
                                    </td>
                                    <td>
                                        <?php if ($stage['min_age'] && $stage['max_age']): ?>
                                            <?php echo $stage['min_age']; ?> - <?php echo $stage['max_age']; ?> <?php echo __('years'); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo $stage['duration_years']; ?> <?php echo __('years'); ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $stage['classes_count']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning"><?php echo $stage['subjects_count']; ?></span>
                                    </td>
                                    <td>
                                        <?php if ($stage['status'] === 'active'): ?>
                                            <span class="badge bg-success"><?php echo __('active'); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="view.php?id=<?php echo $stage['id']; ?>" 
                                               class="btn btn-outline-info" title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $stage['id']; ?>" 
                                               class="btn btn-outline-primary" title="<?php echo __('edit'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="confirmDelete(<?php echo $stage['id']; ?>, '<?php echo htmlspecialchars($stage['stage_name'], ENT_QUOTES); ?>')"
                                                    title="<?php echo __('delete'); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="<?php echo __('pagination'); ?>">
                            <ul class="pagination justify-content-center mb-0">
                                <?php
                                $query_params = $_GET;
                                unset($query_params['page']);
                                $base_url = 'index.php?' . http_build_query($query_params);
                                $base_url .= empty($query_params) ? 'page=' : '&page=';
                                ?>
                                
                                <?php if ($current_page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo $base_url . ($current_page - 1); ?>">
                                            <?php echo __('previous'); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                                        <a class="page-link" href="<?php echo $base_url . $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($current_page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo $base_url . ($current_page + 1); ?>">
                                            <?php echo __('next'); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// التفاعل مع البحث والفلترة
function handleStageSelection() {
    const stageSelect = document.getElementById('stage_id');
    const searchBtn = document.getElementById('searchBtn');
    const viewStageBtn = document.getElementById('viewStageBtn');
    const stageInfo = document.getElementById('stageInfo');
    const stageInfoText = document.getElementById('stageInfoText');
    const selectedValue = stageSelect.value;

    // تحديث الواجهة حسب الاختيار
    if (selectedValue) {
        const selectedOption = stageSelect.options[stageSelect.selectedIndex];
        const selectedText = selectedOption.text;
        const stageName = selectedOption.getAttribute('data-stage-name');
        const stageCode = selectedOption.getAttribute('data-stage-code');

        // تحديث زر البحث
        searchBtn.innerHTML = '<i class="fas fa-filter me-1"></i>فلترة';
        searchBtn.title = 'فلترة النتائج للمرحلة: ' + selectedText;

        // إظهار زر عرض التفاصيل
        viewStageBtn.style.display = 'block';
        viewStageBtn.title = 'عرض تفاصيل: ' + selectedText;

        // إظهار معلومات المرحلة
        stageInfoText.innerHTML = `تم اختيار: <strong>${stageName}</strong> - رمز المرحلة: <code>${stageCode}</code>`;
        stageInfo.style.display = 'block';

        // إضافة تأثيرات بصرية
        stageSelect.classList.add('border-primary');
        searchBtn.classList.remove('btn-primary');
        searchBtn.classList.add('btn-info');
    } else {
        // إعادة تعيين الواجهة
        searchBtn.innerHTML = '<i class="fas fa-search me-1"></i><?php echo __('search'); ?>';
        searchBtn.title = '<?php echo __('search'); ?>';

        // إخفاء زر عرض التفاصيل ومعلومات المرحلة
        viewStageBtn.style.display = 'none';
        stageInfo.style.display = 'none';

        // إزالة التأثيرات البصرية
        stageSelect.classList.remove('border-primary');
        searchBtn.classList.remove('btn-info');
        searchBtn.classList.add('btn-primary');
    }
}

// عرض تفاصيل المرحلة المختارة
function viewSelectedStage() {
    const stageSelect = document.getElementById('stage_id');
    const selectedValue = stageSelect.value;

    if (selectedValue) {
        // إظهار رسالة تحميل
        Swal.fire({
            title: 'جاري التحميل...',
            text: 'يتم تحضير صفحة المرحلة',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // الانتقال لصفحة عرض المرحلة
        setTimeout(() => {
            window.location.href = 'view.php?id=' + selectedValue;
        }, 500);
    }
}

// تفاعل مع تغيير الفلاتر الأخرى
function handleFilterChange() {
    const statusSelect = document.getElementById('status');
    const sortSelect = document.getElementById('sort');
    const orderSelect = document.getElementById('order');

    // إضافة تأثيرات بصرية للفلاتر النشطة
    [statusSelect, sortSelect, orderSelect].forEach(select => {
        if (select.value && select.value !== select.options[0].value) {
            select.classList.add('border-info');
        } else {
            select.classList.remove('border-info');
        }
    });
}

// تشغيل البحث التلقائي عند تغيير المرحلة (اختياري)
function autoSearch() {
    const stageSelect = document.getElementById('stage_id');
    if (stageSelect.value) {
        // تأخير قصير لإظهار التأثير البصري
        setTimeout(() => {
            document.getElementById('searchForm').submit();
        }, 300);
    }
}

// إعداد الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الواجهة حسب القيم الحالية
    handleStageSelection();
    handleFilterChange();

    // إضافة مستمعي الأحداث
    document.getElementById('status').addEventListener('change', handleFilterChange);
    document.getElementById('sort').addEventListener('change', handleFilterChange);
    document.getElementById('order').addEventListener('change', handleFilterChange);

    // يمكن تفعيل البحث التلقائي بإلغاء التعليق عن السطر التالي
    // document.getElementById('stage_id').addEventListener('change', autoSearch);
});

// تحسين تجربة المستخدم مع النموذج
document.getElementById('searchForm').addEventListener('submit', function(e) {
    const searchBtn = document.getElementById('searchBtn');

    // إظهار حالة التحميل
    searchBtn.disabled = true;
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري البحث...';

    // السماح بإرسال النموذج
    return true;
});

function confirmDelete(stageId, stageName) {
    // استخدام SweetAlert للتأكيد
    Swal.fire({
        title: '<?php echo __('confirm_delete'); ?>',
        html: `
            <div class="text-center mb-3">
                <i class="fas fa-trash-alt fa-3x text-danger"></i>
            </div>
            <p><?php echo __('delete_stage_confirmation'); ?></p>
            <div class="alert alert-warning">
                <strong><?php echo __('stage_name'); ?>:</strong> ${stageName}
            </div>
            <p class="text-muted small"><?php echo __('delete_stage_warning'); ?></p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>',
        cancelButtonText: '<i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار رسالة تحميل
            Swal.fire({
                title: '<?php echo __('deleting'); ?>...',
                text: '<?php echo __('please_wait'); ?>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // الانتقال لصفحة الحذف
            window.location.href = 'delete.php?id=' + stageId;
        }
    });
}
</script>

<?php require_once '../includes/footer.php'; ?>
