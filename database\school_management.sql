-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- نظام إدارة المدارس الكامل والمحدث
-- Complete and Updated School Management System
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 29, 2025 at 12:00 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12
--
-- تم تحديث قاعدة البيانات لتشمل جميع الجداول المطلوبة
-- Updated database to include all required tables

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `school_management`
--

-- --------------------------------------------------------

--
-- Table structure for table `academic_years`
--

CREATE TABLE `academic_years` (
  `id` int(10) UNSIGNED NOT NULL,
  `year_name` varchar(20) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_current` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive','archived') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `academic_years`
--

INSERT INTO `academic_years` (`id`, `year_name`, `start_date`, `end_date`, `is_current`, `status`, `created_at`, `updated_at`) VALUES
(1, '2024-2025', '2024-09-01', '2025-06-30', 1, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(50) DEFAULT NULL,
  `record_id` int(10) UNSIGNED DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `session_id` varchar(128) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `activity_logs`
--

INSERT INTO `activity_logs` (`id`, `user_id`, `action`, `table_name`, `record_id`, `old_values`, `new_values`, `description`, `ip_address`, `user_agent`, `session_id`, `created_at`) VALUES
(2, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-15 07:36:35'),
(3, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 05:44:38'),
(4, 2, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 05:45:29'),
(5, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 05:45:39'),
(6, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:31:03'),
(7, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:33:51'),
(8, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:38:59'),
(9, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:41:06'),
(10, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:41:20'),
(11, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:41:29'),
(12, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:41:34'),
(13, 2, 'add_student', 'students', 5, NULL, '{\"student_name\":\"rafat sadek\",\"student_number\":5}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 08:57:19'),
(14, 2, 'add_student', 'students', 1, NULL, '{\"student_name\":\"mariam\",\"student_number\":\"101\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 09:35:21'),
(15, 2, 'edit_student', 'students', 1, '{\"old_name\":\"mariam\"}', '{\"new_name\":\"mariam\",\"class_id\":2}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 10:00:41'),
(16, 2, 'edit_student', 'students', 1, '{\"old_name\":\"mariam\"}', '{\"new_name\":\"mariam\",\"class_id\":2}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 10:03:27'),
(17, 2, 'edit_student', 'students', 1, '{\"old_name\":\"mariam\"}', '{\"new_name\":\"mariam\",\"class_id\":1}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 10:03:42'),
(18, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 11:31:54'),
(19, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 03:27:45'),
(20, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 03:56:01'),
(21, 2, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 04:30:11'),
(22, NULL, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-16 04:42:40'),
(23, 2, 'add_teacher', 'teachers', 1, NULL, '{\"teacher_name\":\"misho kamel\",\"employee_id\":\"200\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:22:59'),
(24, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":0,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:45:03'),
(25, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":0,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:45:26'),
(26, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":0,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:46:32'),
(27, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:48:07'),
(28, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:48:46'),
(29, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:49:05'),
(30, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:53:30'),
(31, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 07:56:38'),
(32, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 08:01:05'),
(33, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-16\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 08:01:19'),
(34, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 19:29:11'),
(35, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 19:29:27'),
(36, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-16 19:29:30'),
(38, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 08:15:35'),
(39, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 08:53:06'),
(40, 2, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 08:53:27'),
(41, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 08:58:06'),
(42, 2, 'edit_grade', 'grades', 1, '{\"grade_name\":\"\\u0627\\u0644\\u062a\\u0645\\u0647\\u064a\\u062f\\u064a \\u0627\\u0644\\u0623\\u0648\\u0644\",\"grade_code\":\"PRE1\",\"stage_id\":1,\"sort_order\":1}', '{\"grade_name\":\"\\u0627\\u0644\\u062a\\u0645\\u0647\\u064a\\u062f\\u064a \\u0627\\u0644\\u0623\\u0648\\u0644\",\"grade_code\":\"PRE1\",\"stage_id\":1,\"sort_order\":1}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 10:48:10'),
(43, 2, 'edit_grade', 'grades', 1, '{\"grade_name\":\"\\u0627\\u0644\\u062a\\u0645\\u0647\\u064a\\u062f\\u064a \\u0627\\u0644\\u0623\\u0648\\u0644\",\"grade_code\":\"PRE1\",\"stage_id\":1,\"sort_order\":1}', '{\"grade_name\":\"\\u0627\\u0644\\u062a\\u0645\\u0647\\u064a\\u062f\\u064a \\u0627\\u0644\\u0623\\u0648\\u0644\",\"grade_code\":\"PRE1\",\"stage_id\":1,\"sort_order\":1}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 10:54:39'),
(44, 2, 'delete_grade', 'grades', 1, '{\"grade_name\":\"\\u0627\\u0644\\u062a\\u0645\\u0647\\u064a\\u062f\\u064a \\u0627\\u0644\\u0623\\u0648\\u0644\",\"grade_code\":\"PRE1\",\"stage_id\":1}', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 11:00:07'),
(45, 2, 'delete_grade', 'grades', 6, '{\"grade_name\":\"\\u0627\\u0644\\u0635\\u0641 \\u0627\\u0644\\u0631\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0627\\u0628\\u062a\\u062f\\u0627\\u0626\\u064a\",\"grade_code\":\"G4\",\"stage_id\":2}', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 11:10:50'),
(46, 2, 'edit_stage', 'educational_stages', 1, '{\"stage_name\":\"\\u0631\\u064a\\u0627\\u0636 \\u0627\\u0644\\u0623\\u0637\\u0641\\u0627\\u0644\",\"stage_code\":\"KG\",\"sort_order\":1}', '{\"stage_name\":\"\\u0631\\u064a\\u0627\\u0636 \\u0627\\u0644\\u0623\\u0637\\u0641\\u0627\\u0644\",\"stage_code\":\"KG\",\"sort_order\":1}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 11:16:49'),
(47, 2, 'delete_stage', 'educational_stages', 1, '{\"stage_name\":\"\\u0631\\u064a\\u0627\\u0636 \\u0627\\u0644\\u0623\\u0637\\u0641\\u0627\\u0644\",\"stage_code\":\"KG\"}', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 11:30:35'),
(48, 2, 'delete_stage', 'educational_stages', 4, '{\"stage_name\":\"\\u0627\\u0644\\u0645\\u0631\\u062d\\u0644\\u0629 \\u0627\\u0644\\u062b\\u0627\\u0646\\u0648\\u064a\\u0629\",\"stage_code\":\"HIGH\"}', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 11:30:58'),
(49, 2, 'delete_stage', 'educational_stages', 3, '{\"stage_name\":\"\\u0627\\u0644\\u0645\\u0631\\u062d\\u0644\\u0629 \\u0627\\u0644\\u0645\\u062a\\u0648\\u0633\\u0637\\u0629\",\"stage_code\":\"MID\"}', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 11:32:12'),
(50, 2, 'add_subject', 'subjects', 15, NULL, '{\"subject_name\":\"\\u0627\\u0644\\u0631\\u064a\\u0627\\u0636\\u064a\\u0627\\u062a\",\"subject_code\":\"MATH101\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 11:48:36'),
(51, 2, 'delete_student', 'students', 1, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 14:04:20'),
(52, 2, 'add_student', 'students', 2, NULL, '{\"student_name\":\"\\u0645\\u062d\\u0645\\u0648\\u062f \\u0627\\u0644\\u062d\\u0627\\u062c \\u0627\\u0644\\u0633\\u064a\\u062f\",\"student_number\":\"20250001\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 14:05:47'),
(53, 2, 'delete_student', 'students', 2, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-17 14:11:39'),
(55, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 06:12:18'),
(56, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 06:13:25'),
(57, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 06:14:44'),
(58, 2, 'add_student', 'students', 3, NULL, '{\"student_name\":\"\\u0645\\u0627\\u0631\\u062a\\u0646\",\"student_number\":\"20250001\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 06:32:33'),
(59, 2, 'delete_user', 'users', 6, NULL, '{\"user_id\":6,\"delete_type\":\"user\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 06:33:17'),
(60, 2, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 07:45:33'),
(61, 15, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 08:04:12'),
(62, 15, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 08:06:28'),
(63, 15, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 08:06:33'),
(64, 15, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 08:06:55'),
(65, 15, 'register_staff_leave', 'staff_leaves', 0, NULL, '{\"staff_id\":12,\"leave_type\":\"sick\",\"start_date\":\"2025-07-20\",\"end_date\":\"2025-07-25\",\"total_days\":6}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 08:33:54'),
(66, 15, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-20\",\"new_records\":3,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 08:34:06'),
(67, 15, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-20\",\"new_records\":0,\"updated_records\":3}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 08:41:22'),
(68, 15, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:01:17'),
(69, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:02:40'),
(70, 2, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:03:12'),
(71, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:03:25'),
(72, 2, 'add_stage', 'educational_stages', 5, NULL, '{\"stage_name\":\"\\u0627\\u0644\\u0645\\u0631\\u062d\\u0644\\u0629 \\u0627\\u0644\\u0627\\u0639\\u062f\\u0627\\u062f\\u064a\\u0629\",\"stage_code\":\"200\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:05:05'),
(73, 2, 'delete_user', 'users', 13, NULL, '{\"user_id\":13,\"delete_type\":\"user\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:12:36'),
(74, 2, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:15:18'),
(75, 17, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:15:30'),
(76, 17, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:15:57'),
(77, 12, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:16:03'),
(78, 12, 'logout', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:16:21'),
(79, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:16:58'),
(80, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:21:40'),
(81, 2, 'update_settings', 'system_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 09:21:42'),
(82, 15, 'delete_staff_leave', 'staff_leaves', 4, '{\"id\":4,\"user_id\":12,\"user_type\":\"teacher\",\"leave_type\":\"sick\",\"start_date\":\"2025-07-20\",\"end_date\":\"2025-07-25\",\"total_days\":6,\"reason\":\"\",\"medical_certificate\":null,\"status\":\"approved\",\"applied_by\":15,\"approved_by\":null,\"approved_at\":null,\"rejection_reason\":null,\"notes\":null,\"created_at\":\"2025-07-20 11:33:54\",\"updated_at\":\"2025-07-20 11:33:54\"}', '{\"staff_id\":12,\"leave_type\":\"sick\",\"start_date\":\"2025-07-20\",\"end_date\":\"2025-07-25\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 09:49:38'),
(83, 15, 'register_staff_leave', 'staff_leaves', 8, NULL, '{\"staff_id\":12,\"leave_type\":\"sick\",\"start_date\":\"2025-07-21\",\"end_date\":\"2025-07-26\",\"total_days\":6,\"updated_attendance_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 09:55:50'),
(84, 15, 'edit_staff_leave', 'staff_leaves', 8, '{\"id\":8,\"user_id\":12,\"user_type\":\"teacher\",\"leave_type\":\"sick\",\"start_date\":\"2025-07-21\",\"end_date\":\"2025-07-26\",\"total_days\":6,\"reason\":\"\\u0645\\u0631\\u0636\",\"medical_certificate\":null,\"status\":\"approved\",\"applied_by\":15,\"approved_by\":null,\"approved_at\":null,\"rejection_reason\":null,\"notes\":null,\"created_at\":\"2025-07-20 12:55:50\",\"updated_at\":\"2025-07-20 12:55:50\"}', '{\"new_leave_type\":\"sick\",\"new_start_date\":\"2025-07-20\",\"new_end_date\":\"2025-07-26\",\"new_total_days\":7}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 09:57:02'),
(85, 15, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-20\",\"new_records\":2,\"updated_records\":3}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 10:02:35'),
(86, 15, 'register_staff_leave', 'staff_leaves', 11, NULL, '{\"staff_id\":18,\"leave_type\":\"sick\",\"start_date\":\"2025-07-20\",\"end_date\":\"2025-07-23\",\"total_days\":4,\"updated_attendance_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 10:04:08'),
(87, 15, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-20\",\"new_records\":1,\"updated_records\":3}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-20 10:24:49'),
(88, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 12:28:47'),
(89, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 15:52:37'),
(90, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 16:50:29'),
(91, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 16:52:26'),
(92, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 16:53:52'),
(93, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 16:56:45'),
(94, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 16:58:13'),
(95, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:38:24'),
(96, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:40:10'),
(97, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:41:11'),
(98, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:42:08'),
(99, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:44:46'),
(100, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:44:55'),
(101, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:45:27'),
(102, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:45:36'),
(103, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:45:50'),
(104, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:46:38'),
(105, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:46:55'),
(106, 2, 'add_payment', 'student_payments', 0, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:47:22'),
(107, 2, 'add_payment', 'student_payments', 19, NULL, '{\"student_id\":3,\"amount\":5000,\"payment_method\":\"cash\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 17:49:54'),
(108, 2, 'take_attendance', 'attendance', NULL, NULL, '{\"class_id\":1,\"subject_id\":0,\"date\":\"2025-07-20\",\"new_records\":1,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-20 18:49:57'),
(109, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-21 06:17:03'),
(110, 2, 'add_student', 'students', 4, NULL, '{\"student_name\":\"zyad ahmed\",\"student_number\":\"101\"}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-21 08:32:55'),
(111, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-21 13:21:11'),
(112, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-21 14:40:56'),
(113, 2, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-21\",\"new_records\":2,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-21 14:53:20'),
(114, 2, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-21\",\"new_records\":2,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-21 15:04:30'),
(115, 2, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-21\",\"new_records\":0,\"updated_records\":2}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-21 15:15:33'),
(116, 2, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-20\",\"new_records\":2,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, '2025-07-21 15:43:33'),
(117, 2, 'login', NULL, NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-22 09:48:41'),
(118, 2, 'take_staff_attendance', 'attendance', NULL, NULL, '{\"date\":\"2025-07-22\",\"new_records\":2,\"updated_records\":0}', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', NULL, '2025-07-22 10:06:45');

-- --------------------------------------------------------

--
-- Table structure for table `admin_attendance`
--

CREATE TABLE `admin_attendance` (
  `id` int(10) UNSIGNED NOT NULL,
  `admin_id` int(10) UNSIGNED NOT NULL,
  `attendance_date` date NOT NULL,
  `status` enum('present','absent','late','excused','sick_leave','regular_leave') NOT NULL DEFAULT 'present',
  `check_in_time` time DEFAULT NULL,
  `check_out_time` time DEFAULT NULL,
  `notes` varchar(255) DEFAULT NULL,
  `recorded_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_attendance`
--

INSERT INTO `admin_attendance` (`id`, `admin_id`, `attendance_date`, `status`, `check_in_time`, `check_out_time`, `notes`, `recorded_by`, `created_at`, `updated_at`) VALUES
(1, 2, '2025-07-20', 'present', '08:00:00', '15:00:00', '', 15, '2025-07-20 11:34:06', '2025-07-20 13:02:35'),
(2, 15, '2025-07-20', 'present', '08:00:00', '15:00:00', '', 15, '2025-07-20 11:34:06', '2025-07-20 13:02:35'),
(3, 18, '2025-07-20', 'sick_leave', '00:00:00', '00:00:00', '', 15, '2025-07-20 13:02:35', '2025-07-20 13:24:49'),
(4, 17, '2025-07-20', 'present', '08:00:00', '15:00:00', '', 15, '2025-07-20 13:02:35', '2025-07-20 13:24:49'),
(5, 16, '2025-07-20', 'present', '08:00:00', '15:00:00', '', 15, '2025-07-20 13:24:49', '2025-07-20 13:24:49'),
(6, 18, '2025-07-21', 'sick_leave', '00:00:00', '00:00:00', '', 2, '2025-07-21 18:04:30', '2025-07-21 18:04:30'),
(7, 17, '2025-07-21', 'present', '08:00:00', '15:00:00', '', 2, '2025-07-21 18:04:30', '2025-07-21 18:04:30');

-- --------------------------------------------------------

--
-- Table structure for table `attendance`
--

CREATE TABLE `attendance` (
  `id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `class_id` int(10) UNSIGNED NOT NULL,
  `subject_id` int(10) UNSIGNED DEFAULT NULL,
  `teacher_id` int(10) UNSIGNED DEFAULT NULL,
  `academic_year_id` int(10) UNSIGNED NOT NULL,
  `attendance_date` date NOT NULL,
  `period_number` tinyint(3) UNSIGNED DEFAULT NULL,
  `status` enum('present','absent','late','excused','sick') NOT NULL,
  `arrival_time` time DEFAULT NULL,
  `departure_time` time DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `marked_by` int(10) UNSIGNED DEFAULT NULL,
  `marked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `attendance`
--

INSERT INTO `attendance` (`id`, `student_id`, `class_id`, `subject_id`, `teacher_id`, `academic_year_id`, `attendance_date`, `period_number`, `status`, `arrival_time`, `departure_time`, `notes`, `marked_by`, `marked_at`, `updated_at`) VALUES
(9, 4, 1, NULL, NULL, 1, '2025-07-21', NULL, 'present', NULL, NULL, '', NULL, '2025-07-21 14:53:20', '2025-07-21 14:53:20'),
(10, 3, 1, NULL, NULL, 1, '2025-07-21', NULL, 'present', NULL, NULL, '', NULL, '2025-07-21 14:53:20', '2025-07-21 14:53:20'),
(11, 4, 1, NULL, NULL, 1, '2025-07-20', NULL, 'absent', NULL, NULL, '', NULL, '2025-07-21 15:43:33', '2025-07-21 15:43:33'),
(12, 3, 1, NULL, NULL, 1, '2025-07-20', NULL, 'present', NULL, NULL, '', NULL, '2025-07-21 15:43:33', '2025-07-21 15:43:33'),
(13, 4, 1, NULL, NULL, 1, '2025-07-22', NULL, 'present', NULL, NULL, '', NULL, '2025-07-22 10:06:45', '2025-07-22 10:06:45'),
(14, 3, 1, NULL, NULL, 1, '2025-07-22', NULL, 'present', NULL, NULL, '', NULL, '2025-07-22 10:06:45', '2025-07-22 10:06:45');

-- --------------------------------------------------------

--
-- Table structure for table `backups`
--

CREATE TABLE `backups` (
  `id` int(10) UNSIGNED NOT NULL,
  `backup_name` varchar(255) NOT NULL,
  `backup_type` enum('full','database','files','incremental') NOT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `file_size` bigint(20) UNSIGNED DEFAULT NULL,
  `compression_type` varchar(20) DEFAULT NULL,
  `database_included` tinyint(1) DEFAULT 0,
  `files_included` tinyint(1) DEFAULT 0,
  `tables_included` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tables_included`)),
  `directories_included` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`directories_included`)),
  `status` enum('in_progress','completed','failed','cancelled') DEFAULT 'in_progress',
  `progress_percentage` tinyint(3) UNSIGNED DEFAULT 0,
  `error_message` text DEFAULT NULL,
  `checksum` varchar(64) DEFAULT NULL,
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bank_accounts`
--

CREATE TABLE `bank_accounts` (
  `id` int(10) UNSIGNED NOT NULL,
  `account_name` varchar(200) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `bank_name` varchar(200) NOT NULL,
  `branch_name` varchar(200) DEFAULT NULL,
  `account_type` enum('current','savings','business') DEFAULT 'current',
  `balance` decimal(15,2) DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'EGP',
  `is_active` tinyint(1) DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bank_accounts`
--

INSERT INTO `bank_accounts` (`id`, `account_name`, `account_number`, `bank_name`, `branch_name`, `account_type`, `balance`, `currency`, `is_active`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'الحساب الرئيسي للمدرسة', '*********', 'البنك الأهلي المصري', 'فرع المعادي', 'business', 50000.00, 'EGP', 1, 'الحساب الرئيسي لاستقبال رسوم الطلاب', '2025-07-29 12:00:00', '2025-07-29 12:00:00'),
(2, 'حساب المصروفات', '*********', 'بنك مصر', 'فرع المعادي', 'current', 25000.00, 'EGP', 1, 'حساب خاص بالمصروفات اليومية', '2025-07-29 12:00:00', '2025-07-29 12:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `books`
--

CREATE TABLE `books` (
  `id` int(10) UNSIGNED NOT NULL,
  `book_code` varchar(50) NOT NULL,
  `title` varchar(200) NOT NULL,
  `author` varchar(100) DEFAULT NULL,
  `publisher` varchar(100) DEFAULT NULL,
  `subject_id` int(10) UNSIGNED DEFAULT NULL,
  `grade_level` varchar(50) DEFAULT NULL,
  `book_type` enum('textbook','workbook','reference') DEFAULT 'textbook',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `stock_quantity` int(10) UNSIGNED DEFAULT 0,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `books`
--

INSERT INTO `books` (`id`, `book_code`, `title`, `author`, `publisher`, `subject_id`, `grade_level`, `book_type`, `price`, `stock_quantity`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, 'MATH-G1-001', 'الرياضيات للصف الأول', 'أحمد محمد', 'دار المعرفة', NULL, 'الصف الأول', 'textbook', 45.00, 100, NULL, 'active', '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(2, 'ARAB-G1-001', 'اللغة العربية للصف الأول', 'فاطمة أحمد', 'دار التعليم', NULL, 'الصف الأول', 'textbook', 40.00, 100, NULL, 'active', '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(3, 'ENG-G1-001', 'اللغة الإنجليزية للصف الأول', 'Sarah Johnson', 'Education House', NULL, 'الصف الأول', 'textbook', 50.00, 100, NULL, 'active', '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(4, 'SCI-G2-001', 'العلوم للصف الثاني', 'محمد علي', 'دار العلوم', NULL, 'الصف الثاني', 'textbook', 55.00, 100, NULL, 'active', '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(5, 'MATH-G2-WB', 'كراسة تمارين الرياضيات', 'سعاد محمود', 'دار التمارين', NULL, 'الصف الثاني', 'textbook', 25.00, 100, NULL, 'active', '2025-07-20 10:31:15', '2025-07-20 10:31:15');

-- --------------------------------------------------------

--
-- Table structure for table `classes`
--

CREATE TABLE `classes` (
  `id` int(10) UNSIGNED NOT NULL,
  `class_name` varchar(100) NOT NULL,
  `class_name_en` varchar(100) DEFAULT NULL,
  `grade_level` varchar(50) NOT NULL,
  `section` varchar(10) DEFAULT NULL,
  `capacity` tinyint(3) UNSIGNED DEFAULT 30,
  `current_students` tinyint(3) UNSIGNED DEFAULT 0,
  `class_teacher_id` int(10) UNSIGNED DEFAULT NULL,
  `room_number` varchar(20) DEFAULT NULL,
  `academic_year_id` int(10) UNSIGNED DEFAULT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `stage_id` int(10) UNSIGNED DEFAULT NULL,
  `grade_id` int(10) UNSIGNED DEFAULT NULL,
  `teacher_id` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `classes`
--

INSERT INTO `classes` (`id`, `class_name`, `class_name_en`, `grade_level`, `section`, `capacity`, `current_students`, `class_teacher_id`, `room_number`, `academic_year_id`, `status`, `created_at`, `updated_at`, `stage_id`, `grade_id`, `teacher_id`) VALUES
(1, 'الصف الأول الابتدائي - أ', 'Grade 1 - A', 'primary_1', NULL, 25, 2, NULL, NULL, 1, 'active', '2025-07-15 04:55:07', '2025-07-21 08:32:55', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `discounts`
--

CREATE TABLE `discounts` (
  `id` int(10) UNSIGNED NOT NULL,
  `discount_name` varchar(100) NOT NULL,
  `discount_code` varchar(20) DEFAULT NULL,
  `discount_type` enum('percentage','fixed_amount') NOT NULL,
  `discount_value` decimal(10,2) NOT NULL,
  `applicable_to` enum('all','specific_students','class','grade','fee_category') DEFAULT 'all',
  `conditions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`conditions`)),
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `max_usage` int(10) UNSIGNED DEFAULT NULL,
  `current_usage` int(10) UNSIGNED DEFAULT 0,
  `is_stackable` tinyint(1) DEFAULT 0,
  `priority` tinyint(3) UNSIGNED DEFAULT 1,
  `status` enum('active','inactive','expired') DEFAULT 'active',
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `discounts`
--

INSERT INTO `discounts` (`id`, `discount_name`, `discount_code`, `discount_type`, `discount_value`, `applicable_to`, `conditions`, `start_date`, `end_date`, `max_usage`, `current_usage`, `is_stackable`, `priority`, `status`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'خصم الأخوة', 'SIBLINGS', 'percentage', 10.00, 'specific_students', '{\"min_siblings\": 2, \"description\": \"خصم للطلاب الذين لديهم أخوة في المدرسة\"}', NULL, NULL, NULL, 0, 0, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(2, 'خصم التفوق', 'EXCELLENCE', 'percentage', 15.00, 'specific_students', '{\"min_grade\": 90, \"description\": \"خصم للطلاب المتفوقين\"}', NULL, NULL, NULL, 0, 0, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(3, 'خصم الموظفين', 'STAFF', 'percentage', 20.00, 'specific_students', '{\"staff_children\": true, \"description\": \"خصم لأبناء الموظفين\"}', NULL, NULL, NULL, 0, 0, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(4, 'منحة كاملة', 'SCHOLARSHIP', 'percentage', 100.00, 'specific_students', '{\"scholarship\": true, \"description\": \"منحة دراسية كاملة للطلاب المحتاجين\"}', NULL, NULL, NULL, 0, 0, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(5, 'خصم الدفع المبكر', 'EARLY_PAYMENT', 'percentage', 5.00, 'all', '{\"early_days\": 30, \"description\": \"خصم للدفع قبل 30 يوم من تاريخ الاستحقاق\"}', NULL, NULL, NULL, 0, 0, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `educational_stages`
--

CREATE TABLE `educational_stages` (
  `id` int(10) UNSIGNED NOT NULL,
  `stage_name` varchar(100) NOT NULL,
  `stage_name_en` varchar(100) DEFAULT NULL,
  `stage_code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `sort_order` tinyint(3) UNSIGNED DEFAULT 1,
  `min_age` tinyint(3) UNSIGNED DEFAULT NULL,
  `max_age` tinyint(3) UNSIGNED DEFAULT NULL,
  `duration_years` tinyint(3) UNSIGNED DEFAULT 1,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `educational_stages`
--

INSERT INTO `educational_stages` (`id`, `stage_name`, `stage_name_en`, `stage_code`, `description`, `sort_order`, `min_age`, `max_age`, `duration_years`, `status`, `created_at`, `updated_at`) VALUES
(2, 'المرحلة الابتدائية', 'Primary School', 'PRI', 'المرحلة الابتدائية من الصف الأول إلى السادس', 2, 6, 11, 6, 'active', '2025-07-17 10:15:35', '2025-07-17 10:15:35'),
(5, 'المرحلة الاعدادية', 'prip school', '200', '', 3, NULL, NULL, 3, 'active', '2025-07-20 09:05:05', '2025-07-20 09:05:05');

-- --------------------------------------------------------

--
-- Table structure for table `error_logs`
--

CREATE TABLE `error_logs` (
  `id` int(10) UNSIGNED NOT NULL,
  `error_type` varchar(50) NOT NULL,
  `error_message` text NOT NULL,
  `error_code` varchar(20) DEFAULT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `line_number` int(10) UNSIGNED DEFAULT NULL,
  `stack_trace` longtext DEFAULT NULL,
  `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`context`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `request_url` text DEFAULT NULL,
  `request_method` varchar(10) DEFAULT NULL,
  `request_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`request_data`)),
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `session_id` varchar(128) DEFAULT NULL,
  `severity` enum('low','medium','high','critical') DEFAULT 'medium',
  `is_resolved` tinyint(1) DEFAULT 0,
  `resolved_by` int(10) UNSIGNED DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `error_logs`
--

INSERT INTO `error_logs` (`id`, `error_type`, `error_message`, `error_code`, `file_path`, `line_number`, `stack_trace`, `context`, `ip_address`, `user_agent`, `request_url`, `request_method`, `request_data`, `user_id`, `session_id`, `severity`, `is_resolved`, `resolved_by`, `resolved_at`, `created_at`) VALUES
(1, 'general', 'Error updating student: SQLSTATE[42S22]: Column not found: 1054 Unknown column \'address\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2/students/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"e81d999d8112968bb580398d801785b4e651871350717a79b913963c5dd5b782\",\"full_name\":\"mariam\",\"student_id\":\"101\",\"email\":\"<EMAIL>\",\"phone\":\"0115232545236\",\"date_of_birth\":\"2020-06-09\",\"gender\":\"female\",\"national_id\":\"3252312545821\",\"address\":\"egypt\",\"class_id\":\"2\",\"enrollment_date\":\"2025-07-16\",\"status\":\"active\",\"parent_name\":\"shaker\",\"parent_phone\":\"011252326542\",\"parent_email\":\"<EMAIL>\",\"parent_national_id\":\"3251254852211\",\"emergency_contact_name\":\"joe\",\"emergency_contact_phone\":\"01152326524588\",\"medical_conditions\":\"\",\"notes\":\"\"}', 2, 'kfug3jgh0brgs7oj0nlh53e3mb', 'medium', 0, NULL, NULL, '2025-07-16 09:57:22'),
(2, 'general', 'Error updating student: SQLSTATE[42S22]: Column not found: 1054 Unknown column \'address\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2/students/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"030e598c8c876029c3909e4ddd344a49fb48415772f9223022551a32f3106171\",\"full_name\":\"mariam\",\"student_id\":\"101\",\"email\":\"<EMAIL>\",\"phone\":\"0115232545236\",\"date_of_birth\":\"2020-06-09\",\"gender\":\"female\",\"national_id\":\"3252312545821\",\"address\":\"egypt\",\"class_id\":\"2\",\"enrollment_date\":\"2025-07-16\",\"status\":\"active\",\"parent_name\":\"shaker\",\"parent_phone\":\"011252326542\",\"parent_email\":\"<EMAIL>\",\"parent_national_id\":\"3251254852211\",\"emergency_contact_name\":\"joe\",\"emergency_contact_phone\":\"01152326524588\",\"medical_conditions\":\"\",\"notes\":\"\"}', 2, 'kfug3jgh0brgs7oj0nlh53e3mb', 'medium', 0, NULL, NULL, '2025-07-16 09:57:53'),
(3, 'general', 'Error updating student: SQLSTATE[42S22]: Column not found: 1054 Unknown column \'address\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2/students/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"ecf31229815532f7a39b0564c85b0209b08b6fcb71bc5eea57a076d8eb202c20\",\"full_name\":\"mariam\",\"student_id\":\"101\",\"email\":\"<EMAIL>\",\"phone\":\"0115232545236\",\"date_of_birth\":\"2020-06-09\",\"gender\":\"female\",\"national_id\":\"3252312545821\",\"address\":\"egypt\",\"class_id\":\"2\",\"enrollment_date\":\"2025-07-16\",\"status\":\"active\",\"parent_name\":\"shaker\",\"parent_phone\":\"011252326542\",\"parent_email\":\"<EMAIL>\",\"parent_national_id\":\"3251254852211\",\"emergency_contact_name\":\"joe\",\"emergency_contact_phone\":\"01152326524588\",\"medical_conditions\":\"\",\"notes\":\"\"}', 2, 'kfug3jgh0brgs7oj0nlh53e3mb', 'medium', 0, NULL, NULL, '2025-07-16 09:58:13'),
(4, 'general', 'Error updating student: SQLSTATE[42S22]: Column not found: 1054 Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2/students/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"bc16d584cbd1e5b9a88a5d13035a0b01d4df50e29e99ad09e3712b1d4334af13\",\"full_name\":\"mariam\",\"student_id\":\"101\",\"email\":\"<EMAIL>\",\"phone\":\"0115232545236\",\"date_of_birth\":\"2020-06-09\",\"gender\":\"female\",\"national_id\":\"3252312545821\",\"address\":\"egypt\",\"class_id\":\"18\",\"enrollment_date\":\"2025-07-16\",\"status\":\"active\",\"parent_name\":\"shaker\",\"parent_phone\":\"011252326542\",\"parent_email\":\"<EMAIL>\",\"parent_national_id\":\"3251254852211\",\"emergency_contact_name\":\"joe\",\"emergency_contact_phone\":\"01152326524588\",\"medical_conditions\":\"\",\"notes\":\"\"}', 2, 'kfug3jgh0brgs7oj0nlh53e3mb', 'medium', 0, NULL, NULL, '2025-07-16 09:59:28'),
(5, 'general', 'Error updating student: SQLSTATE[42S22]: Column not found: 1054 Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2/students/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"020822fec3134d269aa9f225fdcebcba447b7a17fe55f11f75b909da5220f5e9\",\"full_name\":\"mariam\",\"student_id\":\"101\",\"email\":\"<EMAIL>\",\"phone\":\"0115232545236\",\"date_of_birth\":\"2020-06-09\",\"gender\":\"female\",\"national_id\":\"3252312545821\",\"address\":\"egypt\",\"class_id\":\"1\",\"enrollment_date\":\"2025-07-16\",\"status\":\"active\",\"parent_name\":\"shaker\",\"parent_phone\":\"011252326542\",\"parent_email\":\"<EMAIL>\",\"parent_national_id\":\"3251254852211\",\"emergency_contact_name\":\"joe\",\"emergency_contact_phone\":\"01152326524588\",\"medical_conditions\":\"\",\"notes\":\"\"}', 2, 'kfug3jgh0brgs7oj0nlh53e3mb', 'medium', 0, NULL, NULL, '2025-07-16 09:59:36'),
(6, 'general', 'Error adding teacher: Unknown column \'position\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/add.php', 'POST', '{\"csrf_token\":\"a51914351e9d4bb2527f400ace24a14da17f345ffa6b897fd10dc4ba8a496d77\",\"full_name\":\"misho kamel\",\"username\":\"misho\",\"email\":\"<EMAIL>\",\"password\":\"*********\",\"confirm_password\":\"*********\",\"phone\":\"01252458521\",\"national_id\":\"************\",\"date_of_birth\":\"2018-11-15\",\"gender\":\"male\",\"address\":\"port ghlip\",\"employee_id\":\"200\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"position\":\"\\u0645\\u0639\\u0644\\u0645 \",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u064a\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"experience_years\":\"5\",\"salary\":\"5000\",\"emergency_contact\":\"\",\"notes\":\"\",\"status\":\"active\",\"class_ids\":[\"1\",\"2\"]}', 2, 'jl1bv8ef21hn2qbhf54a0n3jlv', 'medium', 0, NULL, NULL, '2025-07-16 07:13:38'),
(7, 'general', 'Error adding teacher: Unknown column \'emergency_contact\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/add.php', 'POST', '{\"csrf_token\":\"6894e20c90a7f21d35b8877d1f24b16cbce62b1f8f407d1c702a84ccb4263d00\",\"full_name\":\"misho kamel\",\"username\":\"misho\",\"email\":\"<EMAIL>\",\"password\":\"*********\",\"confirm_password\":\"*********\",\"phone\":\"01252458521\",\"national_id\":\"************\",\"date_of_birth\":\"2018-11-15\",\"gender\":\"male\",\"address\":\"port ghlip\",\"employee_id\":\"200\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u064a\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"experience_years\":\"5\",\"salary\":\"5000\",\"emergency_contact\":\"\",\"notes\":\"\",\"status\":\"active\",\"class_ids\":[\"1\",\"2\"]}', 2, 'jl1bv8ef21hn2qbhf54a0n3jlv', 'medium', 0, NULL, NULL, '2025-07-16 07:16:37'),
(8, 'general', 'Error adding teacher: Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/add.php', 'POST', '{\"csrf_token\":\"83ff144802c436c8d174e20606542f60d6cabb648c4d2aa94a271e1b79529f45\",\"full_name\":\"misho kamel\",\"username\":\"misho\",\"email\":\"<EMAIL>\",\"password\":\"*********\",\"confirm_password\":\"*********\",\"phone\":\"01252458521\",\"national_id\":\"************\",\"date_of_birth\":\"2018-11-15\",\"gender\":\"male\",\"address\":\"port ghlip\",\"employee_id\":\"200\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u064a\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"experience_years\":\"5\",\"salary\":\"5000\",\"emergency_contact\":\"\",\"notes\":\"\",\"status\":\"active\",\"class_ids\":[\"1\",\"2\"]}', 2, 'jl1bv8ef21hn2qbhf54a0n3jlv', 'medium', 0, NULL, NULL, '2025-07-16 07:18:08'),
(9, 'general', 'Error adding teacher: Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/add.php', 'POST', '{\"csrf_token\":\"90fc62db3bf1cf861d39fc9b41b9ce6a828ec246858db848ce5a26fa3219c69b\",\"full_name\":\"misho kamel\",\"username\":\"misho\",\"email\":\"<EMAIL>\",\"password\":\"*********\",\"confirm_password\":\"*********\",\"phone\":\"01252458521\",\"national_id\":\"************\",\"date_of_birth\":\"2018-11-15\",\"gender\":\"male\",\"address\":\"port ghlip\",\"employee_id\":\"200\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u064a\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"experience_years\":\"5\",\"salary\":\"5000\",\"notes\":\"\\u0645\\u0639\\u0644\\u0645 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649 \",\"status\":\"active\",\"class_ids\":[\"1\",\"2\"]}', 2, 'jl1bv8ef21hn2qbhf54a0n3jlv', 'medium', 0, NULL, NULL, '2025-07-16 07:18:39'),
(10, 'general', 'Error updating teacher: Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"ebe2c9814b165c5fa1711574042a5b9f724ac4b57927ce8a646b794cab29b2ab\",\"employee_id\":\"200\",\"email\":\"<EMAIL>\",\"phone\":\"\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"experience_years\":\"5\",\"salary\":\"5000\",\"status\":\"active\",\"notes\":\"\"}', 2, 'ppsjffoujlg2gurreesrhj835m', 'medium', 0, NULL, NULL, '2025-07-17 13:26:02'),
(11, 'general', 'Error updating teacher: Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"4639a9ae3d4ac7d731c246a60b4bcee77020e5701c42ba87959f466f30588891\",\"employee_id\":\"200\",\"email\":\"<EMAIL>\",\"phone\":\"\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u064a\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"experience_years\":\"5\",\"salary\":\"5000\",\"status\":\"active\",\"notes\":\"\"}', 2, 'ppsjffoujlg2gurreesrhj835m', 'medium', 0, NULL, NULL, '2025-07-17 14:23:57'),
(12, 'general', 'Error updating teacher: Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"ba8af48aa17a053b255bbd4b1ddfdfebe3474a5d8bb55677920d079db892bba3\",\"employee_id\":\"200\",\"email\":\"<EMAIL>\",\"phone\":\"\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u064a\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"experience_years\":\"5\",\"salary\":\"5000\",\"status\":\"active\",\"notes\":\"\"}', 2, 'ppsjffoujlg2gurreesrhj835m', 'medium', 0, NULL, NULL, '2025-07-17 14:29:03'),
(13, 'general', 'Error updating teacher: Unknown column \'notes\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//teachers/edit.php?id=1', 'POST', '{\"id\":\"1\",\"csrf_token\":\"6e6b40104b3c32ebb93b647d77544a59874732accaf3849a68ff98095cf515d4\",\"employee_id\":\"200\",\"email\":\"<EMAIL>\",\"phone\":\"\",\"hire_date\":\"\",\"department\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"qualification\":\"\\u0628\\u0643\\u0627\\u0644\\u064a\\u0631\\u064a\\u0648\\u0633 \\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"specialization\":\"\\u062d\\u0627\\u0633\\u0628 \\u0627\\u0644\\u0649\",\"experience_years\":\"5\",\"salary\":\"5000\",\"status\":\"active\",\"notes\":\"\"}', 2, 'ppsjffoujlg2gurreesrhj835m', 'medium', 0, NULL, NULL, '2025-07-17 14:29:11'),
(14, 'general', 'Error deleting user: Cannot delete or update a parent row: a foreign key constraint fails (`school_management`.`teacher_attendance`, CONSTRAINT `teacher_attendance_ibfk_2` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`))', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//users/delete.php?id=2', 'GET', '{\"id\":\"2\"}', 15, 'vgbmiecghs4o9pabeir5uusuhg', 'medium', 0, NULL, NULL, '2025-07-20 08:08:27'),
(15, 'general', 'Error deleting user: Cannot delete or update a parent row: a foreign key constraint fails (`school_management`.`teacher_attendance`, CONSTRAINT `teacher_attendance_ibfk_2` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`))', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//users/delete.php?id=2', 'GET', '{\"id\":\"2\"}', 15, 'vgbmiecghs4o9pabeir5uusuhg', 'medium', 0, NULL, NULL, '2025-07-20 08:08:32'),
(16, 'general', 'Error deleting user: Cannot delete or update a parent row: a foreign key constraint fails (`school_management`.`admin_attendance`, CONSTRAINT `admin_attendance_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`))', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//users/delete.php?id=2', 'GET', '{\"id\":\"2\"}', 15, 'vgbmiecghs4o9pabeir5uusuhg', 'medium', 0, NULL, NULL, '2025-07-20 08:48:04'),
(17, 'general', 'Error deleting user: Cannot delete or update a parent row: a foreign key constraint fails (`school_management`.`admin_attendance`, CONSTRAINT `admin_attendance_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`))', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//users/delete.php?id=15', 'GET', '{\"id\":\"15\"}', 15, 'epmf19rgqf2qc0e6etpts2sccf', 'medium', 0, NULL, NULL, '2025-07-20 09:35:12'),
(18, 'general', 'Error deleting user: Cannot delete or update a parent row: a foreign key constraint fails (`school_management`.`admin_attendance`, CONSTRAINT `admin_attendance_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`))', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//users/delete.php?id=15', 'GET', '{\"id\":\"15\"}', 15, 'epmf19rgqf2qc0e6etpts2sccf', 'medium', 0, NULL, NULL, '2025-07-20 09:35:20'),
(19, 'general', 'Error saving bulk attendance: Unknown column \'recorded_by\' in \'field list\'', NULL, '', 0, NULL, NULL, '::1', '0', '/school_system_v2//attendance/bulk_attendance.php', 'POST', '{\"csrf_token\":\"ca04c2900ee1d64f98de4c9c07b82c46889eba4e230806660d68ea4b36a8a358\",\"class_id\":\"1\",\"subject_id\":\"0\",\"attendance_date\":\"2025-07-20\",\"status\":\"present\",\"notes\":\"\",\"save_bulk_attendance\":\"\"}', 15, 'epmf19rgqf2qc0e6etpts2sccf', 'medium', 0, NULL, NULL, '2025-07-20 10:23:26');

-- --------------------------------------------------------

--
-- Table structure for table `exams`
--

CREATE TABLE `exams` (
  `id` int(10) UNSIGNED NOT NULL,
  `exam_title` varchar(200) NOT NULL,
  `exam_description` text DEFAULT NULL,
  `subject_id` int(10) UNSIGNED NOT NULL,
  `class_id` int(10) UNSIGNED NOT NULL,
  `teacher_id` int(10) UNSIGNED NOT NULL,
  `academic_year_id` int(10) UNSIGNED NOT NULL,
  `semester` enum('first','second','summer') DEFAULT 'first',
  `exam_type` enum('quiz','midterm','final','assignment','project') DEFAULT 'quiz',
  `exam_date` date DEFAULT NULL,
  `exam_time` time DEFAULT NULL,
  `duration_minutes` smallint(5) UNSIGNED DEFAULT 60,
  `total_marks` decimal(5,2) DEFAULT 100.00,
  `passing_marks` decimal(5,2) DEFAULT 60.00,
  `weight_percentage` decimal(5,2) DEFAULT 100.00,
  `instructions` text DEFAULT NULL,
  `exam_questions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`exam_questions`)),
  `status` enum('draft','published','active','completed','cancelled') DEFAULT 'draft',
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exam_attempts`
--

CREATE TABLE `exam_attempts` (
  `id` int(10) UNSIGNED NOT NULL,
  `exam_id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `attempt_date` datetime DEFAULT current_timestamp(),
  `score` decimal(5,2) DEFAULT NULL,
  `status` enum('in_progress','completed','graded') DEFAULT 'in_progress',
  `answers` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exam_questions`
--

CREATE TABLE `exam_questions` (
  `id` int(10) UNSIGNED NOT NULL,
  `exam_id` int(10) UNSIGNED NOT NULL,
  `question_text` text NOT NULL,
  `question_type` varchar(50) NOT NULL,
  `options` text DEFAULT NULL,
  `correct_answer` text DEFAULT NULL,
  `marks` int(11) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `fee_categories`
--

CREATE TABLE `fee_categories` (
  `id` int(10) UNSIGNED NOT NULL,
  `category_name` varchar(100) NOT NULL,
  `category_name_en` varchar(100) DEFAULT NULL,
  `category_code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `is_mandatory` tinyint(1) DEFAULT 1,
  `is_recurring` tinyint(1) DEFAULT 0,
  `applies_to` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`applies_to`)),
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `fee_categories`
--

INSERT INTO `fee_categories` (`id`, `category_name`, `category_name_en`, `category_code`, `description`, `is_mandatory`, `is_recurring`, `applies_to`, `status`, `created_at`, `updated_at`) VALUES
(1, 'رسوم دراسية', 'Tuition Fees', 'TUITION', 'الرسوم الدراسية الأساسية للفصل الدراسي', 1, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(2, 'رسوم تسجيل', 'Registration Fees', 'REGISTRATION', 'رسوم التسجيل والقبول لمرة واحدة', 1, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(3, 'رسوم كتب ومواد', 'Books and Materials', 'BOOKS', 'رسوم الكتب والمواد التعليمية', 1, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(4, 'رسوم نقل', 'Transportation Fees', 'TRANSPORT', 'رسوم النقل المدرسي', 0, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(5, 'رسوم أنشطة', 'Activity Fees', 'ACTIVITIES', 'رسوم الأنشطة اللاصفية والرحلات', 0, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(6, 'رسوم زي مدرسي', 'Uniform Fees', 'UNIFORM', 'رسوم الزي المدرسي', 0, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(7, 'رسوم امتحانات', 'Exam Fees', 'EXAMS', 'رسوم الامتحانات الخاصة والشهادات', 0, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(8, 'رسوم تأمين', 'Insurance Fees', 'INSURANCE', 'رسوم التأمين الصحي والحوادث', 0, 0, NULL, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `fee_structures`
--

CREATE TABLE `fee_structures` (
  `id` int(10) UNSIGNED NOT NULL,
  `fee_category_id` int(10) UNSIGNED NOT NULL,
  `academic_year_id` int(10) UNSIGNED NOT NULL,
  `grade_level` varchar(50) DEFAULT NULL,
  `class_id` int(10) UNSIGNED DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'SAR',
  `due_date_offset_days` smallint(6) DEFAULT 30,
  `late_fee_amount` decimal(10,2) DEFAULT 0.00,
  `late_fee_percentage` decimal(5,2) DEFAULT 0.00,
  `installments_allowed` tinyint(1) DEFAULT 0,
  `max_installments` tinyint(3) UNSIGNED DEFAULT 1,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `fee_structures`
--

INSERT INTO `fee_structures` (`id`, `fee_category_id`, `academic_year_id`, `grade_level`, `class_id`, `amount`, `currency`, `due_date_offset_days`, `late_fee_amount`, `late_fee_percentage`, `installments_allowed`, `max_installments`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, NULL, 0.00, 'SAR', 30, 0.00, 0.00, 0, 1, 'active', '2025-07-21 09:48:40', '2025-07-21 09:48:40');

-- --------------------------------------------------------

--
-- Table structure for table `fee_types`
--

CREATE TABLE `fee_types` (
  `id` int(10) UNSIGNED NOT NULL,
  `type_name` varchar(100) NOT NULL,
  `type_name_en` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `default_amount` decimal(10,2) DEFAULT 0.00,
  `is_mandatory` tinyint(1) DEFAULT 1,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `fee_types`
--

INSERT INTO `fee_types` (`id`, `type_name`, `type_name_en`, `description`, `default_amount`, `is_mandatory`, `status`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'رسوم دراسية', 'Tuition Fees', 'الرسوم الدراسية الأساسية', 5000.00, 1, 'active', NULL, '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(2, 'رسوم التسجيل', 'Registration Fees', 'رسوم التسجيل والقبول', 500.00, 1, 'active', NULL, '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(3, 'رسوم الكتب', 'Book Fees', 'رسوم الكتب والمواد التعليمية', 800.00, 1, 'active', NULL, '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(4, 'رسوم النشاطات', 'Activity Fees', 'رسوم الأنشطة اللاصفية', 300.00, 1, 'active', NULL, '2025-07-20 10:31:15', '2025-07-20 10:31:15'),
(5, 'رسوم النقل', 'Transportation Fees', 'رسوم النقل المدرسي', 1200.00, 1, 'active', NULL, '2025-07-20 10:31:15', '2025-07-20 10:31:15');

-- --------------------------------------------------------

--
-- Table structure for table `fee_type_classes`
--

CREATE TABLE `fee_type_classes` (
  `id` int(10) UNSIGNED NOT NULL,
  `fee_type_id` int(10) UNSIGNED NOT NULL,
  `class_id` int(10) UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `grades`
--

CREATE TABLE `grades` (
  `id` int(10) UNSIGNED NOT NULL,
  `grade_name` varchar(100) NOT NULL,
  `grade_name_en` varchar(100) DEFAULT NULL,
  `grade_code` varchar(20) NOT NULL,
  `stage_id` int(10) UNSIGNED NOT NULL,
  `description` text DEFAULT NULL,
  `sort_order` tinyint(3) UNSIGNED DEFAULT 1,
  `min_age` tinyint(3) UNSIGNED DEFAULT NULL,
  `max_age` tinyint(3) UNSIGNED DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `grades`
--

INSERT INTO `grades` (`id`, `grade_name`, `grade_name_en`, `grade_code`, `stage_id`, `description`, `sort_order`, `min_age`, `max_age`, `status`, `created_at`, `updated_at`) VALUES
(3, 'الصف الأول الابتدائي', 'Grade 1', 'G1', 2, 'الصف الأول من المرحلة الابتدائية', 1, 6, 7, 'active', '2025-07-17 10:41:27', '2025-07-17 13:31:58'),
(4, 'الصف الثاني الابتدائي', 'Grade 2', 'G2', 2, 'الصف الثاني من المرحلة الابتدائية', 2, 7, 8, 'active', '2025-07-17 10:41:27', '2025-07-17 10:41:27'),
(5, 'الصف الثالث الابتدائي', 'Grade 3', 'G3', 2, 'الصف الثالث من المرحلة الابتدائية', 3, 8, 9, 'active', '2025-07-17 10:41:27', '2025-07-17 10:41:27'),
(7, 'الصف الخامس الابتدائي', 'Grade 5', 'G5', 2, 'الصف الخامس من المرحلة الابتدائية', 5, 10, 11, 'active', '2025-07-17 10:41:27', '2025-07-17 10:41:27'),
(8, 'الصف السادس الابتدائي', 'Grade 6', 'G6', 2, 'الصف السادس من المرحلة الابتدائية', 6, 11, 12, 'active', '2025-07-17 10:41:27', '2025-07-17 10:41:27');

-- --------------------------------------------------------

--
-- Table structure for table `installment_payments`
--

CREATE TABLE `installment_payments` (
  `id` int(11) NOT NULL,
  `installment_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` enum('cash','bank_transfer','check','card','online') NOT NULL DEFAULT 'cash',
  `payment_reference` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `installment_plans`
--

CREATE TABLE `installment_plans` (
  `id` int(10) UNSIGNED NOT NULL,
  `plan_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `installment_plans`
--

INSERT INTO `installment_plans` (`id`, `plan_name`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, 'خطة الدفع الشهرية', 'دفع الرسوم على 10 أقساط شهرية', 'active', '2025-07-29 12:00:00', '2025-07-29 12:00:00'),
(2, 'خطة الدفع الفصلية', 'دفع الرسوم على 3 أقساط فصلية', 'active', '2025-07-29 12:00:00', '2025-07-29 12:00:00'),
(3, 'خطة الدفع النصف سنوية', 'دفع الرسوم على قسطين نصف سنويين', 'active', '2025-07-29 12:00:00', '2025-07-29 12:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `leave_settings`
--

CREATE TABLE `leave_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `leave_type` enum('sick','regular','emergency','annual','maternity','paternity') NOT NULL,
  `max_days_per_year` int(10) UNSIGNED DEFAULT NULL,
  `max_consecutive_days` int(10) UNSIGNED DEFAULT NULL,
  `requires_medical_certificate` tinyint(1) DEFAULT 0,
  `auto_approve` tinyint(1) DEFAULT 0,
  `advance_notice_days` int(10) UNSIGNED DEFAULT 0,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `leave_settings`
--

INSERT INTO `leave_settings` (`id`, `leave_type`, `max_days_per_year`, `max_consecutive_days`, `requires_medical_certificate`, `auto_approve`, `advance_notice_days`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'sick', 30, 7, 1, 0, 0, 'إجازة مرضية - تتطلب شهادة طبية للإجازات أكثر من 3 أيام', 1, '2025-07-20 07:24:50', '2025-07-20 07:24:50'),
(2, 'regular', 30, 15, 0, 0, 3, 'إجازة اعتيادية - تتطلب إشعار مسبق 3 أيام', 1, '2025-07-20 07:24:50', '2025-07-20 07:24:50'),
(3, 'emergency', 5, 3, 0, 1, 0, 'إجازة طارئة - تتم الموافقة عليها تلقائياً', 1, '2025-07-20 07:24:50', '2025-07-20 07:24:50'),
(4, 'annual', 21, 21, 0, 0, 7, 'إجازة سنوية - تتطلب إشعار مسبق أسبوع', 1, '2025-07-20 07:24:50', '2025-07-20 07:24:50'),
(5, 'maternity', 90, 90, 1, 0, 30, 'إجازة أمومة - تتطلب شهادة طبية', 1, '2025-07-20 07:24:50', '2025-07-20 07:24:50'),
(6, 'paternity', 7, 7, 0, 0, 7, 'إجازة أبوة - تتطلب إشعار مسبق أسبوع', 1, '2025-07-20 07:24:50', '2025-07-20 07:24:50');

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int(10) UNSIGNED NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `success` tinyint(1) DEFAULT 0,
  `failure_reason` varchar(255) DEFAULT NULL,
  `attempted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `login_attempts`
--

INSERT INTO `login_attempts` (`id`, `username`, `email`, `ip_address`, `user_agent`, `success`, `failure_reason`, `attempted_at`) VALUES
(1, '<EMAIL>', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-15 07:15:21'),
(2, '<EMAIL>', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-15 07:15:27'),
(3, '<EMAIL>', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-15 07:15:35'),
(4, '<EMAIL>', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-15 07:16:32'),
(5, '<EMAIL>', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-15 07:16:46'),
(6, 'admin@ad', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-20 06:09:09'),
(7, '<EMAIL>', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-20 06:09:27'),
(8, '<EMAIL>', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, 'Invalid credentials', '2025-07-20 06:09:37');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error','announcement') DEFAULT 'info',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `category` varchar(50) DEFAULT NULL,
  `action_url` varchar(255) DEFAULT NULL,
  `action_text` varchar(50) DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `is_read` tinyint(1) DEFAULT 0,
  `is_global` tinyint(1) DEFAULT 0,
  `target_roles` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`target_roles`)),
  `expires_at` datetime DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `priority`, `category`, `action_url`, `action_text`, `data`, `is_read`, `is_global`, `target_roles`, `expires_at`, `read_at`, `created_at`) VALUES
(6, 12, 'welcome_to_system', 'your_account_created_successfully', 'success', 'normal', NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, '2025-07-16 07:22:59'),
(8, 16, 'welcome_to_system', 'your_account_created_successfully', 'success', 'normal', NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, '2025-07-20 06:32:33'),
(27, 19, 'welcome_to_system', 'your_account_created_successfully', 'success', 'normal', NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, '2025-07-21 08:32:55');

-- --------------------------------------------------------

--
-- Table structure for table `remember_tokens`
--

CREATE TABLE `remember_tokens` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `staff`
--

CREATE TABLE `staff` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `employee_id` varchar(50) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `position` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `bank_account` varchar(50) DEFAULT NULL,
  `emergency_contact_name` varchar(100) DEFAULT NULL,
  `emergency_contact_phone` varchar(20) DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `documents` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`documents`)),
  `status` enum('active','inactive','on_leave','terminated') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `leave_types`
--

CREATE TABLE `leave_types` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `name_en` varchar(100) DEFAULT NULL,
  `code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `max_days_per_year` int(10) UNSIGNED DEFAULT NULL,
  `max_days_per_request` int(10) UNSIGNED DEFAULT NULL,
  `min_days_notice` int(10) UNSIGNED DEFAULT 1,
  `requires_medical_certificate` tinyint(1) DEFAULT 0,
  `requires_replacement` tinyint(1) DEFAULT 0,
  `approval_levels` tinyint(3) UNSIGNED DEFAULT 1,
  `is_paid` tinyint(1) DEFAULT 1,
  `affects_salary` tinyint(1) DEFAULT 0,
  `can_be_carried_forward` tinyint(1) DEFAULT 0,
  `color_code` varchar(7) DEFAULT '#007bff',
  `icon` varchar(50) DEFAULT 'fas fa-calendar',
  `sort_order` tinyint(3) UNSIGNED DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `leave_types`
--

INSERT INTO `leave_types` (`id`, `name`, `name_en`, `code`, `description`, `max_days_per_year`, `max_days_per_request`, `min_days_notice`, `requires_medical_certificate`, `requires_replacement`, `approval_levels`, `is_paid`, `affects_salary`, `can_be_carried_forward`, `color_code`, `icon`, `sort_order`, `is_active`) VALUES
(1, 'إجازة سنوية', 'Annual Leave', 'ANNUAL', 'الإجازة السنوية المستحقة للموظف', 30, 15, 7, 0, 1, 2, 1, 0, 1, '#28a745', 'fas fa-calendar-check', 1, 1),
(2, 'إجازة مرضية', 'Sick Leave', 'SICK', 'إجازة مرضية بتقرير طبي', 90, 30, 0, 1, 0, 1, 1, 0, 0, '#dc3545', 'fas fa-user-injured', 2, 1),
(3, 'إجازة طارئة', 'Emergency Leave', 'EMERGENCY', 'إجازة طارئة لظروف استثنائية', 7, 3, 0, 0, 0, 1, 1, 0, 0, '#ffc107', 'fas fa-exclamation-triangle', 3, 1),
(4, 'إجازة أمومة', 'Maternity Leave', 'MATERNITY', 'إجازة أمومة للموظفات', 90, 90, 30, 1, 0, 2, 1, 0, 0, '#e83e8c', 'fas fa-baby', 4, 1),
(5, 'إجازة أبوة', 'Paternity Leave', 'PATERNITY', 'إجازة أبوة للموظفين', 7, 7, 7, 0, 0, 1, 1, 0, 0, '#6f42c1', 'fas fa-male', 5, 1),
(6, 'إجازة حج/عمرة', 'Pilgrimage Leave', 'PILGRIMAGE', 'إجازة أداء فريضة الحج أو العمرة', 21, 21, 60, 0, 1, 2, 1, 0, 0, '#20c997', 'fas fa-mosque', 6, 1),
(7, 'إجازة بدون راتب', 'Unpaid Leave', 'UNPAID', 'إجازة بدون راتب لظروف خاصة', NULL, 30, 14, 0, 1, 2, 0, 1, 0, '#6c757d', 'fas fa-ban', 7, 1),
(8, 'إجازة دراسية', 'Study Leave', 'STUDY', 'إجازة للدراسة أو التدريب', NULL, 365, 30, 0, 1, 2, 1, 0, 0, '#17a2b8', 'fas fa-graduation-cap', 8, 1);

-- --------------------------------------------------------

--
-- Table structure for table `permission_types`
--

CREATE TABLE `permission_types` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `name_en` varchar(100) DEFAULT NULL,
  `code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `max_hours_per_day` decimal(4,2) DEFAULT NULL,
  `max_hours_per_month` decimal(5,2) DEFAULT NULL,
  `requires_replacement` tinyint(1) DEFAULT 0,
  `approval_levels` tinyint(3) UNSIGNED DEFAULT 1,
  `affects_salary` tinyint(1) DEFAULT 0,
  `color_code` varchar(7) DEFAULT '#007bff',
  `icon` varchar(50) DEFAULT 'fas fa-clock',
  `sort_order` tinyint(3) UNSIGNED DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permission_types`
--

INSERT INTO `permission_types` (`id`, `name`, `name_en`, `code`, `description`, `max_hours_per_day`, `max_hours_per_month`, `requires_replacement`, `approval_levels`, `affects_salary`, `color_code`, `icon`, `sort_order`, `is_active`) VALUES
(1, 'إذن بالساعات', 'Hourly Permission', 'HOURLY', 'إذن لساعات محددة خلال اليوم', 4.00, 20.00, 0, 1, 0, '#007bff', 'fas fa-clock', 1, 1),
(2, 'إذن طبي', 'Medical Appointment', 'MEDICAL', 'إذن لموعد طبي', 4.00, 16.00, 0, 1, 0, '#dc3545', 'fas fa-stethoscope', 2, 1),
(3, 'إذن شخصي', 'Personal Permission', 'PERSONAL', 'إذن لأمور شخصية', 3.00, 12.00, 0, 1, 1, '#ffc107', 'fas fa-user', 3, 1),
(4, 'تأخير صباحي', 'Late Arrival', 'LATE', 'إذن تأخير عن موعد الحضور', 2.00, 8.00, 0, 1, 1, '#fd7e14', 'fas fa-clock', 4, 1),
(5, 'انصراف مبكر', 'Early Departure', 'EARLY', 'إذن انصراف قبل نهاية الدوام', 3.00, 15.00, 0, 1, 1, '#6610f2', 'fas fa-sign-out-alt', 5, 1);

-- --------------------------------------------------------

--
-- Table structure for table `employee_leave_balances`
--

CREATE TABLE `employee_leave_balances` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `leave_type_id` int(10) UNSIGNED NOT NULL,
  `year` year(4) NOT NULL,
  `allocated_days` decimal(5,2) NOT NULL DEFAULT 0.00,
  `used_days` decimal(5,2) NOT NULL DEFAULT 0.00,
  `pending_days` decimal(5,2) NOT NULL DEFAULT 0.00,
  `remaining_days` decimal(5,2) GENERATED ALWAYS AS ((`allocated_days` - `used_days` - `pending_days`)) STORED,
  `carried_forward_days` decimal(5,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `staff_leaves`
--

CREATE TABLE `staff_leaves` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `leave_type_id` int(10) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `total_days` decimal(5,2) NOT NULL,
  `working_days` decimal(5,2) NOT NULL,
  `reason` text NOT NULL,
  `medical_certificate` varchar(255) DEFAULT NULL,
  `replacement_user_id` int(10) UNSIGNED DEFAULT NULL,
  `replacement_notes` text DEFAULT NULL,
  `status` enum('pending','approved','rejected','cancelled') DEFAULT 'pending',
  `workflow_step` tinyint(3) UNSIGNED DEFAULT 1,
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `applied_by` int(10) UNSIGNED NOT NULL,
  `approved_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_by` int(10) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `cancelled_by` int(10) UNSIGNED DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` text DEFAULT NULL,
  `hr_notes` text DEFAULT NULL,
  `manager_notes` text DEFAULT NULL,
  `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
  `notification_sent` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `staff_leaves`
--

INSERT INTO `staff_leaves` (`id`, `user_id`, `user_type`, `leave_type`, `start_date`, `end_date`, `total_days`, `reason`, `medical_certificate`, `status`, `applied_by`, `approved_by`, `approved_at`, `rejection_reason`, `notes`, `created_at`, `updated_at`) VALUES
(8, 12, 'teacher', 'sick', '2025-07-20', '2025-07-26', 7, 'مرض', NULL, 'approved', 15, NULL, NULL, NULL, NULL, '2025-07-20 09:55:50', '2025-07-20 09:57:02'),
(11, 18, '', 'sick', '2025-07-20', '2025-07-23', 4, '', NULL, 'approved', 15, NULL, NULL, NULL, NULL, '2025-07-20 10:04:08', '2025-07-20 10:04:08');

-- --------------------------------------------------------

--
-- Table structure for table `students`
--

CREATE TABLE `students` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `student_number` varchar(50) DEFAULT NULL,
  `student_id` varchar(50) NOT NULL,
  `class_id` int(10) UNSIGNED DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `blood_type` varchar(5) DEFAULT NULL,
  `enrollment_date` date DEFAULT NULL,
  `graduation_date` date DEFAULT NULL,
  `parent_name` varchar(100) DEFAULT NULL,
  `parent_phone` varchar(20) DEFAULT NULL,
  `parent_email` varchar(100) DEFAULT NULL,
  `parent_national_id` varchar(20) DEFAULT NULL,
  `emergency_contact_name` varchar(100) DEFAULT NULL,
  `emergency_contact_phone` varchar(20) DEFAULT NULL,
  `medical_conditions` text DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `documents` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`documents`)),
  `academic_year_id` int(10) UNSIGNED DEFAULT NULL,
  `status` enum('active','inactive','graduated','transferred','suspended') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `students`
--

INSERT INTO `students` (`id`, `user_id`, `student_number`, `student_id`, `class_id`, `phone`, `address`, `date_of_birth`, `gender`, `nationality`, `national_id`, `blood_type`, `enrollment_date`, `graduation_date`, `parent_name`, `parent_phone`, `parent_email`, `parent_national_id`, `emergency_contact_name`, `emergency_contact_phone`, `medical_conditions`, `profile_picture`, `documents`, `academic_year_id`, `status`, `created_at`, `updated_at`, `notes`) VALUES
(3, 16, '20250003', '20250001', 1, NULL, '', '0000-00-00', 'male', NULL, '', NULL, '2025-07-20', NULL, '', '', '', '', '', '', '', NULL, NULL, NULL, 'active', '2025-07-20 06:32:33', '2025-07-21 14:57:38', NULL),
(4, 19, '20250004', '101', 1, NULL, 'port said', '2018-06-05', 'male', NULL, '032532652154', NULL, '2025-07-21', NULL, 'ahmed', '01152458523', '<EMAIL>', '0252322523258', '', '', '', NULL, NULL, NULL, 'active', '2025-07-21 08:32:55', '2025-07-21 14:57:38', NULL);

--
-- Triggers `students`
--
DELIMITER $$
CREATE TRIGGER `update_class_student_count_delete` AFTER DELETE ON `students` FOR EACH ROW BEGIN
    IF OLD.class_id IS NOT NULL THEN
        UPDATE `classes`
        SET `current_students` = (
            SELECT COUNT(*) FROM `students`
            WHERE `class_id` = OLD.class_id AND `status` = 'active'
        )
        WHERE `id` = OLD.class_id;
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `update_class_student_count_insert` AFTER INSERT ON `students` FOR EACH ROW BEGIN
    IF NEW.class_id IS NOT NULL THEN
        UPDATE `classes`
        SET `current_students` = (
            SELECT COUNT(*) FROM `students`
            WHERE `class_id` = NEW.class_id AND `status` = 'active'
        )
        WHERE `id` = NEW.class_id;
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `update_class_student_count_update` AFTER UPDATE ON `students` FOR EACH ROW BEGIN
    -- Update old class count
    IF OLD.class_id IS NOT NULL THEN
        UPDATE `classes`
        SET `current_students` = (
            SELECT COUNT(*) FROM `students`
            WHERE `class_id` = OLD.class_id AND `status` = 'active'
        )
        WHERE `id` = OLD.class_id;
    END IF;

    -- Update new class count
    IF NEW.class_id IS NOT NULL AND NEW.class_id != OLD.class_id THEN
        UPDATE `classes`
        SET `current_students` = (
            SELECT COUNT(*) FROM `students`
            WHERE `class_id` = NEW.class_id AND `status` = 'active'
        )
        WHERE `id` = NEW.class_id;
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `student_book_orders`
--

CREATE TABLE `student_book_orders` (
  `id` int(10) UNSIGNED NOT NULL,
  `order_number` varchar(50) NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `order_date` date NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `final_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `status` enum('pending','confirmed','delivered','cancelled') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_fees`
--

CREATE TABLE `student_fees` (
  `id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `fee_type_id` int(10) UNSIGNED DEFAULT NULL,
  `fee_structure_id` int(10) UNSIGNED NOT NULL,
  `academic_year_id` int(10) UNSIGNED NOT NULL,
  `semester` enum('first','second','summer','annual') DEFAULT 'annual',
  `base_amount` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `discount_percentage` decimal(5,2) DEFAULT 0.00,
  `discount_reason` varchar(255) DEFAULT NULL,
  `late_fee` decimal(10,2) DEFAULT 0.00,
  `final_amount` decimal(10,2) NOT NULL,
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `remaining_amount` decimal(10,2) GENERATED ALWAYS AS (`final_amount` - `paid_amount`) STORED,
  `due_date` date DEFAULT NULL,
  `installment_number` tinyint(3) UNSIGNED DEFAULT 1,
  `total_installments` tinyint(3) UNSIGNED DEFAULT 1,
  `status` enum('pending','partial','paid','overdue','cancelled','refunded') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `student_fees`
--

INSERT INTO `student_fees` (`id`, `student_id`, `fee_type_id`, `fee_structure_id`, `academic_year_id`, `semester`, `base_amount`, `discount_amount`, `discount_percentage`, `discount_reason`, `late_fee`, `final_amount`, `paid_amount`, `due_date`, `installment_number`, `total_installments`, `status`, `notes`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 4, 4, 1, 1, 'first', 1000.00, 0.00, 0.00, NULL, 0.00, 1000.00, 0.00, '2025-07-21', 1, 1, 'paid', '', NULL, '2025-07-21 09:48:40', '2025-07-21 11:12:10'),
(3, 3, 3, 1, 1, 'first', 2000.00, 0.00, 0.00, NULL, 0.00, 2000.00, 2000.00, '2025-07-21', 1, 1, 'paid', '', NULL, '2025-07-21 11:42:43', '2025-07-21 11:42:43'),
(4, 4, 5, 1, 1, 'first', 700.00, 0.00, 0.00, NULL, 0.00, 700.00, 700.00, '2025-07-21', 1, 1, 'paid', 'رسوم نقل', NULL, '2025-07-21 11:43:13', '2025-07-21 11:43:13'),
(5, 4, 3, 1, 1, 'first', 1000.00, 0.00, 0.00, NULL, 0.00, 1000.00, 1000.00, '2025-07-21', 1, 1, 'paid', '', NULL, '2025-07-21 12:40:18', '2025-07-21 12:40:18');

-- --------------------------------------------------------

--
-- Table structure for table `student_grades`
--

CREATE TABLE `student_grades` (
  `id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `exam_id` int(10) UNSIGNED DEFAULT NULL,
  `subject_id` int(10) UNSIGNED NOT NULL,
  `score` decimal(5,2) NOT NULL,
  `total_marks` decimal(5,2) NOT NULL,
  `percentage` decimal(5,2) GENERATED ALWAYS AS (`score` / `total_marks` * 100) STORED,
  `grade_letter` varchar(5) DEFAULT NULL,
  `grade_points` decimal(3,2) DEFAULT NULL,
  `exam_type` enum('quiz','midterm','final','assignment','project','oral') DEFAULT 'quiz',
  `graded_by` int(10) UNSIGNED NOT NULL,
  `graded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `notes` text DEFAULT NULL,
  `status` enum('active','cancelled','revised') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `student_grades`
--

INSERT INTO `student_grades` (`id`, `student_id`, `exam_id`, `subject_id`, `score`, `total_marks`, `grade_letter`, `grade_points`, `exam_type`, `graded_by`, `graded_at`, `notes`, `status`, `created_at`, `updated_at`) VALUES
(1, 3, NULL, 6, 97.00, 100.00, 'A', NULL, 'quiz', 2, '2025-07-22 09:59:43', NULL, 'active', '2025-07-22 09:59:43', '2025-07-22 09:59:43'),
(2, 3, NULL, 7, 87.00, 100.00, 'B', NULL, 'quiz', 15, '2025-07-22 09:59:43', NULL, 'active', '2025-07-22 09:59:43', '2025-07-22 09:59:43'),
(3, 3, NULL, 10, 83.00, 100.00, 'B', NULL, 'quiz', 15, '2025-07-22 09:59:43', NULL, 'active', '2025-07-22 09:59:43', '2025-07-22 09:59:43'),
(4, 4, NULL, 6, 81.00, 100.00, 'B', NULL, 'quiz', 12, '2025-07-22 09:59:43', NULL, 'active', '2025-07-22 09:59:43', '2025-07-22 09:59:43'),
(5, 4, NULL, 7, 93.00, 100.00, 'A', NULL, 'quiz', 12, '2025-07-22 09:59:43', NULL, 'active', '2025-07-22 09:59:43', '2025-07-22 09:59:43'),
(6, 4, NULL, 10, 77.00, 100.00, 'C', NULL, 'quiz', 2, '2025-07-22 09:59:43', NULL, 'active', '2025-07-22 09:59:43', '2025-07-22 09:59:43');

-- --------------------------------------------------------

--
-- Table structure for table `student_installments`
--

CREATE TABLE `student_installments` (
  `id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `student_fee_id` int(11) UNSIGNED DEFAULT NULL,
  `installment_plan_id` int(11) UNSIGNED DEFAULT NULL,
  `fee_id` int(10) UNSIGNED DEFAULT NULL,
  `installment_number` tinyint(3) UNSIGNED NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_installments` tinyint(3) UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `due_date` date NOT NULL,
  `paid_date` date DEFAULT NULL,
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `status` enum('pending','paid','overdue','cancelled') DEFAULT 'pending',
  `payment_method` enum('cash','bank_transfer','credit_card','online') DEFAULT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `student_installments`
--

INSERT INTO `student_installments` (`id`, `student_id`, `student_fee_id`, `installment_plan_id`, `fee_id`, `installment_number`, `total_amount`, `total_installments`, `amount`, `due_date`, `paid_date`, `paid_amount`, `status`, `payment_method`, `payment_reference`, `notes`, `created_at`, `updated_at`) VALUES
(1, 3, NULL, NULL, NULL, 100, 10000.00, 1, 10000.00, '2025-07-21', NULL, 7500.00, '', NULL, NULL, 'tuition: ', '2025-07-21 08:28:33', '2025-07-21 09:15:00'),
(2, 4, NULL, NULL, NULL, 102, 10000.00, 1, 10000.00, '2025-07-21', '2025-07-21', 10000.00, 'paid', NULL, NULL, 'tuition: ', '2025-07-21 08:33:26', '2025-07-21 09:15:30');

-- --------------------------------------------------------

--
-- Table structure for table `student_payments`
--

CREATE TABLE `student_payments` (
  `id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED DEFAULT NULL,
  `student_fee_id` int(10) UNSIGNED DEFAULT NULL,
  `installment_id` int(10) UNSIGNED DEFAULT NULL,
  `payment_method` enum('cash','bank_transfer','check','card','online','mobile_payment') NOT NULL,
  `payment_type` varchar(100) DEFAULT 'دفعة عامة',
  `amount` decimal(10,2) NOT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `payment_date` date NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `check_number` varchar(50) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `receipt_number` varchar(50) NOT NULL,
  `payment_gateway` varchar(50) DEFAULT NULL,
  `gateway_transaction_id` varchar(100) DEFAULT NULL,
  `status` enum('pending','confirmed','cancelled','refunded','failed') DEFAULT 'confirmed',
  `notes` text DEFAULT NULL,
  `processed_by` int(10) UNSIGNED DEFAULT NULL,
  `processed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `confirmed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `student_payments`
--

INSERT INTO `student_payments` (`id`, `student_id`, `student_fee_id`, `installment_id`, `payment_method`, `payment_type`, `amount`, `payment_reference`, `payment_date`, `reference_number`, `bank_name`, `check_number`, `transaction_id`, `receipt_number`, `payment_gateway`, `gateway_transaction_id`, `status`, `notes`, `processed_by`, `processed_at`, `confirmed_at`, `created_at`, `updated_at`) VALUES
(19, 3, NULL, NULL, 'cash', 'دفعة عامة', 5000.00, 'PAY-********-3073', '2025-07-20', NULL, '', '', '', '', NULL, NULL, 'confirmed', '', 2, '2025-07-20 17:49:54', NULL, '2025-07-20 17:49:54', '2025-07-20 17:49:54'),
(20, 3, NULL, 1, 'cash', 'دفعة عامة', 2500.00, '', '2025-07-21', NULL, NULL, NULL, NULL, 'PAY-********-15206', NULL, NULL, 'confirmed', '', 2, '2025-07-21 09:15:00', NULL, '2025-07-21 09:15:00', '2025-07-21 09:15:00'),
(21, 4, NULL, 2, 'cash', 'دفعة عامة', 5000.00, '', '2025-07-21', NULL, NULL, NULL, NULL, 'PAY-********-21404', NULL, NULL, 'confirmed', '', 2, '2025-07-21 09:15:30', NULL, '2025-07-21 09:15:30', '2025-07-21 09:15:30'),
(25, 3, 3, NULL, 'cash', 'دفعة عامة', 2000.00, 'FEE-**************-000003', '2025-07-21', NULL, NULL, NULL, NULL, 'REC-**************-000003', NULL, NULL, 'confirmed', 'دفع رسم تلقائي - رسوم الكتب', 2, '2025-07-21 11:42:43', NULL, '2025-07-21 11:42:43', '2025-07-21 11:42:43'),
(26, 4, 4, NULL, 'cash', 'دفعة عامة', 700.00, 'FEE-********144313-000004', '2025-07-21', NULL, NULL, NULL, NULL, 'REC-********144313-000004', NULL, NULL, 'confirmed', 'دفع رسم تلقائي - رسوم النقل', 2, '2025-07-21 11:43:13', NULL, '2025-07-21 11:43:13', '2025-07-21 11:43:13'),
(29, 4, 5, NULL, 'cash', 'دفعة رسوم الكتب', 1000.00, 'PAY-********154018-000005', '2025-07-21', NULL, NULL, NULL, NULL, 'REC-********154018-000005', NULL, NULL, 'confirmed', 'دفع رسوم الكتب - دفعة رسوم الكتب', 2, '2025-07-21 12:40:18', NULL, '2025-07-21 12:40:18', '2025-07-21 12:40:18');

--
-- Triggers `student_payments`
--
DELIMITER $$
CREATE TRIGGER `update_fee_status_after_payment` AFTER INSERT ON `student_payments` FOR EACH ROW BEGIN
    DECLARE total_paid DECIMAL(10,2);
    DECLARE fee_amount DECIMAL(10,2);

    SELECT COALESCE(SUM(amount), 0) INTO total_paid
    FROM `student_payments`
    WHERE `student_fee_id` = NEW.student_fee_id AND `status` = 'confirmed';

    SELECT `final_amount` INTO fee_amount
    FROM `student_fees`
    WHERE `id` = NEW.student_fee_id;

    UPDATE `student_fees`
    SET `paid_amount` = total_paid,
        `status` = CASE
            WHEN total_paid >= fee_amount THEN 'paid'
            WHEN total_paid > 0 THEN 'partial'
            ELSE 'pending'
        END
    WHERE `id` = NEW.student_fee_id;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `subjects`
--

CREATE TABLE `subjects` (
  `id` int(10) UNSIGNED NOT NULL,
  `subject_name` varchar(100) NOT NULL,
  `subject_name_en` varchar(100) DEFAULT NULL,
  `subject_code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `credit_hours` tinyint(3) UNSIGNED DEFAULT 1,
  `department` varchar(50) DEFAULT NULL,
  `grade_levels` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`grade_levels`)),
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `stage_id` int(10) UNSIGNED DEFAULT NULL,
  `grade_id` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subjects`
--

INSERT INTO `subjects` (`id`, `subject_name`, `subject_name_en`, `subject_code`, `description`, `credit_hours`, `department`, `grade_levels`, `status`, `created_at`, `updated_at`, `stage_id`, `grade_id`) VALUES
(6, 'اللغة العربية', 'Arabic Language', 'AR', NULL, 4, 'اللغات', '[\"primary_1\",\"primary_2\",\"primary_3\",\"primary_4\",\"primary_5\",\"primary_6\",\"middle_1\",\"middle_2\",\"middle_3\",\"high_1\",\"high_2\",\"high_3\"]', 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07', NULL, NULL),
(7, 'اللغة الإنجليزية', 'English Language', 'EN', NULL, 3, 'اللغات', '[\"primary_1\",\"primary_2\",\"primary_3\",\"primary_4\",\"primary_5\",\"primary_6\",\"middle_1\",\"middle_2\",\"middle_3\",\"high_1\",\"high_2\",\"high_3\"]', 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07', NULL, NULL),
(10, 'التربية الإسلامية', 'Islamic Education', 'IE', NULL, 2, 'الدين', '[\"primary_1\",\"primary_2\",\"primary_3\",\"primary_4\",\"primary_5\",\"primary_6\",\"middle_1\",\"middle_2\",\"middle_3\",\"high_1\",\"high_2\",\"high_3\"]', 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07', NULL, NULL),
(11, 'التربية البدنية', 'Physical Education', 'PE', NULL, 1, 'التربية', '[\"primary_1\",\"primary_2\",\"primary_3\",\"primary_4\",\"primary_5\",\"primary_6\",\"middle_1\",\"middle_2\",\"middle_3\",\"high_1\",\"high_2\",\"high_3\"]', 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07', NULL, NULL),
(12, 'التربية الفنية', 'Art Education', 'ART', NULL, 1, 'التربية', '[\"primary_1\",\"primary_2\",\"primary_3\",\"primary_4\",\"primary_5\",\"primary_6\",\"middle_1\",\"middle_2\",\"middle_3\"]', 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07', NULL, NULL),
(15, 'الرياضيات', 'MATH', 'MATH101', '', 20, '', NULL, 'active', '2025-07-17 11:48:36', '2025-07-17 12:02:18', 2, 3);

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` longtext DEFAULT NULL,
  `setting_type` enum('string','number','boolean','json','text') DEFAULT 'string',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `is_editable` tinyint(1) DEFAULT 1,
  `validation_rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`validation_rules`)),
  `updated_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`, `is_editable`, `validation_rules`, `updated_by`, `updated_at`, `created_at`) VALUES
(1, 'school_name', 'المدرسة اليونانية الحديثة', 'string', 'general', 'اسم المدرسة بالعربية', 1, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(2, 'school_name_en', 'THE MODERN GREEK SCHOOL', 'string', 'general', 'اسم المدرسة بالإنجليزية', 1, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(3, 'school_address', 'بورسعيد', 'text', 'general', 'عنوان المدرسة', 1, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(4, 'school_phone', '+966-11-XXX-XXXX', 'string', 'general', 'هاتف المدرسة', 1, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(5, 'school_email', '<EMAIL>', 'string', 'general', 'بريد المدرسة الإلكتروني', 1, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(6, 'currency_symbol', 'ج.م', 'string', 'financial', 'رمز العملة', 1, 1, NULL, 2, '2025-07-21 12:07:00', '2025-07-15 04:55:07'),
(7, 'currency_code', 'EGP', 'string', 'financial', 'كود العملة', 1, 1, NULL, NULL, '2025-07-21 12:07:00', '2025-07-15 04:55:07'),
(8, 'language', 'en', 'string', 'general', 'اللغة الافتراضية', 1, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(9, 'timezone', 'Asia/Riyadh', 'string', 'general', 'المنطقة الزمنية', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(10, 'date_format', 'Y-m-d', 'string', 'general', 'تنسيق التاريخ', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(11, 'time_format', 'H:i', 'string', 'general', 'تنسيق الوقت', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(12, 'items_per_page', '20', 'number', 'general', 'عدد العناصر في الصفحة', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(13, 'max_file_size', '10485760', 'number', 'system', 'الحد الأقصى لحجم الملف (بايت)', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(14, 'allowed_file_types', '[&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;jpg&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;jpeg&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;png&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;pdf&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;doc&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;docx&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;xls&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;xlsx&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;]', 'json', 'system', 'أنواع الملفات المسموحة', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(15, 'backup_enabled', '0', 'boolean', 'system', 'تفعيل النسخ الاحتياطي', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(16, 'backup_frequency', 'daily', 'string', 'system', 'تكرار النسخ الاحتياطي', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(17, 'email_notifications', '0', 'boolean', 'notifications', 'تفعيل إشعارات البريد الإلكتروني', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(18, 'sms_notifications', '0', 'boolean', 'notifications', 'تفعيل إشعارات الرسائل النصية', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(19, 'maintenance_mode', '0', 'boolean', 'system', 'وضع الصيانة', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(20, 'registration_open', 'true', 'boolean', 'academic', 'فتح التسجيل للطلاب الجدد', 1, 1, NULL, NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(21, 'online_payment_enabled', 'true', 'boolean', 'financial', 'تفعيل الدفع الإلكتروني', 1, 1, NULL, NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(22, 'grade_scale', '{\"A+\":95,\"A\":90,\"B+\":85,\"B\":80,\"C+\":75,\"C\":70,\"D+\":65,\"D\":60,\"F\":0}', 'json', 'academic', 'سلم الدرجات', 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-15 04:55:07'),
(25, 'school_description', '', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(26, 'school_description_en', '', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(30, 'school_website', '', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(31, 'academic_year', '', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(32, 'semester', '', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(44, 'registration_enabled', '0', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(47, 'session_timeout', '3600', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(48, 'password_min_length', '8', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(49, 'login_attempts', '5', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(50, 'lockout_duration', '900', 'string', 'general', NULL, 0, 1, NULL, 2, '2025-07-20 09:21:42', '2025-07-16 08:31:03'),
(402, 'currency_name', 'جنيه مصري', 'string', 'general', NULL, 0, 1, NULL, NULL, '2025-07-21 12:07:00', '2025-07-21 12:07:00');

-- --------------------------------------------------------

--
-- Table structure for table `teachers`
--

CREATE TABLE `teachers` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `employee_id` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `qualification` varchar(100) DEFAULT NULL,
  `specialization` varchar(100) DEFAULT NULL,
  `experience_years` tinyint(3) UNSIGNED DEFAULT 0,
  `hire_date` date DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `bank_account` varchar(50) DEFAULT NULL,
  `emergency_contact_name` varchar(100) DEFAULT NULL,
  `emergency_contact_phone` varchar(20) DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `documents` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`documents`)),
  `status` enum('active','inactive','on_leave','terminated') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `teachers`
--

INSERT INTO `teachers` (`id`, `user_id`, `employee_id`, `phone`, `address`, `date_of_birth`, `gender`, `nationality`, `national_id`, `qualification`, `specialization`, `experience_years`, `hire_date`, `department`, `salary`, `bank_account`, `emergency_contact_name`, `emergency_contact_phone`, `profile_picture`, `documents`, `status`, `created_at`, `updated_at`) VALUES
(1, 12, '200', NULL, 'port ghlip', '2018-11-15', 'male', NULL, '************', 'بكاليريوس حاسب الى', 'حاسب الى', 5, '0000-00-00', 'حاسب الى', 5000.00, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-16 07:22:59', '2025-07-16 07:22:59');

-- --------------------------------------------------------

--
-- Table structure for table `teacher_assignments`
--

CREATE TABLE `teacher_assignments` (
  `id` int(10) UNSIGNED NOT NULL,
  `teacher_id` int(10) UNSIGNED NOT NULL,
  `class_id` int(10) UNSIGNED NOT NULL,
  `subject_id` int(10) UNSIGNED NOT NULL,
  `academic_year_id` int(10) UNSIGNED NOT NULL,
  `semester` enum('first','second','summer') DEFAULT 'first',
  `is_class_teacher` tinyint(1) DEFAULT 0,
  `weekly_hours` tinyint(3) UNSIGNED DEFAULT 1,
  `status` enum('active','inactive','completed') DEFAULT 'active',
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `teacher_assignments`
--

INSERT INTO `teacher_assignments` (`id`, `teacher_id`, `class_id`, `subject_id`, `academic_year_id`, `semester`, `is_class_teacher`, `weekly_hours`, `status`, `assigned_at`, `created_at`, `updated_at`) VALUES
(10, 1, 0, 7, 0, 'first', 0, 1, 'active', '2025-07-22 10:23:13', '2025-07-22 10:23:13', '2025-07-22 10:23:13');

-- --------------------------------------------------------

--
-- Table structure for table `teacher_attendance`
--

CREATE TABLE `teacher_attendance` (
  `id` int(10) UNSIGNED NOT NULL,
  `teacher_id` int(10) UNSIGNED NOT NULL,
  `attendance_date` date NOT NULL,
  `status` enum('present','absent','late','excused','sick_leave','regular_leave') NOT NULL DEFAULT 'present',
  `check_in_time` time DEFAULT NULL,
  `check_out_time` time DEFAULT NULL,
  `notes` varchar(255) DEFAULT NULL,
  `recorded_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `teacher_attendance`
--

INSERT INTO `teacher_attendance` (`id`, `teacher_id`, `attendance_date`, `status`, `check_in_time`, `check_out_time`, `notes`, `recorded_by`, `created_at`, `updated_at`) VALUES
(1, 1, '2025-07-16', 'absent', '08:00:00', '11:00:00', '', 2, '2025-07-16 10:24:48', '2025-07-16 10:24:48'),
(2, 1, '2025-07-17', 'absent', '00:00:00', '00:00:00', '', 2, '2025-07-17 11:46:53', '2025-07-17 11:46:53'),
(3, 1, '2025-07-20', 'sick_leave', '00:00:00', '00:00:00', '', 15, '2025-07-20 11:34:06', '2025-07-20 13:24:49');

-- --------------------------------------------------------

--
-- Table structure for table `uploaded_files`
--

CREATE TABLE `uploaded_files` (
  `id` int(10) UNSIGNED NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(10) UNSIGNED NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `file_hash` varchar(64) DEFAULT NULL,
  `directory` varchar(100) DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `uploaded_by` int(10) UNSIGNED DEFAULT NULL,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(10) UNSIGNED NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','teacher','student','staff') NOT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(255) DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `login_attempts` tinyint(3) UNSIGNED DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`preferences`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `username`, `email`, `phone`, `gender`, `profile_picture`, `password`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `last_activity`, `login_attempts`, `locked_until`, `preferences`, `created_at`, `updated_at`) VALUES
(2, 'Admin', '', '<EMAIL>', NULL, NULL, NULL, '$2y$10$oLcJPz0TySZ.p09zGQpliO2NUbriQPF//iKTAO2Vjtz4C.39Pkvni', 'admin', 'active', 0, NULL, NULL, NULL, '2025-07-22 12:48:41', NULL, 0, NULL, NULL, '2025-07-15 06:24:50', '2025-07-22 09:48:41'),
(12, 'misho kamel', 'misho', '<EMAIL>', '01252458521', NULL, NULL, '$2y$10$lgS8JmTOrvnF3eA3q5mM0ug8eijGwTmGBIxPInQG5IFXlLPDzz5CO', 'teacher', 'active', 0, NULL, NULL, NULL, '2025-07-20 12:16:03', NULL, 0, NULL, NULL, '2025-07-16 07:22:59', '2025-07-20 09:16:03'),
(15, 'System Administrator', 'admin', '<EMAIL>', NULL, NULL, NULL, '$2y$10$ClA0dk78azfXehTpZRXXge4/6UPtMuae5m/8hURj4.o2JLQvhm6XG', 'admin', 'active', 0, NULL, NULL, NULL, '2025-07-20 11:06:55', NULL, 0, NULL, NULL, '2025-07-20 06:09:13', '2025-07-20 09:35:31'),
(16, 'مارتن', 'martin', '<EMAIL>', '', NULL, NULL, '$2y$10$o8J7JpEk9tXmumSC6CkIIusOxJmMS3r4cEhq5lfqvvtJu94mUVOGy', 'student', 'active', 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-07-20 06:32:33', '2025-07-20 06:32:33'),
(17, 'amir kamel', '', '<EMAIL>', NULL, NULL, NULL, '$2y$10$43STueqZLD.HFIbORKRwEOkzzBfb4ohLibQATVg16jbqpuThqYIVW', 'staff', 'active', 0, NULL, NULL, NULL, '2025-07-20 12:15:30', NULL, 0, NULL, NULL, '2025-07-20 09:13:06', '2025-07-20 09:15:30'),
(18, 'ahmed mon', '', '<EMAIL>', NULL, NULL, NULL, '$2y$10$HL8.7VyS2iDK8EFok9msY.He3.uCjS47wgMrH0WlVmTx2vJwS3PKa', 'staff', 'active', 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-07-20 09:45:08', '2025-07-20 09:45:08'),
(19, 'zyad ahmed', 'zyad', '<EMAIL>', '012325452652', NULL, NULL, '$2y$10$lm7LQ.bfO42UqAdsPE2xjOAPJdhnsyqvx28.Myb.q/BmFXhX7ycYa', 'student', 'active', 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-07-21 08:32:55', '2025-07-21 08:32:55');

-- --------------------------------------------------------

--
-- Table structure for table `daily_expenses`
--

CREATE TABLE `daily_expenses` (
  `id` int(10) UNSIGNED NOT NULL,
  `expense_date` date NOT NULL,
  `category_id` int(10) UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `receipt_number` varchar(100) DEFAULT NULL,
  `payment_method` enum('cash','bank_transfer','check','credit_card') DEFAULT 'cash',
  `vendor_name` varchar(200) DEFAULT NULL,
  `vendor_phone` varchar(20) DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'approved',
  `approved_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_by` int(10) UNSIGNED NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int(10) UNSIGNED NOT NULL,
  `expense_category_id` int(10) UNSIGNED DEFAULT NULL,
  `expense_name` varchar(200) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `expense_date` date NOT NULL,
  `payment_method` enum('cash','bank_transfer','check','credit_card') DEFAULT 'cash',
  `reference_number` varchar(100) DEFAULT NULL,
  `vendor_name` varchar(200) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `receipt_file` varchar(255) DEFAULT NULL,
  `approved_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `status` enum('pending','approved','rejected','paid') DEFAULT 'pending',
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expense_categories`
--

CREATE TABLE `expense_categories` (
  `id` int(10) UNSIGNED NOT NULL,
  `category_name` varchar(100) NOT NULL,
  `category_code` varchar(20) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-money-bill',
  `color` varchar(20) DEFAULT '#007bff',
  `daily_limit` decimal(10,2) DEFAULT NULL,
  `monthly_limit` decimal(10,2) DEFAULT NULL,
  `budget_limit` decimal(10,2) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expense_categories`
--

INSERT INTO `expense_categories` (`id`, `category_name`, `category_code`, `description`, `icon`, `color`, `daily_limit`, `monthly_limit`, `budget_limit`, `is_active`, `status`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'مصروفات إدارية', 'ADMIN', 'قرطاسية ومستلزمات مكتبية', 'fas fa-clipboard-list', '#007bff', 500.00, 15000.00, NULL, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(2, 'مصروفات تشغيلية', 'UTILITIES', 'كهرباء ومياه وغاز', 'fas fa-bolt', '#28a745', 1000.00, 30000.00, NULL, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(3, 'مصروفات صيانة', 'MAINTENANCE', 'إصلاحات وقطع غيار', 'fas fa-tools', '#ffc107', 800.00, 25000.00, NULL, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(4, 'مصروفات نظافة', 'CLEANING', 'مواد تنظيف ومستلزمات', 'fas fa-broom', '#17a2b8', 300.00, 10000.00, NULL, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(5, 'مصروفات نقل', 'TRANSPORT', 'وقود ومواصلات', 'fas fa-car', '#dc3545', 600.00, 20000.00, NULL, 1, 'active', NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `exam_results`
--

CREATE TABLE `exam_results` (
  `id` int(10) UNSIGNED NOT NULL,
  `exam_id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `obtained_marks` decimal(5,2) DEFAULT 0.00,
  `percentage` decimal(5,2) DEFAULT 0.00,
  `grade` varchar(5) DEFAULT NULL,
  `status` enum('pass','fail','absent','pending') DEFAULT 'pending',
  `answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`answers`)),
  `start_time` timestamp NULL DEFAULT NULL,
  `submit_time` timestamp NULL DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `installments`
--

CREATE TABLE `installments` (
  `id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `fee_type_id` int(10) UNSIGNED NOT NULL,
  `academic_year_id` int(10) UNSIGNED NOT NULL,
  `installment_number` tinyint(3) UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `due_date` date NOT NULL,
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `paid_date` date DEFAULT NULL,
  `late_fee` decimal(10,2) DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `status` enum('pending','paid','overdue','cancelled') DEFAULT 'pending',
  `payment_method` enum('cash','bank_transfer','check','credit_card','online') DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` int(10) UNSIGNED NOT NULL,
  `student_id` int(10) UNSIGNED NOT NULL,
  `fee_type_id` int(10) UNSIGNED DEFAULT NULL,
  `installment_id` int(10) UNSIGNED DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` enum('cash','bank_transfer','check','credit_card','online') DEFAULT 'cash',
  `transaction_id` varchar(100) DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `check_number` varchar(50) DEFAULT NULL,
  `discount_applied` decimal(10,2) DEFAULT 0.00,
  `late_fee_paid` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `status` enum('pending','completed','failed','refunded') DEFAULT 'completed',
  `processed_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `staff_absences_with_deduction`
--

CREATE TABLE `staff_absences_with_deduction` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `absence_date` date NOT NULL,
  `absence_type` enum('sick','personal','emergency','unauthorized') NOT NULL DEFAULT 'unauthorized',
  `deduction_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `reason` text DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `approved_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `processed_by` int(10) UNSIGNED DEFAULT NULL,
  `recorded_by` int(10) UNSIGNED DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `deduction_settings`
--

CREATE TABLE `deduction_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `absence_type` enum('sick','personal','emergency','unauthorized') NOT NULL,
  `deduction_type` enum('fixed','percentage','daily_rate') NOT NULL DEFAULT 'fixed',
  `deduction_value` decimal(10,2) NOT NULL DEFAULT 0.00,
  `max_allowed_per_month` int(10) UNSIGNED DEFAULT NULL,
  `requires_approval` tinyint(1) DEFAULT 1,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `deduction_settings`
--

INSERT INTO `deduction_settings` (`id`, `absence_type`, `deduction_type`, `deduction_value`, `max_allowed_per_month`, `requires_approval`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'unauthorized', 'daily_rate', 100.00, NULL, 1, 'خصم للغياب بدون عذر - معدل يومي', 1, '2025-07-29 12:00:00', '2025-07-29 12:00:00'),
(2, 'personal', 'daily_rate', 50.00, 3, 1, 'خصم للغياب الشخصي - حد أقصى 3 أيام شهرياً', 1, '2025-07-29 12:00:00', '2025-07-29 12:00:00'),
(3, 'sick', 'fixed', 0.00, 5, 0, 'إجازة مرضية - بدون خصم حتى 5 أيام شهرياً', 1, '2025-07-29 12:00:00', '2025-07-29 12:00:00'),
(4, 'emergency', 'percentage', 25.00, 2, 1, 'غياب طارئ - خصم 25% من الراتب اليومي', 1, '2025-07-29 12:00:00', '2025-07-29 12:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext DEFAULT NULL,
  `last_activity` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `staff_permissions`
--

CREATE TABLE `staff_permissions` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `permission_type_id` int(10) UNSIGNED NOT NULL,
  `permission_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `duration_hours` decimal(4,2) NOT NULL,
  `reason` text NOT NULL,
  `medical_certificate` varchar(255) DEFAULT NULL,
  `replacement_user_id` int(10) UNSIGNED DEFAULT NULL,
  `replacement_notes` text DEFAULT NULL,
  `status` enum('pending','approved','rejected','cancelled') DEFAULT 'pending',
  `workflow_step` tinyint(3) UNSIGNED DEFAULT 1,
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `applied_by` int(10) UNSIGNED NOT NULL,
  `approved_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_by` int(10) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `cancelled_by` int(10) UNSIGNED DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` text DEFAULT NULL,
  `manager_notes` text DEFAULT NULL,
  `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
  `affects_attendance` tinyint(1) DEFAULT 1,
  `salary_deduction` decimal(8,2) DEFAULT 0.00,
  `notification_sent` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `approval_workflow`
--

CREATE TABLE `approval_workflow` (
  `id` int(10) UNSIGNED NOT NULL,
  `request_type` enum('leave','permission') NOT NULL,
  `request_id` int(10) UNSIGNED NOT NULL,
  `step_number` tinyint(3) UNSIGNED NOT NULL,
  `approver_role` varchar(50) NOT NULL,
  `approver_id` int(10) UNSIGNED DEFAULT NULL,
  `status` enum('pending','approved','rejected','skipped') DEFAULT 'pending',
  `comments` text DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `leave_notifications`
--

CREATE TABLE `leave_notifications` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `request_type` enum('leave','permission') NOT NULL,
  `request_id` int(10) UNSIGNED NOT NULL,
  `notification_type` enum('request_submitted','approved','rejected','cancelled','reminder') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `academic_years`
--
ALTER TABLE `academic_years`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `year_name` (`year_name`),
  ADD KEY `idx_year_name` (`year_name`),
  ADD KEY `idx_is_current` (`is_current`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_table_name` (`table_name`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_user_action` (`user_id`,`action`);

--
-- Indexes for table `admin_attendance`
--
ALTER TABLE `admin_attendance`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `recorded_by` (`recorded_by`);

--
-- Indexes for table `attendance`
--
ALTER TABLE `attendance`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_student_date_period` (`student_id`,`attendance_date`,`period_number`),
  ADD KEY `subject_id` (`subject_id`),
  ADD KEY `teacher_id` (`teacher_id`),
  ADD KEY `academic_year_id` (`academic_year_id`),
  ADD KEY `marked_by` (`marked_by`),
  ADD KEY `idx_attendance_date` (`attendance_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_student_date` (`student_id`,`attendance_date`),
  ADD KEY `idx_class_date` (`class_id`,`attendance_date`),
  ADD KEY `idx_attendance_student_date_status` (`student_id`,`attendance_date`,`status`);

--
-- Indexes for table `backups`
--
ALTER TABLE `backups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_backup_type` (`backup_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_created_by` (`created_by`);

--
-- Indexes for table `bank_accounts`
--
ALTER TABLE `bank_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `account_number` (`account_number`),
  ADD KEY `bank_name` (`bank_name`),
  ADD KEY `account_type` (`account_type`),
  ADD KEY `is_active` (`is_active`);

--
-- Indexes for table `books`
--
ALTER TABLE `books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_book_code` (`book_code`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `classes`
--
ALTER TABLE `classes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_grade_level` (`grade_level`),
  ADD KEY `idx_class_teacher_id` (`class_teacher_id`),
  ADD KEY `idx_academic_year_id` (`academic_year_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_stage_id` (`stage_id`),
  ADD KEY `idx_grade_id` (`grade_id`),
  ADD KEY `idx_teacher_id` (`teacher_id`);

--
-- Indexes for table `discounts`
--
ALTER TABLE `discounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `discount_code` (`discount_code`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_discount_code` (`discount_code`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_start_date` (`start_date`),
  ADD KEY `idx_end_date` (`end_date`);

--
-- Indexes for table `educational_stages`
--
ALTER TABLE `educational_stages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `stage_code` (`stage_code`),
  ADD KEY `idx_stage_name` (`stage_name`),
  ADD KEY `idx_sort_order` (`sort_order`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `error_logs`
--
ALTER TABLE `error_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `resolved_by` (`resolved_by`),
  ADD KEY `idx_error_type` (`error_type`),
  ADD KEY `idx_severity` (`severity`),
  ADD KEY `idx_is_resolved` (`is_resolved`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- Indexes for table `exams`
--
ALTER TABLE `exams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subject_id` (`subject_id`),
  ADD KEY `idx_exam_date` (`exam_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_academic_year` (`academic_year_id`),
  ADD KEY `idx_class_subject` (`class_id`,`subject_id`),
  ADD KEY `idx_teacher_id` (`teacher_id`),
  ADD KEY `idx_exams_class_subject_date` (`class_id`,`subject_id`,`exam_date`);

--
-- Indexes for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `student_id` (`student_id`);

--
-- Indexes for table `exam_questions`
--
ALTER TABLE `exam_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exam_id` (`exam_id`);

--
-- Indexes for table `fee_categories`
--
ALTER TABLE `fee_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `category_code` (`category_code`),
  ADD KEY `idx_category_code` (`category_code`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_is_mandatory` (`is_mandatory`);

--
-- Indexes for table `fee_structures`
--
ALTER TABLE `fee_structures`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fee_category_id` (`fee_category_id`),
  ADD KEY `class_id` (`class_id`),
  ADD KEY `idx_academic_year` (`academic_year_id`),
  ADD KEY `idx_grade_level` (`grade_level`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `fee_types`
--
ALTER TABLE `fee_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `fee_type_classes`
--
ALTER TABLE `fee_type_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `fee_type_class_unique` (`fee_type_id`,`class_id`),
  ADD KEY `fee_type_id` (`fee_type_id`),
  ADD KEY `class_id` (`class_id`),
  ADD KEY `is_active` (`is_active`);

--
-- Indexes for table `grades`
--
ALTER TABLE `grades`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `grade_code` (`grade_code`),
  ADD UNIQUE KEY `stage_sort` (`stage_id`,`sort_order`),
  ADD KEY `idx_grade_name` (`grade_name`),
  ADD KEY `idx_stage_id` (`stage_id`),
  ADD KEY `idx_sort_order` (`sort_order`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `installment_payments`
--
ALTER TABLE `installment_payments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `installment_plans`
--
ALTER TABLE `installment_plans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `leave_settings`
--
ALTER TABLE `leave_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_leave_type` (`leave_type`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_ip` (`ip_address`),
  ADD KEY `idx_attempted_at` (`attempted_at`),
  ADD KEY `idx_ip_attempted` (`ip_address`,`attempted_at`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_is_global` (`is_global`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_expires` (`expires_at`),
  ADD KEY `idx_user_expires` (`user_id`,`expires_at`);

--
-- Indexes for table `staff`
--
ALTER TABLE `staff`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD UNIQUE KEY `employee_id` (`employee_id`),
  ADD UNIQUE KEY `national_id` (`national_id`),
  ADD KEY `idx_employee_id` (`employee_id`),
  ADD KEY `idx_department` (`department`),
  ADD KEY `idx_national_id` (`national_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `staff_leaves`
--
ALTER TABLE `staff_leaves`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_user_type` (`user_type`),
  ADD KEY `idx_leave_type` (`leave_type`),
  ADD KEY `idx_start_date` (`start_date`),
  ADD KEY `idx_end_date` (`end_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_applied_by` (`applied_by`),
  ADD KEY `idx_approved_by` (`approved_by`),
  ADD KEY `idx_date_range` (`start_date`,`end_date`);

--
-- Indexes for table `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD UNIQUE KEY `student_id` (`student_id`),
  ADD UNIQUE KEY `national_id` (`national_id`),
  ADD KEY `academic_year_id` (`academic_year_id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_class_id` (`class_id`),
  ADD KEY `idx_national_id` (`national_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_enrollment_date` (`enrollment_date`),
  ADD KEY `idx_students_class_status_year` (`class_id`,`status`,`academic_year_id`);

--
-- Indexes for table `student_book_orders`
--
ALTER TABLE `student_book_orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_order_number` (`order_number`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `student_fees`
--
ALTER TABLE `student_fees`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fee_structure_id` (`fee_structure_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_academic_year` (`academic_year_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_student_year` (`student_id`,`academic_year_id`),
  ADD KEY `idx_student_fees_student_year_status` (`student_id`,`academic_year_id`,`status`);

--
-- Indexes for table `student_grades`
--
ALTER TABLE `student_grades`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_exam_id` (`exam_id`),
  ADD KEY `idx_subject_id` (`subject_id`),
  ADD KEY `idx_graded_by` (`graded_by`),
  ADD KEY `idx_graded_at` (`graded_at`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `student_installments`
--
ALTER TABLE `student_installments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `fk_installment_fee` (`student_fee_id`),
  ADD KEY `fk_installment_plan` (`installment_plan_id`);

--
-- Indexes for table `student_payments`
--
ALTER TABLE `student_payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `receipt_number` (`receipt_number`),
  ADD KEY `student_fee_id` (`student_fee_id`),
  ADD KEY `processed_by` (`processed_by`),
  ADD KEY `idx_payment_date` (`payment_date`),
  ADD KEY `idx_receipt_number` (`receipt_number`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_reference_number` (`reference_number`),
  ADD KEY `idx_payments_date_status_amount` (`payment_date`,`status`,`amount`);

--
-- Indexes for table `subjects`
--
ALTER TABLE `subjects`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subject_code` (`subject_code`),
  ADD KEY `idx_subject_code` (`subject_code`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_department` (`department`),
  ADD KEY `idx_stage_id` (`stage_id`),
  ADD KEY `idx_grade_id` (`grade_id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `idx_setting_key` (`setting_key`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_is_public` (`is_public`);

--
-- Indexes for table `teachers`
--
ALTER TABLE `teachers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD UNIQUE KEY `employee_id` (`employee_id`),
  ADD UNIQUE KEY `national_id` (`national_id`),
  ADD KEY `idx_employee_id` (`employee_id`),
  ADD KEY `idx_national_id` (`national_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_specialization` (`specialization`),
  ADD KEY `idx_teachers_status_specialization` (`status`,`specialization`);

--
-- Indexes for table `teacher_assignments`
--
ALTER TABLE `teacher_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_assignment` (`teacher_id`,`class_id`,`subject_id`,`academic_year_id`,`semester`),
  ADD KEY `idx_teacher_id` (`teacher_id`),
  ADD KEY `idx_class_id` (`class_id`),
  ADD KEY `idx_subject_id` (`subject_id`),
  ADD KEY `idx_academic_year` (`academic_year_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_assignments_teacher_year_status` (`teacher_id`,`academic_year_id`,`status`);

--
-- Indexes for table `teacher_attendance`
--
ALTER TABLE `teacher_attendance`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teacher_id` (`teacher_id`),
  ADD KEY `recorded_by` (`recorded_by`);

--
-- Indexes for table `uploaded_files`
--
ALTER TABLE `uploaded_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_filename` (`filename`),
  ADD KEY `idx_file_hash` (`file_hash`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_uploaded_by` (`uploaded_by`),
  ADD KEY `idx_upload_date` (`upload_date`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_role_status` (`role`,`status`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- Indexes for table `daily_expenses`
--
ALTER TABLE `daily_expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `expense_date` (`expense_date`),
  ADD KEY `status` (`status`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `approved_by` (`approved_by`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `expense_category_id` (`expense_category_id`),
  ADD KEY `expense_date` (`expense_date`),
  ADD KEY `status` (`status`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `approved_by` (`approved_by`);

--
-- Indexes for table `expense_categories`
--
ALTER TABLE `expense_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `category_code` (`category_code`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `exam_results`
--
ALTER TABLE `exam_results`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `exam_student_unique` (`exam_id`,`student_id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `installments`
--
ALTER TABLE `installments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `fee_type_id` (`fee_type_id`),
  ADD KEY `academic_year_id` (`academic_year_id`),
  ADD KEY `due_date` (`due_date`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `fee_type_id` (`fee_type_id`),
  ADD KEY `installment_id` (`installment_id`),
  ADD KEY `payment_date` (`payment_date`),
  ADD KEY `status` (`status`),
  ADD KEY `processed_by` (`processed_by`);

--
-- Indexes for table `staff_absences_with_deduction`
--
ALTER TABLE `staff_absences_with_deduction`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `absence_date` (`absence_date`),
  ADD KEY `absence_type` (`absence_type`),
  ADD KEY `status` (`status`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `deduction_settings`
--
ALTER TABLE `deduction_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `absence_type` (`absence_type`),
  ADD KEY `is_active` (`is_active`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- Indexes for table `leave_types`
--
ALTER TABLE `leave_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `idx_name` (`name`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `permission_types`
--
ALTER TABLE `permission_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `idx_name` (`name`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `employee_leave_balances`
--
ALTER TABLE `employee_leave_balances`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_leave_year` (`user_id`,`leave_type_id`,`year`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_leave_type_id` (`leave_type_id`),
  ADD KEY `idx_year` (`year`);

--
-- Indexes for table `staff_permissions`
--
ALTER TABLE `staff_permissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_permission_type_id` (`permission_type_id`),
  ADD KEY `idx_permission_date` (`permission_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_applied_by` (`applied_by`),
  ADD KEY `idx_approved_by` (`approved_by`),
  ADD KEY `idx_user_date` (`user_id`,`permission_date`);

--
-- Indexes for table `approval_workflow`
--
ALTER TABLE `approval_workflow`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_request` (`request_type`,`request_id`),
  ADD KEY `idx_approver_id` (`approver_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_step_number` (`step_number`);

--
-- Indexes for table `leave_notifications`
--
ALTER TABLE `leave_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_request` (`request_type`,`request_id`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `academic_years`
--
ALTER TABLE `academic_years`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=119;

--
-- AUTO_INCREMENT for table `admin_attendance`
--
ALTER TABLE `admin_attendance`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `attendance`
--
ALTER TABLE `attendance`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `backups`
--
ALTER TABLE `backups`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bank_accounts`
--
ALTER TABLE `bank_accounts`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `books`
--
ALTER TABLE `books`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `classes`
--
ALTER TABLE `classes`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `discounts`
--
ALTER TABLE `discounts`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `educational_stages`
--
ALTER TABLE `educational_stages`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `error_logs`
--
ALTER TABLE `error_logs`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `exams`
--
ALTER TABLE `exams`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exam_questions`
--
ALTER TABLE `exam_questions`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `fee_categories`
--
ALTER TABLE `fee_categories`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `fee_structures`
--
ALTER TABLE `fee_structures`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `fee_types`
--
ALTER TABLE `fee_types`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `fee_type_classes`
--
ALTER TABLE `fee_type_classes`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `grades`
--
ALTER TABLE `grades`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `installment_payments`
--
ALTER TABLE `installment_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `installment_plans`
--
ALTER TABLE `installment_plans`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `leave_settings`
--
ALTER TABLE `leave_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `staff`
--
ALTER TABLE `staff`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `staff_leaves`
--
ALTER TABLE `staff_leaves`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `students`
--
ALTER TABLE `students`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `student_book_orders`
--
ALTER TABLE `student_book_orders`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_fees`
--
ALTER TABLE `student_fees`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `student_grades`
--
ALTER TABLE `student_grades`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `student_installments`
--
ALTER TABLE `student_installments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `student_payments`
--
ALTER TABLE `student_payments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `subjects`
--
ALTER TABLE `subjects`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=403;

--
-- AUTO_INCREMENT for table `teachers`
--
ALTER TABLE `teachers`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `teacher_assignments`
--
ALTER TABLE `teacher_assignments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `teacher_attendance`
--
ALTER TABLE `teacher_attendance`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `uploaded_files`
--
ALTER TABLE `uploaded_files`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `daily_expenses`
--
ALTER TABLE `daily_expenses`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expense_categories`
--
ALTER TABLE `expense_categories`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `exam_results`
--
ALTER TABLE `exam_results`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `installments`
--
ALTER TABLE `installments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `staff_absences_with_deduction`
--
ALTER TABLE `staff_absences_with_deduction`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `deduction_settings`
--
ALTER TABLE `deduction_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `leave_types`
--
ALTER TABLE `leave_types`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `permission_types`
--
ALTER TABLE `permission_types`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `employee_leave_balances`
--
ALTER TABLE `employee_leave_balances`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `staff_permissions`
--
ALTER TABLE `staff_permissions`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `approval_workflow`
--
ALTER TABLE `approval_workflow`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `leave_notifications`
--
ALTER TABLE `leave_notifications`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `admin_attendance`
--
ALTER TABLE `admin_attendance`
  ADD CONSTRAINT `admin_attendance_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `admin_attendance_ibfk_2` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `attendance`
--
ALTER TABLE `attendance`
  ADD CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `attendance_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `attendance_ibfk_3` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `attendance_ibfk_4` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `attendance_ibfk_5` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `attendance_ibfk_6` FOREIGN KEY (`marked_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `backups`
--
ALTER TABLE `backups`
  ADD CONSTRAINT `backups_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `classes`
--
ALTER TABLE `classes`
  ADD CONSTRAINT `fk_classes_academic_year` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_classes_grade` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_classes_stage` FOREIGN KEY (`stage_id`) REFERENCES `educational_stages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_classes_teacher_new` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `discounts`
--
ALTER TABLE `discounts`
  ADD CONSTRAINT `discounts_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `error_logs`
--
ALTER TABLE `error_logs`
  ADD CONSTRAINT `error_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `error_logs_ibfk_2` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `exams`
--
ALTER TABLE `exams`
  ADD CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exams_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exams_ibfk_3` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exams_ibfk_4` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD CONSTRAINT `exam_attempts_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exam_attempts_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `exam_questions`
--
ALTER TABLE `exam_questions`
  ADD CONSTRAINT `exam_questions_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `fee_structures`
--
ALTER TABLE `fee_structures`
  ADD CONSTRAINT `fee_structures_ibfk_1` FOREIGN KEY (`fee_category_id`) REFERENCES `fee_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fee_structures_ibfk_2` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fee_structures_ibfk_3` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `fee_type_classes`
--
ALTER TABLE `fee_type_classes`
  ADD CONSTRAINT `fee_type_classes_fee_type_id_foreign` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fee_type_classes_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `grades`
--
ALTER TABLE `grades`
  ADD CONSTRAINT `fk_grades_stage` FOREIGN KEY (`stage_id`) REFERENCES `educational_stages` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  ADD CONSTRAINT `remember_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff`
--
ALTER TABLE `staff`
  ADD CONSTRAINT `staff_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_leaves`
--
ALTER TABLE `staff_leaves`
  ADD CONSTRAINT `fk_staff_leaves_applied_by` FOREIGN KEY (`applied_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_staff_leaves_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_staff_leaves_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `students`
--
ALTER TABLE `students`
  ADD CONSTRAINT `students_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `students_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `students_ibfk_3` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `student_fees`
--
ALTER TABLE `student_fees`
  ADD CONSTRAINT `student_fees_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_fees_ibfk_2` FOREIGN KEY (`fee_structure_id`) REFERENCES `fee_structures` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_fees_ibfk_3` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_fees_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `student_grades`
--
ALTER TABLE `student_grades`
  ADD CONSTRAINT `fk_student_grades_exam` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_student_grades_graded_by` FOREIGN KEY (`graded_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `fk_student_grades_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_student_grades_subject` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_installments`
--
ALTER TABLE `student_installments`
  ADD CONSTRAINT `fk_installment_fee` FOREIGN KEY (`student_fee_id`) REFERENCES `student_fees` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_installment_plan` FOREIGN KEY (`installment_plan_id`) REFERENCES `installment_plans` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `student_payments`
--
ALTER TABLE `student_payments`
  ADD CONSTRAINT `student_payments_ibfk_1` FOREIGN KEY (`student_fee_id`) REFERENCES `student_fees` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_payments_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `subjects`
--
ALTER TABLE `subjects`
  ADD CONSTRAINT `fk_subjects_grade` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_subjects_stage` FOREIGN KEY (`stage_id`) REFERENCES `educational_stages` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `teachers`
--
ALTER TABLE `teachers`
  ADD CONSTRAINT `teachers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `teacher_attendance`
--
ALTER TABLE `teacher_attendance`
  ADD CONSTRAINT `teacher_attendance_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`),
  ADD CONSTRAINT `teacher_attendance_ibfk_2` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `uploaded_files`
--
ALTER TABLE `uploaded_files`
  ADD CONSTRAINT `uploaded_files_ibfk_1` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `daily_expenses`
--
ALTER TABLE `daily_expenses`
  ADD CONSTRAINT `daily_expenses_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `expense_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `daily_expenses_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `daily_expenses_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `expenses`
--
ALTER TABLE `expenses`
  ADD CONSTRAINT `expenses_expense_category_id_foreign` FOREIGN KEY (`expense_category_id`) REFERENCES `expense_categories` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `expenses_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `expenses_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `exam_results`
--
ALTER TABLE `exam_results`
  ADD CONSTRAINT `exam_results_exam_id_foreign` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exam_results_student_id_foreign` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `installments`
--
ALTER TABLE `installments`
  ADD CONSTRAINT `installments_student_id_foreign` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `installments_fee_type_id_foreign` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `installments_academic_year_id_foreign` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_student_id_foreign` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_fee_type_id_foreign` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `payments_installment_id_foreign` FOREIGN KEY (`installment_id`) REFERENCES `installments` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `payments_processed_by_foreign` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `staff_absences_with_deduction`
--
ALTER TABLE `staff_absences_with_deduction`
  ADD CONSTRAINT `staff_absences_with_deduction_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_absences_with_deduction_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_absences_with_deduction_processed_by_foreign` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_absences_with_deduction_recorded_by_foreign` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_absences_with_deduction_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `leave_types`
--
-- No foreign key constraints needed for leave_types

--
-- Constraints for table `permission_types`
--
-- No foreign key constraints needed for permission_types

--
-- Constraints for table `employee_leave_balances`
--
ALTER TABLE `employee_leave_balances`
  ADD CONSTRAINT `employee_leave_balances_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `employee_leave_balances_leave_type_id_foreign` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_leaves`
--
ALTER TABLE `staff_leaves`
  ADD CONSTRAINT `staff_leaves_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_leaves_leave_type_id_foreign` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types` (`id`) ON DELETE RESTRICT,
  ADD CONSTRAINT `staff_leaves_replacement_user_id_foreign` FOREIGN KEY (`replacement_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_leaves_applied_by_foreign` FOREIGN KEY (`applied_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  ADD CONSTRAINT `staff_leaves_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_leaves_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_leaves_cancelled_by_foreign` FOREIGN KEY (`cancelled_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `staff_permissions`
--
ALTER TABLE `staff_permissions`
  ADD CONSTRAINT `staff_permissions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_permissions_permission_type_id_foreign` FOREIGN KEY (`permission_type_id`) REFERENCES `permission_types` (`id`) ON DELETE RESTRICT,
  ADD CONSTRAINT `staff_permissions_replacement_user_id_foreign` FOREIGN KEY (`replacement_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_permissions_applied_by_foreign` FOREIGN KEY (`applied_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  ADD CONSTRAINT `staff_permissions_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_permissions_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_permissions_cancelled_by_foreign` FOREIGN KEY (`cancelled_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `approval_workflow`
--
ALTER TABLE `approval_workflow`
  ADD CONSTRAINT `approval_workflow_approver_id_foreign` FOREIGN KEY (`approver_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `leave_notifications`
--
ALTER TABLE `leave_notifications`
  ADD CONSTRAINT `leave_notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
