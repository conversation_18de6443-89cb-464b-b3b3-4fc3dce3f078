<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$error_message = '';
$success_message = '';

// معالجة إضافة الإداري
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
        unset($_SESSION['success_message']);
    } else {
        // جمع البيانات وتنظيفها
        $full_name = clean_input($_POST['full_name'] ?? '');
        $email = clean_input($_POST['email'] ?? '');
        $username = clean_input($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $phone = clean_input($_POST['phone'] ?? '');
        $national_id = clean_input($_POST['national_id'] ?? '');
        $date_of_birth = clean_input($_POST['date_of_birth'] ?? '');
        $gender = clean_input($_POST['gender'] ?? '');
        $address = clean_input($_POST['address'] ?? '');
        $employee_id = clean_input($_POST['employee_id'] ?? '');
        $hire_date = clean_input($_POST['hire_date'] ?? '');
        $department = clean_input($_POST['department'] ?? '');
        $position = clean_input($_POST['position'] ?? '');
        $salary = floatval($_POST['salary'] ?? 0);
        $status = clean_input($_POST['status'] ?? 'active');

        // التحقق من صحة البيانات
        $errors = [];
        if (empty($full_name)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        if (empty($username)) {
            $errors[] = 'اسم المستخدم مطلوب';
        } elseif (get_user_by_username($username)) {
            $errors[] = 'اسم المستخدم موجود بالفعل';
        }
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!validate_email($email)) {
            $errors[] = 'البريد الإلكتروني غير صالح';
        } elseif (get_user_by_email($email)) {
            $errors[] = 'البريد الإلكتروني موجود بالفعل';
        }
        if (empty($password)) {
            $errors[] = 'كلمة المرور مطلوبة';
        } elseif (!validate_password($password)) {
            $errors[] = 'كلمة المرور قصيرة جداً';
        } elseif ($password !== $confirm_password) {
            $errors[] = 'كلمات المرور غير متطابقة';
        }
        if (empty($employee_id)) {
            $errors[] = 'رقم الموظف مطلوب';
        } else {
            $stmt = $conn->prepare("SELECT id FROM staff WHERE employee_id = ?");
            $stmt->bind_param("s", $employee_id);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $errors[] = 'رقم الموظف موجود بالفعل';
            }
        }
        if (!empty($national_id) && !validate_national_id($national_id)) {
            $errors['national_id'] = 'الرقم القومي غير صالح';
        }
        if (!empty($phone) && !validate_phone($phone)) {
            $errors['phone'] = 'رقم الهاتف غير صالح';
        }
        if (!empty($date_of_birth) && !validate_date($date_of_birth)) {
            $errors[] = 'تاريخ الميلاد غير صالح';
        }
        if (!empty($hire_date) && !validate_date($hire_date)) {
            $errors[] = 'تاريخ التوظيف غير صالح';
        }
        if (empty($gender) || !in_array($gender, ['male', 'female'])) {
            $errors[] = 'الجنس مطلوب';
        }

        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();
            try {
                // إنشاء المستخدم
                $hashed_password = hash_password($password);
                $stmt = $conn->prepare("
                    INSERT INTO users (full_name, username, email, password, role, status, phone, gender, created_at) 
                    VALUES (?, ?, ?, ?, 'staff', ?, ?, ?, NOW())
                ");
                $stmt->bind_param("sssssss", $full_name, $username, $email, $hashed_password, $status, $phone, $gender);
                $stmt->execute();
                $user_id = $conn->insert_id;
                $stmt->close();

                // إنشاء الإداري
                $stmt = $conn->prepare("
                    INSERT INTO staff (
                        user_id, employee_id, phone, address, date_of_birth, gender, 
                        nationality, national_id, hire_date, department, position, 
                        salary, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
                ");
                $nationality = 'مصري'; // قيمة افتراضية
                $stmt->bind_param("issssssssssd", 
                    $user_id, $employee_id, $phone, $address, $date_of_birth, $gender,
                    $nationality, $national_id, $hire_date, $department, $position, $salary
                );
                $stmt->execute();
                $staff_id = $conn->insert_id;
                $stmt->close();

                $conn->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'add_staff', 'staff', $staff_id, null, [
                    'staff_name' => $full_name,
                    'employee_id' => $employee_id
                ]);

                $_SESSION['success_message'] = 'تم إضافة الإداري بنجاح';
                header('Location: index.php');
                exit();
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = 'حدث خطأ أثناء إضافة الإداري';
                log_error("Error adding staff: " . $e->getMessage());
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// تحميل ملف اللغة
load_language();
$page_title = 'إضافة إداري جديد';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">إضافة إداري جديد</h1>
                    <p class="text-muted">إضافة موظف إداري جديد إلى النظام</p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <!-- Error/Success Messages -->
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Add Staff Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>بيانات الإداري الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2"></i>المعلومات الشخصية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="male" <?php echo ($_POST['gender'] ?? '') === 'male' ? 'selected' : ''; ?>>ذكر</option>
                                    <option value="female" <?php echo ($_POST['gender'] ?? '') === 'female' ? 'selected' : ''; ?>>أنثى</option>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار الجنس</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                       value="<?php echo htmlspecialchars($_POST['date_of_birth'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="national_id" class="form-label">الرقم القومي</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" 
                                       value="<?php echo htmlspecialchars($_POST['national_id'] ?? ''); ?>" 
                                       pattern="[0-9]{14}" maxlength="14">
                                <div class="form-text">14 رقم</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="2"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-key me-2"></i>معلومات الحساب
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صالح</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                <div class="invalid-feedback">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <div class="invalid-feedback">يرجى تأكيد كلمة المرور</div>
                            </div>
                        </div>

                        <!-- Job Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-briefcase me-2"></i>المعلومات الوظيفية
                                </h6>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="employee_id" class="form-label">رقم الموظف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employee_id" name="employee_id"
                                       value="<?php echo htmlspecialchars($_POST['employee_id'] ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال رقم الموظف</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="hire_date" class="form-label">تاريخ التوظيف</label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date"
                                       value="<?php echo htmlspecialchars($_POST['hire_date'] ?? ''); ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="department" name="department"
                                       value="<?php echo htmlspecialchars($_POST['department'] ?? ''); ?>"
                                       placeholder="مثل: الشؤون الإدارية، المحاسبة، الموارد البشرية">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">المنصب</label>
                                <input type="text" class="form-control" id="position" name="position"
                                       value="<?php echo htmlspecialchars($_POST['position'] ?? ''); ?>"
                                       placeholder="مثل: مدير إداري، محاسب، سكرتير">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="salary" class="form-label">الراتب</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="salary" name="salary"
                                           value="<?php echo htmlspecialchars($_POST['salary'] ?? ''); ?>"
                                           min="0" step="0.01">
                                    <span class="input-group-text">جنيه</span>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">حالة الحساب</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo ($_POST['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($_POST['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="index.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ الإداري
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// Auto-generate username from full name
document.getElementById('full_name').addEventListener('input', function() {
    const fullName = this.value;
    const username = fullName.toLowerCase()
        .replace(/\s+/g, '_')
        .replace(/[^a-z0-9_]/g, '');

    if (username && !document.getElementById('username').value) {
        document.getElementById('username').value = username;
    }
});

// National ID validation
document.getElementById('national_id').addEventListener('input', function() {
    const value = this.value;
    if (value && !/^[0-9]{14}$/.test(value)) {
        this.setCustomValidity('الرقم القومي يجب أن يكون 14 رقم');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
