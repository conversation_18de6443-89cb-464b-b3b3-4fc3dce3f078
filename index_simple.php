<?php
/**
 * ملف فهرس مبسط للاختبار
 */

// بدء الجلسة
session_start();

// التحقق من تسجيل الدخول
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard/');
    exit();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدارس</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .welcome-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
    </style>
</head>
<body>
    <div class="welcome-card">
        <h1 class="mb-4">نظام إدارة المدارس</h1>
        <p class="mb-4">مرحباً بك في نظام إدارة المدارس الشامل</p>
        <div class="d-grid gap-2">
            <a href="login.php" class="btn btn-primary btn-lg">تسجيل الدخول</a>
            <a href="dashboard/" class="btn btn-outline-primary">لوحة التحكم</a>
        </div>
        
        <hr class="my-4">
        
        <div class="text-muted">
            <small>
                <strong>بيانات تسجيل الدخول التجريبية:</strong><br>
                المدير: admin / password<br>
                المعلم: teacher1 / password<br>
                الطالب: student1 / password
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
