<?php
/**
 * صفحة حذف حساب بنكي
 * Delete Bank Account Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// التحقق من وجود معرف الحساب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: bank_accounts.php?error=' . urlencode('معرف الحساب غير صالح'));
    exit();
}

$account_id = intval($_GET['id']);

// جلب بيانات الحساب
$stmt = $conn->prepare("SELECT * FROM bank_accounts WHERE id = ?");
$stmt->bind_param("i", $account_id);
$stmt->execute();
$account = $stmt->get_result()->fetch_assoc();

if (!$account) {
    header('Location: bank_accounts.php?error=' . urlencode('الحساب غير موجود'));
    exit();
}

// التحقق من استخدام الحساب في الأقساط
$check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM student_installments WHERE bank_account_id = ?");
$check_stmt->bind_param("i", $account_id);
$check_stmt->execute();
$usage_count = $check_stmt->get_result()->fetch_assoc()['count'];

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } elseif ($usage_count > 0) {
        $error_message = "لا يمكن حذف هذا الحساب لأنه مستخدم في $usage_count قسط";
    } else {
        $delete_stmt = $conn->prepare("DELETE FROM bank_accounts WHERE id = ?");
        $delete_stmt->bind_param("i", $account_id);

        if ($delete_stmt->execute()) {
            // إعادة توجيه مع رسالة نجاح
            header('Location: bank_accounts.php?success=' . urlencode('تم حذف الحساب البنكي بنجاح'));
            exit();
        } else {
            $error_message = 'خطأ في حذف الحساب البنكي: ' . $conn->error;
        }
    }
}

$page_title = 'حذف الحساب البنكي';
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0"><i class="fas fa-trash me-2"></i>حذف الحساب البنكي</h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <!-- عرض بيانات الحساب -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">بيانات الحساب المراد حذفه</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>اسم البنك:</strong> <?php echo htmlspecialchars($account['bank_name']); ?></p>
                                    <p><strong>رقم الحساب:</strong> <code><?php echo htmlspecialchars($account['account_number']); ?></code></p>
                                    <p><strong>اسم صاحب الحساب:</strong> <?php echo htmlspecialchars($account['account_name'] ?? 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>رقم الآيبان:</strong> <?php echo htmlspecialchars($account['iban'] ?? 'غير محدد'); ?></p>
                                    <p><strong>اسم الفرع:</strong> <?php echo htmlspecialchars($account['branch_name'] ?? 'غير محدد'); ?></p>
                                    <p><strong>الحالة:</strong>
                                        <?php if ($account['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            <?php if (!empty($account['notes'])): ?>
                            <p><strong>ملاحظات:</strong> <?php echo htmlspecialchars($account['notes']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- معلومات الاستخدام -->
                    <?php if ($usage_count > 0): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذا الحساب مستخدم في <strong><?php echo $usage_count; ?></strong> قسط.
                        لا يمكن حذفه حتى يتم إزالة جميع الأقساط المرتبطة به أو تغيير الحساب البنكي لها.
                    </div>
                    <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        هذا الحساب غير مستخدم في أي أقساط. يمكن حذفه بأمان.
                    </div>
                    <?php endif; ?>

                    <?php if ($usage_count == 0): ?>
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirm_delete" required>
                                <label class="form-check-label" for="confirm_delete">
                                    <strong>أؤكد أنني أريد حذف هذا الحساب البنكي نهائياً</strong>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="bank_accounts.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>حذف الحساب نهائياً
                            </button>
                        </div>
                    </form>
                    <?php else: ?>
                    <!-- إذا كان الحساب مستخدم، عرض زر العودة فقط -->
                    <div class="text-center">
                        <a href="bank_accounts.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل/إلغاء تفعيل زر الحذف بناءً على التأكيد
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirm_delete');
    const deleteButton = document.querySelector('button[type="submit"]');

    if (confirmCheckbox && deleteButton) {
        confirmCheckbox.addEventListener('change', function() {
            deleteButton.disabled = !this.checked;
        });

        // تعطيل الزر في البداية
        deleteButton.disabled = true;
    }
});
</script>

<?php include_once '../../includes/footer.php'; ?>

