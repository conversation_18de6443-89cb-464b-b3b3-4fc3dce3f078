<?php
/**
 * صفحة إدارة الكتب المدرسية
 * School Books Management Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// دالة مساعدة لتحويل عناصر المصفوفة إلى مراجع (لحل مشكلة bind_param)
function refValues($arr) {
    if (strnatcmp(phpversion(), '5.3') >= 0) {
        $refs = [];
        foreach ($arr as $key => $value) {
            $refs[$key] = &$arr[$key];
        }
        return $refs;
    }
    return $arr;
}

// دالة موحدة وآمنة لربط المتغيرات مع bind_param
function safeBindParam($stmt, $types, $params, $limit = null, $offset = null) {
    $refs = [];
    if (!empty($types) && count($params) > 0) {
        $binds = array_merge([$types], $params);
        if ($limit !== null && $offset !== null) {
            $binds[] = $limit;
            $binds[] = $offset;
            $types .= 'ii';
        }
        foreach ($binds as $key => $value) {
            $refs[$key] = &$binds[$key];
        }
        call_user_func_array([$stmt, 'bind_param'], $refs);
    } elseif ($limit !== null && $offset !== null) {
        $stmt->bind_param('ii', $limit, $offset);
    }
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// معالجة الحذف
if (isset($_POST['delete_book'])) {
    $book_id = intval($_POST['book_id']);

    global $conn;
    $conn->begin_transaction();

    try {
        // التحقق من وجود طلبات مرتبطة
        $check_orders = $conn->prepare("SELECT COUNT(*) as count FROM student_book_order_items WHERE book_id = ?");
        $check_orders->bind_param("i", $book_id);
        $check_orders->execute();
        $order_count = $check_orders->get_result()->fetch_assoc()['count'];

        if ($order_count > 0) {
            throw new Exception("Cannot delete book with existing orders");
        }

        // حذف الكتاب
        $delete_book = $conn->prepare("DELETE FROM school_books WHERE id = ?");
        $delete_book->bind_param("i", $book_id);
        $delete_book->execute();

        $conn->commit();

        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'delete_school_book', 'school_books', $book_id);

        $_SESSION['success_message'] = __('deleted_successfully');
    } catch (Exception $e) {
        $conn->rollback();
        if (strpos($e->getMessage(), "Cannot delete book with existing orders") !== false) {
            $_SESSION['error_message'] = __('cannot_delete_book_with_orders');
        } else {
            $_SESSION['error_message'] = __('error_occurred');
        }
        log_error("Error deleting school book: " . $e->getMessage());
    }

    header('Location: index.php');
    exit();
}

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$subject_filter = clean_input($_GET['subject_id'] ?? '');
$grade_filter = clean_input($_GET['grade_level'] ?? '');
$academic_year_filter = clean_input($_GET['academic_year'] ?? get_current_academic_year());
$status_filter = clean_input($_GET['status'] ?? '');
$book_type_filter = clean_input($_GET['book_type'] ?? '');

// بناء استعلام البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(sb.book_title LIKE ? OR sb.book_title_en LIKE ? OR sb.publisher LIKE ? OR sb.isbn LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    $types .= "ssss";
}

if (!empty($subject_filter)) {
    $where_conditions[] = "sb.subject_id = ?";
    $params[] = $subject_filter;
    $types .= "i";
}

if (!empty($grade_filter)) {
    $where_conditions[] = "sb.grade_level = ?";
    $params[] = $grade_filter;
    $types .= "s";
}

if (!empty($academic_year_filter)) {
    $where_conditions[] = "sb.academic_year = ?";
    $params[] = $academic_year_filter;
    $types .= "s";
}

if (!empty($status_filter)) {
    $where_conditions[] = "sb.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

if (!empty($book_type_filter)) {
    $where_conditions[] = "sb.book_type = ?";
    $params[] = $book_type_filter;
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM books sb
    LEFT JOIN subjects s ON sb.subject_id = s.id
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
$limit = null;
$offset_var = null;
safeBindParam($count_stmt, $types, $params, $limit, $offset_var);
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب الكتب
$query = "
    SELECT
        sb.*,
        s.subject_name,
        0 as order_count,
        0 as total_ordered
    FROM books sb
    LEFT JOIN subjects s ON sb.subject_id = s.id
    WHERE $where_clause
    ORDER BY sb.created_at DESC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
$limit = $records_per_page;
$offset_var = $offset;
safeBindParam($stmt, $types, $params, $limit, $offset_var);
$stmt->execute();
$books = $stmt->get_result();

// جلب قوائم الفلترة
$subjects = $conn->query("SELECT id, subject_name FROM subjects WHERE status = 'active' ORDER BY subject_name");

$grade_levels = $conn->query("SELECT DISTINCT grade_level FROM classes ORDER BY grade_level");

$academic_years = $conn->query("SELECT DISTINCT YEAR(created_at) as academic_year FROM books ORDER BY academic_year DESC");

// إحصائيات سريعة
$stats_query = "
    SELECT
        COUNT(*) as total_books,
        SUM(stock_quantity) as total_stock,
        SUM(price * stock_quantity) as total_value,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_books,
        COUNT(CASE WHEN stock_quantity <= 10 THEN 1 END) as low_stock_books
    FROM books
    WHERE YEAR(created_at) = ?
";

$stats_stmt = $conn->prepare($stats_query);
$year_to_filter = $academic_year_filter ?: date('Y');
$stats_stmt->bind_param("i", $year_to_filter);
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('school_books'); ?></h1>
            <p class="text-muted"><?php echo __('manage_school_books_inventory'); ?></p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('add_book'); ?>
            </a>
            <a href="bulk_import.php" class="btn btn-success">
                <i class="fas fa-upload me-2"></i><?php echo __('bulk_import'); ?>
            </a>
            <a href="orders.php" class="btn btn-info">
                <i class="fas fa-shopping-cart me-2"></i><?php echo __('book_orders'); ?>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-book text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_books']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_books'); ?></p>
                            <small class="text-success">
                                <?php echo number_format($stats['active_books']); ?> <?php echo __('active'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-boxes text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_stock']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_stock'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient p-3 rounded-3">
                                <i class="fas fa-dollar-sign text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_value'], 2); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('inventory_value'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-exclamation-triangle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['low_stock_books']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('low_stock'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>