<?php
/**
 * صفحة عرض الصفوف الدراسية
 * School Grades Management Page
 */

require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('school_grades');

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$stage_filter = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'es.sort_order, g.sort_order';
$sort_order = isset($_GET['order']) && $_GET['order'] === 'desc' ? 'DESC' : 'ASC';

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(g.grade_name LIKE ? OR g.grade_name_en LIKE ? OR g.grade_code LIKE ? OR g.description LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

if ($stage_filter > 0) {
    $where_conditions[] = "g.stage_id = ?";
    $params[] = $stage_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "g.status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد السجلات
$count_query = "
    SELECT COUNT(*) as total 
    FROM grades g 
    INNER JOIN educational_stages es ON g.stage_id = es.id 
    $where_clause
";
$stmt = $conn->prepare($count_query);
if ($stmt) {
    if (!empty($params)) {
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
    }
    $stmt->execute();
    $total_records = $stmt->get_result()->fetch_assoc()['total'];
    $stmt->close();
} else {
    $total_records = 0;
}

// إعدادات الصفحات
$records_per_page = 15;
$total_pages = ceil($total_records / $records_per_page);
$current_page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($current_page - 1) * $records_per_page;

// جلب الصفوف الدراسية
$query = "
    SELECT 
        g.*,
        es.stage_name,
        es.stage_code,
        es.sort_order as stage_sort_order,
        COUNT(DISTINCT c.id) as classes_count
    FROM grades g
    INNER JOIN educational_stages es ON g.stage_id = es.id
    LEFT JOIN classes c ON g.id = c.grade_id AND c.status = 'active'
    $where_clause
    GROUP BY g.id
    ORDER BY $sort_by $sort_order
    LIMIT ? OFFSET ?
";

$school_grades = [];
$stmt = $conn->prepare($query);
if ($stmt) {
    $all_params = array_merge($params, [$records_per_page, $offset]);
    $types = str_repeat('s', count($params)) . 'ii';
    $stmt->bind_param($types, ...$all_params);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $school_grades[] = $row;
    }
    $stmt->close();
}

// جلب المراحل الدراسية للفلترة
$stages_query = "SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = [];
if ($stages_result) {
    while ($row = $stages_result->fetch_assoc()) {
        $stages[] = $row;
    }
}

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_grades,
        SUM(CASE WHEN g.status = 'active' THEN 1 ELSE 0 END) as active_grades,
        SUM(CASE WHEN g.status = 'inactive' THEN 1 ELSE 0 END) as inactive_grades,
        COUNT(DISTINCT g.stage_id) as stages_with_grades
    FROM grades g
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result ? $stats_result->fetch_assoc() : ['total_grades' => 0, 'active_grades' => 0, 'inactive_grades' => 0, 'stages_with_grades' => 0];
?>

<div class="container-fluid">
    <!-- Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                <?php echo __('school_grades'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('manage_school_grades'); ?></p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('add_grade'); ?>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['total_grades']; ?></h4>
                            <p class="mb-0"><?php echo __('total_grades'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-graduation-cap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['active_grades']; ?></h4>
                            <p class="mb-0"><?php echo __('active_grades'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['inactive_grades']; ?></h4>
                            <p class="mb-0"><?php echo __('inactive_grades'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo array_sum(array_column($school_grades, 'classes_count')); ?></h4>
                            <p class="mb-0"><?php echo __('total_classes'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-school fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label"><?php echo __('search'); ?></label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="<?php echo __('search_grades'); ?>">
                </div>
                <div class="col-md-3">
                    <label for="stage_id" class="form-label"><?php echo __('educational_stage'); ?></label>
                    <select class="form-select" id="stage_id" name="stage_id">
                        <option value=""><?php echo __('all_stages'); ?></option>
                        <?php foreach ($stages as $stage): ?>
                            <option value="<?php echo $stage['id']; ?>" <?php echo $stage_filter == $stage['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($stage['stage_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>
                            <?php echo __('active'); ?>
                        </option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>
                            <?php echo __('inactive'); ?>
                        </option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort" class="form-label"><?php echo __('sort_by'); ?></label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="es.sort_order, g.sort_order" <?php echo $sort_by === 'es.sort_order, g.sort_order' ? 'selected' : ''; ?>>
                            <?php echo __('stage_and_grade_order'); ?>
                        </option>
                        <option value="g.grade_name" <?php echo $sort_by === 'g.grade_name' ? 'selected' : ''; ?>>
                            <?php echo __('grade_name'); ?>
                        </option>
                        <option value="g.created_at" <?php echo $sort_by === 'g.created_at' ? 'selected' : ''; ?>>
                            <?php echo __('created_at'); ?>
                        </option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Results Section -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <?php echo __('grades_list'); ?> 
                <span class="badge bg-secondary"><?php echo $total_records; ?></span>
            </h5>
            <?php if (!empty($search) || $stage_filter > 0 || !empty($status_filter)): ?>
                <a href="index.php" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                </a>
            <?php endif; ?>
        </div>
        <div class="card-body p-0">
            <?php if (empty($school_grades)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo __('no_grades_found'); ?></h5>
                    <p class="text-muted"><?php echo __('no_grades_message'); ?></p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_first_grade'); ?>
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th><?php echo __('stage'); ?></th>
                                <th><?php echo __('grade_name'); ?></th>
                                <th><?php echo __('grade_code'); ?></th>
                                <th><?php echo __('sort_order'); ?></th>
                                <th><?php echo __('age_range'); ?></th>
                                <th><?php echo __('classes'); ?></th>
                                <th><?php echo __('status'); ?></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($school_grades as $grade): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($grade['stage_code']); ?></span>
                                        <small><?php echo htmlspecialchars($grade['stage_name']); ?></small>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($grade['grade_name']); ?></strong>
                                            <?php if (!empty($grade['grade_name_en'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($grade['grade_name_en']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <code><?php echo htmlspecialchars($grade['grade_code']); ?></code>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $grade['sort_order']; ?></span>
                                    </td>
                                    <td>
                                        <?php if ($grade['min_age'] && $grade['max_age']): ?>
                                            <?php echo $grade['min_age']; ?> - <?php echo $grade['max_age']; ?> <?php echo __('years'); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $grade['classes_count']; ?></span>
                                    </td>
                                    <td>
                                        <?php if ($grade['status'] === 'active'): ?>
                                            <span class="badge bg-success"><?php echo __('active'); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="view.php?id=<?php echo $grade['id']; ?>" 
                                               class="btn btn-outline-info" title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $grade['id']; ?>" 
                                               class="btn btn-outline-primary" title="<?php echo __('edit'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="confirmDelete(<?php echo $grade['id']; ?>, '<?php echo htmlspecialchars($grade['grade_name'], ENT_QUOTES); ?>')"
                                                    title="<?php echo __('delete'); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="<?php echo __('pagination'); ?>">
                            <ul class="pagination justify-content-center mb-0">
                                <?php
                                $query_params = $_GET;
                                unset($query_params['page']);
                                $base_url = 'index.php?' . http_build_query($query_params);
                                $base_url .= empty($query_params) ? 'page=' : '&page=';
                                ?>
                                
                                <?php if ($current_page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo $base_url . ($current_page - 1); ?>">
                                            <?php echo __('previous'); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                                        <a class="page-link" href="<?php echo $base_url . $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($current_page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php echo $base_url . ($current_page + 1); ?>">
                                            <?php echo __('next'); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function confirmDelete(gradeId, gradeName) {
    // استخدام SweetAlert للتأكيد
    Swal.fire({
        title: '<?php echo __('confirm_delete'); ?>',
        html: `
            <div class="text-center mb-3">
                <i class="fas fa-trash-alt fa-3x text-danger"></i>
            </div>
            <p><?php echo __('delete_grade_confirmation'); ?></p>
            <div class="alert alert-warning">
                <strong><?php echo __('grade_name'); ?>:</strong> ${gradeName}
            </div>
            <p class="text-muted small"><?php echo __('delete_grade_warning'); ?></p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>',
        cancelButtonText: '<i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار رسالة تحميل
            Swal.fire({
                title: '<?php echo __('deleting'); ?>...',
                text: '<?php echo __('please_wait'); ?>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // الانتقال لصفحة الحذف
            window.location.href = 'delete.php?id=' + gradeId;
        }
    });
}
</script>

<?php require_once '../includes/footer.php'; ?>
