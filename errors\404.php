<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        .error-description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-home:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .floating-icon {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .icon-1 { top: 10%; left: 10%; animation-delay: 0s; }
        .icon-2 { top: 20%; right: 10%; animation-delay: 2s; }
        .icon-3 { bottom: 20%; left: 15%; animation-delay: 4s; }
        .icon-4 { bottom: 10%; right: 20%; animation-delay: 1s; }
    </style>
</head>
<body>
    <!-- Floating Icons -->
    <i class="fas fa-school floating-icon icon-1" style="font-size: 3rem;"></i>
    <i class="fas fa-book floating-icon icon-2" style="font-size: 2.5rem;"></i>
    <i class="fas fa-graduation-cap floating-icon icon-3" style="font-size: 3.5rem;"></i>
    <i class="fas fa-chalkboard-teacher floating-icon icon-4" style="font-size: 2rem;"></i>

    <div class="error-container">
        <div class="error-code">404</div>
        <div class="error-message">
            <i class="fas fa-search me-3"></i>
            الصفحة غير موجودة
        </div>
        <div class="error-description">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
            <br>
            يرجى التحقق من الرابط أو العودة إلى الصفحة الرئيسية.
        </div>
        
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="/" class="btn-home">
                <i class="fas fa-home me-2"></i>
                الصفحة الرئيسية
            </a>
            <a href="/dashboard/" class="btn-home">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </a>
            <a href="javascript:history.back()" class="btn-home">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للخلف
            </a>
        </div>
        
        <div class="mt-4">
            <small style="opacity: 0.7;">
                إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بمدير النظام
            </small>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate error code on load
            const errorCode = document.querySelector('.error-code');
            errorCode.style.animation = 'bounce 1s ease-out';
            
            // Add CSS for bounce animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes bounce {
                    0%, 20%, 60%, 100% {
                        transform: translateY(0);
                    }
                    40% {
                        transform: translateY(-30px);
                    }
                    80% {
                        transform: translateY(-15px);
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
