<?php
/**
 * صفحة استيراد الرسوم بالجملة
 * Bulk Import Fees Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$errors = [];
$success_message = '';
$imported_count = 0;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        die('CSRF token validation failed.');
    }

    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] == UPLOAD_ERR_OK) {
        $file = $_FILES['csv_file']['tmp_name'];
        $file_type = mime_content_type($file);

        if ($file_type == 'text/csv' || $file_type == 'text/plain') {
            $handle = fopen($file, 'r');
            if ($handle !== FALSE) {
                $conn->begin_transaction();
                try {
                    // Skip header row
                    fgetcsv($handle, 1000, ',');

                    $stmt = $conn->prepare("INSERT INTO student_fees (student_id, fee_type_id, academic_year, amount, due_date, status) VALUES (?, ?, ?, ?, ?, ?)");

                    while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                        if (count($data) >= 6) {
                            // Find student_id from national_id
                            $student_stmt = $conn->prepare("SELECT id FROM students WHERE national_id = ?");
                            $student_stmt->bind_param('s', $data[0]);
                            $student_stmt->execute();
                            $student_result = $student_stmt->get_result();
                            if ($student = $student_result->fetch_assoc()) {
                                $student_id = $student['id'];
                                $fee_type_id = $data[1];
                                $academic_year = $data[2];
                                $amount = $data[3];
                                $due_date = $data[4];
                                $status = $data[5];

                                $stmt->bind_param('iisdss', $student_id, $fee_type_id, $academic_year, $amount, $due_date, $status);
                                $stmt->execute();
                                $imported_count++;
                            }
                        }
                    }
                    $conn->commit();
                    $_SESSION['success_message'] = sprintf(__('%d_fees_imported_successfully'), $imported_count);
                    header('Location: index.php');
                    exit();
                } catch (Exception $e) {
                    $conn->rollback();
                    $errors[] = __('error_during_import') . ': ' . $e->getMessage();
                }
                fclose($handle);
            } else {
                $errors[] = __('error_opening_file');
            }
        } else {
            $errors[] = __('invalid_file_type_csv');
        }
    } else {
        $errors[] = __('no_file_uploaded_or_error');
    }
}

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('import_fees'); ?></h1>
            <p class="text-muted"><?php echo __('upload_csv_to_import_fees'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_fees'); ?>
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <p class="mb-0"><?php echo $error; ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="alert alert-info">
                <h5><?php echo __('instructions'); ?></h5>
                <p><?php echo __('csv_format_instructions'); ?></p>
                <p><code>national_id, fee_type_id, academic_year, amount, due_date (YYYY-MM-DD), status</code></p>
                <p><a href="../../templates/fees_template.csv" download class="btn btn-sm btn-outline-primary"><i class="fas fa-download me-2"></i><?php echo __('download_template'); ?></a></p>
            </div>

            <form action="import.php" method="POST" enctype="multipart/form-data">
                <?php echo generate_csrf_token(); ?>
                <div class="mb-3">
                    <label for="csv_file" class="form-label"><?php echo __('select_csv_file'); ?> <span class="text-danger">*</span></label>
                    <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                </div>
                <button type="submit" class="btn btn-primary"><i class="fas fa-upload me-2"></i><?php echo __('import_fees'); ?></button>
            </form>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
