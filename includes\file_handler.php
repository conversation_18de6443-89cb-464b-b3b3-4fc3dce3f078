<?php
/**
 * نظام معالجة الملفات المتقدم
 * Advanced File Handler System
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

/**
 * فئة معالجة الملفات
 * File Handler Class
 */
class FileHandler {
    
    private static $instance = null;
    private $upload_path;
    private $allowed_types;
    private $max_file_size;
    
    private function __construct() {
        $this->upload_path = UPLOADS_PATH;
        $this->allowed_types = ALLOWED_FILE_TYPES;
        $this->max_file_size = MAX_FILE_SIZE;
        
        $this->createUploadDirectories();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * إنشاء مجلدات الرفع
     * Create upload directories
     */
    private function createUploadDirectories() {
        $directories = [
            $this->upload_path,
            $this->upload_path . '/' . PROFILE_UPLOADS_DIR,
            $this->upload_path . '/' . DOCUMENT_UPLOADS_DIR,
            $this->upload_path . '/' . TEMP_UPLOADS_DIR,
            $this->upload_path . '/' . BACKUP_UPLOADS_DIR
        ];
        
        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // Create .htaccess for security
            $htaccess_file = $dir . '/.htaccess';
            if (!file_exists($htaccess_file)) {
                $htaccess_content = "Options -Indexes\n";
                $htaccess_content .= "<Files *.php>\n";
                $htaccess_content .= "    Order Allow,Deny\n";
                $htaccess_content .= "    Deny from all\n";
                $htaccess_content .= "</Files>\n";
                file_put_contents($htaccess_file, $htaccess_content);
            }
        }
    }
    
    /**
     * رفع ملف واحد
     * Upload single file
     */
    public function uploadFile($file, $directory = DOCUMENT_UPLOADS_DIR, $options = []) {
        try {
            // Validate file
            $validation = $this->validateFile($file, $options);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // Generate unique filename
            $filename = $this->generateUniqueFilename($file['name'], $directory);
            $file_path = $this->upload_path . '/' . $directory . '/' . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $file_path)) {
                return ['success' => false, 'message' => 'Failed to move uploaded file'];
            }
            
            // Set proper permissions
            chmod($file_path, 0644);
            
            // Process file if needed
            $this->processUploadedFile($file_path, $file['type'], $options);
            
            // Store file information in database
            $file_info = $this->storeFileInfo($filename, $directory, $file, $options);
            
            return [
                'success' => true,
                'filename' => $filename,
                'file_path' => $file_path,
                'file_url' => UPLOADS_URL . '/' . $directory . '/' . $filename,
                'file_info' => $file_info
            ];
            
        } catch (Exception $e) {
            log_error("File upload error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * رفع ملفات متعددة
     * Upload multiple files
     */
    public function uploadMultipleFiles($files, $directory = DOCUMENT_UPLOADS_DIR, $options = []) {
        $results = [];
        
        // Handle different file array structures
        if (isset($files['name']) && is_array($files['name'])) {
            // Multiple files in single input
            for ($i = 0; $i < count($files['name']); $i++) {
                $file = [
                    'name' => $files['name'][$i],
                    'type' => $files['type'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                ];
                
                $results[] = $this->uploadFile($file, $directory, $options);
            }
        } else {
            // Multiple file inputs
            foreach ($files as $file) {
                $results[] = $this->uploadFile($file, $directory, $options);
            }
        }
        
        return $results;
    }
    
    /**
     * التحقق من صحة الملف
     * Validate file
     */
    private function validateFile($file, $options = []) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'message' => $this->getUploadErrorMessage($file['error'])];
        }
        
        // Check file size
        $max_size = $options['max_size'] ?? $this->max_file_size;
        if ($file['size'] > $max_size) {
            return ['valid' => false, 'message' => 'File size exceeds maximum allowed size'];
        }
        
        // Check file type
        $allowed_types = $options['allowed_types'] ?? $this->allowed_types;
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_extension, $allowed_types)) {
            return ['valid' => false, 'message' => 'File type not allowed'];
        }
        
        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!$this->isValidMimeType($mime_type, $file_extension)) {
            return ['valid' => false, 'message' => 'Invalid file type'];
        }
        
        // Check for malicious content
        if ($this->containsMaliciousContent($file['tmp_name'])) {
            return ['valid' => false, 'message' => 'File contains malicious content'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * إنشاء اسم ملف فريد
     * Generate unique filename
     */
    private function generateUniqueFilename($original_name, $directory) {
        $extension = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        $basename = pathinfo($original_name, PATHINFO_FILENAME);
        
        // Clean filename
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $basename = substr($basename, 0, 50); // Limit length
        
        // Generate unique filename
        $timestamp = time();
        $random = bin2hex(random_bytes(4));
        $filename = $basename . '_' . $timestamp . '_' . $random . '.' . $extension;
        
        // Ensure uniqueness
        $counter = 1;
        $original_filename = $filename;
        while (file_exists($this->upload_path . '/' . $directory . '/' . $filename)) {
            $filename = pathinfo($original_filename, PATHINFO_FILENAME) . '_' . $counter . '.' . $extension;
            $counter++;
        }
        
        return $filename;
    }
    
    /**
     * معالجة الملف المرفوع
     * Process uploaded file
     */
    private function processUploadedFile($file_path, $mime_type, $options = []) {
        // Process images
        if (strpos($mime_type, 'image/') === 0) {
            $this->processImage($file_path, $options);
        }
        
        // Process documents
        if (in_array($mime_type, ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])) {
            $this->processDocument($file_path, $options);
        }
    }
    
    /**
     * معالجة الصور
     * Process images
     */
    private function processImage($file_path, $options = []) {
        // Create thumbnails if requested
        if (isset($options['create_thumbnail']) && $options['create_thumbnail']) {
            $this->createThumbnail($file_path, $options);
        }
        
        // Optimize image
        if (isset($options['optimize']) && $options['optimize']) {
            $this->optimizeImage($file_path, $options);
        }
        
        // Remove EXIF data for privacy
        if (isset($options['remove_exif']) && $options['remove_exif']) {
            $this->removeExifData($file_path);
        }
    }
    
    /**
     * إنشاء صورة مصغرة
     * Create thumbnail
     */
    private function createThumbnail($file_path, $options = []) {
        $thumbnail_width = $options['thumbnail_width'] ?? 150;
        $thumbnail_height = $options['thumbnail_height'] ?? 150;
        
        $image_info = getimagesize($file_path);
        if (!$image_info) {
            return false;
        }
        
        $original_width = $image_info[0];
        $original_height = $image_info[1];
        $image_type = $image_info[2];
        
        // Calculate thumbnail dimensions
        $aspect_ratio = $original_width / $original_height;
        if ($thumbnail_width / $thumbnail_height > $aspect_ratio) {
            $thumbnail_width = $thumbnail_height * $aspect_ratio;
        } else {
            $thumbnail_height = $thumbnail_width / $aspect_ratio;
        }
        
        // Create image resource
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($file_path);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($file_path);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($file_path);
                break;
            default:
                return false;
        }
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($thumbnail_width, $thumbnail_height);
        
        // Preserve transparency for PNG and GIF
        if ($image_type == IMAGETYPE_PNG || $image_type == IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefilledrectangle($thumbnail, 0, 0, $thumbnail_width, $thumbnail_height, $transparent);
        }
        
        imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $thumbnail_width, $thumbnail_height, $original_width, $original_height);
        
        // Save thumbnail
        $thumbnail_path = $this->getThumbnailPath($file_path);
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                imagejpeg($thumbnail, $thumbnail_path, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($thumbnail, $thumbnail_path, 8);
                break;
            case IMAGETYPE_GIF:
                imagegif($thumbnail, $thumbnail_path);
                break;
        }
        
        imagedestroy($source);
        imagedestroy($thumbnail);
        
        return $thumbnail_path;
    }
    
    /**
     * الحصول على مسار الصورة المصغرة
     * Get thumbnail path
     */
    private function getThumbnailPath($file_path) {
        $path_info = pathinfo($file_path);
        return $path_info['dirname'] . '/thumb_' . $path_info['basename'];
    }
    
    /**
     * تحسين الصورة
     * Optimize image
     */
    private function optimizeImage($file_path, $options = []) {
        $quality = $options['quality'] ?? 85;
        $max_width = $options['max_width'] ?? 1920;
        $max_height = $options['max_height'] ?? 1080;
        
        $image_info = getimagesize($file_path);
        if (!$image_info) {
            return false;
        }
        
        $original_width = $image_info[0];
        $original_height = $image_info[1];
        $image_type = $image_info[2];
        
        // Check if resizing is needed
        if ($original_width <= $max_width && $original_height <= $max_height) {
            return true; // No resizing needed
        }
        
        // Calculate new dimensions
        $aspect_ratio = $original_width / $original_height;
        if ($max_width / $max_height > $aspect_ratio) {
            $new_width = $max_height * $aspect_ratio;
            $new_height = $max_height;
        } else {
            $new_width = $max_width;
            $new_height = $max_width / $aspect_ratio;
        }
        
        // Create and resize image
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($file_path);
                $resized = imagecreatetruecolor($new_width, $new_height);
                imagecopyresampled($resized, $source, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
                imagejpeg($resized, $file_path, $quality);
                break;
                
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($file_path);
                $resized = imagecreatetruecolor($new_width, $new_height);
                imagealphablending($resized, false);
                imagesavealpha($resized, true);
                $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
                imagefilledrectangle($resized, 0, 0, $new_width, $new_height, $transparent);
                imagecopyresampled($resized, $source, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
                imagepng($resized, $file_path, 8);
                break;
                
            default:
                return false;
        }
        
        imagedestroy($source);
        imagedestroy($resized);
        
        return true;
    }
    
    /**
     * إزالة بيانات EXIF
     * Remove EXIF data
     */
    private function removeExifData($file_path) {
        $image_info = getimagesize($file_path);
        if ($image_info && $image_info[2] == IMAGETYPE_JPEG) {
            $source = imagecreatefromjpeg($file_path);
            imagejpeg($source, $file_path, 90);
            imagedestroy($source);
        }
    }
    
    /**
     * معالجة المستندات
     * Process documents
     */
    private function processDocument($file_path, $options = []) {
        // Extract text content for indexing
        if (isset($options['extract_text']) && $options['extract_text']) {
            $this->extractTextContent($file_path);
        }
        
        // Generate preview if possible
        if (isset($options['generate_preview']) && $options['generate_preview']) {
            $this->generateDocumentPreview($file_path);
        }
    }
    
    /**
     * حفظ معلومات الملف في قاعدة البيانات
     * Store file information in database
     */
    private function storeFileInfo($filename, $directory, $file, $options = []) {
        global $conn;
        
        if (!$conn) {
            return null;
        }
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO uploaded_files (
                    filename, original_name, file_path, file_size, mime_type,
                    directory, uploaded_by, upload_date, file_hash
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?)
            ");
            
            $file_path = $directory . '/' . $filename;
            $file_hash = hash_file('sha256', $this->upload_path . '/' . $file_path);
            $uploaded_by = $_SESSION['user_id'] ?? null;
            
            $stmt->bind_param("sssissis",
                $filename,
                $file['name'],
                $file_path,
                $file['size'],
                $file['type'],
                $directory,
                $uploaded_by,
                $file_hash
            );
            
            $stmt->execute();
            
            return [
                'id' => $conn->insert_id,
                'filename' => $filename,
                'original_name' => $file['name'],
                'file_size' => $file['size'],
                'mime_type' => $file['type'],
                'file_hash' => $file_hash
            ];
            
        } catch (Exception $e) {
            log_error("Error storing file info: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * حذف ملف
     * Delete file
     */
    public function deleteFile($filename, $directory = DOCUMENT_UPLOADS_DIR) {
        try {
            $file_path = $this->upload_path . '/' . $directory . '/' . $filename;
            
            if (file_exists($file_path)) {
                unlink($file_path);
                
                // Delete thumbnail if exists
                $thumbnail_path = $this->getThumbnailPath($file_path);
                if (file_exists($thumbnail_path)) {
                    unlink($thumbnail_path);
                }
                
                // Remove from database
                $this->removeFileFromDatabase($filename, $directory);
                
                return ['success' => true, 'message' => 'File deleted successfully'];
            } else {
                return ['success' => false, 'message' => 'File not found'];
            }
            
        } catch (Exception $e) {
            log_error("File deletion error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to delete file'];
        }
    }
    
    /**
     * إزالة الملف من قاعدة البيانات
     * Remove file from database
     */
    private function removeFileFromDatabase($filename, $directory) {
        global $conn;
        
        if (!$conn) {
            return false;
        }
        
        try {
            $stmt = $conn->prepare("DELETE FROM uploaded_files WHERE filename = ? AND directory = ?");
            $stmt->bind_param("ss", $filename, $directory);
            return $stmt->execute();
        } catch (Exception $e) {
            log_error("Error removing file from database: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من نوع MIME
     * Check MIME type validity
     */
    private function isValidMimeType($mime_type, $extension) {
        $valid_types = [
            'jpg' => ['image/jpeg', 'image/pjpeg'],
            'jpeg' => ['image/jpeg', 'image/pjpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'pdf' => ['application/pdf'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'xls' => ['application/vnd.ms-excel'],
            'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            'txt' => ['text/plain']
        ];
        
        return isset($valid_types[$extension]) && in_array($mime_type, $valid_types[$extension]);
    }
    
    /**
     * التحقق من المحتوى الضار
     * Check for malicious content
     */
    private function containsMaliciousContent($file_path) {
        // Read first few bytes to check for PHP tags or other suspicious content
        $handle = fopen($file_path, 'r');
        if ($handle) {
            $content = fread($handle, 1024);
            fclose($handle);
            
            // Check for PHP tags
            if (strpos($content, '<?php') !== false || strpos($content, '<?=') !== false) {
                return true;
            }
            
            // Check for script tags
            if (stripos($content, '<script') !== false) {
                return true;
            }
            
            // Check for other suspicious patterns
            $suspicious_patterns = [
                'eval(',
                'exec(',
                'system(',
                'shell_exec(',
                'passthru(',
                'base64_decode('
            ];
            
            foreach ($suspicious_patterns as $pattern) {
                if (stripos($content, $pattern) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * الحصول على رسالة خطأ الرفع
     * Get upload error message
     */
    private function getUploadErrorMessage($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File size exceeds server limit';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File size exceeds form limit';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
}

// ===================================
// HELPER FUNCTIONS
// ===================================

/**
 * Get file handler instance
 */
function file_handler() {
    return FileHandler::getInstance();
}

/**
 * Upload file (shorthand)
 */
function upload_file($file, $directory = DOCUMENT_UPLOADS_DIR, $options = []) {
    return file_handler()->uploadFile($file, $directory, $options);
}

/**
 * Delete file (shorthand)
 */
function delete_file($filename, $directory = DOCUMENT_UPLOADS_DIR) {
    return file_handler()->deleteFile($filename, $directory);
}

/**
 * Get file URL
 */
function get_file_url($filename, $directory = DOCUMENT_UPLOADS_DIR) {
    return UPLOADS_URL . '/' . $directory . '/' . $filename;
}

/**
 * Check if file exists
 */
function file_exists_in_uploads($filename, $directory = DOCUMENT_UPLOADS_DIR) {
    return file_exists(UPLOADS_PATH . '/' . $directory . '/' . $filename);
}
?>
