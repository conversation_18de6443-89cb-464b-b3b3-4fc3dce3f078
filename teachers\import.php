<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$error_message = '';
$success_message = '';
$import_report = [];

// معالجة الاستيراد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['import_file'])) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $file = $_FILES['import_file'];
        if ($file['error'] === UPLOAD_ERR_OK) {
            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($ext, ['csv'])) {
                $error_message = __('invalid_file_type');
            } else {
                $handle = fopen($file['tmp_name'], 'r');
                if ($handle) {
                    // قراءة المحتوى وتحويل الترميز
                    $content = file_get_contents($file['tmp_name']);

                    // إزالة BOM إذا كان موجوداً
                    $content = str_replace("\xEF\xBB\xBF", '', $content);

                    // تحويل الترميز إلى UTF-8 إذا لزم الأمر
                    if (!mb_check_encoding($content, 'UTF-8')) {
                        $content = mb_convert_encoding($content, 'UTF-8', 'auto');
                    }

                    // كتابة المحتوى المحسن إلى ملف مؤقت
                    $temp_file = tempnam(sys_get_temp_dir(), 'csv_import_teachers_');
                    file_put_contents($temp_file, $content);

                    // إعادة فتح الملف المحسن
                    fclose($handle);
                    $handle = fopen($temp_file, 'r');

                    $header = fgetcsv($handle);
                    $row_num = 1;
                    $added = 0;
                    $errors = 0;
                    while (($row = fgetcsv($handle)) !== false) {
                        $row_num++;
                        $data = array_combine($header, $row);
                        // جمع البيانات
                        $full_name = clean_input($data['full_name'] ?? '');
                        $username = clean_input($data['username'] ?? '');
                        $email = clean_input($data['email'] ?? '');
                        $password = $data['password'] ?? '';
                        $phone = clean_input($data['phone'] ?? '');
                        $national_id = clean_input($data['national_id'] ?? '');
                        $date_of_birth = clean_input($data['date_of_birth'] ?? '');
                        $gender = clean_input($data['gender'] ?? '');
                        $address = clean_input($data['address'] ?? '');
                        $employee_id = clean_input($data['employee_id'] ?? '');
                        $hire_date = clean_input($data['hire_date'] ?? '');
                        $department = clean_input($data['department'] ?? '');
                        $qualification = clean_input($data['qualification'] ?? '');
                        $specialization = clean_input($data['specialization'] ?? '');
                        $experience_years = intval($data['experience_years'] ?? 0);
                        $salary = floatval($data['salary'] ?? 0);
                        $status = clean_input($data['status'] ?? 'active');
                        // التحقق من صحة البيانات
                        $row_errors = [];
                        if (empty($full_name) || empty($username) || empty($email) || empty($password) || empty($employee_id) || empty($gender)) {
                            $row_errors[] = __('required_fields_missing');
                        }
                        if (!validate_email($email)) {
                            $row_errors[] = __('invalid_email');
                        }
                        if (!validate_password($password)) {
                            $row_errors[] = __('password_too_short');
                        }
                        if (get_user_by_username($username)) {
                            $row_errors[] = __('username_exists');
                        }
                        if (get_user_by_email($email)) {
                            $row_errors[] = __('email_exists');
                        }
                        $stmt = $conn->prepare("SELECT id FROM teachers WHERE employee_id = ?");
                        $stmt->bind_param("s", $employee_id);
                        $stmt->execute();
                        if ($stmt->get_result()->num_rows > 0) {
                            $row_errors[] = __('employee_id_already_exists');
                        }
                        if (!empty($national_id) && !validate_national_id($national_id)) {
                            $row_errors[] = __('invalid_national_id');
                        }
                        if (!empty($phone) && !validate_phone($phone)) {
                            $row_errors[] = __('invalid_phone');
                        }
                        if (!empty($date_of_birth) && !validate_date($date_of_birth)) {
                            $row_errors[] = __('invalid_date_of_birth');
                        }
                        if (!empty($hire_date) && !validate_date($hire_date)) {
                            $row_errors[] = __('invalid_hire_date');
                        }
                        if (empty($row_errors)) {
                            $conn->begin_transaction();
                            try {
                                $hashed_password = hash_password($password);
                                $user_stmt = $conn->prepare("
                                    INSERT INTO users (username, email, password, full_name, phone, role, status, created_at) 
                                    VALUES (?, ?, ?, ?, ?, 'teacher', ?, NOW())
                                ");
                                $user_stmt->bind_param("ssssss", $username, $email, $hashed_password, $full_name, $phone, $status);
                                $user_stmt->execute();
                                $user_id = $conn->insert_id;
                                $teacher_stmt = $conn->prepare("
                                    INSERT INTO teachers (
                                        user_id, employee_id, national_id, date_of_birth, gender, address, hire_date, department, qualification, specialization, experience_years, salary, status, created_at
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                                ");
                                $teacher_stmt->bind_param(
                                    "isssssssssssdssss",
                                    $user_id, $employee_id, $national_id, $date_of_birth, $gender, $address, $hire_date, $department, $qualification, $specialization, $experience_years, $salary, $status
                                );
                                $teacher_stmt->execute();
                                $conn->commit();
                                $added++;
                                $import_report[] = [
                                    'row' => $row_num,
                                    'status' => 'success',
                                    'message' => __('teacher_added_successfully')
                                ];
                            } catch (Exception $e) {
                                $conn->rollback();
                                $errors++;
                                $import_report[] = [
                                    'row' => $row_num,
                                    'status' => 'error',
                                    'message' => $e->getMessage()
                                ];
                            }
                        } else {
                            $errors++;
                            $import_report[] = [
                                'row' => $row_num,
                                'status' => 'error',
                                'message' => implode('; ', $row_errors)
                            ];
                        }
                    }
                    fclose($handle);

                    // تنظيف الملف المؤقت
                    if (isset($temp_file) && file_exists($temp_file)) {
                        unlink($temp_file);
                    }

                    $success_message = sprintf(__('import_summary'), $added, $errors);
                } else {
                    $error_message = __('file_open_error');
                }
            }
        } else {
            $error_message = __('file_upload_error');
        }
    }
}

$page_title = __('import_teachers');
require_once '../includes/header.php';
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}
?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('import_teachers'); ?></h1>
            <p class="text-muted"><?php echo __('import_teachers_info'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-upload me-2"></i><?php echo __('import_teachers'); ?>
            </h5>
        </div>
        <div class="card-body">
            <!-- تعليمات الاستيراد -->
            <div class="alert alert-info">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد
                </h6>
                <ul class="mb-2">
                    <li>يجب أن يكون الملف بصيغة CSV مع ترميز UTF-8</li>
                    <li>الصف الأول يجب أن يحتوي على رؤوس الأعمدة</li>
                    <li>الأعمدة المطلوبة: full_name, username, email, phone</li>
                    <li>الأعمدة الاختيارية: national_id, date_of_birth, gender, address, qualification, specialization, experience_years, hire_date, salary</li>
                    <li>استخدم النموذج المرفق لضمان التوافق مع النظام</li>
                </ul>
                <div class="alert alert-warning mb-0">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>ملاحظة:</strong> لضمان ظهور النصوص العربية بشكل صحيح، يُنصح بتحميل النموذج واستخدامه مباشرة.
                    </small>
                </div>
            </div>

            <!-- نموذج الاستيراد -->
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                <div class="mb-3">
                    <label for="import_file" class="form-label"><?php echo __('choose_file'); ?> (CSV) <span class="text-danger">*</span></label>
                    <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv" required>
                    <div class="form-text">الحد الأقصى لحجم الملف: 5MB</div>
                    <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="generate_csv_sample.php" class="btn btn-outline-info">
                        <i class="fas fa-download me-2"></i>تحميل نموذج CSV
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i><?php echo __('import'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php if (!empty($import_report)): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">نتائج الاستيراد</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>الصف</th>
                                <th>الحالة</th>
                                <th>الرسالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($import_report as $report): ?>
                                <tr>
                                    <td><?php echo $report['row']; ?></td>
                                    <td><?php echo $report['status'] === 'success' ? '<span class="badge bg-success">نجاح</span>' : '<span class="badge bg-danger">خطأ</span>'; ?></td>
                                    <td><?php echo htmlspecialchars($report['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php require_once '../includes/footer.php'; ?> 