/**
 * ملف المظهر الداكن
 * Dark Theme Stylesheet
 */

[data-theme="dark"] {
    --primary-color: #4c63d2;
    --secondary-color: #6c5ce7;
    --success-color: #00b894;
    --danger-color: #e17055;
    --warning-color: #fdcb6e;
    --info-color: #74b9ff;
    --light-color: #2c3e50;
    --dark-color: #ecf0f1;
    --bg-primary: #1a1a1a;
    --bg-secondary: #2c3e50;
    --bg-tertiary: #34495e;
    --text-primary: #ecf0f1;
    --text-secondary: #bdc3c7;
    --border-color: #34495e;
}

/* الخلفية العامة */
[data-theme="dark"] body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c3e50 100%);
    color: var(--text-primary);
}

/* الشريط العلوي */
[data-theme="dark"] .navbar {
    background: rgba(44, 62, 80, 0.95) !important;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .navbar-brand {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .navbar .btn {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .navbar .btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--primary-color);
}

/* الشريط الجانبي */
[data-theme="dark"] .sidebar {
    background: rgba(44, 62, 80, 0.95);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .sidebar-nav .nav-link {
    color: var(--text-primary);
}

[data-theme="dark"] .sidebar-nav .nav-link:hover {
    background: rgba(76, 99, 210, 0.2);
    color: var(--primary-color);
}

[data-theme="dark"] .sidebar-nav .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

/* المحتوى الرئيسي */
[data-theme="dark"] .main-content {
    background: rgba(44, 62, 80, 0.95);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

/* البطاقات */
[data-theme="dark"] .card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .card-header {
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .card-footer {
    background-color: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
}

/* النماذج */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(76, 99, 210, 0.25);
}

[data-theme="dark"] .form-control::placeholder {
    color: var(--text-secondary);
}

[data-theme="dark"] .form-label {
    color: var(--text-primary);
}

[data-theme="dark"] .input-group-text {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* الجداول */
[data-theme="dark"] .table {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

[data-theme="dark"] .table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--border-color);
}

[data-theme="dark"] .table td {
    border-color: var(--border-color);
}

[data-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(76, 99, 210, 0.1);
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

/* الأزرار */
[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

[data-theme="dark"] .btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .btn-outline-info {
    color: var(--info-color);
    border-color: var(--info-color);
}

[data-theme="dark"] .btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
}

[data-theme="dark"] .btn-outline-warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
}

[data-theme="dark"] .btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

/* التنبيهات */
[data-theme="dark"] .alert {
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .alert-primary {
    background-color: rgba(76, 99, 210, 0.2);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

[data-theme="dark"] .alert-success {
    background-color: rgba(0, 184, 148, 0.2);
    border-color: var(--success-color);
    color: var(--success-color);
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(253, 203, 110, 0.2);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(225, 112, 85, 0.2);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

[data-theme="dark"] .alert-info {
    background-color: rgba(116, 185, 255, 0.2);
    border-color: var(--info-color);
    color: var(--info-color);
}

/* النوافذ المنبثقة */
[data-theme="dark"] .modal-content {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .modal-body {
    color: var(--text-primary);
}

[data-theme="dark"] .modal-footer {
    border-top: 1px solid var(--border-color);
}

/* القوائم المنسدلة */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-primary);
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-theme="dark"] .dropdown-divider {
    border-color: var(--border-color);
}

/* الترقيم */
[data-theme="dark"] .pagination .page-link {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

[data-theme="dark"] .pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--primary-color);
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-secondary);
}

/* شريط التقدم */
[data-theme="dark"] .progress {
    background-color: var(--bg-tertiary);
}

[data-theme="dark"] .progress-bar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/* الشارات */
[data-theme="dark"] .badge.bg-secondary {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary);
}

[data-theme="dark"] .badge.bg-light {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary);
}

/* القوائم */
[data-theme="dark"] .list-group-item {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .list-group-item:hover {
    background-color: var(--bg-tertiary);
}

[data-theme="dark"] .list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* التبويبات */
[data-theme="dark"] .nav-tabs {
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .nav-tabs .nav-link {
    color: var(--text-secondary);
    border-color: transparent;
}

[data-theme="dark"] .nav-tabs .nav-link:hover {
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .nav-tabs .nav-link.active {
    background-color: var(--bg-secondary);
    border-color: var(--border-color) var(--border-color) var(--bg-secondary);
    color: var(--text-primary);
}

[data-theme="dark"] .tab-content {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-top: none;
}

/* النصوص */
[data-theme="dark"] .text-muted {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-dark {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .text-light {
    color: var(--text-secondary) !important;
}

/* الحدود */
[data-theme="dark"] .border {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .border-top {
    border-top-color: var(--border-color) !important;
}

[data-theme="dark"] .border-bottom {
    border-bottom-color: var(--border-color) !important;
}

[data-theme="dark"] .border-start {
    border-left-color: var(--border-color) !important;
}

[data-theme="dark"] .border-end {
    border-right-color: var(--border-color) !important;
}

/* الخلفيات */
[data-theme="dark"] .bg-light {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary);
}

[data-theme="dark"] .bg-white {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary);
}

/* شريط التمرير */
[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/* التحديد */
[data-theme="dark"] ::selection {
    background: var(--primary-color);
    color: white;
}

[data-theme="dark"] ::-moz-selection {
    background: var(--primary-color);
    color: white;
}

/* أنماط خاصة بالمكونات */
[data-theme="dark"] .stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

[data-theme="dark"] .exam-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .attendance-present {
    background-color: rgba(0, 184, 148, 0.2);
    color: var(--success-color);
    border-color: var(--success-color);
}

[data-theme="dark"] .attendance-absent {
    background-color: rgba(225, 112, 85, 0.2);
    color: var(--danger-color);
    border-color: var(--danger-color);
}

[data-theme="dark"] .attendance-late {
    background-color: rgba(253, 203, 110, 0.2);
    color: var(--warning-color);
    border-color: var(--warning-color);
}

[data-theme="dark"] .attendance-excused {
    background-color: rgba(116, 185, 255, 0.2);
    color: var(--info-color);
    border-color: var(--info-color);
}
