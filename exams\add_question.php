<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

$error = '';
$success = '';

// جلب الامتحانات
$exams = $conn->query("SELECT id, exam_title FROM exams");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $question_text = trim($_POST['question_text'] ?? '');
    $marks = intval($_POST['marks'] ?? 0);
    $exam_id = intval($_POST['exam_id'] ?? 0);
    if ($question_text && $exam_id) {
        $stmt = $conn->prepare("INSERT INTO exam_questions (question_text, marks, exam_id) VALUES (?, ?, ?)");
        $stmt->bind_param("sii", $question_text, $marks, $exam_id);
        if ($stmt->execute()) {
            $success = 'تمت إضافة السؤال بنجاح';
        } else {
            $error = 'حدث خطأ أثناء الإضافة';
        }
    } else {
        $error = 'يرجى تعبئة جميع الحقول المطلوبة';
    }
}
?>
<div class="container">
    <h2>إضافة سؤال جديد</h2>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label>نص السؤال</label>
            <textarea name="question_text" class="form-control" required></textarea>
        </div>
        <div class="mb-3">
            <label>الدرجة</label>
            <input type="number" name="marks" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>الامتحان</label>
            <select name="exam_id" class="form-control" required>
                <option value="">اختر الامتحان</option>
                <?php while($ex = $exams->fetch_assoc()): ?>
                <option value="<?= $ex['id'] ?>"><?= htmlspecialchars($ex['exam_title']) ?></option>
                <?php endwhile; ?>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">حفظ</button>
        <a href="question_bank.php" class="btn btn-secondary">رجوع</a>
    </form>
</div>
<?php require_once '../includes/footer.php'; ?> 