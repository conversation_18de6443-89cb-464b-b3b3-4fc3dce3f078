<?php
/**
 * مدير النظام الموحد للغياب والإجازات
 * Unified Absence and Leave Manager
 */

// تعريف الثوابت
define('SYSTEM_INIT', true);
define('DEBUG_MODE', true);

// تضمين ملفات التكوين
require_once 'includes/config.php';
require_once 'config/database.php';

// تعيين الترميز
mysqli_set_charset($conn, 'utf8mb4');

echo "<h1>🏢 مدير النظام الموحد للغياب والإجازات</h1>";
echo "<h1>🏢 Unified Absence and Leave Manager</h1>";

// التحقق من الاتصال بقاعدة البيانات
if (!isset($conn) || !$conn) {
    echo "<div class='alert alert-danger'>❌ فشل في الاتصال بقاعدة البيانات</div>";
    exit;
}

echo "<div class='alert alert-success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'record_absence':
            recordAbsence($conn);
            break;
        case 'approve_absence':
            approveAbsence($conn);
            break;
        case 'reject_absence':
            rejectAbsence($conn);
            break;
    }
}

/**
 * تسجيل غياب أو إجازة جديدة
 */
function recordAbsence($conn) {
    try {
        $user_id = intval($_POST['user_id']);
        $absence_date = clean_input($_POST['absence_date']);
        $end_date = clean_input($_POST['end_date']) ?: null;
        $absence_type = clean_input($_POST['absence_type']);
        $absence_category = clean_input($_POST['absence_category']);
        $days_count = intval($_POST['days_count']) ?: 1;
        $reason = clean_input($_POST['reason']);
        $has_deduction = intval($_POST['has_deduction']) ?: 0;
        $applied_by = 1; // يجب أن يكون من الجلسة
        
        $stmt = $conn->prepare("CALL record_unified_absence(?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("issssissi", $user_id, $absence_date, $end_date, $absence_type, 
                         $absence_category, $days_count, $reason, $has_deduction, $applied_by);
        
        if ($stmt->execute()) {
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            echo "<div class='alert alert-success'>✅ " . $row['message'] . "</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في تسجيل الغياب: " . $stmt->error . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

/**
 * الموافقة على غياب أو إجازة
 */
function approveAbsence($conn) {
    try {
        $absence_id = intval($_POST['absence_id']);
        $approved_by = 1; // يجب أن يكون من الجلسة
        $notes = clean_input($_POST['notes']);
        
        $stmt = $conn->prepare("CALL approve_absence(?, ?, ?)");
        $stmt->bind_param("iis", $absence_id, $approved_by, $notes);
        
        if ($stmt->execute()) {
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            echo "<div class='alert alert-success'>✅ " . $row['message'] . "</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في الموافقة: " . $stmt->error . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

/**
 * رفض غياب أو إجازة
 */
function rejectAbsence($conn) {
    try {
        $absence_id = intval($_POST['absence_id']);
        $rejected_by = 1; // يجب أن يكون من الجلسة
        $rejection_reason = clean_input($_POST['rejection_reason']);
        
        $stmt = $conn->prepare("
            UPDATE unified_staff_absences 
            SET status = 'rejected', rejected_by = ?, rejected_at = NOW(), rejection_reason = ?
            WHERE id = ?
        ");
        $stmt->bind_param("isi", $rejected_by, $rejection_reason, $absence_id);
        
        if ($stmt->execute()) {
            echo "<div class='alert alert-success'>✅ تم رفض الطلب بنجاح</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في رفض الطلب: " . $stmt->error . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

try {
    // عرض نموذج تسجيل غياب/إجازة جديدة
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h3>📝 تسجيل غياب أو إجازة جديدة</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    // جلب قائمة المستخدمين
    $users_result = $conn->query("SELECT id, full_name, role FROM users WHERE role IN ('teacher', 'staff', 'admin') ORDER BY full_name");
    
    echo "<form method='POST' class='row g-3'>";
    echo "<input type='hidden' name='action' value='record_absence'>";
    
    echo "<div class='col-md-6'>";
    echo "<label class='form-label'>الموظف:</label>";
    echo "<select name='user_id' class='form-select' required>";
    echo "<option value=''>اختر الموظف</option>";
    while ($user = $users_result->fetch_assoc()) {
        echo "<option value='" . $user['id'] . "'>" . $user['full_name'] . " (" . $user['role'] . ")</option>";
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<label class='form-label'>نوع الطلب:</label>";
    echo "<select name='absence_category' class='form-select' required onchange='updateAbsenceTypes(this.value)'>";
    echo "<option value=''>اختر النوع</option>";
    echo "<option value='absence'>غياب</option>";
    echo "<option value='leave'>إجازة</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<label class='form-label'>تصنيف الغياب/الإجازة:</label>";
    echo "<select name='absence_type' class='form-select' required id='absence_type_select'>";
    echo "<option value=''>اختر التصنيف</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<label class='form-label'>تاريخ البداية:</label>";
    echo "<input type='date' name='absence_date' class='form-control' required>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<label class='form-label'>تاريخ النهاية (اختياري):</label>";
    echo "<input type='date' name='end_date' class='form-control'>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<label class='form-label'>عدد الأيام:</label>";
    echo "<input type='number' name='days_count' class='form-control' value='1' min='1' required>";
    echo "</div>";
    
    echo "<div class='col-md-12'>";
    echo "<label class='form-label'>السبب:</label>";
    echo "<textarea name='reason' class='form-control' rows='3'></textarea>";
    echo "</div>";
    
    echo "<div class='col-md-12'>";
    echo "<div class='form-check'>";
    echo "<input type='checkbox' name='has_deduction' value='1' class='form-check-input' id='has_deduction'>";
    echo "<label class='form-check-label' for='has_deduction'>يتطلب خصم من الراتب</label>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-12'>";
    echo "<button type='submit' class='btn btn-primary'>تسجيل الطلب</button>";
    echo "</div>";
    
    echo "</form>";
    echo "</div>";
    echo "</div>";
    
    // عرض الطلبات المعلقة
    echo "<div class='card mt-4'>";
    echo "<div class='card-header'>";
    echo "<h3>⏳ الطلبات المعلقة</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    $pending_result = $conn->query("
        SELECT * FROM staff_absences_comprehensive 
        WHERE status = 'pending' 
        ORDER BY absence_date DESC 
        LIMIT 10
    ");
    
    if ($pending_result && $pending_result->num_rows > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>الموظف</th>";
        echo "<th>التاريخ</th>";
        echo "<th>النوع</th>";
        echo "<th>التصنيف</th>";
        echo "<th>الأيام</th>";
        echo "<th>الخصم</th>";
        echo "<th>السبب</th>";
        echo "<th>الإجراءات</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        while ($row = $pending_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['user_name'] . "</td>";
            echo "<td>" . $row['absence_date'] . "</td>";
            echo "<td>" . $row['absence_type_ar'] . "</td>";
            echo "<td>" . $row['absence_category_ar'] . "</td>";
            echo "<td>" . $row['calculated_days'] . "</td>";
            echo "<td>" . $row['has_deduction_ar'] . "</td>";
            echo "<td>" . substr($row['reason'], 0, 50) . "...</td>";
            echo "<td>";
            echo "<button class='btn btn-sm btn-success me-1' onclick='approveAbsence(" . $row['id'] . ")'>موافقة</button>";
            echo "<button class='btn btn-sm btn-danger' onclick='rejectAbsence(" . $row['id'] . ")'>رفض</button>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>لا توجد طلبات معلقة</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // عرض إحصائيات سريعة
    echo "<div class='card mt-4'>";
    echo "<div class='card-header'>";
    echo "<h3>📊 إحصائيات سريعة</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    $stats_result = $conn->query("
        SELECT 
            absence_category_ar,
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN has_deduction = 1 THEN deduction_amount ELSE 0 END) as total_deductions
        FROM staff_absences_comprehensive 
        WHERE absence_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY absence_category_ar
    ");
    
    if ($stats_result && $stats_result->num_rows > 0) {
        echo "<div class='row'>";
        while ($stat = $stats_result->fetch_assoc()) {
            echo "<div class='col-md-6'>";
            echo "<div class='card border-primary'>";
            echo "<div class='card-body text-center'>";
            echo "<h5>" . $stat['absence_category_ar'] . "</h5>";
            echo "<p class='mb-1'>إجمالي: " . $stat['total_count'] . "</p>";
            echo "<p class='mb-1'>مُعتمد: " . $stat['approved_count'] . "</p>";
            echo "<p class='mb-1'>معلق: " . $stat['pending_count'] . "</p>";
            echo "<p class='mb-0'>إجمالي الخصومات: " . number_format($stat['total_deductions'], 2) . " ريال</p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ في النظام</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

?>

<script>
// تحديث أنواع الغياب/الإجازة حسب التصنيف
function updateAbsenceTypes(category) {
    const select = document.getElementById('absence_type_select');
    select.innerHTML = '<option value="">اختر التصنيف</option>';
    
    if (category === 'absence') {
        select.innerHTML += '<option value="unauthorized">غياب غير مبرر</option>';
        select.innerHTML += '<option value="sick">غياب مرضي</option>';
        select.innerHTML += '<option value="personal">غياب شخصي</option>';
        select.innerHTML += '<option value="emergency">غياب طارئ</option>';
    } else if (category === 'leave') {
        select.innerHTML += '<option value="annual">إجازة سنوية</option>';
        select.innerHTML += '<option value="sick">إجازة مرضية</option>';
        select.innerHTML += '<option value="maternity">إجازة أمومة</option>';
        select.innerHTML += '<option value="paternity">إجازة أبوة</option>';
        select.innerHTML += '<option value="study">إجازة دراسية</option>';
        select.innerHTML += '<option value="unpaid">إجازة بدون راتب</option>';
    }
}

// الموافقة على طلب
function approveAbsence(absenceId) {
    const notes = prompt('ملاحظات الموافقة (اختياري):');
    if (notes !== null) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="approve_absence">
            <input type="hidden" name="absence_id" value="${absenceId}">
            <input type="hidden" name="notes" value="${notes}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// رفض طلب
function rejectAbsence(absenceId) {
    const reason = prompt('سبب الرفض:');
    if (reason && reason.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="reject_absence">
            <input type="hidden" name="absence_id" value="${absenceId}">
            <input type="hidden" name="rejection_reason" value="${reason}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
.alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.alert-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
.card { border: 1px solid #dee2e6; border-radius: 0.375rem; margin-bottom: 1rem; }
.card-header { padding: 0.75rem 1.25rem; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; }
.card-body { padding: 1.25rem; }
.table { width: 100%; border-collapse: collapse; margin: 10px 0; }
.table th, .table td { padding: 8px; text-align: right; border-bottom: 1px solid #ddd; }
.table-striped tbody tr:nth-child(odd) { background-color: #f9f9f9; }
.form-control, .form-select { width: 100%; padding: 0.375rem 0.75rem; margin-bottom: 0.5rem; border: 1px solid #ced4da; border-radius: 0.375rem; }
.btn { padding: 0.375rem 0.75rem; margin: 0.125rem; border: none; border-radius: 0.375rem; cursor: pointer; }
.btn-primary { background-color: #0d6efd; color: white; }
.btn-success { background-color: #198754; color: white; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
.row { display: flex; flex-wrap: wrap; margin: -0.5rem; }
.col-md-6 { flex: 0 0 50%; padding: 0.5rem; }
.col-md-12, .col-12 { flex: 0 0 100%; padding: 0.5rem; }
.form-check { margin: 0.5rem 0; }
.form-check-input { margin-left: 0.5rem; }
.table-responsive { overflow-x: auto; }
.me-1 { margin-left: 0.25rem; }
.mt-4 { margin-top: 1.5rem; }
.text-center { text-align: center; }
.border-primary { border-color: #0d6efd !important; }
</style>
