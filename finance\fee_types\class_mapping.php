<?php
/**
 * ربط أنواع الرسوم بالصفوف الدراسية
 * Fee Types Class Mapping
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة تحديث المبالغ
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صحيح';
    } else {
        $action = clean_input($_POST['action'] ?? '');
        
        if ($action === 'update_amounts') {
            $mappings = $_POST['mappings'] ?? [];
            $updated_count = 0;
            
            foreach ($mappings as $mapping_id => $amount) {
                $amount = floatval($amount);
                if ($amount >= 0) {
                    $update_stmt = $conn->prepare("UPDATE fee_type_classes SET amount = ?, updated_at = NOW() WHERE id = ?");
                    $update_stmt->bind_param("di", $amount, $mapping_id);
                    if ($update_stmt->execute()) {
                        $updated_count++;
                    }
                }
            }
            
            if ($updated_count > 0) {
                $success_message = "تم تحديث {$updated_count} مبلغ بنجاح";
            } else {
                $error_message = "لم يتم تحديث أي مبلغ";
            }
        }
        
        elseif ($action === 'toggle_status') {
            $mapping_id = intval($_POST['mapping_id'] ?? 0);
            if ($mapping_id > 0) {
                $toggle_stmt = $conn->prepare("UPDATE fee_type_classes SET is_active = !is_active WHERE id = ?");
                $toggle_stmt->bind_param("i", $mapping_id);
                if ($toggle_stmt->execute()) {
                    $success_message = "تم تغيير حالة الربط بنجاح";
                } else {
                    $error_message = "خطأ في تغيير حالة الربط";
                }
            }
        }

        elseif ($action === 'delete_mapping') {
            $mapping_id = intval($_POST['mapping_id'] ?? 0);
            if ($mapping_id > 0) {
                // جلب معلومات الربط قبل الحذف للتأكيد
                $mapping_info_stmt = $conn->prepare("
                    SELECT ft.type_name, c.class_name
                    FROM fee_type_classes ftc
                    JOIN fee_types ft ON ftc.fee_type_id = ft.id
                    JOIN classes c ON ftc.class_id = c.id
                    WHERE ftc.id = ?
                ");
                $mapping_info_stmt->bind_param("i", $mapping_id);
                $mapping_info_stmt->execute();
                $mapping_info = $mapping_info_stmt->get_result()->fetch_assoc();

                if ($mapping_info) {
                    $delete_stmt = $conn->prepare("DELETE FROM fee_type_classes WHERE id = ?");
                    $delete_stmt->bind_param("i", $mapping_id);
                    if ($delete_stmt->execute()) {
                        $success_message = "تم حذف ربط '{$mapping_info['type_name']}' مع '{$mapping_info['class_name']}' بنجاح";
                    } else {
                        $error_message = "خطأ في حذف الربط: " . $conn->error;
                    }
                } else {
                    $error_message = "الربط غير موجود";
                }
            }
        }
        
        elseif ($action === 'add_mapping') {
            $fee_type_id = intval($_POST['fee_type_id'] ?? 0);
            $class_id = intval($_POST['class_id'] ?? 0);
            $amount = floatval($_POST['amount'] ?? 0);
            
            if ($fee_type_id > 0 && $class_id > 0 && $amount >= 0) {
                $insert_stmt = $conn->prepare("
                    INSERT INTO fee_type_classes (fee_type_id, class_id, amount, is_active) 
                    VALUES (?, ?, ?, 1)
                    ON DUPLICATE KEY UPDATE amount = VALUES(amount), is_active = 1, updated_at = NOW()
                ");
                $insert_stmt->bind_param("iid", $fee_type_id, $class_id, $amount);
                
                if ($insert_stmt->execute()) {
                    $success_message = "تم إضافة/تحديث الربط بنجاح";
                } else {
                    $error_message = "خطأ في إضافة الربط: " . $conn->error;
                }
            } else {
                $error_message = "يرجى ملء جميع الحقول بقيم صحيحة";
            }
        }
    }
}

// جلب البيانات
$fee_types = $conn->query("SELECT id, type_name FROM fee_types WHERE status = 'active' ORDER BY type_name");
$classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");

// جلب الروابط الحالية
$mappings_query = "
    SELECT 
        ftc.*,
        ft.type_name,
        c.class_name,
        c.grade_level
    FROM fee_type_classes ftc
    JOIN fee_types ft ON ftc.fee_type_id = ft.id
    JOIN classes c ON ftc.class_id = c.id
    ORDER BY ft.type_name, c.grade_level, c.class_name
";
$mappings = $conn->query($mappings_query);

$page_title = 'ربط الرسوم بالصفوف';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-link me-2"></i>ربط أنواع الرسوم بالصفوف الدراسية
            </h1>
            <p class="text-muted mb-0">تحديد مبالغ الرسوم لكل صف دراسي</p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة لأنواع الرسوم
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج إضافة ربط جديد -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>إضافة ربط جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="add_mapping">
                        
                        <div class="mb-3">
                            <label class="form-label">نوع الرسوم</label>
                            <select class="form-select" name="fee_type_id" required>
                                <option value="">اختر نوع الرسوم</option>
                                <?php if ($fee_types && $fee_types->num_rows > 0): ?>
                                    <?php while ($fee_type = $fee_types->fetch_assoc()): ?>
                                        <option value="<?php echo $fee_type['id']; ?>">
                                            <?php echo htmlspecialchars($fee_type['type_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الصف الدراسي</label>
                            <select class="form-select" name="class_id" required>
                                <option value="">اختر الصف</option>
                                <?php if ($classes && $classes->num_rows > 0): ?>
                                    <?php 
                                    $classes->data_seek(0);
                                    while ($class = $classes->fetch_assoc()): ?>
                                        <option value="<?php echo $class['id']; ?>">
                                            <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['grade_level']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">المبلغ</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="amount" 
                                       min="0" step="0.01" required placeholder="0.00">
                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>إضافة الربط
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- جدول الروابط الحالية -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>الروابط الحالية
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if ($mappings && $mappings->num_rows > 0): ?>
                    <form method="POST" action="" id="update_amounts_form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="update_amounts">
                        
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>نوع الرسوم</th>
                                        <th>الصف</th>
                                        <th>المرحلة</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $current_fee_type = '';
                                    while ($mapping = $mappings->fetch_assoc()): 
                                        $is_new_fee_type = ($current_fee_type != $mapping['type_name']);
                                        if ($is_new_fee_type) {
                                            $current_fee_type = $mapping['type_name'];
                                        }
                                    ?>
                                    <tr <?php if (!$mapping['is_active']) echo 'class="table-secondary"'; ?>>
                                        <td>
                                            <?php if ($is_new_fee_type): ?>
                                                <strong><?php echo htmlspecialchars($mapping['type_name']); ?></strong>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($mapping['class_name']); ?></td>
                                        <td>
                                            <small class="text-muted"><?php echo htmlspecialchars($mapping['grade_level']); ?></small>
                                        </td>
                                        <td>
                                            <div class="input-group input-group-sm" style="width: 150px;">
                                                <input type="number" class="form-control" 
                                                       name="mappings[<?php echo $mapping['id']; ?>]"
                                                       value="<?php echo $mapping['amount']; ?>"
                                                       min="0" step="0.01"
                                                       <?php if (!$mapping['is_active']) echo 'disabled'; ?>>
                                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($mapping['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <!-- زر تفعيل/إلغاء تفعيل -->
                                                <form method="POST" action="" style="display: inline;">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="mapping_id" value="<?php echo $mapping['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-warning"
                                                            title="<?php echo $mapping['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $mapping['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                    </button>
                                                </form>

                                                <!-- زر الحذف -->
                                                <form method="POST" action="" style="display: inline;">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                                    <input type="hidden" name="action" value="delete_mapping">
                                                    <input type="hidden" name="mapping_id" value="<?php echo $mapping['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-danger"
                                                            title="حذف الربط"
                                                            onclick="return confirm('هل أنت متأكد من حذف ربط <?php echo htmlspecialchars($mapping['type_name']); ?> مع <?php echo htmlspecialchars($mapping['class_name']); ?>؟\n\nلا يمكن التراجع عن هذا الإجراء.')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="card-footer">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ جميع التعديلات
                            </button>
                        </div>
                    </form>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-link fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد روابط</h5>
                        <p class="text-muted">ابدأ بإضافة ربط بين نوع رسوم وصف دراسي</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.getElementById('update_amounts_form');
    if (form) {
        const inputs = form.querySelectorAll('input[type="number"]');
        let hasChanges = false;
        
        inputs.forEach(input => {
            const originalValue = input.value;
            input.addEventListener('input', function() {
                hasChanges = (this.value !== originalValue);
                updateSaveButton();
            });
        });
        
        function updateSaveButton() {
            const saveButton = form.querySelector('button[type="submit"]');
            if (hasChanges) {
                saveButton.classList.remove('btn-success');
                saveButton.classList.add('btn-warning');
                saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات (تم التغيير)';
            } else {
                saveButton.classList.remove('btn-warning');
                saveButton.classList.add('btn-success');
                saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ جميع التعديلات';
            }
        }
    }
});
</script>

<?php include_once '../../includes/footer.php'; ?>
