<?php
/**
 * API إدارة الإشعارات
 * Notifications Management API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

define('SYSTEM_INIT', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/notification_functions.php';

// بدء الجلسة والتحقق من الصلاحيات
session_start();
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];
$user_id = $_SESSION['user_id'];

try {
    switch ($method) {
        case 'GET':
            handle_get_notifications($user_id);
            break;
            
        case 'POST':
            handle_create_notification();
            break;
            
        case 'PUT':
            handle_update_notification($user_id);
            break;
            
        case 'DELETE':
            handle_delete_notification($user_id);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    log_error("Error in notifications API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * جلب الإشعارات
 */
function handle_get_notifications($user_id) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            $limit = intval($_GET['limit'] ?? 10);
            $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
            
            $notifications = get_user_notifications($user_id, $limit, $unread_only);
            $unread_count = get_unread_notifications_count($user_id);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'unread_count' => $unread_count,
                'total_count' => count($notifications)
            ]);
            break;
            
        case 'count':
            $unread_count = get_unread_notifications_count($user_id);
            echo json_encode([
                'success' => true,
                'unread_count' => $unread_count
            ]);
            break;
            
        case 'recent':
            $notifications = get_user_notifications($user_id, 5, false);
            echo json_encode([
                'success' => true,
                'notifications' => array_map(function($notification) {
                    return [
                        'id' => $notification['id'],
                        'title' => $notification['title'],
                        'message' => truncate_text($notification['message'], 100),
                        'type' => $notification['type'],
                        'is_read' => $notification['is_read'],
                        'created_at' => $notification['created_at'],
                        'time_ago' => time_ago($notification['created_at'])
                    ];
                }, $notifications)
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * إنشاء إشعار جديد
 */
function handle_create_notification() {
    global $conn;
    
    // التحقق من الصلاحيات
    if (!check_permission('admin')) {
        http_response_code(403);
        echo json_encode(['error' => 'Forbidden']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON']);
        return;
    }
    
    $required_fields = ['user_id', 'title', 'message'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $user_id = intval($input['user_id']);
    $title = clean_input($input['title']);
    $message = clean_input($input['message']);
    $type = clean_input($input['type'] ?? 'info');
    $action_url = clean_input($input['action_url'] ?? null);
    
    // إنشاء الإشعار
    if (isset($input['user_ids']) && is_array($input['user_ids'])) {
        // إشعار جماعي
        $result = add_bulk_notification($input['user_ids'], $title, $message, $type, $action_url);
        echo json_encode([
            'success' => true,
            'message' => 'Bulk notification sent',
            'sent_count' => $result
        ]);
    } else {
        // إشعار فردي
        $result = add_notification($user_id, $title, $message, $type, $action_url);
        echo json_encode([
            'success' => $result,
            'message' => $result ? 'Notification sent successfully' : 'Failed to send notification'
        ]);
    }
}

/**
 * تحديث الإشعار (تحديد كمقروء)
 */
function handle_update_notification($user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON']);
        return;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'mark_read':
            if (isset($input['notification_id'])) {
                $notification_id = intval($input['notification_id']);
                $result = mark_notification_read($notification_id, $user_id);
            } else {
                $result = mark_all_notifications_read($user_id);
            }
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? 'Notification(s) marked as read' : 'Failed to update notification(s)'
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * حذف الإشعار
 */
function handle_delete_notification($user_id) {
    global $conn;
    
    $notification_id = intval($_GET['id'] ?? 0);
    
    if (empty($notification_id)) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required']);
        return;
    }
    
    try {
        $stmt = $conn->prepare("DELETE FROM notifications WHERE id = ? AND user_id = ?");
        $stmt->bind_param("ii", $notification_id, $user_id);
        $result = $stmt->execute();
        
        if ($result && $conn->affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Notification deleted successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Notification not found or already deleted'
            ]);
        }
        
    } catch (Exception $e) {
        log_error("Error deleting notification: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete notification']);
    }
}

/**
 * حساب الوقت المنقضي
 */
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return __('just_now');
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' ' . __('minutes_ago');
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' ' . __('hours_ago');
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ' ' . __('days_ago');
    } else {
        return format_date($datetime);
    }
}

/**
 * اختصار النص
 */
function truncate_text($text, $length = 100) {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . '...';
}
?>
