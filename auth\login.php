<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

define('SYSTEM_INIT', true);
require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

session_start();

// Debug: تحقق من وصول الطلب إلى ملف تسجيل الدخول
file_put_contents(__DIR__ . '/../debug_login.txt', "login.php loaded | POST: " . print_r($_POST, true) . "\n", FILE_APPEND);

// التحقق من وجود ملف التثبيت
if (!file_exists('../config/installed.lock')) {
    header('Location: ../install/setup.php');
    exit();
}

// إذا كان المستخدم مسجل دخوله بالفعل، إعادة توجيه إلى لوحة التحكم
if (is_logged_in()) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = clean_input($_POST['email'] ?? '', 'email');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    // التحقق من صحة البيانات
    if (empty($email) || empty($password)) {
        $error_message = __('please_fill_all_fields');
    } else {
        // التحقق من محاولات تسجيل الدخول
        $ip_address = get_client_ip();
        $login_check = security()->checkLoginAttempts($email, $ip_address);
        
        if (!$login_check['allowed']) {
            $minutes_left = ceil($login_check['lockout_time'] / 60);
            $error_message = sprintf(__('account_locked_minutes'), $minutes_left);
        } else {
            // محاولة تسجيل الدخول
            $user = authenticate_user($email, $password);
            
            if ($user) {
                // تسجيل محاولة ناجحة
                security()->logLoginAttempt($email, $ip_address, $_SERVER['HTTP_USER_AGENT'] ?? '', true);
                
                // إنشاء الجلسة
                create_user_session($user);
                
                // تذكرني
                if ($remember_me) {
                    set_remember_me_cookie($user['id']);
                }
                
                // تسجيل النشاط
                log_user_activity($user['id'], 'login', 'User logged in');
                
                // إعادة التوجيه
                $redirect_url = $_GET['redirect'] ?? '../dashboard/';
                header('Location: ' . $redirect_url);
                exit();
            } else {
                // تسجيل محاولة فاشلة
                security()->logLoginAttempt($email, $ip_address, $_SERVER['HTTP_USER_AGENT'] ?? '', false, 'Invalid credentials');
                $error_message = __('invalid_credentials');
            }
        }
    }
}

// الحصول على إعدادات النظام
$school_name = get_system_setting('school_name', 'School Management System');
$current_language = get_current_language();
$is_rtl = in_array($current_language, RTL_LANGUAGES);
?>

<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('login'); ?> - <?php echo $school_name; ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/custom.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        .login-content {
            position: relative;
            z-index: 2;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .language-switcher {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            padding: 10px 15px;
            color: white;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="dropdown position-fixed" style="top: 20px; right: 20px; z-index: 1000;">
        <button class="language-switcher dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-globe me-1"></i>
            <?php echo $current_language === 'ar' ? 'العربية' : 'English'; ?>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
            <li><a class="dropdown-item" href="?lang=en">English</a></li>
        </ul>
    </div>

    <div class="login-container">
        <div class="login-content">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h2 class="fw-bold text-dark"><?php echo __('welcome_back'); ?></h2>
                <p class="text-muted"><?php echo __('login_to_continue'); ?></p>
            </div>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <div class="form-floating">
                    <input type="email" 
                           class="form-control" 
                           id="email" 
                           name="email" 
                           placeholder="<?php echo __('email'); ?>"
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                           required>
                    <label for="email">
                        <i class="fas fa-envelope me-2"></i>
                        <?php echo __('email'); ?>
                    </label>
                    <div class="invalid-feedback">
                        <?php echo __('please_enter_valid_email'); ?>
                    </div>
                </div>

                <div class="form-floating">
                    <input type="password" 
                           class="form-control" 
                           id="password" 
                           name="password" 
                           placeholder="<?php echo __('password'); ?>"
                           required>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>
                        <?php echo __('password'); ?>
                    </label>
                    <div class="invalid-feedback">
                        <?php echo __('please_enter_password'); ?>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                    <label class="form-check-label" for="remember_me">
                        <?php echo __('remember_me'); ?>
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    <?php echo __('login'); ?>
                </button>
            </form>

            <div class="text-center mt-4">
                <a href="forgot_password.php" class="text-decoration-none">
                    <?php echo __('forgot_password'); ?>?
                </a>
            </div>

            <div class="text-center mt-3">
                <a href="../index.php" class="text-muted text-decoration-none">
                    <i class="fas fa-arrow-left me-1"></i>
                    <?php echo __('back_to_home'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Language switcher
        document.addEventListener('DOMContentLoaded', function() {
            const langLinks = document.querySelectorAll('.dropdown-item[href*="lang="]');
            langLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.href.split('lang=')[1];
                    window.location.href = '?lang=' + lang;
                });
            });
        });
    </script>
</body>
</html>
