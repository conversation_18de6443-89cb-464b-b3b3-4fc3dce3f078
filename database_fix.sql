-- إصلا<PERSON> قاعدة البيانات school_management
-- Database Fix Script for school_management

USE school_management;

-- إصلاح جدول staff_leaves إذا كان يحتوي على أعمدة خاطئة
-- Fix staff_leaves table if it contains wrong columns

-- التحقق من وجود أعمدة خاطئة وحذفها إذا كانت موجودة
-- Check for wrong columns and drop them if they exist

-- حذف الفهارس الخاطئة إذا كانت موجودة
-- Drop wrong indexes if they exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = 'school_management' 
     AND TABLE_NAME = 'staff_leaves' 
     AND INDEX_NAME = 'idx_user_type') > 0,
    'ALTER TABLE staff_leaves DROP INDEX idx_user_type',
    'SELECT "Index idx_user_type does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = 'school_management' 
     AND TABLE_NAME = 'staff_leaves' 
     AND INDEX_NAME = 'idx_leave_type') > 0,
    'ALTER TABLE staff_leaves DROP INDEX idx_leave_type',
    'SELECT "Index idx_leave_type does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- حذف الأعمدة الخاطئة إذا كانت موجودة
-- Drop wrong columns if they exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'school_management' 
     AND TABLE_NAME = 'staff_leaves' 
     AND COLUMN_NAME = 'user_type') > 0,
    'ALTER TABLE staff_leaves DROP COLUMN user_type',
    'SELECT "Column user_type does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'school_management' 
     AND TABLE_NAME = 'staff_leaves' 
     AND COLUMN_NAME = 'leave_type') > 0,
    'ALTER TABLE staff_leaves DROP COLUMN leave_type',
    'SELECT "Column leave_type does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'school_management' 
     AND TABLE_NAME = 'staff_leaves' 
     AND COLUMN_NAME = 'notes') > 0,
    'ALTER TABLE staff_leaves DROP COLUMN notes',
    'SELECT "Column notes does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة الفهارس الصحيحة إذا لم تكن موجودة
-- Add correct indexes if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = 'school_management' 
     AND TABLE_NAME = 'staff_leaves' 
     AND INDEX_NAME = 'idx_leave_type_id') = 0,
    'ALTER TABLE staff_leaves ADD KEY idx_leave_type_id (leave_type_id)',
    'SELECT "Index idx_leave_type_id already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة القيود الخارجية إذا لم تكن موجودة
-- Add foreign key constraints if they don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
     WHERE TABLE_SCHEMA = 'school_management' 
     AND TABLE_NAME = 'staff_leaves' 
     AND CONSTRAINT_NAME = 'staff_leaves_leave_type_id_foreign') = 0,
    'ALTER TABLE staff_leaves ADD CONSTRAINT staff_leaves_leave_type_id_foreign FOREIGN KEY (leave_type_id) REFERENCES leave_types (id) ON DELETE RESTRICT',
    'SELECT "Foreign key staff_leaves_leave_type_id_foreign already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إدراج بيانات تجريبية صحيحة في جدول staff_leaves
-- Insert correct sample data into staff_leaves table
INSERT IGNORE INTO staff_leaves (
    id, user_id, leave_type_id, start_date, end_date, 
    total_days, working_days, reason, status, applied_by, 
    created_at, updated_at
) VALUES 
(8, 12, 2, '2025-07-20', '2025-07-26', 7.00, 7.00, 'مرض', 'approved', 15, '2025-07-20 09:55:50', '2025-07-20 09:57:02'),
(11, 18, 2, '2025-07-20', '2025-07-23', 4.00, 4.00, '', 'approved', 15, '2025-07-20 10:04:08', '2025-07-20 10:04:08');

-- التحقق من وجود جداول أخرى مطلوبة
-- Check for other required tables

-- إنشاء جدول system_settings إذا لم يكن موجوداً
-- Create system_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_settings (
    id int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    setting_key varchar(100) NOT NULL,
    setting_value text,
    description text,
    setting_type enum('string','number','boolean','json') DEFAULT 'string',
    is_public tinyint(1) DEFAULT 0,
    created_at timestamp NOT NULL DEFAULT current_timestamp(),
    updated_at timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (id),
    UNIQUE KEY setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج إعدادات النظام الأساسية
-- Insert basic system settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('system_name', 'نظام إدارة المدارس', 'اسم النظام'),
('system_version', '2.0.0', 'إصدار النظام'),
('timezone', 'Asia/Riyadh', 'المنطقة الزمنية'),
('language', 'ar', 'اللغة الافتراضية'),
('currency', 'ريال سعودي', 'العملة الافتراضية'),
('currency_symbol', 'ر.س', 'رمز العملة'),
('academic_year_start', '09-01', 'بداية العام الدراسي'),
('academic_year_end', '06-30', 'نهاية العام الدراسي');

-- إنشاء جدول users إذا لم يكن موجوداً
-- Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
    id int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    username varchar(50) NOT NULL,
    email varchar(100) NOT NULL,
    password varchar(255) NOT NULL,
    full_name varchar(100) NOT NULL,
    role enum('admin','teacher','student','staff','parent') NOT NULL DEFAULT 'student',
    status enum('active','inactive','suspended','pending') DEFAULT 'active',
    phone varchar(20) DEFAULT NULL,
    address text DEFAULT NULL,
    profile_picture varchar(255) DEFAULT NULL,
    last_login timestamp NULL DEFAULT NULL,
    email_verified_at timestamp NULL DEFAULT NULL,
    remember_token varchar(100) DEFAULT NULL,
    created_at timestamp NOT NULL DEFAULT current_timestamp(),
    updated_at timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (id),
    UNIQUE KEY username (username),
    UNIQUE KEY email (email),
    KEY idx_role (role),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج مستخدم admin افتراضي إذا لم يكن موجوداً
-- Insert default admin user if it doesn't exist
INSERT IGNORE INTO users (id, username, email, password, full_name, role, status) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 'active'),
(2, 'teacher1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'معلم تجريبي', 'teacher', 'active');

SELECT 'Database fix completed successfully!' as message;
