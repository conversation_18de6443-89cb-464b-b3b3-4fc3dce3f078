<?php
/**
 * تقارير الطلاب
 * Student Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

// فلاتر التقرير
$filter_class = clean_input($_GET['class'] ?? '');
$filter_status = clean_input($_GET['status'] ?? '');
$filter_gender = clean_input($_GET['gender'] ?? '');

// بناء الاستعلام
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($filter_class)) {
    $where_conditions[] = "s.class_id = ?";
    $params[] = $filter_class;
    $param_types .= 'i';
}

if (!empty($filter_status)) {
    $where_conditions[] = "s.status = ?";
    $params[] = $filter_status;
    $param_types .= 's';
}

if (!empty($filter_gender)) {
    $where_conditions[] = "s.gender = ?";
    $params[] = $filter_gender;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب تقارير الطلاب
$students_query = "
    SELECT s.*, u.full_name, u.email, u.phone, c.class_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    $where_clause
    ORDER BY c.class_name, u.full_name
";

$students_stmt = $conn->prepare($students_query);
if (!empty($params)) {
    $students_stmt->bind_param($param_types, ...$params);
}
$students_stmt->execute();
$students_result = $students_stmt->get_result();

// إحصائيات
$stats_query = "
    SELECT 
        COUNT(*) as total_students,
        COUNT(CASE WHEN s.status = 'active' THEN 1 END) as active_students,
        COUNT(CASE WHEN s.gender = 'male' THEN 1 END) as male_students,
        COUNT(CASE WHEN s.gender = 'female' THEN 1 END) as female_students
    FROM students s
    JOIN users u ON s.user_id = u.id
    $where_clause
";

$stats_stmt = $conn->prepare($stats_query);
if (!empty($params)) {
    $stats_stmt->bind_param($param_types, ...$params);
}
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// جلب قائمة الفصول
$classes_query = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_query);

include_once '../includes/header.php';
?>

<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-graduate me-2"></i><?php echo __('student_reports'); ?>
                        </h5>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back_to_reports'); ?>
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i><?php echo __('report_filters'); ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="class" class="form-label"><?php echo __('class'); ?></label>
                                    <select class="form-select" id="class" name="class">
                                        <option value=""><?php echo __('all_classes'); ?></option>
                                        <?php while ($class = $classes_result->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo $filter_class == $class['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                                    <select class="form-select" id="status" name="status">
                                        <option value=""><?php echo __('all_statuses'); ?></option>
                                        <option value="active" <?php echo $filter_status === 'active' ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                                        <option value="inactive" <?php echo $filter_status === 'inactive' ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                                        <option value="graduated" <?php echo $filter_status === 'graduated' ? 'selected' : ''; ?>><?php echo __('graduated'); ?></option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="gender" class="form-label"><?php echo __('gender'); ?></label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value=""><?php echo __('all_genders'); ?></option>
                                        <option value="male" <?php echo $filter_gender === 'male' ? 'selected' : ''; ?>><?php echo __('male'); ?></option>
                                        <option value="female" <?php echo $filter_gender === 'female' ? 'selected' : ''; ?>><?php echo __('female'); ?></option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i><?php echo __('generate_report'); ?>
                                        </button>
                                        <a href="?export=excel&<?php echo http_build_query($_GET); ?>" class="btn btn-success">
                                            <i class="fas fa-file-excel me-2"></i><?php echo __('export_excel'); ?>
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- إحصائيات -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['total_students'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('total_students'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['active_students'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('active_students'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['male_students'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('male_students'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($stats['female_students'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('female_students'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الطلاب -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><?php echo __('student_id'); ?></th>
                                    <th><?php echo __('full_name'); ?></th>
                                    <th><?php echo __('class'); ?></th>
                                    <th><?php echo __('gender'); ?></th>
                                    <th><?php echo __('phone'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                    <th><?php echo __('enrollment_date'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($students_result->num_rows > 0): ?>
                                    <?php while ($student = $students_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                            <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($student['class_name'] ?? '-'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $student['gender'] === 'male' ? 'primary' : 'pink'; ?>">
                                                    <?php echo __($student['gender']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($student['phone'] ?? '-'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $student['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo __($student['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y/m/d', strtotime($student['enrollment_date'])); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center"><?php echo __('no_students_found'); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
