<?php
/**
 * تعديل المصروف اليومي
 * Edit Daily Expense
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// التحقق من وجود معرف المصروف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف المصروف غير صحيح';
    header('Location: index.php');
    exit();
}

$expense_id = intval($_GET['id']);

// جلب بيانات المصروف
$expense_query = "
    SELECT 
        de.*,
        ec.category_name
    FROM daily_expenses de
    JOIN expense_categories ec ON de.category_id = ec.id
    WHERE de.id = ?
";

$expense_stmt = $conn->prepare($expense_query);
$expense_stmt->bind_param("i", $expense_id);
$expense_stmt->execute();
$expense_result = $expense_stmt->get_result();

if ($expense_result->num_rows === 0) {
    $_SESSION['error_message'] = 'المصروف غير موجود';
    header('Location: index.php');
    exit();
}

$expense = $expense_result->fetch_assoc();

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $expense_date = $_POST['expense_date'] ?? '';
    $category_id = intval($_POST['category_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $receipt_number = trim($_POST['receipt_number'] ?? '');
    $payment_method = $_POST['payment_method'] ?? 'cash';
    $vendor_name = trim($_POST['vendor_name'] ?? '');
    $vendor_phone = trim($_POST['vendor_phone'] ?? '');
    $status = $_POST['status'] ?? $expense['status'];
    
    $errors = [];
    
    // التحقق من صحة البيانات
    if (empty($expense_date)) {
        $errors[] = 'تاريخ المصروف مطلوب';
    }
    
    if ($category_id <= 0) {
        $errors[] = 'فئة المصروف مطلوبة';
    }
    
    if ($amount <= 0) {
        $errors[] = 'مبلغ المصروف يجب أن يكون أكبر من صفر';
    }
    
    if (empty($description)) {
        $errors[] = 'وصف المصروف مطلوب';
    }
    
    if (empty($errors)) {
        try {
            $conn->begin_transaction();
            
            // تحديث المصروف
            $update_query = "
                UPDATE daily_expenses 
                SET expense_date = ?, 
                    category_id = ?, 
                    amount = ?, 
                    description = ?, 
                    receipt_number = ?, 
                    payment_method = ?, 
                    vendor_name = ?, 
                    vendor_phone = ?, 
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ";
            
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param(
                "sidssssssi",
                $expense_date,
                $category_id,
                $amount,
                $description,
                $receipt_number,
                $payment_method,
                $vendor_name,
                $vendor_phone,
                $status,
                $expense_id
            );
            
            if ($update_stmt->execute()) {
                $conn->commit();
                
                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'update_expense', 'daily_expenses', $expense_id, [
                    'amount' => $amount,
                    'category_id' => $category_id,
                    'description' => $description
                ]);
                
                $_SESSION['success_message'] = 'تم تحديث المصروف بنجاح';
                header('Location: view.php?id=' . $expense_id);
                exit();
            } else {
                throw new Exception('فشل في تحديث المصروف');
            }
            
        } catch (Exception $e) {
            $conn->rollback();
            $errors[] = 'حدث خطأ: ' . $e->getMessage();
            log_error("Error updating expense: " . $e->getMessage());
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// جلب الفئات
$categories_query = "SELECT id, category_name FROM expense_categories WHERE is_active = 1 ORDER BY category_name";
$categories_result = $conn->query($categories_query);

$page_title = 'تعديل المصروف - ' . $expense['description'];
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2"></i>تعديل المصروف
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../index.php">المالية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">المصروفات</a></li>
                    <li class="breadcrumb-item active">تعديل المصروف</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="view.php?id=<?php echo $expense['id']; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للمعاينة
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نموذج التعديل -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-edit me-2"></i>تعديل بيانات المصروف
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="expense_date" class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="expense_date" name="expense_date" 
                               value="<?php echo htmlspecialchars($expense['expense_date']); ?>" required>
                        <div class="invalid-feedback">يرجى إدخال تاريخ المصروف</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="category_id" class="form-label">فئة المصروف <span class="text-danger">*</span></label>
                        <select class="form-select" id="category_id" name="category_id" required>
                            <option value="">اختر الفئة</option>
                            <?php while ($category = $categories_result->fetch_assoc()): ?>
                                <option value="<?php echo $category['id']; ?>" 
                                        <?php echo ($expense['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار فئة المصروف</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label">المبلغ (ج.م) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="amount" name="amount" 
                               value="<?php echo $expense['amount']; ?>" step="0.01" min="0.01" required>
                        <div class="invalid-feedback">يرجى إدخال مبلغ صحيح</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="cash" <?php echo ($expense['payment_method'] == 'cash') ? 'selected' : ''; ?>>نقدي</option>
                            <option value="bank_transfer" <?php echo ($expense['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>تحويل بنكي</option>
                            <option value="credit_card" <?php echo ($expense['payment_method'] == 'credit_card') ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                            <option value="check" <?php echo ($expense['payment_method'] == 'check') ? 'selected' : ''; ?>>شيك</option>
                        </select>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <label for="description" class="form-label">وصف المصروف <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="3" required><?php echo htmlspecialchars($expense['description']); ?></textarea>
                        <div class="invalid-feedback">يرجى إدخال وصف المصروف</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="receipt_number" class="form-label">رقم الإيصال</label>
                        <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                               value="<?php echo htmlspecialchars($expense['receipt_number']); ?>">
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="pending" <?php echo ($expense['status'] == 'pending') ? 'selected' : ''; ?>>في انتظار الموافقة</option>
                            <option value="approved" <?php echo ($expense['status'] == 'approved') ? 'selected' : ''; ?>>معتمد</option>
                            <option value="rejected" <?php echo ($expense['status'] == 'rejected') ? 'selected' : ''; ?>>مرفوض</option>
                        </select>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="vendor_name" class="form-label">اسم المورد</label>
                        <input type="text" class="form-control" id="vendor_name" name="vendor_name" 
                               value="<?php echo htmlspecialchars($expense['vendor_name']); ?>">
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="vendor_phone" class="form-label">هاتف المورد</label>
                        <input type="tel" class="form-control" id="vendor_phone" name="vendor_phone" 
                               value="<?php echo htmlspecialchars($expense['vendor_phone']); ?>">
                    </div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                        <a href="view.php?id=<?php echo $expense['id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteExpense(<?php echo $expense['id']; ?>)">
                            <i class="fas fa-trash me-2"></i>حذف المصروف
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

function deleteExpense(expenseId) {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟ لا يمكن التراجع عن هذا الإجراء.')) {
        window.location.href = 'delete.php?id=' + expenseId;
    }
}
</script>
