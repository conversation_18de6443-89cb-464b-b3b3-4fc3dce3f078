# نظام إدارة المدارس
## School Management System

نظام شامل لإدارة المدارس يتضمن إدارة الطلاب والمعلمين والفصول والمواد والرسوم والحضور والغياب.

## 🚀 التثبيت السريع

### 1. متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مساحة تخزين كافية

### 2. خطوات التثبيت

#### أ) إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة: `school_management`
2. استورد ملف: `database/school_management.sql`

#### ب) إعداد الاتصال
حدث ملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'school_management');
define('DB_USER', 'your_username');
define('DB_PASSWORD', 'your_password');
```

#### ج) ضبط الصلاحيات
```bash
chmod 755 uploads/
chmod 755 logs/
```

### 3. بيانات الدخول
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `password`

⚠️ **مهم:** غير كلمة المرور فوراً بعد الدخول!

## 📋 الميزات الرئيسية

### 👥 إدارة الطلاب
- تسجيل وإدارة بيانات الطلاب
- تتبع الحضور والغياب
- إدارة الدرجات والنتائج

### 👨‍🏫 إدارة المعلمين
- إضافة وإدارة المعلمين
- تكليف المواد والفصول
- تتبع الحضور والإجازات

### 💰 النظام المالي
- إدارة الرسوم والأقساط
- تتبع المدفوعات
- المصروفات اليومية
- التقارير المالية الشاملة

### 🏫 إدارة الفصول والمواد
- إنشاء الفصول والمراحل الدراسية
- إدارة المواد الدراسية
- ربط المعلمين بالمواد

### 📊 التقارير
- تقارير الحضور والغياب
- التقارير المالية التفصيلية
- تقارير الدرجات والنتائج
## 🗂️ هيكل المشروع

```
school_system_v2/
├── database/
│   └── school_management.sql    # قاعدة البيانات الوحيدة
├── config/
│   └── database.php            # إعدادات الاتصال
├── includes/                   # الملفات المشتركة
├── dashboard/                  # لوحة التحكم
├── students/                   # إدارة الطلاب
├── teachers/                   # إدارة المعلمين
├── finance/                    # النظام المالي
├── attendance/                 # الحضور والغياب
├── reports/                    # التقارير
└── assets/                     # الملفات الثابتة
```

## 🔧 الإعدادات

### تغيير اللغة
يمكن التبديل بين العربية والإنجليزية من أعلى الصفحة.

### إعدادات العملة
يمكن تغيير العملة من: **الإعدادات → إعدادات النظام**

### النسخ الاحتياطية
يُنصح بإنشاء نسخة احتياطية دورية من قاعدة البيانات.
## 📞 الدعم الفني

للحصول على المساعدة:
1. تحقق من سجلات الأخطاء في مجلد `logs/`
2. تأكد من صحة إعدادات قاعدة البيانات
3. تحقق من صلاحيات الملفات والمجلدات

---

**الإصدار:** 2.0
**آخر تحديث:** 2025-07-29
**الحالة:** مستقر ومُختبر ✅
