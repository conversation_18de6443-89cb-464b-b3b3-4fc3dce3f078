<?php
/**
 * صفحة المواد الدراسية - نسخة نظيفة وصحيحة
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// معالجة الفلاتر
$stage_filter = isset($_GET['stage']) ? intval($_GET['stage']) : 0;
$grade_filter = isset($_GET['grade']) ? intval($_GET['grade']) : 0;
$department_filter = isset($_GET['department']) ? trim($_GET['department']) : '';
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';

// فحص الأعمدة المتاحة في جدول subjects
$subjects_structure = $conn->query("DESCRIBE subjects");
$subjects_columns = [];
while ($row = $subjects_structure->fetch_assoc()) {
    $subjects_columns[] = $row['Field'];
}

$has_stage_id = in_array('stage_id', $subjects_columns);
$has_grade_id = in_array('grade_id', $subjects_columns);

// بناء استعلام المواد مع الفلاتر
$where_conditions = [];
$params = [];
$param_types = '';

if ($stage_filter > 0 && $has_stage_id) {
    $where_conditions[] = "s.stage_id = ?";
    $params[] = $stage_filter;
    $param_types .= 'i';
}

if ($grade_filter > 0 && $has_grade_id) {
    $where_conditions[] = "s.grade_id = ?";
    $params[] = $grade_filter;
    $param_types .= 'i';
}

if (!empty($department_filter)) {
    $where_conditions[] = "s.department = ?";
    $params[] = $department_filter;
    $param_types .= 's';
}

if (!empty($status_filter)) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if (!empty($search_query)) {
    $where_conditions[] = "(s.subject_name LIKE ? OR s.subject_code LIKE ? OR s.description LIKE ?)";
    $search_param = "%{$search_query}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// بناء الاستعلام بشكل آمن
$select_fields = ['s.*'];
$joins = [];

// فحص الجداول المتاحة
$tables_result = $conn->query("SHOW TABLES");
$available_tables = [];
while ($row = $tables_result->fetch_array()) {
    $available_tables[] = $row[0];
}

// إضافة المراحل التعليمية إذا كانت متاحة
if ($has_stage_id && in_array('educational_stages', $available_tables)) {
    $joins[] = 'LEFT JOIN educational_stages es ON s.stage_id = es.id';
    $select_fields[] = 'es.stage_name';
} else {
    $select_fields[] = 'NULL as stage_name';
}

// إضافة الصفوف إذا كانت متاحة
if ($has_grade_id && in_array('grades', $available_tables)) {
    $joins[] = 'LEFT JOIN grades g ON s.grade_id = g.id';
    $select_fields[] = 'g.grade_name';
} else {
    $select_fields[] = 'NULL as grade_name';
}

// إضافة عدد المعلمين إذا كان الجدول متاحاً
if (in_array('teacher_assignments', $available_tables)) {
    $joins[] = 'LEFT JOIN teacher_assignments ta ON s.id = ta.subject_id';
    $select_fields[] = 'COUNT(DISTINCT ta.teacher_id) as teacher_count';
} else {
    $select_fields[] = '0 as teacher_count';
}

// إضافة عدد الطلاب بشكل آمن
if (in_array('students', $available_tables) && $has_grade_id) {
    $joins[] = 'LEFT JOIN students st ON s.grade_id = st.class_id';
    $select_fields[] = 'COUNT(DISTINCT st.id) as student_count';
} else {
    $select_fields[] = '0 as student_count';
}

$subjects_query = "
    SELECT " . implode(', ', $select_fields) . "
    FROM subjects s
    " . implode(' ', $joins) . "
    {$where_clause}
    GROUP BY s.id
    ORDER BY s.subject_name
";

$subjects_stmt = $conn->prepare($subjects_query);
if (!empty($params)) {
    $subjects_stmt->bind_param($param_types, ...$params);
}
$subjects_stmt->execute();
$subjects_result = $subjects_stmt->get_result();
$subjects = $subjects_result->fetch_all(MYSQLI_ASSOC);

// جلب الإحصائيات
$stats_query = "
    SELECT
        COUNT(*) as total_subjects,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subjects,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_subjects,
        COUNT(DISTINCT department) as total_departments
    FROM subjects
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// جلب البيانات للفلاتر
$stages = [];
$grades = [];
$departments = [];

if ($has_stage_id && in_array('educational_stages', $available_tables)) {
    $stages_result = $conn->query("SELECT id, stage_name FROM educational_stages WHERE status = 'active' ORDER BY sort_order");
    $stages = $stages_result->fetch_all(MYSQLI_ASSOC);
}

if ($has_grade_id && in_array('grades', $available_tables)) {
    $grades_result = $conn->query("SELECT g.id, g.grade_name, g.stage_id FROM grades g WHERE g.status = 'active' ORDER BY g.sort_order");
    $grades = $grades_result->fetch_all(MYSQLI_ASSOC);
}

$departments_result = $conn->query("SELECT DISTINCT department FROM subjects WHERE department IS NOT NULL AND department != '' ORDER BY department");
$departments = $departments_result->fetch_all(MYSQLI_ASSOC);

$page_title = 'المواد الدراسية';
include_once '../includes/header.php';
?>

<!-- SweetAlert2 للحذف -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-book-open me-2 text-primary"></i>المواد الدراسية
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item active">المواد الدراسية</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مادة
            </a>
            <a href="import.php" class="btn btn-outline-primary">
                <i class="fas fa-upload me-2"></i>استيراد CSV
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المواد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_subjects']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المواد النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['active_subjects']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الأقسام
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_departments']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المواد غير النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['inactive_subjects']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pause-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نظام البحث والفلترة -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>البحث والفلترة
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" id="filterForm">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">البحث في المواد</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($search_query); ?>"
                                   placeholder="اسم المادة أو الكود...">
                        </div>
                    </div>

                    <?php if (!empty($stages)): ?>
                    <div class="col-md-2 mb-3">
                        <label for="stage" class="form-label">المرحلة الدراسية</label>
                        <select class="form-select" id="stage" name="stage">
                            <option value="">جميع المراحل</option>
                            <?php foreach ($stages as $stage): ?>
                                <option value="<?php echo $stage['id']; ?>"
                                        <?php echo ($stage_filter == $stage['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($stage['stage_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($grades)): ?>
                    <div class="col-md-2 mb-3">
                        <label for="grade" class="form-label">الصف الدراسي</label>
                        <select class="form-select" id="grade" name="grade">
                            <option value="">جميع الصفوف</option>
                            <?php foreach ($grades as $grade): ?>
                                <option value="<?php echo $grade['id']; ?>"
                                        <?php echo ($grade_filter == $grade['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($grade['grade_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>

                    <div class="col-md-2 mb-3">
                        <label for="department" class="form-label">القسم</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">جميع الأقسام</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo htmlspecialchars($dept['department']); ?>"
                                        <?php echo ($department_filter == $dept['department']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($dept['department']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo ($status_filter == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>

                    <div class="col-md-1 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </button>
                        <span class="text-muted ms-3">
                            <i class="fas fa-info-circle me-1"></i>
                            عرض <?php echo count($subjects); ?> من أصل <?php echo $stats['total_subjects']; ?> مادة
                        </span>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض المواد -->
    <?php if (empty($subjects)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مواد دراسية</h5>
                <p class="text-muted">لم يتم العثور على أي مواد تطابق معايير البحث المحددة.</p>
                <a href="add.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة مادة جديدة
                </a>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table me-2"></i>قائمة المواد الدراسية
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم المادة</th>
                                <th>كود المادة</th>
                                <th>القسم</th>
                                <?php if (!empty($stages)): ?>
                                <th>المرحلة</th>
                                <?php endif; ?>
                                <?php if (!empty($grades)): ?>
                                <th>الصف</th>
                                <?php endif; ?>
                                <th>الساعات</th>
                                <th>الحالة</th>
                                <th>المعلمين</th>
                                <th>الطلاب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subjects as $subject): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-book me-2 text-primary"></i>
                                        <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($subject['subject_code'] ?: 'غير محدد'); ?></code>
                                </td>
                                <td><?php echo htmlspecialchars($subject['department'] ?: 'غير محدد'); ?></td>
                                <?php if (!empty($stages)): ?>
                                <td><?php echo htmlspecialchars($subject['stage_name'] ?: 'غير محدد'); ?></td>
                                <?php endif; ?>
                                <?php if (!empty($grades)): ?>
                                <td><?php echo htmlspecialchars($subject['grade_name'] ?: 'غير محدد'); ?></td>
                                <?php endif; ?>
                                <td>
                                    <span class="badge bg-info"><?php echo $subject['credit_hours'] ?: 1; ?> ساعة</span>
                                </td>
                                <td>
                                    <?php if ($subject['status'] == 'active'): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $subject['teacher_count']; ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-warning"><?php echo $subject['student_count']; ?></span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="view.php?id=<?php echo $subject['id']; ?>"
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit.php?id=<?php echo $subject['id']; ?>"
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" title="حذف"
                                                onclick="confirmDelete(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['subject_name'], ENT_QUOTES); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// دالة تأكيد الحذف
function confirmDelete(subjectId, subjectName) {
    console.log("confirmDelete called:", subjectId, subjectName);

    // التحقق من وجود SweetAlert2
    if (typeof Swal !== "undefined") {
        Swal.fire({
            title: "تأكيد الحذف",
            text: "هل أنت متأكد من حذف المادة: " + subjectName + "؟",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545",
            cancelButtonColor: "#6c757d",
            confirmButtonText: "حذف",
            cancelButtonText: "إلغاء",
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                submitDeleteForm(subjectId);
            }
        });
    } else {
        // استخدام confirm عادي إذا لم تكن SweetAlert متاحة
        if (confirm("هل أنت متأكد من حذف المادة: " + subjectName + "؟\n\nلا يمكن التراجع عن هذا الإجراء.")) {
            submitDeleteForm(subjectId);
        }
    }
}

// دالة إرسال نموذج الحذف
function submitDeleteForm(subjectId) {
    try {
        var form = document.createElement("form");
        form.method = "POST";
        form.action = "delete.php?id=" + subjectId;

        var confirmInput = document.createElement("input");
        confirmInput.type = "hidden";
        confirmInput.name = "confirm_delete";
        confirmInput.value = "1";

        form.appendChild(confirmInput);
        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error("Error submitting form:", error);
        alert("حدث خطأ أثناء الحذف: " + error.message);
    }
}

// دالة مسح الفلاتر
function clearFilters() {
    document.getElementById("search").value = "";
    if (document.getElementById("stage")) document.getElementById("stage").value = "";
    if (document.getElementById("grade")) document.getElementById("grade").value = "";
    document.getElementById("department").value = "";
    document.getElementById("status").value = "";
    document.getElementById("filterForm").submit();
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.text-xs {
    font-size: 0.7rem;
}
.font-weight-bold {
    font-weight: 700 !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}

.table th {
    background-color: #5a5c69;
    color: white;
    font-weight: 600;
    border: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.1);
}

.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.alert {
    border: none;
    border-radius: 0.5rem;
}
</style>

<?php include_once '../includes/footer.php'; ?>
