<?php
/**
 * إصلاح تلقائي لأولوية الحضور حسب الحالة المُدخلة
 * Auto fix attendance priority based on entered status
 */

echo "<h1>🔧 إصلاح تلقائي لأولوية الحضور</h1>";

// الاتصال بقاعدة البيانات
$conn = new mysqli('localhost', 'root', '', 'school_management');

if ($conn->connect_error) {
    echo "<div class='alert alert-danger'>❌ فشل في الاتصال: " . $conn->connect_error . "</div>";
    exit;
}

$conn->set_charset("utf8mb4");
echo "<div class='alert alert-success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

try {
    echo "<h3>🔍 البحث عن التضاربات في البيانات:</h3>";
    
    // البحث عن جميع التضاربات (حضور + غياب بخصم في نفس اليوم)
    $conflicts_query = "
        SELECT 
            u.id as user_id,
            u.full_name,
            sa.attendance_date,
            sa.status as attendance_status,
            sa.id as attendance_id,
            COALESCE(usa.id, sawd.id) as absence_id,
            COALESCE(usa.has_deduction, 1) as has_deduction,
            COALESCE(usa.deduction_amount, sawd.deduction_amount) as deduction_amount,
            CASE 
                WHEN usa.id IS NOT NULL THEN 'unified'
                WHEN sawd.id IS NOT NULL THEN 'traditional'
                ELSE 'none'
            END as absence_type
        FROM staff_attendance sa
        JOIN users u ON sa.user_id = u.id
        LEFT JOIN unified_staff_absences usa ON sa.user_id = usa.user_id AND sa.attendance_date = usa.absence_date AND usa.has_deduction = 1
        LEFT JOIN staff_absences_with_deduction sawd ON sa.user_id = sawd.user_id AND sa.attendance_date = sawd.absence_date
        WHERE (usa.id IS NOT NULL OR sawd.id IS NOT NULL)
        AND sa.status != 'absent_with_deduction'
        ORDER BY sa.attendance_date DESC, u.full_name
    ";
    
    $conflicts_result = $conn->query($conflicts_query);
    
    if ($conflicts_result && $conflicts_result->num_rows > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ تم العثور على " . $conflicts_result->num_rows . " تضارب في البيانات</h4>";
        echo "</div>";
        
        echo "<table class='table table-striped'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>الموظف</th>";
        echo "<th>التاريخ</th>";
        echo "<th>حالة الحضور الحالية</th>";
        echo "<th>نوع الغياب</th>";
        echo "<th>مبلغ الخصم</th>";
        echo "<th>الإجراء</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        $conflicts_data = [];
        while ($conflict = $conflicts_result->fetch_assoc()) {
            $conflicts_data[] = $conflict;
            
            echo "<tr class='table-danger'>";
            echo "<td>" . $conflict['full_name'] . "</td>";
            echo "<td>" . $conflict['attendance_date'] . "</td>";
            echo "<td><span class='badge bg-success'>" . $conflict['attendance_status'] . "</span></td>";
            echo "<td><span class='badge bg-danger'>غياب بخصم (" . $conflict['absence_type'] . ")</span></td>";
            echo "<td>" . $conflict['deduction_amount'] . " ريال</td>";
            echo "<td><span class='badge bg-warning'>يحتاج إصلاح</span></td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        
        // نموذج الإصلاح
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_all_conflicts'])) {
            echo "<h3>🔧 جاري إصلاح جميع التضاربات:</h3>";
            
            $fixed_count = 0;
            $errors = [];
            
            foreach ($conflicts_data as $conflict) {
                $user_id = $conflict['user_id'];
                $attendance_date = $conflict['attendance_date'];
                $attendance_id = $conflict['attendance_id'];
                $user_name = $conflict['full_name'];
                
                try {
                    // بدء معاملة
                    $conn->begin_transaction();
                    
                    // حذف سجل الحضور المتضارب
                    $delete_stmt = $conn->prepare("DELETE FROM staff_attendance WHERE id = ?");
                    $delete_stmt->bind_param("i", $attendance_id);
                    $delete_stmt->execute();
                    
                    // إنشاء سجل حضور جديد للغياب بالخصم
                    $insert_stmt = $conn->prepare("
                        INSERT INTO staff_attendance (user_id, attendance_date, status, check_in_time, check_out_time, notes, created_at) 
                        VALUES (?, ?, 'absent_with_deduction', NULL, NULL, 'غياب بالخصم - تم الإصلاح التلقائي', NOW())
                    ");
                    $insert_stmt->bind_param("is", $user_id, $attendance_date);
                    $insert_stmt->execute();
                    
                    // تأكيد المعاملة
                    $conn->commit();
                    
                    echo "<p>✅ تم إصلاح التضارب للموظف: <strong>$user_name</strong> في تاريخ: $attendance_date</p>";
                    $fixed_count++;
                    
                } catch (Exception $e) {
                    // إلغاء المعاملة في حالة الخطأ
                    $conn->rollback();
                    $errors[] = "خطأ في إصلاح $user_name: " . $e->getMessage();
                }
            }
            
            echo "<div class='alert alert-success'>";
            echo "<h4>🎉 تم الانتهاء من الإصلاح!</h4>";
            echo "<p><strong>عدد التضاربات المُصلحة:</strong> $fixed_count</p>";
            echo "</div>";
            
            if (!empty($errors)) {
                echo "<div class='alert alert-danger'>";
                echo "<h4>❌ أخطاء حدثت أثناء الإصلاح:</h4>";
                echo "<ul>";
                foreach ($errors as $error) {
                    echo "<li>$error</li>";
                }
                echo "</ul>";
                echo "</div>";
            }
            
            echo "<div class='mt-4'>";
            echo "<h4>🔗 روابط للتحقق:</h4>";
            echo "<a href='attendance/smart_attendance.php?tab=admins&date=2025-07-31' class='btn btn-primary' target='_blank'>صفحة الحضور - الإدارة</a>";
            echo "<a href='attendance/smart_attendance.php?tab=staff&date=2025-07-31' class='btn btn-secondary' target='_blank'>صفحة الحضور - الموظفين</a>";
            echo "<a href='attendance/smart_attendance.php?tab=teachers&date=2025-07-31' class='btn btn-info' target='_blank'>صفحة الحضور - المعلمين</a>";
            echo "</div>";
            
        } else {
            echo "<div class='alert alert-info'>";
            echo "<h4>💡 الإصلاح المقترح:</h4>";
            echo "<p>سيتم إصلاح جميع التضاربات بحيث:</p>";
            echo "<ul>";
            echo "<li>✅ <strong>حذف سجلات الحضور المتضاربة</strong></li>";
            echo "<li>✅ <strong>الاحتفاظ بسجلات الغياب بالخصم</strong></li>";
            echo "<li>✅ <strong>إنشاء سجلات حضور جديدة بحالة 'absent_with_deduction'</strong></li>";
            echo "<li>✅ <strong>قفل أوقات الدخول والخروج</strong></li>";
            echo "</ul>";
            echo "<p><strong>النتيجة:</strong> ستظهر الحالة الصحيحة 'غياب بالخصم' بدلاً من 'حاضر'</p>";
            echo "</div>";
            
            echo "<form method='POST'>";
            echo "<div class='form-check mb-3'>";
            echo "<input type='checkbox' class='form-check-input' id='confirm_fix' required>";
            echo "<label class='form-check-label' for='confirm_fix'>";
            echo "أؤكد أنني أريد إصلاح جميع التضاربات وإعطاء الأولوية للغياب بالخصم";
            echo "</label>";
            echo "</div>";
            echo "<button type='submit' name='fix_all_conflicts' class='btn btn-danger btn-lg'>🔧 إصلاح جميع التضاربات</button>";
            echo "</form>";
        }
        
    } else {
        echo "<div class='alert alert-success'>";
        echo "<h4>✅ لا توجد تضاربات في البيانات</h4>";
        echo "<p>جميع سجلات الحضور والغياب متسقة ولا تحتاج إصلاح.</p>";
        echo "</div>";
    }
    
    // إضافة قاعدة لمنع التضاربات المستقبلية
    echo "<h3>🛡️ منع التضاربات المستقبلية:</h3>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_prevention_rule'])) {
        // إنشاء trigger لمنع التضاربات
        $trigger_sql = "
        DROP TRIGGER IF EXISTS prevent_attendance_conflict;
        
        DELIMITER //
        CREATE TRIGGER prevent_attendance_conflict
        BEFORE INSERT ON staff_attendance
        FOR EACH ROW
        BEGIN
            DECLARE absence_count INT DEFAULT 0;
            
            -- التحقق من وجود غياب بخصم في نفس التاريخ
            SELECT COUNT(*) INTO absence_count
            FROM (
                SELECT user_id FROM unified_staff_absences 
                WHERE user_id = NEW.user_id AND absence_date = NEW.attendance_date AND has_deduction = 1
                UNION
                SELECT user_id FROM staff_absences_with_deduction 
                WHERE user_id = NEW.user_id AND absence_date = NEW.attendance_date
            ) as absences;
            
            -- إذا كان هناك غياب بخصم وحالة الحضور ليست absent_with_deduction
            IF absence_count > 0 AND NEW.status != 'absent_with_deduction' THEN
                SET NEW.status = 'absent_with_deduction';
                SET NEW.check_in_time = NULL;
                SET NEW.check_out_time = NULL;
                SET NEW.notes = 'تم التصحيح تلقائياً - غياب بالخصم';
            END IF;
        END//
        DELIMITER ;
        ";
        
        if ($conn->multi_query($trigger_sql)) {
            // استهلاك جميع النتائج
            do {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            } while ($conn->next_result());
            
            echo "<div class='alert alert-success'>✅ تم إنشاء قاعدة منع التضاربات المستقبلية!</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في إنشاء قاعدة منع التضاربات: " . $conn->error . "</div>";
        }
    } else {
        echo "<div class='alert alert-info'>";
        echo "<h4>💡 قاعدة الحماية:</h4>";
        echo "<p>يمكن إنشاء قاعدة تلقائية لمنع حدوث تضاربات في المستقبل.</p>";
        echo "<p>هذه القاعدة ستقوم بـ:</p>";
        echo "<ul>";
        echo "<li>فحص تلقائي عند إدراج سجل حضور جديد</li>";
        echo "<li>إذا وُجد غياب بخصم، ستُصحح الحالة تلقائياً</li>";
        echo "<li>قفل أوقات الدخول والخروج تلقائياً</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='add_prevention_rule' class='btn btn-info'>🛡️ إنشاء قاعدة منع التضاربات</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ في العملية</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

$conn->close();

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
.alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.alert-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
.alert-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
.table { width: 100%; border-collapse: collapse; margin: 10px 0; }
.table th, .table td { padding: 8px; text-align: right; border-bottom: 1px solid #ddd; font-size: 14px; }
.table-striped tbody tr:nth-child(odd) { background-color: #f9f9f9; }
.table-danger { background-color: #f8d7da; }
.badge { padding: 0.25em 0.5em; font-size: 0.75em; border-radius: 0.375rem; }
.bg-success { background-color: #198754; color: white; }
.bg-danger { background-color: #dc3545; color: white; }
.bg-warning { background-color: #ffc107; color: black; }
.btn { padding: 0.375rem 0.75rem; margin: 0.125rem; border: none; border-radius: 0.375rem; cursor: pointer; text-decoration: none; display: inline-block; }
.btn-primary { background-color: #0d6efd; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-info { background-color: #0dcaf0; color: black; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-lg { padding: 0.5rem 1rem; font-size: 1.125rem; }
.form-check { margin: 10px 0; }
.form-check-input { margin-left: 0.5rem; }
.mt-4 { margin-top: 1.5rem; }
.mb-3 { margin-bottom: 1rem; }
h3 { color: #333; margin-top: 30px; }
</style>
