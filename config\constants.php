<?php
/**
 * ثوابت النظام العامة
 * System Constants
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

// ===================================
// SYSTEM INFORMATION
// ===================================
if (!defined('SYSTEM_NAME')) define('SYSTEM_NAME', 'School Management System');
if (!defined('SYSTEM_VERSION')) define('SYSTEM_VERSION', '2.0.0');
if (!defined('SYSTEM_AUTHOR')) define('SYSTEM_AUTHOR', 'School Management Team');
if (!defined('SYSTEM_RELEASE_DATE')) define('SYSTEM_RELEASE_DATE', '2024-01-01');
if (!defined('SYSTEM_BUILD')) define('SYSTEM_BUILD', '20240101');

// ===================================
// PATHS AND DIRECTORIES
// ===================================
if (!defined('ROOT_PATH')) define('ROOT_PATH', dirname(dirname(__FILE__)));
if (!defined('INCLUDES_PATH')) define('INCLUDES_PATH', ROOT_PATH . '/includes');
if (!defined('UPLOADS_PATH')) define('UPLOADS_PATH', ROOT_PATH . '/uploads');
if (!defined('LOGS_PATH')) define('LOGS_PATH', ROOT_PATH . '/logs');
if (!defined('CACHE_PATH')) define('CACHE_PATH', ROOT_PATH . '/cache');
if (!defined('BACKUPS_PATH')) define('BACKUPS_PATH', ROOT_PATH . '/backups');
if (!defined('ASSETS_PATH')) define('ASSETS_PATH', ROOT_PATH . '/assets');

// URL Paths
if (!defined('BASE_URL')) define('BASE_URL', get_base_url());
if (!defined('ASSETS_URL')) define('ASSETS_URL', BASE_URL . '/assets');
if (!defined('UPLOADS_URL')) define('UPLOADS_URL', BASE_URL . '/uploads');

// ===================================
// DATABASE CONFIGURATION
// ===================================
if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}
if (!defined('DB_COLLATE')) {
    define('DB_COLLATE', 'utf8mb4_unicode_ci');
}
if (!defined('DB_PREFIX')) {
    define('DB_PREFIX', 'sms_');
}

// ===================================
// SECURITY SETTINGS
// ===================================
if (!defined('CSRF_TOKEN_NAME')) define('CSRF_TOKEN_NAME', '_token');
if (!defined('CSRF_TOKEN_EXPIRE')) define('CSRF_TOKEN_EXPIRE', 3600); // 1 hour
if (!defined('SESSION_TIMEOUT')) define('SESSION_TIMEOUT', 7200); // 2 hours
if (!defined('PASSWORD_MIN_LENGTH')) define('PASSWORD_MIN_LENGTH', 8);
if (!defined('PASSWORD_REQUIRE_SPECIAL')) define('PASSWORD_REQUIRE_SPECIAL', true);
if (!defined('PASSWORD_REQUIRE_NUMBERS')) define('PASSWORD_REQUIRE_NUMBERS', true);
if (!defined('PASSWORD_REQUIRE_UPPERCASE')) define('PASSWORD_REQUIRE_UPPERCASE', true);
if (!defined('LOGIN_MAX_ATTEMPTS')) define('LOGIN_MAX_ATTEMPTS', 5);
if (!defined('LOGIN_LOCKOUT_TIME')) define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes
if (!defined('ENCRYPTION_METHOD')) define('ENCRYPTION_METHOD', 'AES-256-CBC');

// ===================================
// FILE UPLOAD SETTINGS
// ===================================
if (!defined('MAX_FILE_SIZE')) define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
if (!defined('ALLOWED_IMAGE_TYPES')) define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
if (!defined('ALLOWED_DOCUMENT_TYPES')) define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']);
if (!defined('ALLOWED_FILE_TYPES')) define('ALLOWED_FILE_TYPES', array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES));
if (!defined('PROFILE_PICTURE_MAX_SIZE')) define('PROFILE_PICTURE_MAX_SIZE', 2 * 1024 * 1024); // 2MB
if (!defined('DOCUMENT_MAX_SIZE')) define('DOCUMENT_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// Upload directories
if (!defined('PROFILE_UPLOADS_DIR')) define('PROFILE_UPLOADS_DIR', 'profiles');
if (!defined('DOCUMENT_UPLOADS_DIR')) define('DOCUMENT_UPLOADS_DIR', 'documents');
if (!defined('TEMP_UPLOADS_DIR')) define('TEMP_UPLOADS_DIR', 'temp');
if (!defined('BACKUP_UPLOADS_DIR')) define('BACKUP_UPLOADS_DIR', 'backups');

// ===================================
// PAGINATION SETTINGS
// ===================================
if (!defined('ITEMS_PER_PAGE')) define('ITEMS_PER_PAGE', 20);
if (!defined('MAX_ITEMS_PER_PAGE')) define('MAX_ITEMS_PER_PAGE', 100);
if (!defined('PAGINATION_LINKS')) define('PAGINATION_LINKS', 5);

// ===================================
// DATE AND TIME SETTINGS
// ===================================
if (!defined('DEFAULT_TIMEZONE')) define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
if (!defined('DEFAULT_DATE_FORMAT')) define('DEFAULT_DATE_FORMAT', 'Y-m-d');
if (!defined('DEFAULT_TIME_FORMAT')) define('DEFAULT_TIME_FORMAT', 'H:i');
if (!defined('DEFAULT_DATETIME_FORMAT')) define('DEFAULT_DATETIME_FORMAT', 'Y-m-d H:i:s');
if (!defined('DISPLAY_DATE_FORMAT')) define('DISPLAY_DATE_FORMAT', 'd/m/Y');
if (!defined('DISPLAY_TIME_FORMAT')) define('DISPLAY_TIME_FORMAT', 'H:i');
if (!defined('DISPLAY_DATETIME_FORMAT')) define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// ===================================
// LANGUAGE SETTINGS
// ===================================
if (!defined('DEFAULT_LANGUAGE')) define('DEFAULT_LANGUAGE', 'ar');
if (!defined('SUPPORTED_LANGUAGES')) define('SUPPORTED_LANGUAGES', ['ar', 'en']);
if (!defined('RTL_LANGUAGES')) define('RTL_LANGUAGES', ['ar']);

// ===================================
// CURRENCY SETTINGS
// ===================================
if (!defined('DEFAULT_CURRENCY')) define('DEFAULT_CURRENCY', 'SAR');
if (!defined('CURRENCY_SYMBOL')) define('CURRENCY_SYMBOL', 'ر.س');
if (!defined('CURRENCY_POSITION')) define('CURRENCY_POSITION', 'after'); // before or after
if (!defined('DECIMAL_PLACES')) define('DECIMAL_PLACES', 2);
if (!defined('THOUSANDS_SEPARATOR')) define('THOUSANDS_SEPARATOR', ',');
if (!defined('DECIMAL_SEPARATOR')) define('DECIMAL_SEPARATOR', '.');

// ===================================
// EMAIL SETTINGS
// ===================================
if (!defined('SMTP_HOST')) define('SMTP_HOST', 'localhost');
if (!defined('SMTP_PORT')) define('SMTP_PORT', 587);
if (!defined('SMTP_ENCRYPTION')) define('SMTP_ENCRYPTION', 'tls'); // tls or ssl
if (!defined('SMTP_AUTH')) define('SMTP_AUTH', true);
if (!defined('EMAIL_FROM_NAME')) define('EMAIL_FROM_NAME', 'School Management System');
if (!defined('EMAIL_REPLY_TO')) define('EMAIL_REPLY_TO', '<EMAIL>');

// ===================================
// CACHE SETTINGS
// ===================================
if (!defined('CACHE_ENABLED')) define('CACHE_ENABLED', true);
if (!defined('CACHE_LIFETIME')) define('CACHE_LIFETIME', 3600); // 1 hour
if (!defined('CACHE_PREFIX')) define('CACHE_PREFIX', 'sms_');

// ===================================
// LOG SETTINGS
// ===================================
if (!defined('LOG_ENABLED')) define('LOG_ENABLED', true);
if (!defined('LOG_LEVEL')) define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR, CRITICAL
if (!defined('LOG_MAX_SIZE')) define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
if (!defined('LOG_MAX_FILES')) define('LOG_MAX_FILES', 10);
if (!defined('LOG_DATE_FORMAT')) define('LOG_DATE_FORMAT', 'Y-m-d H:i:s');

// ===================================
// BACKUP SETTINGS
// ===================================
if (!defined('BACKUP_ENABLED')) define('BACKUP_ENABLED', true);
if (!defined('BACKUP_FREQUENCY')) define('BACKUP_FREQUENCY', 'daily'); // daily, weekly, monthly
if (!defined('BACKUP_RETENTION_DAYS')) define('BACKUP_RETENTION_DAYS', 30);
if (!defined('BACKUP_COMPRESSION')) define('BACKUP_COMPRESSION', true);
if (!defined('BACKUP_INCLUDE_FILES')) define('BACKUP_INCLUDE_FILES', true);

// ===================================
// NOTIFICATION SETTINGS
// ===================================
if (!defined('NOTIFICATIONS_ENABLED')) define('NOTIFICATIONS_ENABLED', true);
if (!defined('EMAIL_NOTIFICATIONS_ENABLED')) define('EMAIL_NOTIFICATIONS_ENABLED', true);
if (!defined('SMS_NOTIFICATIONS_ENABLED')) define('SMS_NOTIFICATIONS_ENABLED', false);
if (!defined('PUSH_NOTIFICATIONS_ENABLED')) define('PUSH_NOTIFICATIONS_ENABLED', false);
if (!defined('NOTIFICATION_CLEANUP_DAYS')) define('NOTIFICATION_CLEANUP_DAYS', 90);

// ===================================
// API SETTINGS
// ===================================
if (!defined('API_ENABLED')) define('API_ENABLED', true);
if (!defined('API_VERSION')) define('API_VERSION', 'v1');
if (!defined('API_RATE_LIMIT')) define('API_RATE_LIMIT', 100); // requests per hour
if (!defined('API_TOKEN_EXPIRE')) define('API_TOKEN_EXPIRE', 86400); // 24 hours

// ===================================
// ACADEMIC SETTINGS
// ===================================
if (!defined('ACADEMIC_YEAR_START_MONTH')) define('ACADEMIC_YEAR_START_MONTH', 9); // September
if (!defined('ACADEMIC_YEAR_END_MONTH')) define('ACADEMIC_YEAR_END_MONTH', 6); // June
if (!defined('SEMESTERS')) define('SEMESTERS', ['first', 'second', 'summer']);
if (!defined('GRADE_LEVELS')) define('GRADE_LEVELS', [
    'kg1' => 'KG1',
    'kg2' => 'KG2',
    'grade1' => 'Grade 1',
    'grade2' => 'Grade 2',
    'grade3' => 'Grade 3',
    'grade4' => 'Grade 4',
    'grade5' => 'Grade 5',
    'grade6' => 'Grade 6',
    'grade7' => 'Grade 7',
    'grade8' => 'Grade 8',
    'grade9' => 'Grade 9',
    'grade10' => 'Grade 10',
    'grade11' => 'Grade 11',
    'grade12' => 'Grade 12'
]);

// ===================================
// GRADING SYSTEM
// ===================================
if (!defined('GRADE_SCALE')) define('GRADE_SCALE', [
    'A+' => ['min' => 95, 'max' => 100],
    'A'  => ['min' => 90, 'max' => 94],
    'B+' => ['min' => 85, 'max' => 89],
    'B'  => ['min' => 80, 'max' => 84],
    'C+' => ['min' => 75, 'max' => 79],
    'C'  => ['min' => 70, 'max' => 74],
    'D+' => ['min' => 65, 'max' => 69],
    'D'  => ['min' => 60, 'max' => 64],
    'F'  => ['min' => 0,  'max' => 59]
]);

if (!defined('PASSING_GRADE')) define('PASSING_GRADE', 60);
if (!defined('HONOR_ROLL_GRADE')) define('HONOR_ROLL_GRADE', 85);
if (!defined('DEAN_LIST_GRADE')) define('DEAN_LIST_GRADE', 90);

// ===================================
// ATTENDANCE SETTINGS
// ===================================
if (!defined('ATTENDANCE_REQUIRED_PERCENTAGE')) define('ATTENDANCE_REQUIRED_PERCENTAGE', 75);
if (!defined('ATTENDANCE_WARNING_PERCENTAGE')) define('ATTENDANCE_WARNING_PERCENTAGE', 80);
if (!defined('LATE_ARRIVAL_MINUTES')) define('LATE_ARRIVAL_MINUTES', 15);
if (!defined('ABSENCE_EXCUSE_DAYS')) define('ABSENCE_EXCUSE_DAYS', 3);

// ===================================
// FINANCIAL SETTINGS
// ===================================
if (!defined('LATE_FEE_PERCENTAGE')) define('LATE_FEE_PERCENTAGE', 5);
if (!defined('LATE_FEE_GRACE_DAYS')) define('LATE_FEE_GRACE_DAYS', 7);
if (!defined('INSTALLMENT_FREQUENCIES')) define('INSTALLMENT_FREQUENCIES', ['monthly', 'quarterly', 'semester', 'annual']);
if (!defined('PAYMENT_METHODS')) define('PAYMENT_METHODS', ['cash', 'bank_transfer', 'check', 'card', 'online']);

// ===================================
// SYSTEM LIMITS
// ===================================
if (!defined('MAX_STUDENTS_PER_CLASS')) define('MAX_STUDENTS_PER_CLASS', 35);
if (!defined('MAX_SUBJECTS_PER_TEACHER')) define('MAX_SUBJECTS_PER_TEACHER', 10);
if (!defined('MAX_CLASSES_PER_TEACHER')) define('MAX_CLASSES_PER_TEACHER', 8);
if (!defined('MAX_EXAM_DURATION_MINUTES')) define('MAX_EXAM_DURATION_MINUTES', 180);
if (!defined('MAX_QUESTION_LENGTH')) define('MAX_QUESTION_LENGTH', 1000);
if (!defined('MAX_ANSWER_LENGTH')) define('MAX_ANSWER_LENGTH', 500);

// ===================================
// ERROR CODES
// ===================================
if (!defined('ERROR_CODES')) define('ERROR_CODES', [
    'INVALID_INPUT' => 1001,
    'UNAUTHORIZED' => 1002,
    'FORBIDDEN' => 1003,
    'NOT_FOUND' => 1004,
    'METHOD_NOT_ALLOWED' => 1005,
    'VALIDATION_FAILED' => 1006,
    'DATABASE_ERROR' => 1007,
    'FILE_UPLOAD_ERROR' => 1008,
    'EMAIL_SEND_ERROR' => 1009,
    'RATE_LIMIT_EXCEEDED' => 1010
]);

// ===================================
// STATUS CODES
// ===================================
if (!defined('STATUS_ACTIVE')) define('STATUS_ACTIVE', 'active');
if (!defined('STATUS_INACTIVE')) define('STATUS_INACTIVE', 'inactive');
if (!defined('STATUS_PENDING')) define('STATUS_PENDING', 'pending');
if (!defined('STATUS_SUSPENDED')) define('STATUS_SUSPENDED', 'suspended');
if (!defined('STATUS_DELETED')) define('STATUS_DELETED', 'deleted');
if (!defined('STATUS_COMPLETED')) define('STATUS_COMPLETED', 'completed');
if (!defined('STATUS_CANCELLED')) define('STATUS_CANCELLED', 'cancelled');

// ===================================
// USER ROLES
// ===================================
if (!defined('ROLE_ADMIN')) define('ROLE_ADMIN', 'admin');
if (!defined('ROLE_TEACHER')) define('ROLE_TEACHER', 'teacher');
if (!defined('ROLE_STUDENT')) define('ROLE_STUDENT', 'student');
if (!defined('ROLE_STAFF')) define('ROLE_STAFF', 'staff');
if (!defined('ROLE_PARENT')) define('ROLE_PARENT', 'parent');

// ===================================
// PERMISSIONS
// ===================================
if (!defined('PERMISSIONS')) define('PERMISSIONS', [
    'admin' => [
        'users.view', 'users.create', 'users.edit', 'users.delete',
        'students.view', 'students.create', 'students.edit', 'students.delete',
        'teachers.view', 'teachers.create', 'teachers.edit', 'teachers.delete',
        'classes.view', 'classes.create', 'classes.edit', 'classes.delete',
        'subjects.view', 'subjects.create', 'subjects.edit', 'subjects.delete',
        'stages.view', 'stages.create', 'stages.edit', 'stages.delete',
        'exams.view', 'exams.create', 'exams.edit', 'exams.delete',
        'grades.view', 'grades.create', 'grades.edit', 'grades.delete',
        'attendance.view', 'attendance.create', 'attendance.edit', 'attendance.delete',
        'finance.view', 'finance.create', 'finance.edit', 'finance.delete',
        'reports.view', 'reports.create', 'reports.export',
        'settings.view', 'settings.edit',
        'system.backup', 'system.restore', 'system.maintenance'
    ],
    'teacher' => [
        'students.view',
        'classes.view',
        'subjects.view',
        'exams.view', 'exams.create', 'exams.edit',
        'attendance.view', 'attendance.create', 'attendance.edit',
        'reports.view'
    ],
    'student' => [
        'attendance.view',
        'exams.view',
        'finance.view'
    ],
    'staff' => [
        'students.view',
        'teachers.view',
        'classes.view',
        'reports.view'
    ]
]);

// ===================================
// HELPER FUNCTIONS
// ===================================

/**
 * Get base URL
 */
function get_base_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $script = $_SERVER['SCRIPT_NAME'] ?? '';
    $path = dirname($script);
    
    return $protocol . '://' . $host . ($path === '/' ? '' : $path);
}

// تم حذف أو تعليق تعريف الدالة get_current_academic_year() من هنا لتجنب التكرار

// --- إصلاح مشكلة التثبيت: إنشاء ملف installed.lock تلقائياً إذا لم يكن موجوداً ---
// تم حذف الكود هنا لأنه يسبب مشاكل في تهيئة الاتصال بقاعدة البيانات
?>
