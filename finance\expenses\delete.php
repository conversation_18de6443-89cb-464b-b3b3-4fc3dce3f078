<?php
/**
 * حذف المصروف اليومي
 * Delete Daily Expense
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// التحقق من وجود معرف المصروف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف المصروف غير صحيح';
    header('Location: index.php');
    exit();
}

$expense_id = intval($_GET['id']);

// جلب بيانات المصروف للتأكد من وجوده
$expense_query = "
    SELECT 
        de.*,
        ec.category_name,
        u.full_name as created_by_name
    FROM daily_expenses de
    JOIN expense_categories ec ON de.category_id = ec.id
    LEFT JOIN users u ON de.created_by = u.id
    WHERE de.id = ?
";

$expense_stmt = $conn->prepare($expense_query);
$expense_stmt->bind_param("i", $expense_id);
$expense_stmt->execute();
$expense_result = $expense_stmt->get_result();

if ($expense_result->num_rows === 0) {
    $_SESSION['error_message'] = 'المصروف غير موجود';
    header('Location: index.php');
    exit();
}

$expense = $expense_result->fetch_assoc();

// معالجة طلب الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    try {
        $conn->begin_transaction();
        
        // حذف المصروف
        $delete_query = "DELETE FROM daily_expenses WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bind_param("i", $expense_id);
        
        if ($delete_stmt->execute()) {
            $conn->commit();
            
            // تسجيل النشاط
            log_activity($_SESSION['user_id'], 'delete_expense', 'daily_expenses', $expense_id, [
                'amount' => $expense['amount'],
                'description' => $expense['description'],
                'category' => $expense['category_name']
            ]);
            
            $_SESSION['success_message'] = 'تم حذف المصروف بنجاح';
            header('Location: index.php');
            exit();
        } else {
            throw new Exception('فشل في حذف المصروف');
        }
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = 'حدث خطأ أثناء حذف المصروف: ' . $e->getMessage();
        log_error("Error deleting expense: " . $e->getMessage());
    }
}

$page_title = 'حذف المصروف - ' . $expense['description'];
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-danger">
                <i class="fas fa-trash me-2"></i>حذف المصروف
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../index.php">المالية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">المصروفات</a></li>
                    <li class="breadcrumb-item active">حذف المصروف</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="view.php?id=<?php echo $expense['id']; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للمعاينة
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- تأكيد الحذف -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف المصروف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف المصروف نهائياً من النظام.
                    </div>
                    
                    <!-- معلومات المصروف المراد حذفه -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات المصروف المراد حذفه:</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <strong>التاريخ:</strong> <?php echo date('d/m/Y', strtotime($expense['expense_date'])); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>الفئة:</strong> <?php echo htmlspecialchars($expense['category_name']); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>المبلغ:</strong> <?php echo format_currency($expense['amount']); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>الحالة:</strong> 
                                    <?php
                                    $status_names = [
                                        'pending' => 'في انتظار الموافقة',
                                        'approved' => 'معتمد',
                                        'rejected' => 'مرفوض',
                                        'paid' => 'مدفوع'
                                    ];
                                    echo $status_names[$expense['status']] ?? 'غير محدد';
                                    ?>
                                </div>
                                <div class="col-12 mb-2">
                                    <strong>الوصف:</strong> <?php echo htmlspecialchars($expense['description']); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>مسجل بواسطة:</strong> <?php echo htmlspecialchars($expense['created_by_name'] ?? 'غير محدد'); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>تاريخ التسجيل:</strong> <?php echo date('d/m/Y H:i', strtotime($expense['created_at'])); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نموذج تأكيد الحذف -->
                    <form method="POST">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <p class="mb-0 text-muted">
                                    للمتابعة، يرجى النقر على زر "تأكيد الحذف" أدناه.
                                </p>
                            </div>
                            <div>
                                <a href="view.php?id=<?php echo $expense['id']; ?>" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" name="confirm_delete" class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i>تأكيد الحذف
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
