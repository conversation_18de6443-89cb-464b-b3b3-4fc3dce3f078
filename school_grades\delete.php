<?php
/**
 * صفحة حذف الصف الدراسي
 * Delete School Grade Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// الحصول على معرف الصف
$grade_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($grade_id <= 0) {
    $_SESSION['error_message'] = __('invalid_grade_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات الصف
$grade_query = "
    SELECT g.*, es.stage_name, es.stage_code 
    FROM grades g 
    INNER JOIN educational_stages es ON g.stage_id = es.id 
    WHERE g.id = ?
";
$stmt = $conn->prepare($grade_query);
if (!$stmt) {
    $_SESSION['error_message'] = __('database_error');
    header('Location: index.php');
    exit();
}

$stmt->bind_param('i', $grade_id);
$stmt->execute();
$result = $stmt->get_result();
$grade = $result->fetch_assoc();
$stmt->close();

if (!$grade) {
    $_SESSION['error_message'] = __('grade_not_found');
    header('Location: index.php');
    exit();
}

// التحقق من وجود فصول مرتبطة بالصف
$classes_count_query = "SELECT COUNT(*) as count FROM classes WHERE grade_id = ?";
$stmt = $conn->prepare($classes_count_query);
$classes_count = 0;
if ($stmt) {
    $stmt->bind_param('i', $grade_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $classes_count = $result->fetch_assoc()['count'];
    $stmt->close();
}

// إذا كانت هناك فصول مرتبطة، منع الحذف
if ($classes_count > 0) {
    $_SESSION['error_message'] = __('cannot_delete_grade_has_classes');
    header('Location: view.php?id=' . $grade_id);
    exit();
}

// معالجة طلب الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من وجود تأكيد الحذف
    if (!isset($_POST['confirm_delete'])) {
        $_SESSION['error_message'] = __('delete_not_confirmed');
        header('Location: view.php?id=' . $grade_id);
        exit();
    }

    // التحقق مرة أخرى من عدم وجود فصول (للأمان)
    $stmt = $conn->prepare($classes_count_query);
    if ($stmt) {
        $stmt->bind_param('i', $grade_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $current_classes_count = $result->fetch_assoc()['count'];
        $stmt->close();

        if ($current_classes_count > 0) {
            $_SESSION['error_message'] = __('cannot_delete_grade_has_classes');
            header('Location: view.php?id=' . $grade_id);
            exit();
        }
    }

    // بدء المعاملة
    $conn->begin_transaction();

    try {
        // حذف الصف
        $delete_query = "DELETE FROM grades WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        if (!$stmt) {
            throw new Exception(__('database_error') . ': ' . $conn->error);
        }

        $stmt->bind_param('i', $grade_id);
        if (!$stmt->execute()) {
            throw new Exception(__('database_error') . ': ' . $stmt->error);
        }
        $stmt->close();

        // تسجيل النشاط (يمكن إضافة هذا لاحقاً)
        // log_activity($_SESSION['user_id'], 'delete_grade', 'grades', $grade_id);

        // تأكيد المعاملة
        $conn->commit();

        // رسالة نجاح وإعادة توجيه
        $_SESSION['success_message'] = __('grade_deleted_successfully');
        header('Location: index.php');
        exit();

    } catch (Exception $e) {
        // إلغاء المعاملة في حالة الخطأ
        $conn->rollback();
        
        // تسجيل الخطأ
        error_log("Error deleting grade {$grade_id}: " . $e->getMessage());
        
        $_SESSION['error_message'] = __('delete_grade_error') . ': ' . $e->getMessage();
        header('Location: view.php?id=' . $grade_id);
        exit();
    }
}

// إذا لم يكن طلب POST، عرض صفحة التأكيد
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-trash text-danger me-2"></i>
                <?php echo __('delete_grade'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('confirm_delete_grade'); ?></p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $grade_id; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_grade'); ?>
            </a>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo __('confirm_delete'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo __('warning'); ?>
                        </h6>
                        <p class="mb-0"><?php echo __('delete_grade_confirmation'); ?></p>
                    </div>

                    <!-- Grade Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><?php echo __('grade_to_delete'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold text-muted"><?php echo __('grade_name'); ?>:</td>
                                            <td><?php echo htmlspecialchars($grade['grade_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted"><?php echo __('grade_code'); ?>:</td>
                                            <td><code><?php echo htmlspecialchars($grade['grade_code']); ?></code></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold text-muted"><?php echo __('educational_stage'); ?>:</td>
                                            <td>
                                                <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($grade['stage_code']); ?></span>
                                                <?php echo htmlspecialchars($grade['stage_name']); ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted"><?php echo __('sort_order'); ?>:</td>
                                            <td><span class="badge bg-primary"><?php echo $grade['sort_order']; ?></span></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Consequences Information -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo __('delete_consequences'); ?>
                        </h6>
                        <ul class="mb-0">
                            <li><?php echo __('grade_will_be_permanently_deleted'); ?></li>
                            <li><?php echo __('action_cannot_be_undone'); ?></li>
                            <li><?php echo __('grade_references_will_be_removed'); ?></li>
                        </ul>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="POST" class="mt-4">
                        <input type="hidden" name="confirm_delete" value="1">

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                <?php echo __('confirm_delete_understanding'); ?>
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="view.php?id=<?php echo $grade_id; ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                <i class="fas fa-trash me-2"></i><?php echo __('delete_grade'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الحذف عند تأكيد الفهم
document.getElementById('confirmDelete').addEventListener('change', function() {
    document.getElementById('deleteBtn').disabled = !this.checked;
});

// تأكيد إضافي عند الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('<?php echo __('final_delete_confirmation'); ?>')) {
        e.preventDefault();
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
