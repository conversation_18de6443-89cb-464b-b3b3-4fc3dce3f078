<?php
/**
 * إضافة نوع رسوم جديد
 * Add New Fee Type
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صحيح';
    } else {
        $type_name = clean_input($_POST['type_name'] ?? '');
        $type_name_en = clean_input($_POST['type_name_en'] ?? '');
        $description = clean_input($_POST['description'] ?? '');
        $default_amount = floatval($_POST['default_amount'] ?? 0);
        $is_mandatory = isset($_POST['is_mandatory']) ? 1 : 0;
        $status = clean_input($_POST['status'] ?? 'active');
        
        // التحقق من صحة البيانات
        if (empty($type_name)) {
            $error_message = 'يجب إدخال اسم نوع الرسوم';
        } else {
            // التحقق من عدم تكرار الاسم
            $check_stmt = $conn->prepare("SELECT id FROM fee_types WHERE type_name = ?");
            $check_stmt->bind_param("s", $type_name);
            $check_stmt->execute();
            $existing = $check_stmt->get_result()->fetch_assoc();
            
            if ($existing) {
                $error_message = 'اسم نوع الرسوم موجود مسبقاً';
            } else {
                // إضافة نوع الرسوم الجديد
                $insert_stmt = $conn->prepare("
                    INSERT INTO fee_types (
                        type_name, type_name_en, description, default_amount, 
                        is_mandatory, status, created_by, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $current_user_id = $_SESSION['user_id'];
                $insert_stmt->bind_param("sssdisi", 
                    $type_name, $type_name_en, $description, $default_amount,
                    $is_mandatory, $status, $current_user_id
                );
                
                if ($insert_stmt->execute()) {
                    $success_message = 'تم إضافة نوع الرسوم بنجاح';
                    // إعادة توجيه لصفحة القائمة
                    header("Location: index.php?success=1");
                    exit();
                } else {
                    $error_message = 'خطأ في إضافة نوع الرسوم: ' . $conn->error;
                }
            }
        }
    }
}

$page_title = 'إضافة نوع رسوم جديد';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-plus me-2"></i>إضافة نوع رسوم جديد
            </h1>
            <p class="text-muted mb-0">إضافة نوع رسوم جديد للنظام</p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- نموذج الإضافة -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tag me-2"></i>بيانات نوع الرسوم
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="row">
                            <!-- اسم النوع بالعربية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم نوع الرسوم <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="type_name" 
                                           value="<?php echo htmlspecialchars($_POST['type_name'] ?? ''); ?>" 
                                           required placeholder="مثال: الرسوم الدراسية">
                                    <small class="text-muted">اسم نوع الرسوم باللغة العربية</small>
                                </div>
                            </div>
                            
                            <!-- اسم النوع بالإنجليزية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم النوع بالإنجليزية</label>
                                    <input type="text" class="form-control" name="type_name_en" 
                                           value="<?php echo htmlspecialchars($_POST['type_name_en'] ?? ''); ?>" 
                                           placeholder="Example: Tuition Fees">
                                    <small class="text-muted">اسم نوع الرسوم باللغة الإنجليزية (اختياري)</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الوصف -->
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" name="description" rows="3" 
                                      placeholder="وصف تفصيلي لنوع الرسوم..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            <small class="text-muted">وصف تفصيلي لنوع الرسوم (اختياري)</small>
                        </div>
                        
                        <div class="row">
                            <!-- المبلغ الافتراضي -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ الافتراضي</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="default_amount" 
                                               min="0" step="0.01" 
                                               value="<?php echo $_POST['default_amount'] ?? '0'; ?>" 
                                               placeholder="0.00">
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                    <small class="text-muted">المبلغ الافتراضي لهذا النوع (اختياري)</small>
                                </div>
                            </div>
                            
                            <!-- الحالة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" name="status">
                                        <option value="active" <?php if(($_POST['status'] ?? 'active') == 'active') echo 'selected'; ?>>
                                            نشط
                                        </option>
                                        <option value="inactive" <?php if(($_POST['status'] ?? '') == 'inactive') echo 'selected'; ?>>
                                            غير نشط
                                        </option>
                                    </select>
                                    <small class="text-muted">حالة نوع الرسوم في النظام</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- خيارات إضافية -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_mandatory" 
                                       id="is_mandatory" <?php if(isset($_POST['is_mandatory'])) echo 'checked'; ?>>
                                <label class="form-check-label" for="is_mandatory">
                                    رسوم إجبارية
                                </label>
                                <small class="text-muted d-block">هل هذا النوع من الرسوم إجباري للطلاب؟</small>
                            </div>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ نوع الرسوم
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const typeNameInput = document.querySelector('input[name="type_name"]');
    const typeNameEnInput = document.querySelector('input[name="type_name_en"]');
    
    // اقتراح الاسم الإنجليزي بناءً على العربي
    typeNameInput.addEventListener('input', function() {
        if (!typeNameEnInput.value) {
            const arabicText = this.value;
            let englishSuggestion = '';
            
            // اقتراحات بسيطة
            if (arabicText.includes('دراسية')) englishSuggestion = 'Tuition Fees';
            else if (arabicText.includes('كتب')) englishSuggestion = 'Book Fees';
            else if (arabicText.includes('نقل')) englishSuggestion = 'Transportation Fees';
            else if (arabicText.includes('أنشطة')) englishSuggestion = 'Activity Fees';
            else if (arabicText.includes('تسجيل')) englishSuggestion = 'Registration Fees';
            else if (arabicText.includes('امتحان')) englishSuggestion = 'Examination Fees';
            
            if (englishSuggestion) {
                typeNameEnInput.value = englishSuggestion;
            }
        }
    });
});
</script>

<?php include_once '../../includes/footer.php'; ?>
