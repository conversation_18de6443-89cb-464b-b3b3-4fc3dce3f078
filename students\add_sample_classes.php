<?php
/**
 * إضافة فصول تجريبية لاختبار الترقية والتصعيد
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$message = '';
$error = '';

// إضافة الفصول التجريبية
if (isset($_POST['add_classes'])) {
    $sample_classes = [
        // مرحلة الطفولة المبكرة
        ['name' => 'ما قبل رياض الأطفال - أ', 'level' => 'ما قبل رياض الأطفال', 'capacity' => 20],
        ['name' => 'ما قبل رياض الأطفال - ب', 'level' => 'ما قبل رياض الأطفال', 'capacity' => 20],
        ['name' => 'رياض الأطفال - أ', 'level' => 'رياض الأطفال', 'capacity' => 25],
        ['name' => 'رياض الأطفال - ب', 'level' => 'رياض الأطفال', 'capacity' => 25],
        ['name' => 'الروضة الأولى (KG1) - أ', 'level' => 'الروضة الأولى (KG1)', 'capacity' => 25],
        ['name' => 'الروضة الأولى (KG1) - ب', 'level' => 'الروضة الأولى (KG1)', 'capacity' => 25],
        ['name' => 'الروضة الثانية (KG2) - أ', 'level' => 'الروضة الثانية (KG2)', 'capacity' => 25],
        ['name' => 'الروضة الثانية (KG2) - ب', 'level' => 'الروضة الثانية (KG2)', 'capacity' => 25],
        ['name' => 'التمهيدي - أ', 'level' => 'التمهيدي', 'capacity' => 28],
        ['name' => 'التمهيدي - ب', 'level' => 'التمهيدي', 'capacity' => 28],

        // المرحلة الابتدائية
        ['name' => 'الصف الأول الابتدائي - أ', 'level' => 'الصف الأول الابتدائي', 'capacity' => 30],
        ['name' => 'الصف الأول الابتدائي - ب', 'level' => 'الصف الأول الابتدائي', 'capacity' => 30],
        ['name' => 'الصف الثاني الابتدائي - أ', 'level' => 'الصف الثاني الابتدائي', 'capacity' => 30],
        ['name' => 'الصف الثاني الابتدائي - ب', 'level' => 'الصف الثاني الابتدائي', 'capacity' => 30],
        ['name' => 'الصف الثالث الابتدائي - أ', 'level' => 'الصف الثالث الابتدائي', 'capacity' => 30],
        ['name' => 'الصف الثالث الابتدائي - ب', 'level' => 'الصف الثالث الابتدائي', 'capacity' => 30],
        ['name' => 'الصف الرابع الابتدائي - أ', 'level' => 'الصف الرابع الابتدائي', 'capacity' => 32],
        ['name' => 'الصف الرابع الابتدائي - ب', 'level' => 'الصف الرابع الابتدائي', 'capacity' => 32],
        ['name' => 'الصف الخامس الابتدائي - أ', 'level' => 'الصف الخامس الابتدائي', 'capacity' => 32],
        ['name' => 'الصف الخامس الابتدائي - ب', 'level' => 'الصف الخامس الابتدائي', 'capacity' => 32],
        ['name' => 'الصف السادس الابتدائي - أ', 'level' => 'الصف السادس الابتدائي', 'capacity' => 32],
        ['name' => 'الصف السادس الابتدائي - ب', 'level' => 'الصف السادس الابتدائي', 'capacity' => 32],

        // المرحلة الإعدادية
        ['name' => 'الصف الأول الإعدادي - أ', 'level' => 'الصف الأول الإعدادي', 'capacity' => 35],
        ['name' => 'الصف الأول الإعدادي - ب', 'level' => 'الصف الأول الإعدادي', 'capacity' => 35],
        ['name' => 'الصف الثاني الإعدادي - أ', 'level' => 'الصف الثاني الإعدادي', 'capacity' => 35],
        ['name' => 'الصف الثاني الإعدادي - ب', 'level' => 'الصف الثاني الإعدادي', 'capacity' => 35],
        ['name' => 'الصف الثالث الإعدادي - أ', 'level' => 'الصف الثالث الإعدادي', 'capacity' => 35],
        ['name' => 'الصف الثالث الإعدادي - ب', 'level' => 'الصف الثالث الإعدادي', 'capacity' => 35],

        // المرحلة الثانوية
        ['name' => 'الصف الأول الثانوي - علمي', 'level' => 'الصف الأول الثانوي', 'capacity' => 40],
        ['name' => 'الصف الأول الثانوي - أدبي', 'level' => 'الصف الأول الثانوي', 'capacity' => 40],
        ['name' => 'الصف الثاني الثانوي - علمي', 'level' => 'الصف الثاني الثانوي', 'capacity' => 40],
        ['name' => 'الصف الثاني الثانوي - أدبي', 'level' => 'الصف الثاني الثانوي', 'capacity' => 40],
        ['name' => 'الصف الثالث الثانوي - علمي', 'level' => 'الصف الثالث الثانوي', 'capacity' => 40],
        ['name' => 'الصف الثالث الثانوي - أدبي', 'level' => 'الصف الثالث الثانوي', 'capacity' => 40],

        // التعليم الفني
        ['name' => 'الصف الأول الفني - صناعي', 'level' => 'الصف الأول الفني', 'capacity' => 35],
        ['name' => 'الصف الأول الفني - تجاري', 'level' => 'الصف الأول الفني', 'capacity' => 35],
        ['name' => 'الصف الثاني الفني - صناعي', 'level' => 'الصف الثاني الفني', 'capacity' => 35],
        ['name' => 'الصف الثاني الفني - تجاري', 'level' => 'الصف الثاني الفني', 'capacity' => 35],
        ['name' => 'الصف الثالث الفني - صناعي', 'level' => 'الصف الثالث الفني', 'capacity' => 35],
        ['name' => 'الصف الثالث الفني - تجاري', 'level' => 'الصف الثالث الفني', 'capacity' => 35],
    ];
    
    $conn->begin_transaction();
    
    try {
        $added_count = 0;
        $skipped_count = 0;
        
        foreach ($sample_classes as $class) {
            // التحقق من وجود الفصل
            $check_stmt = $conn->prepare("SELECT id FROM classes WHERE class_name = ?");
            $check_stmt->bind_param("s", $class['name']);
            $check_stmt->execute();
            
            if ($check_stmt->get_result()->num_rows > 0) {
                $skipped_count++;
                continue;
            }
            
            // إضافة الفصل
            $insert_stmt = $conn->prepare("
                INSERT INTO classes (class_name, grade_level, capacity, status, created_at) 
                VALUES (?, ?, ?, 'active', NOW())
            ");
            $insert_stmt->bind_param("ssi", $class['name'], $class['level'], $class['capacity']);
            $insert_stmt->execute();
            $added_count++;
        }
        
        $conn->commit();
        $message = "تم إضافة $added_count فصل جديد، وتم تخطي $skipped_count فصل موجود مسبقاً.";
        
    } catch (Exception $e) {
        $conn->rollback();
        $error = "خطأ في إضافة الفصول: " . $e->getMessage();
    }
}

// جلب الفصول الحالية
$classes_query = "SELECT id, class_name, grade_level, capacity FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_query);
$classes = $classes_result->fetch_all(MYSQLI_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة فصول تجريبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <h1 class="mb-4">
            <i class="fas fa-school me-2"></i>إضافة فصول تجريبية للترقية والتصعيد
        </h1>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
            </div>
        <?php endif; ?>

        <!-- إضافة الفصول -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus me-2"></i>إضافة فصول تجريبية</h5>
                    </div>
                    <div class="card-body">
                        <p>سيتم إضافة فصول لجميع المراحل التعليمية:</p>
                        <ul>
                            <li><strong>مرحلة الطفولة المبكرة:</strong> 10 فصول (5 مستويات × 2 شعبة)</li>
                            <li><strong>المرحلة الابتدائية:</strong> 12 فصل (6 صفوف × 2 شعبة)</li>
                            <li><strong>المرحلة الإعدادية:</strong> 6 فصول (3 صفوف × 2 شعبة)</li>
                            <li><strong>المرحلة الثانوية:</strong> 6 فصول (3 صفوف × 2 تخصص)</li>
                            <li><strong>التعليم الفني:</strong> 6 فصول (3 صفوف × 2 تخصص)</li>
                        </ul>
                        <p class="text-muted small">إجمالي: 40 فصل دراسي</p>
                        <form method="POST">
                            <button type="submit" name="add_classes" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة الفصول التجريبية
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>فوائد إضافة الفصول:</h6>
                            <ul class="mb-0">
                                <li>تمكين عمليات الترقية والتصعيد</li>
                                <li>تنظيم الطلاب حسب المراحل</li>
                                <li>سهولة النقل بين الفصول</li>
                                <li>إدارة أفضل للطلاب</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6>ملاحظة:</h6>
                            <p class="mb-0">الفصول الموجودة مسبقاً لن يتم تكرارها</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الفصول الحالية -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>الفصول الحالية (<?php echo count($classes); ?> فصل)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($classes)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-school fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد فصول حالياً</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الفصل</th>
                                    <th>المستوى الدراسي</th>
                                    <th>السعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($classes as $class): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($class['class_name']); ?></td>
                                        <td><?php echo htmlspecialchars($class['grade_level'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo $class['capacity'] ?? 'غير محدد'; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="mt-4">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-users me-2"></i>صفحة الطلاب
            </a>
            <a href="test_bulk_operations.php" class="btn btn-info">
                <i class="fas fa-vial me-2"></i>اختبار العمليات الجماعية
            </a>
            <a href="simple_bulk_test.php" class="btn btn-success">
                <i class="fas fa-play me-2"></i>اختبار بسيط
            </a>
        </div>
    </div>
</body>
</html>
