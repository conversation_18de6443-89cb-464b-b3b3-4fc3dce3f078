<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

$error = '';
$success = '';

// جلب المواد والفصول
$subjects = $conn->query("SELECT id, subject_name FROM subjects");
$classes = $conn->query("SELECT id, class_name FROM classes");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $exam_title = trim($_POST['exam_title'] ?? '');
    $subject_id = intval($_POST['subject_id'] ?? 0);
    $class_id = intval($_POST['class_id'] ?? 0);
    $total_marks = intval($_POST['total_marks'] ?? 0);
    $exam_type = $_POST['exam_type'] ?? 'quiz';
    $status = $_POST['status'] ?? 'draft';
    if ($exam_title && $subject_id && $class_id) {
        $stmt = $conn->prepare("INSERT INTO exams (exam_title, subject_id, class_id, total_marks, exam_type, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("siiiss", $exam_title, $subject_id, $class_id, $total_marks, $exam_type, $status);
        if ($stmt->execute()) {
            $success = 'تمت إضافة الامتحان بنجاح';
        } else {
            $error = 'حدث خطأ أثناء الإضافة';
        }
    } else {
        $error = 'يرجى تعبئة جميع الحقول المطلوبة';
    }
}
?>
<div class="container">
    <h2>إضافة امتحان جديد</h2>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label>عنوان الامتحان</label>
            <input type="text" name="exam_title" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>المادة</label>
            <select name="subject_id" class="form-control" required>
                <option value="">اختر المادة</option>
                <?php while($sub = $subjects->fetch_assoc()): ?>
                <option value="<?= $sub['id'] ?>"><?= htmlspecialchars($sub['subject_name']) ?></option>
                <?php endwhile; ?>
            </select>
        </div>
        <div class="mb-3">
            <label>الفصل</label>
            <select name="class_id" class="form-control" required>
                <option value="">اختر الفصل</option>
                <?php while($c = $classes->fetch_assoc()): ?>
                <option value="<?= $c['id'] ?>"><?= htmlspecialchars($c['class_name']) ?></option>
                <?php endwhile; ?>
            </select>
        </div>
        <div class="mb-3">
            <label>الدرجة الكلية</label>
            <input type="number" name="total_marks" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>نوع الامتحان</label>
            <select name="exam_type" class="form-control">
                <option value="quiz">اختبار قصير</option>
                <option value="midterm">منتصف الفصل</option>
                <option value="final">نهائي</option>
                <option value="assignment">واجب</option>
            </select>
        </div>
        <div class="mb-3">
            <label>الحالة</label>
            <select name="status" class="form-control">
                <option value="draft">مسودة</option>
                <option value="published">منشور</option>
                <option value="active">نشط</option>
                <option value="completed">مكتمل</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">حفظ</button>
        <a href="index.php" class="btn btn-secondary">رجوع</a>
    </form>
</div>
<?php require_once '../includes/footer.php'; ?> 