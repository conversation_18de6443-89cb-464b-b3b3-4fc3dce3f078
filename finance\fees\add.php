<?php
define('SYSTEM_INIT', true);

/**
 * صفحة إضافة رسم جديد للطالب
 * Add New Student Fee Page
 */

// تحميل الملفات الأساسية
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

$page_title = __('add_student_fee');
require_once '../../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة إضافة الرسم
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات وتنظيفها
        $student_id = intval($_POST['student_id'] ?? 0);
        $fee_type_id = intval($_POST['fee_type_id'] ?? 0);
        $academic_year = clean_input($_POST['academic_year'] ?? '');
        $semester = clean_input($_POST['semester'] ?? '');
        $amount = floatval($_POST['amount'] ?? 0);
        $discount_amount = floatval($_POST['discount_amount'] ?? 0);
        $discount_percentage = floatval($_POST['discount_percentage'] ?? 0);
        $discount_reason = clean_input($_POST['discount_reason'] ?? '');
        $due_date = clean_input($_POST['due_date'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        $create_installments = isset($_POST['create_installments']);
        $installment_plan_id = intval($_POST['installment_plan_id'] ?? 0);

        // التحقق من صحة البيانات
        $errors = [];

        if (empty($student_id)) {
            $errors[] = __('student') . ' ' . __('required_field');
        }

        if (empty($fee_type_id)) {
            $errors[] = __('fee_type') . ' ' . __('required_field');
        }

        if (empty($academic_year)) {
            $errors[] = __('academic_year') . ' ' . __('required_field');
        }

        if (empty($amount) || $amount <= 0) {
            $errors[] = __('amount') . ' ' . __('required_field');
        }

        if (!empty($due_date) && !validate_date($due_date)) {
            $errors[] = __('invalid_due_date');
        }

        // حساب المبلغ النهائي
        $final_amount = $amount;
        if ($discount_percentage > 0) {
            $discount_amount = ($amount * $discount_percentage) / 100;
        }
        $final_amount = $amount - $discount_amount;

        if ($final_amount < 0) {
            $errors[] = __('invalid_discount_amount');
        }

        // التحقق من عدم تكرار الرسم
        if (empty($errors)) {
            $check_stmt = $conn->prepare("
                SELECT id FROM student_fees 
                WHERE student_id = ? AND fee_type_id = ? AND academic_year = ? AND semester = ?
            ");
            $check_stmt->bind_param("iiss", $student_id, $fee_type_id, $academic_year, $semester);
            $check_stmt->execute();
            if ($check_stmt->get_result()->num_rows > 0) {
                $errors[] = __('fee_already_exists');
            }
        }

        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();

            try {
                // إدراج الرسم
                $fee_stmt = $conn->prepare("
                    INSERT INTO student_fees (
                        student_id, fee_type_id, academic_year, semester, amount, 
                        discount_amount, discount_percentage, discount_reason, 
                        final_amount, due_date, notes, created_by, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $fee_stmt->bind_param(
                    "iissdddssssi",
                    $student_id, $fee_type_id, $academic_year, $semester, $amount,
                    $discount_amount, $discount_percentage, $discount_reason,
                    $final_amount, $due_date, $notes, $_SESSION['user_id']
                );
                $fee_stmt->execute();
                $fee_id = $conn->insert_id;

                // إنشاء الأقساط إذا طُلب ذلك
                if ($create_installments && $installment_plan_id > 0) {
                    // جلب تفاصيل خطة الأقساط
                    $plan_stmt = $conn->prepare("SELECT * FROM installment_plans WHERE id = ?");
                    $plan_stmt->bind_param("i", $installment_plan_id);
                    $plan_stmt->execute();
                    $plan = $plan_stmt->get_result()->fetch_assoc();

                    if ($plan) {
                        $installment_amount = $final_amount / $plan['number_of_installments'];
                        $current_due_date = new DateTime($due_date ?: date('Y-m-d'));

                        for ($i = 1; $i <= $plan['number_of_installments']; $i++) {
                            $installment_stmt = $conn->prepare("
                                INSERT INTO student_installments (
                                    student_id, student_fee_id, installment_plan_id, 
                                    installment_number, amount, due_date, created_at
                                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                            ");
                            $installment_stmt->bind_param(
                                "iiiids",
                                $student_id, $fee_id, $installment_plan_id,
                                $i, $installment_amount, $current_due_date->format('Y-m-d')
                            );
                            $installment_stmt->execute();

                            // تحديث تاريخ الاستحقاق للقسط التالي
                            if ($plan['installment_frequency'] === 'monthly') {
                                $current_due_date->add(new DateInterval('P1M'));
                            } elseif ($plan['installment_frequency'] === 'quarterly') {
                                $current_due_date->add(new DateInterval('P3M'));
                            }
                        }
                    }
                }

                $conn->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'add_student_fee', 'student_fees', $fee_id, null, [
                    'student_id' => $student_id,
                    'fee_type_id' => $fee_type_id,
                    'amount' => $final_amount
                ]);

                // إرسال إشعار للطالب
                add_notification($student_id, __('new_fee_assigned'), 
                    __('new_fee_assigned_message') . ': ' . number_format($final_amount, 2), 
                    'info', 'finance/fees/view.php?id=' . $fee_id);

                $_SESSION['success_message'] = __('fee_added_successfully');
                header('Location: index.php');
                exit();

            } catch (Exception $e) {
                $conn->rollback();
                log_error("Error adding student fee: " . $e->getMessage());
                $error_message = __('error_occurred');
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// جلب قائمة الطلاب
$students = $conn->query("
    SELECT s.id, u.full_name, s.student_id as student_number, c.class_name, c.grade_level
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
");

// جلب أنواع الرسوم
$fee_types = $conn->query("SELECT * FROM fee_types ORDER BY type_name");

// جلب خطط الأقساط
$installment_plans = $conn->query("SELECT * FROM installment_plans WHERE status = 'active' ORDER BY plan_name");

// الحصول على السنة الأكاديمية الحالية
$current_academic_year = get_current_academic_year();
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('add_student_fee'); ?></h1>
            <p class="text-muted"><?php echo __('add_new_fee_for_student'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Add Fee Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-money-bill-wave me-2"></i><?php echo __('fee_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-lg-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('basic_information'); ?>
                        </h6>

                        <div class="mb-3">
                            <label for="student_id" class="form-label"><?php echo __('student'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value=""><?php echo __('choose_student'); ?></option>
                                <?php while ($student = $students->fetch_assoc()): ?>
                                    <option value="<?php echo $student['id']; ?>" 
                                            <?php echo (($_POST['student_id'] ?? '') == $student['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($student['full_name'] . ' (' . $student['student_number'] . ')'); ?>
                                        <?php if (!empty($student['class_name'])): ?>
                                            - <?php echo htmlspecialchars($student['class_name']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="fee_type_id" class="form-label"><?php echo __('fee_type'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="fee_type_id" name="fee_type_id" required>
                                <option value=""><?php echo __('choose_fee_type'); ?></option>
                                <?php while ($fee_type = $fee_types->fetch_assoc()): ?>
                                    <option value="<?php echo $fee_type['id']; ?>" 
                                            data-default-amount="<?php echo $fee_type['default_amount']; ?>"
                                            <?php echo (($_POST['fee_type_id'] ?? '') == $fee_type['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($fee_type['type_name']); ?>
                                        <?php if ($fee_type['default_amount'] > 0): ?>
                                            (<?php echo number_format($fee_type['default_amount'], 2); ?> <?php echo CURRENCY_SYMBOL; ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academic_year" class="form-label"><?php echo __('academic_year'); ?> <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="academic_year" 
                                           name="academic_year" 
                                           value="<?php echo htmlspecialchars($_POST['academic_year'] ?? $current_academic_year); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="semester" class="form-label"><?php echo __('semester'); ?></label>
                                    <select class="form-select" id="semester" name="semester">
                                        <option value="first" <?php echo (($_POST['semester'] ?? '') === 'first') ? 'selected' : ''; ?>>
                                            <?php echo __('first_semester'); ?>
                                        </option>
                                        <option value="second" <?php echo (($_POST['semester'] ?? '') === 'second') ? 'selected' : ''; ?>>
                                            <?php echo __('second_semester'); ?>
                                        </option>
                                        <option value="summer" <?php echo (($_POST['semester'] ?? '') === 'summer') ? 'selected' : ''; ?>>
                                            <?php echo __('summer_semester'); ?>
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="amount" class="form-label"><?php echo __('amount'); ?> <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control" 
                                       id="amount" 
                                       name="amount" 
                                       step="0.01"
                                       min="0"
                                       value="<?php echo htmlspecialchars($_POST['amount'] ?? ''); ?>"
                                       required>
                                <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                            </div>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="due_date" class="form-label"><?php echo __('due_date'); ?></label>
                            <input type="date" 
                                   class="form-control" 
                                   id="due_date" 
                                   name="due_date" 
                                   value="<?php echo htmlspecialchars($_POST['due_date'] ?? ''); ?>">
                        </div>
                    </div>

                    <!-- Discount and Installments -->
                    <div class="col-lg-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-percentage me-2"></i><?php echo __('discount_installments'); ?>
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discount_percentage" class="form-label"><?php echo __('discount_percentage'); ?></label>
                                    <div class="input-group">
                                        <input type="number" 
                                               class="form-control" 
                                               id="discount_percentage" 
                                               name="discount_percentage" 
                                               step="0.01"
                                               min="0"
                                               max="100"
                                               value="<?php echo htmlspecialchars($_POST['discount_percentage'] ?? ''); ?>">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discount_amount" class="form-label"><?php echo __('discount_amount'); ?></label>
                                    <div class="input-group">
                                        <input type="number" 
                                               class="form-control" 
                                               id="discount_amount" 
                                               name="discount_amount" 
                                               step="0.01"
                                               min="0"
                                               value="<?php echo htmlspecialchars($_POST['discount_amount'] ?? ''); ?>">
                                        <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="discount_reason" class="form-label"><?php echo __('discount_reason'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="discount_reason" 
                                   name="discount_reason" 
                                   value="<?php echo htmlspecialchars($_POST['discount_reason'] ?? ''); ?>"
                                   placeholder="<?php echo __('reason_for_discount'); ?>">
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="create_installments" 
                                       name="create_installments"
                                       <?php echo isset($_POST['create_installments']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="create_installments">
                                    <?php echo __('create_installment_plan'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="installment_plan_section" style="display: none;">
                            <label for="installment_plan_id" class="form-label"><?php echo __('installment_plan'); ?></label>
                            <select class="form-select" id="installment_plan_id" name="installment_plan_id">
                                <option value=""><?php echo __('choose_plan'); ?></option>
                                <?php while ($plan = $installment_plans->fetch_assoc()): ?>
                                    <option value="<?php echo $plan['id']; ?>" 
                                            <?php echo (($_POST['installment_plan_id'] ?? '') == $plan['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($plan['plan_name']); ?>
                                        (<?php echo $plan['number_of_installments']; ?> <?php echo __('installments'); ?>)
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                            <textarea class="form-control" 
                                      id="notes" 
                                      name="notes" 
                                      rows="4"
                                      placeholder="<?php echo __('additional_notes'); ?>"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                        </div>

                        <!-- Final Amount Display -->
                        <div class="alert alert-info">
                            <h6><?php echo __('final_amount'); ?>:</h6>
                            <div class="h4 mb-0" id="final_amount_display">0.00 <?php echo CURRENCY_SYMBOL; ?></div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('add_fee'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Auto-fill amount when fee type is selected
    document.getElementById('fee_type_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const defaultAmount = selectedOption.getAttribute('data-default-amount');
        if (defaultAmount && defaultAmount > 0) {
            document.getElementById('amount').value = defaultAmount;
            calculateFinalAmount();
        }
    });

    // Show/hide installment plan section
    document.getElementById('create_installments').addEventListener('change', function() {
        const section = document.getElementById('installment_plan_section');
        section.style.display = this.checked ? 'block' : 'none';
    });

    // Calculate final amount
    function calculateFinalAmount() {
        const amount = parseFloat(document.getElementById('amount').value) || 0;
        const discountPercentage = parseFloat(document.getElementById('discount_percentage').value) || 0;
        const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
        
        let finalAmount = amount;
        
        if (discountPercentage > 0) {
            finalAmount = amount - (amount * discountPercentage / 100);
            document.getElementById('discount_amount').value = (amount * discountPercentage / 100).toFixed(2);
        } else if (discountAmount > 0) {
            finalAmount = amount - discountAmount;
            document.getElementById('discount_percentage').value = ((discountAmount / amount) * 100).toFixed(2);
        }
        
        document.getElementById('final_amount_display').textContent = finalAmount.toFixed(2) + ' <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>';
    }

    // Event listeners for discount calculations
    document.getElementById('amount').addEventListener('input', calculateFinalAmount);
    document.getElementById('discount_percentage').addEventListener('input', function() {
        document.getElementById('discount_amount').value = '';
        calculateFinalAmount();
    });
    document.getElementById('discount_amount').addEventListener('input', function() {
        document.getElementById('discount_percentage').value = '';
        calculateFinalAmount();
    });

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        calculateFinalAmount();
        
        // Show installment section if checkbox is checked
        if (document.getElementById('create_installments').checked) {
            document.getElementById('installment_plan_section').style.display = 'block';
        }
    });
</script>

<?php require_once '../../includes/footer.php'; ?>
