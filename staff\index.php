<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين اللغة الافتراضية إذا لم تكن محددة
if (!isset($_SESSION['system_language'])) {
    $_SESSION['system_language'] = 'ar';
}

/**
 * صفحة إدارة الإداريين
 * Staff Management Page
 */

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// معالجة الحذف
if (isset($_POST['delete_staff'])) {
    $staff_id = intval($_POST['staff_id']);

    global $conn;
    $conn->begin_transaction();

    try {
        // الحصول على user_id قبل حذف الإداري
        $stmt = $conn->prepare("SELECT user_id FROM staff WHERE id = ?");
        $stmt->bind_param("i", $staff_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $staff_data = $result->fetch_assoc();
        $user_id = $staff_data['user_id'] ?? null;
        $stmt->close();

        // حذف الإداري
        $stmt = $conn->prepare("DELETE FROM staff WHERE id = ?");
        $stmt->bind_param("i", $staff_id);
        $stmt->execute();
        $stmt->close();

        // حذف المستخدم إذا كان موجوداً
        if ($user_id) {
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $stmt->close();
        }

        $conn->commit();

        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'delete_staff', 'staff', $staff_id, null, [
            'staff_id' => $staff_id,
            'user_id' => $user_id
        ]);

        $_SESSION['success_message'] = 'تم حذف الإداري بنجاح';
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = 'حدث خطأ أثناء الحذف';
        log_error("Error deleting staff: " . $e->getMessage());
    }

    header('Location: index.php');
    exit();
}

// تحميل ملف اللغة قبل استخدام دالة الترجمة
load_language();

$page_title = 'الإداريين';
require_once '../includes/header.php';

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$department_filter = clean_input($_GET['department'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');

// بناء استعلام البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(u.full_name LIKE ? OR s.employee_id LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
    $types .= "ss";
}

if (!empty($department_filter)) {
    $where_conditions[] = "s.department = ?";
    $params[] = $department_filter;
    $types .= "s";
}

if (!empty($status_filter)) {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM staff s
    JOIN users u ON s.user_id = u.id
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب الإداريين
$query = "
    SELECT
        s.*,
        u.username,
        u.email,
        u.status,
        u.last_login,
        u.created_at as user_created_at,
        COALESCE(u.full_name, u.username) as display_name
    FROM staff s
    JOIN users u ON s.user_id = u.id
    WHERE $where_clause
    ORDER BY u.full_name ASC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
if (!$stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($query));
}
$params[] = $records_per_page;
$params[] = $offset;
$types .= "ii";

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$staff_members = $stmt->get_result();

// جلب قائمة الأقسام للفلترة
$departments = $conn->query("SELECT DISTINCT department FROM staff WHERE department IS NOT NULL ORDER BY department");

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_staff,
        SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_staff,
        COUNT(DISTINCT s.department) as total_departments,
        AVG(DATEDIFF(CURDATE(), s.hire_date) / 365.25) as avg_experience
    FROM staff s
    JOIN users u ON s.user_id = u.id
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">الإداريين</h1>
            <p class="text-muted">إدارة بيانات الموظفين الإداريين</p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة إداري
            </a>
            <a href="import.php" class="btn btn-success">
                <i class="fas fa-upload me-2"></i>استيراد الإداريين
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_staff'] ?? 0); ?></h4>
                            <p class="mb-0">إجمالي الإداريين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-tie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['active_staff'] ?? 0); ?></h4>
                            <p class="mb-0">الإداريين النشطين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_departments'] ?? 0); ?></h4>
                            <p class="mb-0">الأقسام</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['avg_experience'] ?? 0, 1); ?></h4>
                            <p class="mb-0">متوسط سنوات الخبرة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="البحث بالاسم أو رقم الموظف">
                </div>
                <div class="col-md-3">
                    <label for="department_filter" class="form-label">القسم</label>
                    <select class="form-select" id="department_filter" name="department">
                        <option value="">جميع الأقسام</option>
                        <?php while ($dept = $departments->fetch_assoc()): ?>
                            <option value="<?php echo htmlspecialchars($dept['department']); ?>"
                                    <?php echo $department_filter === $dept['department'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($dept['department']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status_filter" class="form-label">الحالة</label>
                    <select class="form-select" id="status_filter" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                        <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>معلق</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Staff Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">قائمة الإداريين</h5>
        </div>
        <div class="card-body">
            <?php if ($staff_members->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الموظف</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th>البريد الإلكتروني</th>
                                <th>الحالة</th>
                                <th>تاريخ التوظيف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($staff = $staff_members->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if (!empty($staff['profile_picture'])): ?>
                                                <img src="<?php echo SYSTEM_URL; ?>/uploads/profiles/<?php echo htmlspecialchars($staff['profile_picture']); ?>"
                                                     class="rounded-circle me-2" width="32" height="32" alt="Profile">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center"
                                                     style="width: 32px; height: 32px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <strong><?php echo htmlspecialchars($staff['display_name']); ?></strong>
                                                <?php if (!empty($staff['phone'])): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($staff['phone']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($staff['employee_id'] ?? 'غير محدد'); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($staff['department'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($staff['position'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($staff['email']); ?></td>
                                    <td>
                                        <?php
                                        $status_class = match($staff['status']) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-secondary',
                                            'suspended' => 'bg-warning',
                                            default => 'bg-secondary'
                                        };
                                        $status_text = match($staff['status']) {
                                            'active' => 'نشط',
                                            'inactive' => 'غير نشط',
                                            'suspended' => 'معلق',
                                            default => 'غير محدد'
                                        };
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                    <td>
                                        <?php if (!empty($staff['hire_date'])): ?>
                                            <?php echo date('Y-m-d', strtotime($staff['hire_date'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="view.php?id=<?php echo $staff['id']; ?>"
                                               class="btn btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $staff['id']; ?>"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="confirmDelete(<?php echo $staff['id']; ?>, '<?php echo htmlspecialchars($staff['display_name'], ENT_QUOTES); ?>')"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Staff pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        السابق
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        التالي
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات إداريين</h5>
                    <p class="text-muted">لم يتم العثور على أي إداريين مطابقين لمعايير البحث</p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة إداري جديد
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الإداري <strong id="staffName"></strong>؟</p>
                <p class="text-danger"><small>تحذير: سيتم حذف جميع البيانات المرتبطة بهذا الإداري نهائياً.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="staff_id" id="deleteStaffId">
                    <button type="submit" name="delete_staff" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(staffId, staffName) {
    document.getElementById('deleteStaffId').value = staffId;
    document.getElementById('staffName').textContent = staffName;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Display success/error messages
<?php if (isset($_SESSION['success_message'])): ?>
    Swal.fire({
        icon: 'success',
        title: 'نجح!',
        text: '<?php echo $_SESSION['success_message']; ?>',
        timer: 3000,
        showConfirmButton: false
    });
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    Swal.fire({
        icon: 'error',
        title: 'خطأ!',
        text: '<?php echo $_SESSION['error_message']; ?>',
        timer: 3000,
        showConfirmButton: false
    });
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>
</script>

<?php require_once '../includes/footer.php'; ?>
