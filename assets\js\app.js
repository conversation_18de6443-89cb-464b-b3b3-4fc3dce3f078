/**
 * School Management System - Main JavaScript Application
 * نظام إدارة المدارس - التطبيق الرئيسي
 */

// Global App Object
window.SchoolApp = {
    config: {
        baseUrl: window.location.origin,
        apiUrl: window.location.origin + '/api',
        language: document.documentElement.lang || 'ar',
        isRTL: document.documentElement.dir === 'rtl',
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        userId: null,
        userRole: null
    },
    
    // Initialize application
    init: function() {
        this.setupGlobalSettings();
        this.setupEventListeners();
        this.setupAjaxDefaults();
        this.setupFormValidation();
        this.setupNotifications();
        this.setupDataTables();
        this.setupCharts();
        this.loadUserPreferences();
        
        console.log('School Management System initialized');
    },
    
    // Setup global settings
    setupGlobalSettings: function() {
        // Set moment.js locale
        if (typeof moment !== 'undefined') {
            moment.locale(this.config.language);
        }
        
        // Set Chart.js defaults
        if (typeof Chart !== 'undefined') {
            Chart.defaults.font.family = this.config.language === 'ar' ? 'Cairo, sans-serif' : 'Inter, sans-serif';
            Chart.defaults.plugins.legend.rtl = this.config.isRTL;
        }
        
        // Set DataTables defaults
        if (typeof $.fn.DataTable !== 'undefined') {
            $.extend(true, $.fn.dataTable.defaults, {
                language: {
                    url: this.config.language === 'ar' ? 
                        '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json' : 
                        '//cdn.datatables.net/plug-ins/1.13.7/i18n/en-GB.json'
                },
                responsive: true,
                pageLength: 20,
                lengthMenu: [[10, 20, 50, 100], [10, 20, 50, 100]]
            });
        }
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        const self = this;
        
        // Handle AJAX errors globally
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            self.handleAjaxError(xhr, thrownError);
        });
        
        // Handle form submissions
        $(document).on('submit', 'form[data-ajax="true"]', function(e) {
            e.preventDefault();
            self.handleAjaxForm($(this));
        });
        
        // Handle delete confirmations
        $(document).on('click', '[data-action="delete"]', function(e) {
            e.preventDefault();
            self.confirmDelete($(this));
        });
        
        // Handle theme toggle
        $(document).on('click', '[data-action="toggle-theme"]', function(e) {
            e.preventDefault();
            self.toggleTheme();
        });
        
        // Handle language toggle
        $(document).on('click', '[data-action="toggle-language"]', function(e) {
            e.preventDefault();
            self.toggleLanguage();
        });
        
        // Handle sidebar toggle
        $(document).on('click', '[data-action="toggle-sidebar"]', function(e) {
            e.preventDefault();
            self.toggleSidebar();
        });
        
        // Handle modal forms
        $(document).on('show.bs.modal', '.modal', function() {
            self.initializeModalContent($(this));
        });
        
        // عند فتح أي نافذة منبثقة، أخفِ طبقة التحميل إن وجدت
        $(document).on('show.bs.modal', '.modal', function() {
            $('#loading-overlay').hide();
        });
        
        // Handle file uploads
        $(document).on('change', 'input[type="file"][data-preview]', function() {
            self.previewFile($(this));
        });
        
        // Auto-save forms
        $(document).on('input change', 'form[data-autosave="true"] input, form[data-autosave="true"] select, form[data-autosave="true"] textarea', 
            this.debounce(function() {
                self.autoSaveForm($(this).closest('form'));
            }, 2000)
        );
    },
    
    // Setup AJAX defaults
    setupAjaxDefaults: function() {
        const self = this;
        
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                // Add CSRF token to all requests
                if (self.config.csrfToken && settings.type !== 'GET') {
                    xhr.setRequestHeader('X-CSRF-TOKEN', self.config.csrfToken);
                }
                
                // Show loading indicator
                if (settings.showLoading !== false) {
                    self.showLoading();
                }
            },
            complete: function(xhr, status) {
                // Hide loading indicator
                self.hideLoading();
            },
            error: function(xhr, status, error) {
                self.handleAjaxError(xhr, error);
            }
        });
    },
    
    // Setup form validation
    setupFormValidation: function() {
        // Bootstrap validation
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
        
        // Custom validation rules
        if (typeof $.validator !== 'undefined') {
            $.validator.addMethod('arabicOnly', function(value, element) {
                return this.optional(element) || /^[\u0600-\u06FF\s]+$/.test(value);
            }, 'يرجى إدخال نص باللغة العربية فقط');
            
            $.validator.addMethod('englishOnly', function(value, element) {
                return this.optional(element) || /^[a-zA-Z\s]+$/.test(value);
            }, 'Please enter English text only');
            
            $.validator.addMethod('phoneNumber', function(value, element) {
                return this.optional(element) || /^[\+]?[0-9\-\(\)\s]+$/.test(value);
            }, 'يرجى إدخال رقم هاتف صحيح');
        }
    },
    
    // Setup notifications
    setupNotifications: function() {
        // Check for new notifications every 30 seconds
        if (this.config.userId) {
            setInterval(() => {
                this.checkNotifications();
            }, 30000);
        }
        
        // Setup notification sound
        this.notificationSound = new Audio('/assets/sounds/notification.mp3');
        this.notificationSound.volume = 0.5;
    },
    
    // Setup DataTables
    setupDataTables: function() {
        if (typeof $.fn.DataTable === 'undefined') return;
        
        // Initialize all data tables
        $('.data-table').each(function() {
            const $table = $(this);
            const options = $table.data('options') || {};
            
            const defaultOptions = {
                responsive: true,
                pageLength: 20,
                order: [[0, 'desc']],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success btn-sm'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger btn-sm'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Print',
                        className: 'btn btn-info btn-sm'
                    }
                ]
            };
            
            $table.DataTable($.extend(true, defaultOptions, options));
        });
    },
    
    // Setup charts
    setupCharts: function() {
        if (typeof Chart === 'undefined') return;
        
        // Initialize all charts
        $('.chart-canvas').each(function() {
            const $canvas = $(this);
            const chartData = $canvas.data('chart');
            
            if (chartData) {
                new Chart($canvas[0], chartData);
            }
        });
    },
    
    // Load user preferences
    loadUserPreferences: function() {
        const preferences = localStorage.getItem('school_app_preferences');
        if (preferences) {
            try {
                const prefs = JSON.parse(preferences);
                this.applyPreferences(prefs);
            } catch (e) {
                console.error('Error loading preferences:', e);
            }
        }
    },
    
    // Apply user preferences
    applyPreferences: function(preferences) {
        if (preferences.theme) {
            document.body.setAttribute('data-theme', preferences.theme);
        }
        
        if (preferences.sidebarCollapsed) {
            document.body.classList.add('sidebar-collapsed');
        }
        
        if (preferences.fontSize) {
            document.documentElement.style.fontSize = preferences.fontSize + 'px';
        }
    },
    
    // Save user preferences
    savePreferences: function(preferences) {
        const currentPrefs = this.getPreferences();
        const newPrefs = Object.assign(currentPrefs, preferences);
        
        localStorage.setItem('school_app_preferences', JSON.stringify(newPrefs));
        
        // Send to server
        this.api.post('/save_preference', newPrefs);
    },
    
    // Get current preferences
    getPreferences: function() {
        const preferences = localStorage.getItem('school_app_preferences');
        return preferences ? JSON.parse(preferences) : {};
    },
    
    // Handle AJAX form submission
    handleAjaxForm: function($form) {
        const self = this;
        const url = $form.attr('action') || window.location.href;
        const method = $form.attr('method') || 'POST';
        const formData = new FormData($form[0]);
        
        $.ajax({
            url: url,
            method: method,
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                self.handleAjaxSuccess(response, $form);
            },
            error: function(xhr, status, error) {
                self.handleAjaxError(xhr, error);
            }
        });
    },
    
    // Handle AJAX success
    handleAjaxSuccess: function(response, $form) {
        if (response.success) {
            this.showNotification(response.message || 'Operation completed successfully', 'success');
            
            if (response.redirect) {
                window.location.href = response.redirect;
            } else if (response.reload) {
                window.location.reload();
            } else if ($form && $form.data('reset-on-success')) {
                $form[0].reset();
            }
        } else {
            this.showNotification(response.message || 'Operation failed', 'error');
        }
    },
    
    // Handle AJAX errors
    handleAjaxError: function(xhr, error) {
        let message = 'An error occurred';
        
        if (xhr.status === 401) {
            message = 'Session expired. Please login again.';
            setTimeout(() => {
                window.location.href = '/auth/login.php';
            }, 2000);
        } else if (xhr.status === 403) {
            message = 'Access denied';
        } else if (xhr.status === 404) {
            message = 'Resource not found';
        } else if (xhr.status === 500) {
            message = 'Server error occurred';
        } else if (xhr.responseJSON && xhr.responseJSON.message) {
            message = xhr.responseJSON.message;
        }
        
        this.showNotification(message, 'error');
    },
    
    // Show notification
    showNotification: function(message, type = 'info', duration = 5000) {
        // Use Toastr if available
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
            return;
        }
        
        // Fallback to custom notification
        const notification = $(`
            <div class="alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show notification-toast" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('#notifications-container').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, duration);
        
        // Play sound for important notifications
        if (type === 'error' || type === 'warning') {
            this.playNotificationSound();
        }
    },
    
    // Play notification sound
    playNotificationSound: function() {
        if (this.notificationSound) {
            this.notificationSound.play().catch(e => {
                // Ignore autoplay restrictions
            });
        }
    },
    
    // Show loading indicator
    showLoading: function() {
        if (!$('#loading-overlay').length) {
            $('body').append(`
                <div id="loading-overlay" class="loading-overlay">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            `);
        }
        $('#loading-overlay').fadeIn(200);
    },
    
    // Hide loading indicator
    hideLoading: function() {
        $('#loading-overlay').fadeOut(200);
    },
    
    // Confirm delete action
    confirmDelete: function($element) {
        const self = this;
        const url = $element.attr('href') || $element.data('url');
        const message = $element.data('message') || 'Are you sure you want to delete this item?';
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Confirm Delete',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    self.performDelete(url);
                }
            });
        } else {
            if (confirm(message)) {
                self.performDelete(url);
            }
        }
    },
    
    // Perform delete action
    performDelete: function(url) {
        const self = this;
        
        $.ajax({
            url: url,
            method: 'DELETE',
            success: function(response) {
                self.handleAjaxSuccess(response);
            },
            error: function(xhr, status, error) {
                self.handleAjaxError(xhr, error);
            }
        });
    },
    
    // Toggle theme
    toggleTheme: function() {
        const currentTheme = document.body.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.body.setAttribute('data-theme', newTheme);
        this.savePreferences({ theme: newTheme });
    },
    
    // Toggle language
    toggleLanguage: function() {
        const currentLang = this.config.language;
        const newLang = currentLang === 'ar' ? 'en' : 'ar';
        
        // Redirect to change language
        window.location.href = `?lang=${newLang}`;
    },
    
    // Toggle sidebar
    toggleSidebar: function() {
        const isCollapsed = document.body.classList.toggle('sidebar-collapsed');
        this.savePreferences({ sidebarCollapsed: isCollapsed });
    },
    
    // Initialize modal content
    initializeModalContent: function($modal) {
        // Initialize form validation in modal
        $modal.find('.needs-validation').each(function() {
            $(this).removeClass('was-validated');
        });
        
        // Initialize select2 in modal
        if (typeof $.fn.select2 !== 'undefined') {
            $modal.find('.select2').select2({
                dropdownParent: $modal
            });
        }
        
        // Initialize date pickers in modal
        if (typeof $.fn.datepicker !== 'undefined') {
            $modal.find('.datepicker').datepicker();
        }
    },
    
    // Preview uploaded file
    previewFile: function($input) {
        const file = $input[0].files[0];
        const previewContainer = $($input.data('preview'));
        
        if (file && previewContainer.length) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                if (file.type.startsWith('image/')) {
                    previewContainer.html(`<img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">`);
                } else {
                    previewContainer.html(`<p><i class="fas fa-file"></i> ${file.name}</p>`);
                }
            };
            
            reader.readAsDataURL(file);
        }
    },
    
    // Auto-save form
    autoSaveForm: function($form) {
        const formData = $form.serialize();
        const url = $form.data('autosave-url') || $form.attr('action');
        
        if (url) {
            $.ajax({
                url: url,
                method: 'POST',
                data: formData + '&autosave=1',
                success: function(response) {
                    if (response.success) {
                        $form.find('.autosave-indicator').text('Saved').fadeIn().delay(2000).fadeOut();
                    }
                }
            });
        }
    },
    
    // Check for new notifications
    checkNotifications: function() {
        $.ajax({
            url: '/api/notifications',
            method: 'GET',
            success: (response) => {
                if (response.success && response.data.length > 0) {
                    this.updateNotificationBadge(response.data.length);
                    
                    // Show new notifications
                    response.data.forEach(notification => {
                        if (notification.is_new) {
                            this.showNotification(notification.message, notification.type);
                        }
                    });
                }
            }
        });
    },
    
    // Update notification badge
    updateNotificationBadge: function(count) {
        const badge = $('.notification-badge');
        if (count > 0) {
            badge.text(count).show();
        } else {
            badge.hide();
        }
    },
    
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // API helper
    api: {
        get: function(endpoint, data = {}) {
            return $.ajax({
                url: SchoolApp.config.apiUrl + endpoint,
                method: 'GET',
                data: data
            });
        },
        
        post: function(endpoint, data = {}) {
            return $.ajax({
                url: SchoolApp.config.apiUrl + endpoint,
                method: 'POST',
                data: data
            });
        },
        
        put: function(endpoint, data = {}) {
            return $.ajax({
                url: SchoolApp.config.apiUrl + endpoint,
                method: 'PUT',
                data: data
            });
        },
        
        delete: function(endpoint) {
            return $.ajax({
                url: SchoolApp.config.apiUrl + endpoint,
                method: 'DELETE'
            });
        }
    }
};

// Initialize app when document is ready
$(document).ready(function() {
    SchoolApp.init();
});

// Export for use in other scripts
window.App = SchoolApp;
