@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    إعداد قاعدة البيانات
echo    Database Setup
echo ========================================
echo.

set MYSQL_PATH=C:\xampp\mysql\bin\mysql.exe

if not exist "%MYSQL_PATH%" (
    echo ❌ خطأ: لم يتم العثور على MySQL
    echo ❌ Error: MySQL not found
    pause
    exit /b 1
)

echo 🔄 بدء إعداد قاعدة البيانات...
echo 🔄 Starting database setup...
echo.

echo الخطوة 1: إعادة تعيين قاعدة البيانات...
echo Step 1: Resetting database...
"%MYSQL_PATH%" -u root -e "DROP DATABASE IF EXISTS school_management; CREATE DATABASE school_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if %errorlevel% neq 0 (
    echo ❌ فشل في إعادة تعيين قاعدة البيانات
    echo ❌ Failed to reset database
    pause
    exit /b 1
)

echo ✅ تم إعادة تعيين قاعدة البيانات بنجاح
echo ✅ Database reset successful
echo.

echo الخطوة 2: رفع البيانات الجديدة...
echo Step 2: Importing new data...
"%MYSQL_PATH%" -u root school_management < "database\school_management_clean.sql"

if %errorlevel% neq 0 (
    echo ❌ فشل في رفع البيانات
    echo ❌ Failed to import data
    pause
    exit /b 1
)

echo ✅ تم رفع البيانات بنجاح
echo ✅ Data import successful
echo.

echo الخطوة 3: التحقق من النتائج...
echo Step 3: Verifying results...
"%MYSQL_PATH%" -u root school_management -e "SHOW TABLES;"

echo.
echo 🎉 تم إعداد قاعدة البيانات بنجاح!
echo 🎉 Database setup completed successfully!
echo.
echo يمكنك الآن استخدام النظام بالبيانات التالية:
echo You can now use the system with the following credentials:
echo Username: admin
echo Password: password
echo Email: <EMAIL>
echo.
pause
