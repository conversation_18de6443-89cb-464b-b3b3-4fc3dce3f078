<?php
/**
 * صفحة عرض تفاصيل الصف الدراسي
 * View School Grade Details Page
 */

require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('view_grade');

// الحصول على معرف الصف
$grade_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($grade_id <= 0) {
    redirect_to('index.php');
}

// جلب بيانات الصف مع المرحلة
$grade_query = "
    SELECT g.*, es.stage_name, es.stage_code, es.stage_name_en
    FROM grades g 
    INNER JOIN educational_stages es ON g.stage_id = es.id 
    WHERE g.id = ?
";
$stmt = $conn->prepare($grade_query);
if (!$stmt) {
    die(__('database_error') . ': ' . $conn->error);
}

$stmt->bind_param('i', $grade_id);
$stmt->execute();
$result = $stmt->get_result();
$grade = $result->fetch_assoc();
$stmt->close();

if (!$grade) {
    redirect_to('index.php');
}

// جلب الفصول المرتبطة بالصف
$classes_query = "
    SELECT 
        c.*,
        COUNT(DISTINCT s.id) as students_count,
        u.full_name as teacher_name
    FROM classes c
    LEFT JOIN students s ON c.id = s.class_id
    LEFT JOIN teachers t ON c.class_teacher_id = t.id
    LEFT JOIN users u ON t.user_id = u.id
    WHERE c.grade_id = ?
    GROUP BY c.id
    ORDER BY c.class_name
";
$stmt = $conn->prepare($classes_query);
$classes = [];
if ($stmt) {
    $stmt->bind_param('i', $grade_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $classes[] = $row;
    }
    $stmt->close();
}

// جلب المواد المرتبطة بالصف
$subjects_query = "
    SELECT
        s.*,
        COUNT(DISTINCT ta.teacher_id) as teachers_count
    FROM subjects s
    LEFT JOIN teacher_assignments ta ON s.id = ta.subject_id
    WHERE s.grade_id = ?
    GROUP BY s.id
    ORDER BY s.subject_name
";
$stmt = $conn->prepare($subjects_query);
$subjects = [];
if ($stmt) {
    $stmt->bind_param('i', $grade_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $subjects[] = $row;
    }
    $stmt->close();
}

// حساب الإحصائيات
$total_classes = count($classes);
$total_subjects = count($subjects);
$total_students = array_sum(array_column($classes, 'students_count'));
$active_classes = count(array_filter($classes, function($c) { return $c['status'] === 'active'; }));
$active_subjects = count(array_filter($subjects, function($s) { return $s['status'] === 'active'; }));
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-eye text-primary me-2"></i>
                <?php echo __('view_grade'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('grade_details'); ?></p>
        </div>
        <div>
            <a href="edit.php?id=<?php echo $grade_id; ?>" class="btn btn-primary me-2">
                <i class="fas fa-edit me-2"></i><?php echo __('edit_grade'); ?>
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_grades'); ?>
            </a>
        </div>
    </div>

    <!-- Grade Information Card -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        <?php echo htmlspecialchars($grade['grade_name']); ?>
                        <span class="badge bg-<?php echo $grade['status'] === 'active' ? 'success' : 'secondary'; ?> ms-2">
                            <?php echo __($grade['status']); ?>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('grade_name'); ?>:</td>
                                    <td><?php echo htmlspecialchars($grade['grade_name']); ?></td>
                                </tr>
                                <?php if (!empty($grade['grade_name_en'])): ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('grade_name_en'); ?>:</td>
                                    <td><?php echo htmlspecialchars($grade['grade_name_en']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('grade_code'); ?>:</td>
                                    <td><code><?php echo htmlspecialchars($grade['grade_code']); ?></code></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('educational_stage'); ?>:</td>
                                    <td>
                                        <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($grade['stage_code']); ?></span>
                                        <?php echo htmlspecialchars($grade['stage_name']); ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('sort_order'); ?>:</td>
                                    <td><span class="badge bg-primary"><?php echo $grade['sort_order']; ?></span></td>
                                </tr>
                                <?php if ($grade['min_age'] || $grade['max_age']): ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('age_range'); ?>:</td>
                                    <td>
                                        <?php if ($grade['min_age'] && $grade['max_age']): ?>
                                            <?php echo $grade['min_age']; ?> - <?php echo $grade['max_age']; ?> <?php echo __('years'); ?>
                                        <?php elseif ($grade['min_age']): ?>
                                            <?php echo __('from'); ?> <?php echo $grade['min_age']; ?> <?php echo __('years'); ?>
                                        <?php elseif ($grade['max_age']): ?>
                                            <?php echo __('up_to'); ?> <?php echo $grade['max_age']; ?> <?php echo __('years'); ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('created_at'); ?>:</td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($grade['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('updated_at'); ?>:</td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($grade['updated_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if (!empty($grade['description'])): ?>
                    <div class="mt-3">
                        <h6 class="text-muted"><?php echo __('description'); ?>:</h6>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($grade['description'])); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Classes Section -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-school me-2"></i>
                        <?php echo __('classes'); ?>
                        <span class="badge bg-secondary"><?php echo $total_classes; ?></span>
                    </h5>
                    <a href="../classes/add.php?grade_id=<?php echo $grade_id; ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i><?php echo __('add_class'); ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($classes)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-school fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted"><?php echo __('no_classes_found'); ?></h6>
                            <p class="text-muted"><?php echo __('no_classes_in_grade'); ?></p>
                            <a href="../classes/add.php?grade_id=<?php echo $grade_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i><?php echo __('add_first_class'); ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th><?php echo __('class_name'); ?></th>
                                        <th><?php echo __('class_teacher'); ?></th>
                                        <th><?php echo __('students'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($classes as $class): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($class['class_name']); ?></strong>
                                                    <?php if (!empty($class['class_name_en'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($class['class_name_en']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if (!empty($class['teacher_name'])): ?>
                                                    <?php echo htmlspecialchars($class['teacher_name']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo __('not_assigned'); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $class['students_count']; ?></span>
                                            </td>
                                            <td>
                                                <?php if ($class['status'] === 'active'): ?>
                                                    <span class="badge bg-success"><?php echo __('active'); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="../classes/view.php?id=<?php echo $class['id']; ?>" 
                                                       class="btn btn-outline-info" title="<?php echo __('view'); ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="../classes/edit.php?id=<?php echo $class['id']; ?>" 
                                                       class="btn btn-outline-primary" title="<?php echo __('edit'); ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar with Statistics -->
        <div class="col-lg-4">
            <!-- Statistics Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo __('grade_statistics'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary mb-0"><?php echo $total_classes; ?></h4>
                            <small class="text-muted"><?php echo __('classes'); ?></small>
                            <br><small class="text-success"><?php echo $active_classes; ?> <?php echo __('active'); ?></small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-warning mb-0"><?php echo $total_subjects; ?></h4>
                            <small class="text-muted"><?php echo __('subjects'); ?></small>
                            <br><small class="text-success"><?php echo $active_subjects; ?> <?php echo __('active'); ?></small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-info mb-0"><?php echo $total_students; ?></h4>
                            <small class="text-muted"><?php echo __('students'); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        <?php echo __('quick_actions'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="edit.php?id=<?php echo $grade_id; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i><?php echo __('edit_grade'); ?>
                        </a>
                        <a href="../classes/add.php?grade_id=<?php echo $grade_id; ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus me-2"></i><?php echo __('add_class'); ?>
                        </a>
                        <a href="../classes/?grade_id=<?php echo $grade_id; ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-list me-2"></i><?php echo __('view_all_classes'); ?>
                        </a>
                        <hr>
                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                onclick="confirmDelete(<?php echo $grade_id; ?>, '<?php echo htmlspecialchars($grade['grade_name'], ENT_QUOTES); ?>')">
                            <i class="fas fa-trash me-2"></i><?php echo __('delete_grade'); ?>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Grade Info Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('additional_info'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted"><?php echo __('grade_id'); ?>:</small><br>
                        <code>#<?php echo $grade['id']; ?></code>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted"><?php echo __('stage_hierarchy'); ?>:</small><br>
                        <span class="small">
                            <?php echo htmlspecialchars($grade['stage_name']); ?> → 
                            <?php echo htmlspecialchars($grade['grade_name']); ?>
                        </span>
                    </div>
                    <?php if ($total_classes > 0): ?>
                    <div>
                        <small class="text-muted"><?php echo __('average_students_per_class'); ?>:</small><br>
                        <span><?php echo round($total_students / $total_classes, 1); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Subjects Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        <?php echo __('subjects'); ?>
                        <span class="badge bg-warning"><?php echo $total_subjects; ?></span>
                    </h5>
                    <a href="../subjects/add.php?grade_id=<?php echo $grade_id; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_subject'); ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($subjects)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted"><?php echo __('no_subjects_found'); ?></h5>
                            <p class="text-muted"><?php echo __('add_first_subject'); ?></p>
                            <a href="../subjects/add.php?grade_id=<?php echo $grade_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i><?php echo __('add_subject'); ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th><?php echo __('subject_name'); ?></th>
                                        <th><?php echo __('subject_code'); ?></th>
                                        <th><?php echo __('credit_hours'); ?></th>
                                        <th><?php echo __('department'); ?></th>
                                        <th><?php echo __('teachers'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subjects as $subject): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                                <?php if (!empty($subject['subject_name_en'])): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($subject['subject_name_en']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><code><?php echo htmlspecialchars($subject['subject_code']); ?></code></td>
                                            <td><?php echo $subject['credit_hours']; ?></td>
                                            <td><?php echo htmlspecialchars($subject['department'] ?: '-'); ?></td>
                                            <td><span class="badge bg-secondary"><?php echo $subject['teachers_count']; ?></span></td>
                                            <td>
                                                <?php if ($subject['status'] === 'active'): ?>
                                                    <span class="badge bg-success"><?php echo __('active'); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="../subjects/view.php?id=<?php echo $subject['id']; ?>"
                                                       class="btn btn-outline-info" title="<?php echo __('view'); ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="../subjects/edit.php?id=<?php echo $subject['id']; ?>"
                                                       class="btn btn-outline-warning" title="<?php echo __('edit'); ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(gradeId, gradeName) {
    <?php if ($total_classes > 0): ?>
        // إذا كانت هناك فصول مرتبطة، عرض تحذير فقط
        Swal.fire({
            title: '<?php echo __('cannot_delete'); ?>',
            html: `
                <div class="text-center mb-3">
                    <i class="fas fa-ban fa-3x text-danger"></i>
                </div>
                <p><?php echo __('cannot_delete_grade_has_classes'); ?></p>
                <div class="alert alert-warning">
                    <strong><?php echo __('grade_name'); ?>:</strong> ${gradeName}
                </div>
                <p class="text-muted small"><?php echo __('total_classes'); ?>: <?php echo $total_classes; ?></p>
            `,
            icon: 'error',
            confirmButtonText: '<?php echo __('ok'); ?>',
            customClass: {
                confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
        });
        return;
    <?php endif; ?>

    // استخدام SweetAlert للتأكيد
    Swal.fire({
        title: '<?php echo __('confirm_delete'); ?>',
        html: `
            <div class="text-center mb-3">
                <i class="fas fa-trash-alt fa-3x text-danger"></i>
            </div>
            <p><?php echo __('delete_grade_confirmation'); ?></p>
            <div class="alert alert-warning">
                <strong><?php echo __('grade_name'); ?>:</strong> ${gradeName}
            </div>
            <p class="text-muted small"><?php echo __('delete_grade_warning'); ?></p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>',
        cancelButtonText: '<i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار رسالة تحميل
            Swal.fire({
                title: '<?php echo __('deleting'); ?>...',
                text: '<?php echo __('please_wait'); ?>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // الانتقال لصفحة الحذف
            window.location.href = 'delete.php?id=' + gradeId;
        }
    });
}
</script>

<?php require_once '../includes/footer.php'; ?>
