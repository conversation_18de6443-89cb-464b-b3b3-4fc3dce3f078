<?php
/**
 * ملف الاتصال بقاعدة البيانات
 * Database Connection Handler
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

/**
 * فئة إدارة قاعدة البيانات
 */
class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $database;
    private $username;
    private $password;
    private $charset;
    
    /**
     * منشئ الفئة
     */
    private function __construct() {
        // التأكد من تعريف الثوابت قبل الاستخدام
        $this->host = defined('DB_HOST') ? DB_HOST : 'localhost';
        $this->database = defined('DB_NAME') ? DB_NAME : 'school_management';
        $this->username = defined('DB_USER') ? DB_USER : 'root';
        $this->password = defined('DB_PASS') ? DB_PASS : (defined('DB_PASSWORD') ? DB_PASSWORD : '');
        $this->charset = defined('DB_CHARSET') ? DB_CHARSET : 'utf8mb4';
        
        $this->connect();
    }
    
    /**
     * الحصول على مثيل واحد من الفئة (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->database};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            // تسجيل نجاح الاتصال
            if (DEBUG_MODE) {
                error_log("Database connection established successfully");
            }
            
        } catch (PDOException $e) {
            // تسجيل خطأ الاتصال
            error_log("Database connection failed: " . $e->getMessage());

            if (DEBUG_MODE) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("عذراً، حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.");
            }
        }
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Select query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام SELECT لصف واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("SelectOne query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $this->connection->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Insert query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $stmt->rowCount();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Update query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $stmt->rowCount();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Delete query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام عام
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Execute query failed: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
    
    /**
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * الحصول على عدد الصفوف
     */
    public function count($table, $where = '', $params = []) {
        $query = "SELECT COUNT(*) as count FROM {$table}";
        if ($where) {
            $query .= " WHERE {$where}";
        }
        
        $result = $this->selectOne($query, $params);
        return $result ? $result['count'] : 0;
    }
    
    /**
     * التحقق من وجود سجل
     */
    public function exists($table, $where, $params = []) {
        $query = "SELECT 1 FROM {$table} WHERE {$where} LIMIT 1";
        $result = $this->selectOne($query, $params);
        return $result !== false;
    }
    
    /**
     * إدراج أو تحديث سجل
     */
    public function insertOrUpdate($table, $data, $updateFields = []) {
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        
        $query = "INSERT INTO {$table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";
        
        if (!empty($updateFields)) {
            $updateParts = [];
            foreach ($updateFields as $field) {
                $updateParts[] = "{$field} = VALUES({$field})";
            }
            $query .= " ON DUPLICATE KEY UPDATE " . implode(', ', $updateParts);
        }
        
        return $this->execute($query, $data);
    }
    
    /**
     * إنشاء استعلام SELECT مع ترقيم الصفحات
     */
    public function paginate($query, $params = [], $page = 1, $perPage = 20) {
        // حساب العدد الإجمالي
        $countQuery = "SELECT COUNT(*) as total FROM ({$query}) as count_table";
        $totalResult = $this->selectOne($countQuery, $params);
        $total = $totalResult ? $totalResult['total'] : 0;
        
        // حساب الإزاحة
        $offset = ($page - 1) * $perPage;
        
        // إضافة LIMIT و OFFSET
        $paginatedQuery = $query . " LIMIT {$perPage} OFFSET {$offset}";
        $data = $this->select($paginatedQuery, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage),
            'has_next' => $page < ceil($total / $perPage),
            'has_prev' => $page > 1
        ];
    }
    
    /**
     * تنظيف الجدول
     */
    public function truncate($table) {
        return $this->execute("TRUNCATE TABLE {$table}");
    }
    
    /**
     * الحصول على معلومات الجدول
     */
    public function getTableInfo($table) {
        return $this->select("DESCRIBE {$table}");
    }
    
    /**
     * إنشاء نسخة احتياطية من الجدول
     */
    public function backupTable($table, $backupTable = null) {
        if (!$backupTable) {
            $backupTable = $table . '_backup_' . date('Y_m_d_H_i_s');
        }
        
        $query = "CREATE TABLE {$backupTable} AS SELECT * FROM {$table}";
        return $this->execute($query);
    }
    
    /**
     * منع الاستنساخ
     */
    private function __clone() {}
    
    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// إنشاء اتصال قاعدة البيانات العام للتوافق مع الكود القديم
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // تحويل PDO إلى MySQLi للتوافق مع الكود القديم
    $mysqli_conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($mysqli_conn->connect_error) {
        throw new Exception("MySQLi connection failed: " . $mysqli_conn->connect_error);
    }
    
    $mysqli_conn->set_charset(DB_CHARSET);
    
    // استخدام المتغير العام للتوافق
    $conn = $mysqli_conn;
    
} catch (Exception $e) {
    error_log("Database initialization failed: " . $e->getMessage());
    
    if (DEBUG_MODE) {
        die("Database initialization failed: " . $e->getMessage());
    } else {
        die("عذراً، حدث خطأ في تهيئة قاعدة البيانات. يرجى المحاولة لاحقاً.");
    }
}

/**
 * دوال مساعدة للاستعلامات الشائعة
 */

/**
 * الحصول على مستخدم بواسطة ID
 */
function get_user_by_id($user_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    return $stmt->get_result()->fetch_assoc();
}

/**
 * الحصول على مستخدم بواسطة اسم المستخدم أو البريد الإلكتروني
 */
function get_user_by_username($username) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param("ss", $username, $username);
    $stmt->execute();
    return $stmt->get_result()->fetch_assoc();
}

/**
 * الحصول على مستخدم بواسطة البريد الإلكتروني
 */
function get_user_by_email($email) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    return $stmt->get_result()->fetch_assoc();
}

/**
 * تحديث آخر دخول للمستخدم
 */
function update_last_login($user_id) {
    global $conn;
    $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    return $stmt->execute();
}

/**
 * الحصول على إحصائيات سريعة
 */
function get_quick_stats() {
    global $conn;

    $stats = [];

    // عدد الطلاب
    $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'student' AND status = 'active'");
    $stats['students'] = $result->fetch_assoc()['count'];

    // عدد المعلمين
    $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'teacher' AND status = 'active'");
    $stats['teachers'] = $result->fetch_assoc()['count'];

    // عدد المراحل الدراسية
    $result = $conn->query("SELECT COUNT(*) as count FROM educational_stages WHERE status = 'active'");
    $stats['stages'] = $result->fetch_assoc()['count'];

    // عدد الصفوف الدراسية
    $result = $conn->query("SELECT COUNT(*) as count FROM grades WHERE status = 'active'");
    $stats['grades'] = $result->fetch_assoc()['count'];

    // عدد الفصول
    $result = $conn->query("SELECT COUNT(*) as count FROM classes WHERE status = 'active'");
    $stats['classes'] = $result->fetch_assoc()['count'];

    // عدد المواد
    $result = $conn->query("SELECT COUNT(*) as count FROM subjects WHERE status = 'active'");
    $stats['subjects'] = $result->fetch_assoc()['count'];
    
    // عدد الامتحانات النشطة
    $result = $conn->query("SELECT COUNT(*) as count FROM exams WHERE status IN ('published', 'active')");
    $stats['active_exams'] = $result->fetch_assoc()['count'];
    
    return $stats;
}

// تشغيل الإصلاح التلقائي لقاعدة البيانات
if ($conn && !defined('DB_AUTO_FIX_DISABLED')) {
    require_once __DIR__ . '/auto_fix_database.php';
    check_and_fix_database($conn);
}
?>
