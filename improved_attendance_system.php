<?php
/**
 * نظام الحضور والغياب الموحد المحسن
 * Improved Unified Attendance and Absence System
 */

echo "<h1>🏢 نظام الحضور والغياب الموحد المحسن</h1>";

// الاتصال بقاعدة البيانات
$conn = new mysqli('localhost', 'root', '', 'school_management');

if ($conn->connect_error) {
    echo "<div class='alert alert-danger'>❌ فشل في الاتصال: " . $conn->connect_error . "</div>";
    exit;
}

$conn->set_charset("utf8mb4");
echo "<div class='alert alert-success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'record_absence_with_deduction':
            recordAbsenceWithDeduction($conn);
            break;
        case 'mark_present':
            markPresent($conn);
            break;
    }
}

/**
 * تسجيل غياب بالخصم
 */
function recordAbsenceWithDeduction($conn) {
    try {
        $user_id = intval($_POST['user_id']);
        $absence_date = $_POST['absence_date'];
        $absence_type = $_POST['absence_type'];
        $reason = $_POST['reason'];
        $deduction_amount = floatval($_POST['deduction_amount']);
        $applied_by = 1; // يجب أن يكون من الجلسة
        
        $stmt = $conn->prepare("CALL record_absence_with_deduction(?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("isssdi", $user_id, $absence_date, $absence_type, $reason, $deduction_amount, $applied_by);
        
        if ($stmt->execute()) {
            $result = $stmt->get_result();
            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                echo "<div class='alert alert-success'>✅ " . $row['message'] . "</div>";
            } else {
                echo "<div class='alert alert-success'>✅ تم تسجيل الغياب بالخصم بنجاح</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في تسجيل الغياب: " . $stmt->error . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

/**
 * تسجيل حضور
 */
function markPresent($conn) {
    try {
        $user_id = intval($_POST['user_id']);
        $attendance_date = $_POST['attendance_date'];
        $check_in_time = $_POST['check_in_time'];
        $check_out_time = $_POST['check_out_time'];
        $notes = $_POST['notes'];
        
        $stmt = $conn->prepare("
            INSERT INTO staff_attendance (user_id, attendance_date, status, check_in_time, check_out_time, notes) 
            VALUES (?, ?, 'present', ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            status = 'present',
            check_in_time = VALUES(check_in_time),
            check_out_time = VALUES(check_out_time),
            notes = VALUES(notes)
        ");
        $stmt->bind_param("issss", $user_id, $attendance_date, $check_in_time, $check_out_time, $notes);
        
        if ($stmt->execute()) {
            echo "<div class='alert alert-success'>✅ تم تسجيل الحضور بنجاح</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في تسجيل الحضور: " . $stmt->error . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

// عرض نظام الحضور والغياب الموحد
echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h3>📊 نظام الحضور والغياب الموحد</h3>";
echo "</div>";
echo "<div class='card-body'>";

// جلب البيانات من View الموحد
$attendance_result = $conn->query("
    SELECT 
        user_id,
        user_name,
        role,
        leave_status,
        attendance_status,
        check_in_time,
        check_out_time,
        notes
    FROM unified_attendance_view
    ORDER BY user_name
");

if ($attendance_result && $attendance_result->num_rows > 0) {
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>#</th>";
    echo "<th>الموظف</th>";
    echo "<th>الدور</th>";
    echo "<th>حالة الإجازة</th>";
    echo "<th>حالة الحضور</th>";
    echo "<th>وقت الدخول</th>";
    echo "<th>وقت الخروج</th>";
    echo "<th>ملاحظات</th>";
    echo "<th>الإجراءات</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    $counter = 1;
    while ($row = $attendance_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $counter++ . "</td>";
        echo "<td>" . $row['user_name'] . "</td>";
        echo "<td>" . translateRole($row['role']) . "</td>";
        echo "<td><span class='badge " . getLeaveStatusClass($row['leave_status']) . "'>" . $row['leave_status'] . "</span></td>";
        echo "<td><span class='badge " . getAttendanceStatusClass($row['attendance_status']) . "'>" . $row['attendance_status'] . "</span></td>";
        echo "<td>" . ($row['check_in_time'] ?: '--:--') . "</td>";
        echo "<td>" . ($row['check_out_time'] ?: '--:--') . "</td>";
        echo "<td>" . substr($row['notes'], 0, 30) . (strlen($row['notes']) > 30 ? '...' : '') . "</td>";
        echo "<td>";
        echo "<button class='btn btn-sm btn-primary me-1' onclick='markPresent(" . $row['user_id'] . ")'>حضور</button>";
        echo "<button class='btn btn-sm btn-warning' onclick='markAbsent(" . $row['user_id'] . ")'>غياب بخصم</button>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>لا توجد بيانات حضور</div>";
}

echo "</div>";
echo "</div>";

// نموذج تسجيل غياب بالخصم
echo "<div class='card mt-4'>";
echo "<div class='card-header'>";
echo "<h3>⚠️ تسجيل غياب بالخصم</h3>";
echo "</div>";
echo "<div class='card-body'>";

$users_result = $conn->query("SELECT id, full_name, role FROM users WHERE role IN ('teacher', 'staff', 'admin') AND status = 'active' ORDER BY full_name");

echo "<form method='POST' class='row g-3'>";
echo "<input type='hidden' name='action' value='record_absence_with_deduction'>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>الموظف:</label>";
echo "<select name='user_id' class='form-select' required>";
echo "<option value=''>اختر الموظف</option>";
while ($user = $users_result->fetch_assoc()) {
    echo "<option value='" . $user['id'] . "'>" . $user['full_name'] . " (" . translateRole($user['role']) . ")</option>";
}
echo "</select>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>تاريخ الغياب:</label>";
echo "<input type='date' name='absence_date' class='form-control' value='" . date('Y-m-d') . "' required>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>نوع الغياب:</label>";
echo "<select name='absence_type' class='form-select' required onchange='updateDeductionAmount(this.value)'>";
echo "<option value=''>اختر نوع الغياب</option>";
echo "<option value='unauthorized'>غياب غير مبرر (100 ريال)</option>";
echo "<option value='personal'>غياب شخصي (50 ريال)</option>";
echo "<option value='emergency'>غياب طارئ (25 ريال)</option>";
echo "<option value='sick'>غياب مرضي (0 ريال)</option>";
echo "</select>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>مبلغ الخصم (ريال):</label>";
echo "<input type='number' name='deduction_amount' class='form-control' step='0.01' min='0' required id='deduction_amount'>";
echo "</div>";

echo "<div class='col-md-12'>";
echo "<label class='form-label'>السبب:</label>";
echo "<textarea name='reason' class='form-control' rows='3' required></textarea>";
echo "</div>";

echo "<div class='col-12'>";
echo "<button type='submit' class='btn btn-warning'>تسجيل غياب بالخصم</button>";
echo "</div>";

echo "</form>";
echo "</div>";
echo "</div>";

// نموذج تسجيل حضور
echo "<div class='card mt-4'>";
echo "<div class='card-header'>";
echo "<h3>✅ تسجيل حضور</h3>";
echo "</div>";
echo "<div class='card-body'>";

$users_result2 = $conn->query("SELECT id, full_name, role FROM users WHERE role IN ('teacher', 'staff', 'admin') AND status = 'active' ORDER BY full_name");

echo "<form method='POST' class='row g-3'>";
echo "<input type='hidden' name='action' value='mark_present'>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>الموظف:</label>";
echo "<select name='user_id' class='form-select' required>";
echo "<option value=''>اختر الموظف</option>";
while ($user = $users_result2->fetch_assoc()) {
    echo "<option value='" . $user['id'] . "'>" . $user['full_name'] . " (" . translateRole($user['role']) . ")</option>";
}
echo "</select>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>تاريخ الحضور:</label>";
echo "<input type='date' name='attendance_date' class='form-control' value='" . date('Y-m-d') . "' required>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>وقت الدخول:</label>";
echo "<input type='time' name='check_in_time' class='form-control' value='" . date('H:i') . "' required>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<label class='form-label'>وقت الخروج (اختياري):</label>";
echo "<input type='time' name='check_out_time' class='form-control'>";
echo "</div>";

echo "<div class='col-md-12'>";
echo "<label class='form-label'>ملاحظات:</label>";
echo "<textarea name='notes' class='form-control' rows='2'></textarea>";
echo "</div>";

echo "<div class='col-12'>";
echo "<button type='submit' class='btn btn-success'>تسجيل حضور</button>";
echo "</div>";

echo "</form>";
echo "</div>";
echo "</div>";

// إحصائيات سريعة
echo "<div class='card mt-4'>";
echo "<div class='card-header'>";
echo "<h3>📈 إحصائيات اليوم</h3>";
echo "</div>";
echo "<div class='card-body'>";

$stats_result = $conn->query("
    SELECT 
        attendance_status,
        COUNT(*) as count
    FROM unified_attendance_view
    GROUP BY attendance_status
");

if ($stats_result && $stats_result->num_rows > 0) {
    echo "<div class='row'>";
    while ($stat = $stats_result->fetch_assoc()) {
        echo "<div class='col-md-3'>";
        echo "<div class='card text-center border-" . getAttendanceStatusColor($stat['attendance_status']) . "'>";
        echo "<div class='card-body'>";
        echo "<h5>" . $stat['attendance_status'] . "</h5>";
        echo "<h3>" . $stat['count'] . "</h3>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

/**
 * دوال مساعدة
 */
function translateRole($role) {
    switch ($role) {
        case 'teacher': return 'المعلمين';
        case 'staff': return 'الموظفين';
        case 'admin': return 'الإدارة';
        default: return $role;
    }
}

function getLeaveStatusClass($status) {
    switch ($status) {
        case 'في إجازة': return 'badge-info';
        case 'متاح': return 'badge-success';
        default: return 'badge-secondary';
    }
}

function getAttendanceStatusClass($status) {
    switch ($status) {
        case 'حاضر': return 'badge-success';
        case 'غياب بالخصم': return 'badge-danger';
        case 'غائب': return 'badge-warning';
        case 'غير محدد': return 'badge-secondary';
        default: return 'badge-secondary';
    }
}

function getAttendanceStatusColor($status) {
    switch ($status) {
        case 'حاضر': return 'success';
        case 'غياب بالخصم': return 'danger';
        case 'غائب': return 'warning';
        case 'غير محدد': return 'secondary';
        default: return 'secondary';
    }
}

$conn->close();

?>

<script>
// تحديث مبلغ الخصم حسب نوع الغياب
function updateDeductionAmount(absenceType) {
    const deductionInput = document.getElementById('deduction_amount');
    
    switch (absenceType) {
        case 'unauthorized':
            deductionInput.value = '100.00';
            break;
        case 'personal':
            deductionInput.value = '50.00';
            break;
        case 'emergency':
            deductionInput.value = '25.00';
            break;
        case 'sick':
            deductionInput.value = '0.00';
            break;
        default:
            deductionInput.value = '';
    }
}

// تسجيل حضور سريع
function markPresent(userId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="mark_present">
        <input type="hidden" name="user_id" value="${userId}">
        <input type="hidden" name="attendance_date" value="${new Date().toISOString().split('T')[0]}">
        <input type="hidden" name="check_in_time" value="${new Date().toTimeString().split(' ')[0].substring(0,5)}">
        <input type="hidden" name="notes" value="تسجيل حضور سريع">
    `;
    document.body.appendChild(form);
    form.submit();
}

// تسجيل غياب بخصم سريع
function markAbsent(userId) {
    const reason = prompt('سبب الغياب:');
    if (reason && reason.trim()) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="record_absence_with_deduction">
            <input type="hidden" name="user_id" value="${userId}">
            <input type="hidden" name="absence_date" value="${new Date().toISOString().split('T')[0]}">
            <input type="hidden" name="absence_type" value="personal">
            <input type="hidden" name="reason" value="${reason}">
            <input type="hidden" name="deduction_amount" value="50.00">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
.alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.alert-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
.card { border: 1px solid #dee2e6; border-radius: 0.375rem; margin-bottom: 1rem; }
.card-header { padding: 0.75rem 1.25rem; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; }
.card-body { padding: 1.25rem; }
.table { width: 100%; border-collapse: collapse; margin: 10px 0; }
.table th, .table td { padding: 8px; text-align: right; border-bottom: 1px solid #ddd; font-size: 12px; }
.table-striped tbody tr:nth-child(odd) { background-color: #f9f9f9; }
.table-responsive { overflow-x: auto; }
.form-control, .form-select { width: 100%; padding: 0.375rem 0.75rem; margin-bottom: 0.5rem; border: 1px solid #ced4da; border-radius: 0.375rem; }
.btn { padding: 0.375rem 0.75rem; margin: 0.125rem; border: none; border-radius: 0.375rem; cursor: pointer; text-decoration: none; display: inline-block; }
.btn-primary { background-color: #0d6efd; color: white; }
.btn-success { background-color: #198754; color: white; }
.btn-warning { background-color: #ffc107; color: black; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
.badge { padding: 0.25em 0.5em; font-size: 0.75em; border-radius: 0.375rem; }
.badge-success { background-color: #198754; color: white; }
.badge-danger { background-color: #dc3545; color: white; }
.badge-warning { background-color: #ffc107; color: black; }
.badge-info { background-color: #0dcaf0; color: black; }
.badge-secondary { background-color: #6c757d; color: white; }
.row { display: flex; flex-wrap: wrap; margin: -0.5rem; }
.col-md-3, .col-md-6, .col-md-12, .col-12 { padding: 0.5rem; }
.col-md-3 { flex: 0 0 25%; }
.col-md-6 { flex: 0 0 50%; }
.col-md-12, .col-12 { flex: 0 0 100%; }
.me-1 { margin-left: 0.25rem; }
.mt-4 { margin-top: 1.5rem; }
.text-center { text-align: center; }
.border-success { border-color: #198754 !important; }
.border-danger { border-color: #dc3545 !important; }
.border-warning { border-color: #ffc107 !important; }
.border-secondary { border-color: #6c757d !important; }
</style>
