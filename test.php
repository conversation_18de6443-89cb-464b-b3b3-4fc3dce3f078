<?php
echo "<h2>اختبار النظام</h2>";
echo "PHP يعمل بشكل صحيح!<br>";
echo "التاريخ والوقت: " . date('Y-m-d H:i:s') . "<br>";
echo "إصدار PHP: " . phpversion() . "<br><br>";

// اختبار الاتصال بقاعدة البيانات
echo "<h3>اختبار قاعدة البيانات:</h3>";
try {
    $conn = new mysqli('localhost', 'root', '', 'school_management');
    if ($conn->connect_error) {
        echo "<span style='color:red'>❌ خطأ في الاتصال بقاعدة البيانات: " . $conn->connect_error . "</span><br>";
    } else {
        echo "<span style='color:green'>✅ الاتصال بقاعدة البيانات ناجح!</span><br>";

        // عرض جميع الجداول
        echo "<h4>الجداول الموجودة:</h4>";
        $tables_result = $conn->query("SHOW TABLES");
        if ($tables_result) {
            while ($table = $tables_result->fetch_array()) {
                echo "- " . $table[0] . "<br>";
            }
        }

        // اختبار وجود جدول users تحديداً
        $result = $conn->query("SHOW TABLES LIKE 'users'");
        if ($result && $result->num_rows > 0) {
            echo "<span style='color:green'>✅ جدول users موجود!</span><br>";

            // عدد المستخدمين
            $count_result = $conn->query("SELECT COUNT(*) as count FROM users");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['count'];
                echo "عدد المستخدمين: " . $count . "<br>";

                // عرض المستخدمين
                $users_result = $conn->query("SELECT id, username, email, role FROM users LIMIT 5");
                if ($users_result) {
                    echo "<h4>المستخدمون:</h4>";
                    while ($user = $users_result->fetch_assoc()) {
                        echo "- ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Role: {$user['role']}<br>";
                    }
                }
            }

            // اختبار العمود remember_token
            $column_check = $conn->query("SHOW COLUMNS FROM users LIKE 'remember_token'");
            if ($column_check && $column_check->num_rows > 0) {
                echo "<span style='color:green'>✅ العمود remember_token موجود</span><br>";
            } else {
                echo "<span style='color:red'>❌ العمود remember_token غير موجود</span><br>";
            }

        } else {
            echo "<span style='color:red'>❌ جدول users غير موجود!</span><br>";
        }
    }
    $conn->close();
} catch (Exception $e) {
    echo "<span style='color:red'>❌ خطأ: " . $e->getMessage() . "</span><br>";
}

echo "<br><h3>اختبار ملفات النظام:</h3>";
echo "ملف config/database.php: " . (file_exists('config/database.php') ? '✅ موجود' : '❌ غير موجود') . "<br>";
echo "ملف includes/functions.php: " . (file_exists('includes/functions.php') ? '✅ موجود' : '❌ غير موجود') . "<br>";
echo "ملف config/installed.lock: " . (file_exists('config/installed.lock') ? '✅ موجود' : '❌ غير موجود') . "<br>";
?>
