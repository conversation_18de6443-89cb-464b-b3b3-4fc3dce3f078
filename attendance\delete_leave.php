<?php
/**
 * حذف الإجازة
 * Delete Leave
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$leave_id = intval($_GET['id'] ?? 0);

if (!$leave_id) {
    header('Location: manage_leaves.php');
    exit();
}

// جلب تفاصيل الإجازة للعرض
$check_stmt = $conn->prepare("
    SELECT sl.*, u.full_name, u.role
    FROM staff_leaves sl
    JOIN users u ON sl.user_id = u.id
    WHERE sl.id = ?
");
$check_stmt->bind_param("i", $leave_id);
$check_stmt->execute();
$result = $check_stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error_message'] = 'الإجازة غير موجودة';
    header('Location: manage_leaves.php');
    exit();
}

$leave = $result->fetch_assoc();

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $_SESSION['error_message'] = 'طلب غير صالح';
    } else {
        try {
            $stmt = $conn->prepare("DELETE FROM staff_leaves WHERE id = ?");
            $stmt->bind_param("i", $leave_id);
            
            if ($stmt->execute()) {
                // تسجيل النشاط
                log_activity($user_id, 'delete_leave', 'staff_leaves', $leave_id);
                
                $_SESSION['success_message'] = 'تم حذف الإجازة بنجاح';
            } else {
                $_SESSION['error_message'] = 'حدث خطأ أثناء الحذف';
            }
        } catch (Exception $e) {
            $_SESSION['error_message'] = 'حدث خطأ: ' . $e->getMessage();
        }
    }
    
    header('Location: manage_leaves.php');
    exit();
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trash me-2"></i>
                        تأكيد حذف الإجازة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من حذف هذه الإجازة؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <!-- عرض تفاصيل الإجازة -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">تفاصيل الإجازة المراد حذفها:</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <th width="40%">الموظف:</th>
                                            <td><?php echo htmlspecialchars($leave['full_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <th>المنصب:</th>
                                            <td><?php echo $leave['role']; ?></td>
                                        </tr>
                                        <tr>
                                            <th>نوع الإجازة:</th>
                                            <td>
                                                <?php
                                                $leave_types = [
                                                    'sick' => 'إجازة مرضية',
                                                    'regular' => 'إجازة اعتيادية',
                                                    'emergency' => 'إجازة طارئة'
                                                ];
                                                echo $leave_types[$leave['leave_type']] ?? $leave['leave_type'];
                                                ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <th width="40%">من تاريخ:</th>
                                            <td><?php echo date('Y-m-d', strtotime($leave['start_date'])); ?></td>
                                        </tr>
                                        <tr>
                                            <th>إلى تاريخ:</th>
                                            <td><?php echo date('Y-m-d', strtotime($leave['end_date'])); ?></td>
                                        </tr>
                                        <tr>
                                            <th>عدد الأيام:</th>
                                            <td><?php echo $leave['total_days']; ?> يوم</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <?php if (!empty($leave['reason'])): ?>
                            <div class="row">
                                <div class="col-12">
                                    <strong>السبب:</strong>
                                    <p class="text-muted mt-1"><?php echo nl2br(htmlspecialchars($leave['reason'])); ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <form method="POST">
                        <?php echo csrf_token_field(); ?>

                        <div class="d-flex justify-content-between">
                            <a href="manage_leaves.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
