# سكريبت إعداد قاعدة البيانات
# Database Setup Script

Write-Host "بدء إعداد قاعدة البيانات..." -ForegroundColor Green
Write-Host "Starting database setup..." -ForegroundColor Green

# مسار MySQL
$mysqlPath = "C:\xampp\mysql\bin\mysql.exe"

# التحقق من وجود MySQL
if (-not (Test-Path $mysqlPath)) {
    Write-Host "خطأ: لم يتم العثور على MySQL في المسار المحدد" -ForegroundColor Red
    Write-Host "Error: MySQL not found at specified path" -ForegroundColor Red
    exit 1
}

try {
    # الخطوة 1: إعادة تعيين قاعدة البيانات
    Write-Host "الخطوة 1: إعادة تعيين قاعدة البيانات..." -ForegroundColor Yellow
    Write-Host "Step 1: Resetting database..." -ForegroundColor Yellow
    
    & $mysqlPath -u root -e "source reset_database.sql"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم إعادة تعيين قاعدة البيانات بنجاح" -ForegroundColor Green
        Write-Host "✅ Database reset successful" -ForegroundColor Green
    } else {
        throw "فشل في إعادة تعيين قاعدة البيانات"
    }
    
    # الخطوة 2: رفع البيانات الجديدة
    Write-Host "الخطوة 2: رفع البيانات الجديدة..." -ForegroundColor Yellow
    Write-Host "Step 2: Importing new data..." -ForegroundColor Yellow
    
    & $mysqlPath -u root school_management -e "source database/school_management_clean.sql"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم رفع البيانات بنجاح" -ForegroundColor Green
        Write-Host "✅ Data import successful" -ForegroundColor Green
    } else {
        throw "فشل في رفع البيانات"
    }
    
    # الخطوة 3: التحقق من النتائج
    Write-Host "الخطوة 3: التحقق من النتائج..." -ForegroundColor Yellow
    Write-Host "Step 3: Verifying results..." -ForegroundColor Yellow
    
    $tableCount = & $mysqlPath -u root school_management -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'school_management';" --skip-column-names
    
    Write-Host "عدد الجداول المُنشأة: $tableCount" -ForegroundColor Cyan
    Write-Host "Number of tables created: $tableCount" -ForegroundColor Cyan
    
    # عرض قائمة الجداول
    Write-Host "قائمة الجداول:" -ForegroundColor Cyan
    Write-Host "Tables list:" -ForegroundColor Cyan
    & $mysqlPath -u root school_management -e "SHOW TABLES;"
    
    Write-Host ""
    Write-Host "🎉 تم إعداد قاعدة البيانات بنجاح!" -ForegroundColor Green
    Write-Host "🎉 Database setup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "يمكنك الآن استخدام النظام بالبيانات التالية:" -ForegroundColor Yellow
    Write-Host "You can now use the system with the following credentials:" -ForegroundColor Yellow
    Write-Host "Username: admin" -ForegroundColor White
    Write-Host "Password: password" -ForegroundColor White
    Write-Host "Email: <EMAIL>" -ForegroundColor White
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
