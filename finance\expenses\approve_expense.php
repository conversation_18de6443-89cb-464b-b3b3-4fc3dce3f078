<?php
/**
 * معالجة طلبات الموافقة والرفض للمصروفات
 * Handle Expense Approval/Rejection Requests
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['expense_id']) || !isset($input['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'بيانات الطلب غير مكتملة']);
    exit();
}

$expense_id = intval($input['expense_id']);
$action = $input['action']; // 'approve' or 'reject'

// التحقق من صحة الإجراء
if (!in_array($action, ['approve', 'reject'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit();
}

try {
    $conn->begin_transaction();
    
    // التحقق من وجود المصروف وحالته
    $check_query = "SELECT id, status, amount, description FROM daily_expenses WHERE id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $expense_id);
    $check_stmt->execute();
    $expense_result = $check_stmt->get_result();
    
    if ($expense_result->num_rows === 0) {
        throw new Exception('المصروف غير موجود');
    }
    
    $expense = $expense_result->fetch_assoc();
    
    if ($expense['status'] !== 'pending') {
        throw new Exception('لا يمكن تعديل حالة هذا المصروف');
    }
    
    // تحديد الحالة الجديدة
    $new_status = ($action === 'approve') ? 'approved' : 'rejected';
    
    // تحديث حالة المصروف
    $update_query = "
        UPDATE daily_expenses 
        SET status = ?, 
            approved_by = ?, 
            approved_at = NOW(),
            updated_at = NOW()
        WHERE id = ?
    ";
    
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("sii", $new_status, $_SESSION['user_id'], $expense_id);
    
    if (!$update_stmt->execute()) {
        throw new Exception('فشل في تحديث حالة المصروف');
    }
    
    $conn->commit();
    
    // تسجيل النشاط
    $activity_action = ($action === 'approve') ? 'approve_expense' : 'reject_expense';
    log_activity($_SESSION['user_id'], $activity_action, 'daily_expenses', $expense_id, [
        'amount' => $expense['amount'],
        'description' => $expense['description'],
        'new_status' => $new_status
    ]);
    
    // إرسال الاستجابة
    $message = ($action === 'approve') ? 'تم اعتماد المصروف بنجاح' : 'تم رفض المصروف بنجاح';
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'new_status' => $new_status,
        'expense_id' => $expense_id
    ]);
    
} catch (Exception $e) {
    $conn->rollback();
    
    // تسجيل الخطأ
    log_error("Error in approve_expense.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
