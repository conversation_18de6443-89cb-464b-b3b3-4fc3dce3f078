<?php
/**
 * تعديل نوع الرسوم
 * Edit Fee Type
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// التحقق من وجود معرف نوع الرسوم
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$fee_type_id = intval($_GET['id']);

// جلب بيانات نوع الرسوم
$fee_type_stmt = $conn->prepare("
    SELECT ft.*,
           (SELECT COUNT(*) FROM student_fees sf WHERE sf.fee_type_id = ft.id) as fees_count,
           (SELECT COUNT(*) FROM student_installments si
            JOIN student_fees sf ON si.student_fee_id = sf.id
            WHERE sf.fee_type_id = ft.id) as installments_count
    FROM fee_types ft
    WHERE ft.id = ?
");
$fee_type_stmt->bind_param("i", $fee_type_id);
$fee_type_stmt->execute();
$fee_type_result = $fee_type_stmt->get_result();

if ($fee_type_result->num_rows === 0) {
    header('Location: index.php');
    exit();
}

$fee_type = $fee_type_result->fetch_assoc();
$total_usage = $fee_type['fees_count'] + $fee_type['installments_count'];

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صحيح';
    } else {
        $type_name = clean_input($_POST['type_name'] ?? '');
        $type_name_en = clean_input($_POST['type_name_en'] ?? '');
        $description = clean_input($_POST['description'] ?? '');
        $default_amount = floatval($_POST['default_amount'] ?? 0);
        $is_mandatory = isset($_POST['is_mandatory']) ? 1 : 0;
        $status = clean_input($_POST['status'] ?? 'active');
        
        // التحقق من صحة البيانات
        if (empty($type_name)) {
            $error_message = 'يجب إدخال اسم نوع الرسوم';
        } else {
            // التحقق من عدم تكرار الاسم (باستثناء النوع الحالي)
            $check_stmt = $conn->prepare("SELECT id FROM fee_types WHERE type_name = ? AND id != ?");
            $check_stmt->bind_param("si", $type_name, $fee_type_id);
            $check_stmt->execute();
            $existing = $check_stmt->get_result()->fetch_assoc();
            
            if ($existing) {
                $error_message = 'اسم نوع الرسوم موجود مسبقاً';
            } else {
                // تحديث نوع الرسوم
                $update_stmt = $conn->prepare("
                    UPDATE fee_types SET 
                        type_name = ?, type_name_en = ?, description = ?, default_amount = ?, 
                        is_mandatory = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                
                $update_stmt->bind_param("sssdisi", 
                    $type_name, $type_name_en, $description, $default_amount,
                    $is_mandatory, $status, $fee_type_id
                );
                
                if ($update_stmt->execute()) {
                    $success_message = 'تم تحديث نوع الرسوم بنجاح';
                    // تحديث البيانات المعروضة
                    $fee_type['type_name'] = $type_name;
                    $fee_type['type_name_en'] = $type_name_en;
                    $fee_type['description'] = $description;
                    $fee_type['default_amount'] = $default_amount;
                    $fee_type['is_mandatory'] = $is_mandatory;
                    $fee_type['status'] = $status;
                } else {
                    $error_message = 'خطأ في تحديث نوع الرسوم: ' . $conn->error;
                }
            }
        }
    }
}

$page_title = 'تعديل نوع الرسوم';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2"></i>تعديل نوع الرسوم
            </h1>
            <p class="text-muted mb-0">تعديل بيانات نوع الرسوم: <?php echo htmlspecialchars($fee_type['type_name']); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- تنبيه الاستخدام -->
    <?php if ($total_usage > 0): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>تنبيه:</strong> هذا النوع مستخدم في <?php echo $total_usage; ?> سجل 
        (<?php echo $fee_type['fees_count']; ?> رسم، <?php echo $fee_type['installments_count']; ?> قسط).
        التعديلات ستؤثر على جميع السجلات المرتبطة.
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج التعديل -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tag me-2"></i>بيانات نوع الرسوم
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="row">
                            <!-- اسم النوع بالعربية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم نوع الرسوم <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="type_name" 
                                           value="<?php echo htmlspecialchars($fee_type['type_name']); ?>" 
                                           required placeholder="مثال: الرسوم الدراسية">
                                    <small class="text-muted">اسم نوع الرسوم باللغة العربية</small>
                                </div>
                            </div>
                            
                            <!-- اسم النوع بالإنجليزية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم النوع بالإنجليزية</label>
                                    <input type="text" class="form-control" name="type_name_en" 
                                           value="<?php echo htmlspecialchars($fee_type['type_name_en'] ?? ''); ?>" 
                                           placeholder="Example: Tuition Fees">
                                    <small class="text-muted">اسم نوع الرسوم باللغة الإنجليزية (اختياري)</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الوصف -->
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" name="description" rows="3" 
                                      placeholder="وصف تفصيلي لنوع الرسوم..."><?php echo htmlspecialchars($fee_type['description'] ?? ''); ?></textarea>
                            <small class="text-muted">وصف تفصيلي لنوع الرسوم (اختياري)</small>
                        </div>
                        
                        <div class="row">
                            <!-- المبلغ الافتراضي -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ الافتراضي</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="default_amount" 
                                               min="0" step="0.01" 
                                               value="<?php echo $fee_type['default_amount']; ?>" 
                                               placeholder="0.00">
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                    <small class="text-muted">المبلغ الافتراضي لهذا النوع (اختياري)</small>
                                </div>
                            </div>
                            
                            <!-- الحالة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" name="status">
                                        <option value="active" <?php if($fee_type['status'] == 'active') echo 'selected'; ?>>
                                            نشط
                                        </option>
                                        <option value="inactive" <?php if($fee_type['status'] == 'inactive') echo 'selected'; ?>>
                                            غير نشط
                                        </option>
                                    </select>
                                    <small class="text-muted">حالة نوع الرسوم في النظام</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- خيارات إضافية -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_mandatory" 
                                       id="is_mandatory" <?php if($fee_type['is_mandatory']) echo 'checked'; ?>>
                                <label class="form-check-label" for="is_mandatory">
                                    رسوم إجبارية
                                </label>
                                <small class="text-muted d-block">هل هذا النوع من الرسوم إجباري للطلاب؟</small>
                            </div>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <!-- إحصائيات الاستخدام -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات الاستخدام
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-0"><?php echo $fee_type['fees_count']; ?></h4>
                                <small class="text-muted">رسوم</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info mb-0"><?php echo $fee_type['installments_count']; ?></h4>
                            <small class="text-muted">أقساط</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <h5 class="text-success mb-0"><?php echo $total_usage; ?></h5>
                        <small class="text-muted">إجمالي الاستخدامات</small>
                    </div>
                </div>
            </div>
            
            <!-- معلومات النوع -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات النوع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">تاريخ الإنشاء:</small>
                        <div><?php echo date('Y-m-d H:i', strtotime($fee_type['created_at'])); ?></div>
                    </div>
                    
                    <?php if ($fee_type['updated_at']): ?>
                    <div class="mb-2">
                        <small class="text-muted">آخر تحديث:</small>
                        <div><?php echo date('Y-m-d H:i', strtotime($fee_type['updated_at'])); ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-2">
                        <small class="text-muted">الحالة:</small>
                        <div>
                            <?php if ($fee_type['status'] == 'active'): ?>
                                <span class="badge bg-success">نشط</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">غير نشط</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div>
                        <small class="text-muted">نوع الرسوم:</small>
                        <div>
                            <?php if ($fee_type['is_mandatory']): ?>
                                <span class="badge bg-warning">إجباري</span>
                            <?php else: ?>
                                <span class="badge bg-info">اختياري</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
