<?php
/**
 * دوال خاصة بإدارة الرسوم
 */

/**
 * تحديث حالة الرسم بناءً على المدفوعات الفعلية
 */
function update_fee_status($fee_id, $conn) {
    try {
        // جلب بيانات الرسم
        $fee_stmt = $conn->prepare("
            SELECT id, final_amount, due_date, status 
            FROM student_fees 
            WHERE id = ?
        ");
        $fee_stmt->bind_param("i", $fee_id);
        $fee_stmt->execute();
        $fee = $fee_stmt->get_result()->fetch_assoc();
        
        if (!$fee) {
            return false;
        }
        
        // حساب إجمالي المدفوعات المؤكدة لهذا الرسم
        $payments_stmt = $conn->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_paid 
            FROM student_payments 
            WHERE student_fee_id = ? AND status = 'confirmed'
        ");
        $payments_stmt->bind_param("i", $fee_id);
        $payments_stmt->execute();
        $total_paid = $payments_stmt->get_result()->fetch_assoc()['total_paid'];
        
        // تحديد الحالة الجديدة
        $new_status = 'pending';
        $remaining_amount = $fee['final_amount'];
        
        if ($total_paid >= $fee['final_amount']) {
            // مدفوع بالكامل
            $new_status = 'paid';
            $remaining_amount = 0;
        } elseif ($total_paid > 0) {
            // مدفوع جزئياً (للأقساط فقط)
            $new_status = 'partial';
            $remaining_amount = $fee['final_amount'] - $total_paid;
        } else {
            // غير مدفوع - تحقق من تاريخ الاستحقاق
            if (strtotime($fee['due_date']) < time()) {
                $new_status = 'overdue';
            } else {
                $new_status = 'pending';
            }
            $remaining_amount = $fee['final_amount'];
        }
        
        // تحديث الرسم
        $update_stmt = $conn->prepare("
            UPDATE student_fees 
            SET status = ?, remaining_amount = ?, updated_at = NOW() 
            WHERE id = ?
        ");
        $update_stmt->bind_param("sdi", $new_status, $remaining_amount, $fee_id);
        
        return $update_stmt->execute();
        
    } catch (Exception $e) {
        error_log("Error updating fee status: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث حالة جميع الرسوم لطالب معين
 */
function update_student_fees_status($student_id, $conn) {
    try {
        // جلب جميع رسوم الطالب
        $fees_stmt = $conn->prepare("SELECT id FROM student_fees WHERE student_id = ?");
        $fees_stmt->bind_param("i", $student_id);
        $fees_stmt->execute();
        $fees = $fees_stmt->get_result();
        
        $updated_count = 0;
        while ($fee = $fees->fetch_assoc()) {
            if (update_fee_status($fee['id'], $conn)) {
                $updated_count++;
            }
        }
        
        return $updated_count;
        
    } catch (Exception $e) {
        error_log("Error updating student fees status: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث حالة جميع الرسوم في النظام
 */
function update_all_fees_status($conn) {
    try {
        // جلب جميع الرسوم
        $fees_stmt = $conn->prepare("SELECT id FROM student_fees");
        $fees_stmt->execute();
        $fees = $fees_stmt->get_result();
        
        $updated_count = 0;
        while ($fee = $fees->fetch_assoc()) {
            if (update_fee_status($fee['id'], $conn)) {
                $updated_count++;
            }
        }
        
        return $updated_count;
        
    } catch (Exception $e) {
        error_log("Error updating all fees status: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب الرسوم غير المدفوعة للطالب
 */
function get_unpaid_fees($student_id, $conn) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                sf.*,
                ft.type_name as fee_type_name,
                COALESCE(SUM(sp.amount), 0) as paid_amount,
                (sf.final_amount - COALESCE(SUM(sp.amount), 0)) as remaining_amount
            FROM student_fees sf
            JOIN fee_types ft ON sf.fee_type_id = ft.id
            LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
            WHERE sf.student_id = ? AND sf.status IN ('pending', 'overdue', 'partial')
            GROUP BY sf.id
            HAVING remaining_amount > 0
            ORDER BY sf.due_date ASC
        ");
        
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error getting unpaid fees: " . $e->getMessage());
        return [];
    }
}

/**
 * التحقق من حالة الرسم
 */
function check_fee_payment_status($fee_id, $conn) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                sf.final_amount,
                sf.status,
                COALESCE(SUM(sp.amount), 0) as total_paid
            FROM student_fees sf
            LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
            WHERE sf.id = ?
            GROUP BY sf.id
        ");
        
        $stmt->bind_param("i", $fee_id);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        if (!$result) {
            return null;
        }
        
        return [
            'fee_amount' => $result['final_amount'],
            'paid_amount' => $result['total_paid'],
            'remaining_amount' => $result['final_amount'] - $result['total_paid'],
            'current_status' => $result['status'],
            'is_fully_paid' => $result['total_paid'] >= $result['final_amount'],
            'has_payments' => $result['total_paid'] > 0
        ];
        
    } catch (Exception $e) {
        error_log("Error checking fee payment status: " . $e->getMessage());
        return null;
    }
}
?>
