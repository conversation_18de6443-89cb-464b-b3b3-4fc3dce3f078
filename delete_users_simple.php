<?php
/**
 * حذف مستخدمين محددين بطريقة مبسطة
 * Delete specific users in a simple way
 */

echo "<h1>🗑️ حذف المستخدمين المحددين (طريقة مبسطة)</h1>";

// الاتصال بقاعدة البيانات
$conn = new mysqli('localhost', 'root', '', 'school_management');

if ($conn->connect_error) {
    echo "<div class='alert alert-danger'>❌ فشل في الاتصال: " . $conn->connect_error . "</div>";
    exit;
}

$conn->set_charset("utf8mb4");
echo "<div class='alert alert-success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

// قائمة المستخدمين المطلوب حذفهم
$emails_to_delete = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
];

echo "<h3>📋 المستخدمين المطلوب حذفهم:</h3>";
echo "<ul>";
foreach ($emails_to_delete as $email) {
    echo "<li>$email</li>";
}
echo "</ul>";

try {
    // البحث عن المستخدمين
    echo "<h3>🔍 البحث عن المستخدمين:</h3>";
    
    $users_to_delete = [];
    foreach ($emails_to_delete as $email) {
        $result = $conn->query("SELECT id, username, email, full_name, role FROM users WHERE email = '$email'");
        
        if ($result && $result->num_rows > 0) {
            $user = $result->fetch_assoc();
            $users_to_delete[] = $user;
            echo "<p>✅ تم العثور على: <strong>" . $user['full_name'] . "</strong> (ID: " . $user['id'] . ")</p>";
        } else {
            echo "<p>⚠️ لم يتم العثور على: $email</p>";
        }
    }
    
    if (empty($users_to_delete)) {
        echo "<div class='alert alert-warning'>⚠️ لم يتم العثور على أي من المستخدمين</div>";
        exit;
    }
    
    // نموذج تأكيد الحذف
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
        echo "<h3>🗑️ بدء عملية الحذف:</h3>";
        
        // تعطيل فحص القيود الخارجية مؤقتاً
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");
        echo "<p>🔧 تم تعطيل فحص القيود الخارجية مؤقتاً</p>";
        
        $deleted_count = 0;
        
        foreach ($users_to_delete as $user) {
            $user_id = $user['id'];
            $user_name = $user['full_name'];
            
            echo "<h4>🔄 حذف المستخدم: $user_name (ID: $user_id)</h4>";
            
            // تنظيف جميع المراجع يدوياً
            echo "<p>🧹 تنظيف المراجع...</p>";
            
            // تنظيف student_grades
            $conn->query("UPDATE student_grades SET graded_by = NULL WHERE graded_by = $user_id");
            
            // تنظيف activity_logs
            $conn->query("UPDATE activity_logs SET user_id = NULL WHERE user_id = $user_id");
            
            // تنظيف approval_workflow
            $conn->query("UPDATE approval_workflow SET approver_id = NULL WHERE approver_id = $user_id");
            
            // تنظيف attendance
            $conn->query("UPDATE attendance SET marked_by = NULL WHERE marked_by = $user_id");
            
            // تنظيف backups
            $conn->query("UPDATE backups SET created_by = NULL WHERE created_by = $user_id");
            
            // تنظيف daily_expenses
            $conn->query("UPDATE daily_expenses SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE daily_expenses SET created_by = NULL WHERE created_by = $user_id");
            
            // تنظيف discounts
            $conn->query("UPDATE discounts SET created_by = NULL WHERE created_by = $user_id");
            
            // تنظيف error_logs
            $conn->query("UPDATE error_logs SET resolved_by = NULL WHERE resolved_by = $user_id");
            $conn->query("UPDATE error_logs SET user_id = NULL WHERE user_id = $user_id");
            
            // تنظيف expenses
            $conn->query("UPDATE expenses SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE expenses SET created_by = NULL WHERE created_by = $user_id");
            
            // تنظيف payments
            $conn->query("UPDATE payments SET processed_by = NULL WHERE processed_by = $user_id");
            
            // تنظيف staff_permissions
            $conn->query("UPDATE staff_permissions SET applied_by = NULL WHERE applied_by = $user_id");
            $conn->query("UPDATE staff_permissions SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE staff_permissions SET cancelled_by = NULL WHERE cancelled_by = $user_id");
            $conn->query("UPDATE staff_permissions SET rejected_by = NULL WHERE rejected_by = $user_id");
            $conn->query("UPDATE staff_permissions SET replacement_user_id = NULL WHERE replacement_user_id = $user_id");
            
            // تنظيف student_fees
            $conn->query("UPDATE student_fees SET created_by = NULL WHERE created_by = $user_id");
            
            // تنظيف student_payments
            $conn->query("UPDATE student_payments SET processed_by = NULL WHERE processed_by = $user_id");
            
            // تنظيف system_settings
            $conn->query("UPDATE system_settings SET updated_by = NULL WHERE updated_by = $user_id");
            
            // تنظيف teacher_attendance
            $conn->query("UPDATE teacher_attendance SET recorded_by = NULL WHERE recorded_by = $user_id");
            
            // تنظيف uploaded_files
            $conn->query("UPDATE uploaded_files SET uploaded_by = NULL WHERE uploaded_by = $user_id");
            
            // تنظيف unified_staff_absences
            $conn->query("UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by = $user_id");
            $conn->query("UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by = $user_id");
            $conn->query("UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id = $user_id");
            
            // تنظيف staff_absences_with_deduction
            $conn->query("UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by = $user_id");
            $conn->query("UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by = $user_id");
            
            // تنظيف staff_leaves
            $conn->query("UPDATE staff_leaves SET applied_by = NULL WHERE applied_by = $user_id");
            $conn->query("UPDATE staff_leaves SET approved_by = NULL WHERE approved_by = $user_id");
            $conn->query("UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by = $user_id");
            $conn->query("UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by = $user_id");
            $conn->query("UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id = $user_id");
            
            // تنظيف staff_attendance
            $conn->query("UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by = $user_id");
            $conn->query("UPDATE staff_attendance SET approved_by = NULL WHERE approved_by = $user_id");
            
            // تنظيف admin_attendance
            $conn->query("UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by = $user_id");
            
            echo "<p>✅ تم تنظيف جميع المراجع</p>";
            
            // حذف السجلات المرتبطة مباشرة
            echo "<p>🗑️ حذف السجلات المرتبطة...</p>";
            
            $conn->query("DELETE FROM employee_leave_balances WHERE user_id = $user_id");
            $conn->query("DELETE FROM leave_notifications WHERE user_id = $user_id");
            $conn->query("DELETE FROM notifications WHERE user_id = $user_id");
            $conn->query("DELETE FROM remember_tokens WHERE user_id = $user_id");
            $conn->query("DELETE FROM staff WHERE user_id = $user_id");
            $conn->query("DELETE FROM staff_permissions WHERE user_id = $user_id");
            $conn->query("DELETE FROM students WHERE user_id = $user_id");
            $conn->query("DELETE FROM teachers WHERE user_id = $user_id");
            $conn->query("DELETE FROM unified_staff_absences WHERE user_id = $user_id");
            $conn->query("DELETE FROM staff_absences_with_deduction WHERE user_id = $user_id");
            $conn->query("DELETE FROM staff_leaves WHERE user_id = $user_id");
            $conn->query("DELETE FROM staff_attendance WHERE user_id = $user_id");
            $conn->query("DELETE FROM admin_attendance WHERE admin_id = $user_id");
            
            echo "<p>✅ تم حذف السجلات المرتبطة</p>";
            
            // حذف المستخدم نفسه
            $delete_result = $conn->query("DELETE FROM users WHERE id = $user_id");
            
            if ($delete_result) {
                echo "<p>✅ تم حذف المستخدم بنجاح</p>";
                $deleted_count++;
            } else {
                echo "<p>❌ فشل في حذف المستخدم: " . $conn->error . "</p>";
            }
            
            echo "<hr>";
        }
        
        // إعادة تفعيل فحص القيود الخارجية
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        echo "<p>🔧 تم إعادة تفعيل فحص القيود الخارجية</p>";
        
        echo "<div class='alert alert-success'>";
        echo "<h4>🎉 تم الانتهاء من عملية الحذف!</h4>";
        echo "<p><strong>تم حذف $deleted_count مستخدم بنجاح</strong></p>";
        echo "</div>";
        
        // التحقق النهائي
        echo "<h3>🔍 التحقق النهائي:</h3>";
        
        foreach ($emails_to_delete as $email) {
            $check_result = $conn->query("SELECT COUNT(*) as count FROM users WHERE email = '$email'");
            $count = $check_result->fetch_assoc()['count'];
            
            if ($count == 0) {
                echo "<p>✅ تم حذف المستخدم: $email</p>";
            } else {
                echo "<p>❌ المستخدم لا يزال موجوداً: $email</p>";
            }
        }
        
    } else {
        // عرض نموذج التأكيد
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ تأكيد الحذف</h4>";
        echo "<p>هل أنت متأكد من حذف هؤلاء المستخدمين؟</p>";
        echo "<p><strong>سيتم تعطيل فحص القيود الخارجية مؤقتاً لضمان نجاح العملية</strong></p>";
        echo "<p><strong>هذا الإجراء لا يمكن التراجع عنه!</strong></p>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<div class='form-check'>";
        echo "<input type='checkbox' class='form-check-input' id='confirm_checkbox' required>";
        echo "<label class='form-check-label' for='confirm_checkbox'>";
        echo "أؤكد أنني أريد حذف هؤلاء المستخدمين نهائياً";
        echo "</label>";
        echo "</div>";
        echo "<br>";
        echo "<button type='submit' name='confirm_delete' class='btn btn-danger'>🗑️ تأكيد الحذف النهائي</button>";
        echo "<a href='" . $_SERVER['PHP_SELF'] . "' class='btn btn-secondary'>❌ إلغاء</a>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ في العملية</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

$conn->close();

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
.alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
.alert-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
.btn { padding: 0.375rem 0.75rem; margin: 0.125rem; border: none; border-radius: 0.375rem; cursor: pointer; text-decoration: none; display: inline-block; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.form-check { margin: 10px 0; }
.form-check-input { margin-left: 0.5rem; }
h3 { color: #333; margin-top: 30px; }
h4 { color: #666; margin-top: 20px; }
hr { margin: 20px 0; border: 1px solid #ddd; }
</style>
