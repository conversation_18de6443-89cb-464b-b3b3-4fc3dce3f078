<?php
/**
 * صفحة المواد الدراسية - متوافقة مع هيكل قاعدة البيانات
 */

require_once "../includes/config.php";
require_once "../includes/functions.php";
require_once "../includes/database.php";
require_once "../includes/security.php";

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission("admin")) {
    header("Location: ../dashboard/");
    exit();
}

// معالجة الفلاتر
$stage_filter = isset($_GET["stage"]) ? intval($_GET["stage"]) : 0;
$grade_filter = isset($_GET["grade"]) ? intval($_GET["grade"]) : 0;
$department_filter = isset($_GET["department"]) ? trim($_GET["department"]) : "";
$status_filter = isset($_GET["status"]) ? trim($_GET["status"]) : "";
$search_query = isset($_GET["search"]) ? trim($_GET["search"]) : "";

// بناء استعلام المواد مع الفلاتر
$where_conditions = [];
$params = [];
$param_types = "";

if ($stage_filter > 0) {
    $where_conditions[] = "s.stage_id = ?";
    $params[] = $stage_filter;
    $param_types .= "i";
}

if ($grade_filter > 0) {
    $where_conditions[] = "s.grade_id = ?";
    $params[] = $grade_filter;
    $param_types .= "i";
}

if (!empty($department_filter)) {
    $where_conditions[] = "s.department = ?";
    $params[] = $department_filter;
    $param_types .= "s";
}

if (!empty($status_filter)) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
    $param_types .= "s";
}

if (!empty($search_query)) {
    $where_conditions[] = "(s.subject_name LIKE ? OR s.subject_code LIKE ? OR s.description LIKE ?)";
    $search_param = "%{$search_query}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= "sss";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// استعلام المواد
$subjects_query = "
    SELECT
        s.*,
        es.stage_name,
        g.grade_name,
        COUNT(DISTINCT ta.teacher_id) as teacher_count,
        COUNT(DISTINCT st.id) as student_count
    FROM subjects s
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN teacher_assignments ta ON s.id = ta.subject_id
    LEFT JOIN students st ON s.grade_id = st.class_id
    {$where_clause}
    GROUP BY s.id
    ORDER BY s.subject_name
";

$subjects_stmt = $conn->prepare($subjects_query);
if (!empty($params)) {
    $subjects_stmt->bind_param($param_types, ...$params);
}
$subjects_stmt->execute();
$subjects_result = $subjects_stmt->get_result();
$subjects = $subjects_result->fetch_all(MYSQLI_ASSOC);

// جلب الإحصائيات
$stats_query = "
    SELECT
        COUNT(*) as total_subjects,
        COUNT(CASE WHEN status = \"active\" THEN 1 END) as active_subjects,
        COUNT(CASE WHEN status = \"inactive\" THEN 1 END) as inactive_subjects,
        COUNT(DISTINCT department) as total_departments
    FROM subjects
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// جلب البيانات للفلاتر
$stages = [];
$grades = [];
$departments = [];

if (in_array("educational_stages", ["academic_years", "activity_logs", "admin_attendance", "attendance", "backups", "bank_accounts", "books", "classes", "daily_expenses", "deduction_settings", "discounts", "educational_stages", "error_logs", "exam_attempts", "exam_questions", "exam_results", "exams", "expense_categories", "expenses", "fee_categories", "fee_structures", "fee_type_classes", "fee_types", "grades", "installment_payments", "installment_plans", "installments", "leave_settings", "leave_types", "login_attempts", "notifications", "payments", "permission_types", "remember_tokens", "staff", "staff_absence_deductions", "staff_absences_with_deduction", "staff_attendance", "staff_leaves", "student_book_orders", "student_fees", "student_grades", "student_installments", "student_payments", "students", "subjects", "system_settings", "teacher_assignments", "teacher_attendance", "teachers", "uploaded_files", "user_sessions", "users"])) {
    $stages_result = $conn->query("SELECT id, stage_name FROM educational_stages WHERE status = \"active\" ORDER BY sort_order");
    $stages = $stages_result->fetch_all(MYSQLI_ASSOC);
}

if (in_array("grades", ["academic_years", "activity_logs", "admin_attendance", "attendance", "backups", "bank_accounts", "books", "classes", "daily_expenses", "deduction_settings", "discounts", "educational_stages", "error_logs", "exam_attempts", "exam_questions", "exam_results", "exams", "expense_categories", "expenses", "fee_categories", "fee_structures", "fee_type_classes", "fee_types", "grades", "installment_payments", "installment_plans", "installments", "leave_settings", "leave_types", "login_attempts", "notifications", "payments", "permission_types", "remember_tokens", "staff", "staff_absence_deductions", "staff_absences_with_deduction", "staff_attendance", "staff_leaves", "student_book_orders", "student_fees", "student_grades", "student_installments", "student_payments", "students", "subjects", "system_settings", "teacher_assignments", "teacher_attendance", "teachers", "uploaded_files", "user_sessions", "users"])) {
    $grades_result = $conn->query("SELECT g.id, g.grade_name, g.stage_id FROM grades g WHERE g.status = \"active\" ORDER BY g.sort_order");
    $grades = $grades_result->fetch_all(MYSQLI_ASSOC);
}

$departments_result = $conn->query("SELECT DISTINCT department FROM subjects WHERE department IS NOT NULL AND department != \"\" ORDER BY department");
$departments = $departments_result->fetch_all(MYSQLI_ASSOC);

$page_title = "المواد الدراسية";
include_once "../includes/header.php";
?>

<!-- SweetAlert2 للحذف -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-book-open me-2 text-primary"></i>المواد الدراسية
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item active">المواد الدراسية</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مادة
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION["success_message"])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION["success_message"]; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION["success_message"]); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION["error_message"])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION["error_message"]; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION["error_message"]); ?>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المواد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats["total_subjects"]); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المواد النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats["active_subjects"]); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الأقسام
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats["total_departments"]); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>