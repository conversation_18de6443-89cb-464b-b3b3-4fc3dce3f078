<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة إضافة الرسم
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات وتنظيفها
        $student_id = intval($_POST['student_id'] ?? 0);
        $fee_type_id = intval($_POST['fee_type_id'] ?? 0);
        $academic_year = clean_input($_POST['academic_year'] ?? '');
        $semester = clean_input($_POST['semester'] ?? '');
        $amount = floatval($_POST['amount'] ?? 0);
        $discount_amount = floatval($_POST['discount_amount'] ?? 0);
        $discount_percentage = floatval($_POST['discount_percentage'] ?? 0);
        $discount_reason = clean_input($_POST['discount_reason'] ?? '');
        $due_date = clean_input($_POST['due_date'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        
        // التحقق من صحة البيانات
        if ($student_id <= 0) {
            $error_message = __('invalid_student');
        } elseif ($fee_type_id <= 0) {
            $error_message = __('invalid_fee_type');
        } elseif ($amount <= 0) {
            $error_message = __('invalid_amount');
        } elseif (empty($due_date)) {
            $error_message = __('invalid_due_date');
        } else {
            try {
                // بدء المعاملة
                $conn->begin_transaction();

                // جلب اسم نوع الرسم
                $fee_type_stmt = $conn->prepare("SELECT type_name FROM fee_types WHERE id = ?");
                $fee_type_stmt->bind_param("i", $fee_type_id);
                $fee_type_stmt->execute();
                $fee_type_name = $fee_type_stmt->get_result()->fetch_assoc()['type_name'] ?? 'رسم عام';
                
                // حساب المبلغ النهائي
                $discount = 0;
                if ($discount_percentage > 0) {
                    $discount = ($amount * $discount_percentage) / 100;
                } elseif ($discount_amount > 0) {
                    $discount = $discount_amount;
                }
                $final_amount = $amount - $discount;
                
                // التحقق من عدم تكرار الرسم (تبسيط للتحقق من الطالب ونوع الرسم فقط)
                $check_stmt = $conn->prepare("SELECT id FROM student_fees WHERE student_id = ? AND fee_type_id = ?");
                $check_stmt->bind_param("ii", $student_id, $fee_type_id);
                $check_stmt->execute();
                
                if ($check_stmt->get_result()->num_rows > 0) {
                    throw new Exception(__('fee_already_exists'));
                }
                
                // إنشاء fee_structure افتراضي إذا لم يكن موجوداً
                $fee_structure_stmt = $conn->prepare("INSERT IGNORE INTO fee_structures (id, fee_category_id, academic_year_id, amount, status) VALUES (1, 1, 1, 0, 'active')");
                $fee_structure_stmt->execute();

                // إضافة الرسم الجديد (استخدام الأعمدة الصحيحة)
                $fee_structure_id = 1; // قيمة افتراضية
                $academic_year_id = 1; // قيمة افتراضية

                $stmt = $conn->prepare("INSERT INTO student_fees (student_id, fee_type_id, fee_structure_id, academic_year_id, semester, base_amount, discount_amount, final_amount, remaining_amount, due_date, status, notes, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 'paid', ?, NOW())");

                if ($stmt) {
                    $stmt->bind_param("iiiisdddsss", $student_id, $fee_type_id, $fee_structure_id, $academic_year_id, $semester, $amount, $discount, $final_amount, $due_date, $notes);
                    
                    if ($stmt->execute()) {
                        $fee_id = $conn->insert_id;

                        // إنشاء سجل دفعة تلقائياً للرسم
                        $payment_reference = 'FEE-' . date('YmdHis') . '-' . str_pad($fee_id, 6, '0', STR_PAD_LEFT);
                        $receipt_number = 'REC-' . date('YmdHis') . '-' . str_pad($fee_id, 6, '0', STR_PAD_LEFT);

                        $payment_stmt = $conn->prepare("
                            INSERT INTO student_payments (
                                student_id, student_fee_id, payment_reference, amount, payment_method,
                                payment_date, receipt_number, notes, status, processed_by, processed_at, created_at
                            ) VALUES (?, ?, ?, ?, 'cash', ?, ?, ?, 'confirmed', ?, NOW(), NOW())
                        ");

                        $payment_notes = __('fee_payment_auto') . ' - ' . $fee_type_name;
                        $payment_stmt->bind_param("iisdsssi",
                            $student_id, $fee_id, $payment_reference, $final_amount,
                            $due_date, $receipt_number, $payment_notes, $_SESSION['user_id']
                        );

                        if (!$payment_stmt->execute()) {
                            throw new Exception(__('failed_to_create_payment_record') . ': ' . $conn->error);
                        }

                        $conn->commit();
                        header("Location: index.php?success=1");
                        exit();
                    } else {
                        throw new Exception(__('database_error') . ': ' . $conn->error);
                    }
                } else {
                    throw new Exception(__('database_error') . ': ' . $conn->error);
                }
                
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = $e->getMessage();
            }
        }
    }
}

// جلب قائمة الطلاب
$students = $conn->query("
    SELECT s.id, u.full_name, s.student_id as student_number, c.class_name, c.grade_level
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
");

// جلب أنواع الرسوم
$fee_types = $conn->query("SELECT * FROM fee_types ORDER BY type_name");

$page_title = __('add_student_fee');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-plus me-2"></i><?php echo __('add_student_fee'); ?></h4>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- اختيار الطالب -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-user-graduate me-2"></i><?php echo __('student_information'); ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('student'); ?> <span class="text-danger">*</span></label>
                                    <select class="form-select" name="student_id" id="student_id" required>
                                        <option value=""><?php echo __('choose_student'); ?></option>
                                        <?php while ($student = $students->fetch_assoc()): ?>
                                            <option value="<?php echo $student['id']; ?>" <?php if(isset($_POST['student_id']) && $_POST['student_id'] == $student['id']) echo 'selected'; ?>>
                                                <?php echo htmlspecialchars($student['full_name']); ?> 
                                                (<?php echo htmlspecialchars($student['student_number']); ?>) 
                                                <?php if (!empty($student['class_name'])): ?>
                                                    - <?php echo htmlspecialchars($student['class_name']); ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تفاصيل الرسم -->
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('fee_details'); ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('fee_type'); ?> <span class="text-danger">*</span></label>
                                            <select class="form-select" name="fee_type_id" required>
                                                <option value=""><?php echo __('choose_fee_type'); ?></option>
                                                <?php 
                                                $fee_types->data_seek(0); // إعادة تعيين المؤشر
                                                while ($fee_type = $fee_types->fetch_assoc()): 
                                                ?>
                                                    <option value="<?php echo $fee_type['id']; ?>" <?php if(isset($_POST['fee_type_id']) && $_POST['fee_type_id'] == $fee_type['id']) echo 'selected'; ?>>
                                                        <?php echo htmlspecialchars($fee_type['type_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('amount'); ?> <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="amount" id="amount" min="0" step="0.01" value="<?php echo $_POST['amount'] ?? ''; ?>" required onchange="calculateFinal()">
                                                <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('academic_year'); ?> <span class="text-danger">*</span></label>
                                            <select class="form-select" name="academic_year" required>
                                                <option value="2024-2025" <?php if(isset($_POST['academic_year']) && $_POST['academic_year'] == '2024-2025') echo 'selected'; ?>>2024-2025</option>
                                                <option value="2025-2026" <?php if(isset($_POST['academic_year']) && $_POST['academic_year'] == '2025-2026') echo 'selected'; ?>>2025-2026</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('semester'); ?> <span class="text-danger">*</span></label>
                                            <select class="form-select" name="semester" required>
                                                <option value="first" <?php if(isset($_POST['semester']) && $_POST['semester'] == 'first') echo 'selected'; ?>><?php echo __('first_semester'); ?></option>
                                                <option value="second" <?php if(isset($_POST['semester']) && $_POST['semester'] == 'second') echo 'selected'; ?>><?php echo __('second_semester'); ?></option>
                                                <option value="annual" <?php if(isset($_POST['semester']) && $_POST['semester'] == 'annual') echo 'selected'; ?>><?php echo __('annual'); ?></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('due_date'); ?> <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" name="due_date" value="<?php echo $_POST['due_date'] ?? ''; ?>" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الخصم -->
                        <div class="card border-warning mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-percentage me-2"></i><?php echo __('discount_optional'); ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('discount_percentage'); ?></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="discount_percentage" id="discount_percentage" min="0" max="100" step="0.01" value="<?php echo $_POST['discount_percentage'] ?? ''; ?>" onchange="calculateFinal()">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('discount_amount'); ?></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="discount_amount" id="discount_amount" min="0" step="0.01" value="<?php echo $_POST['discount_amount'] ?? ''; ?>" onchange="calculateFinal()">
                                                <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('discount_reason'); ?></label>
                                            <input type="text" class="form-control" name="discount_reason" value="<?php echo $_POST['discount_reason'] ?? ''; ?>" placeholder="<?php echo __('optional'); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- عرض المبلغ النهائي -->
                                <div class="alert alert-info" id="final_amount_alert">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><strong><?php echo __('final_amount'); ?>:</strong></span>
                                        <span class="fs-5 fw-bold" id="final_amount">0.00 <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- ملاحظات -->
                        <div class="mb-4">
                            <label class="form-label"><?php echo __('notes'); ?></label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="<?php echo __('notes_placeholder'); ?>"><?php echo $_POST['notes'] ?? ''; ?></textarea>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i><?php echo __('add_fee'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculateFinal() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const discountPercentage = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    
    let discount = 0;
    if (discountPercentage > 0) {
        discount = (amount * discountPercentage) / 100;
        // مسح حقل المبلغ إذا تم إدخال نسبة
        document.getElementById('discount_amount').value = '';
    } else if (discountAmount > 0) {
        discount = discountAmount;
        // مسح حقل النسبة إذا تم إدخال مبلغ
        document.getElementById('discount_percentage').value = '';
    }
    
    const finalAmount = amount - discount;
    const finalAmountSpan = document.getElementById('final_amount');
    
    finalAmountSpan.textContent = finalAmount.toFixed(2) + ' <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>';
    
    // تغيير لون التنبيه
    const alert = document.getElementById('final_amount_alert');
    alert.className = 'alert ';
    if (discount > 0) {
        alert.className += 'alert-success';
    } else {
        alert.className += 'alert-info';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    calculateFinal();
});
</script>

<?php include_once '../../includes/footer.php'; ?>
