<?php
/**
 * نظام تسجيل الحضور الذكي للمعلمين والإداريين
 * Smart Attendance System for Teachers and Admins
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات - فقط الإداريين يمكنهم الوصول
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الإجازات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['leave_action'])) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $leave_type = clean_input($_POST['leave_action']);
        $staff_id = intval($_POST['staff_id']);
        $start_date = clean_input($_POST['start_date']);
        $end_date = clean_input($_POST['end_date']);
        $reason = clean_input($_POST['reason'] ?? '');

        if (empty($staff_id) || empty($start_date) || empty($end_date)) {
            $error_message = 'جميع الحقول مطلوبة';
        } elseif ($start_date > $end_date) {
            $error_message = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
        } else {
            try {
                $conn->begin_transaction();

                // حساب عدد الأيام
                $start = new DateTime($start_date);
                $end = new DateTime($end_date);
                $interval = $start->diff($end);
                $total_days = $interval->days + 1;

                // إدراج الإجازة في جدول staff_leaves
                $leave_stmt = $conn->prepare("
                    INSERT INTO staff_leaves (user_id, leave_type, start_date, end_date, total_days, reason, status, applied_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 'approved', ?, NOW())
                ");
                $leave_stmt->bind_param("isssssi", $staff_id, $leave_type, $start_date, $end_date, $total_days, $reason, $user_id);
                $leave_stmt->execute();

                $leave_id = $conn->insert_id;

                // تحديث سجلات الحضور الموجودة في فترة الإجازة
                $update_attendance_stmt = $conn->prepare("
                    UPDATE attendance
                    SET status = ?,
                        check_in_time = NULL,
                        check_out_time = NULL,
                        notes = CONCAT(COALESCE(notes, ''), IF(COALESCE(notes, '') = '', '', ' - '), ?)
                    WHERE user_id = ?
                    AND attendance_date BETWEEN ? AND ?
                ");

                $leave_status = $leave_type . '_leave';
                $leave_note = 'تم تحديث الحالة إلى ' . __($leave_type . '_leave');
                $update_attendance_stmt->bind_param("ssiss", $leave_status, $leave_note, $staff_id, $start_date, $end_date);
                $update_attendance_stmt->execute();

                $updated_records = $update_attendance_stmt->affected_rows;

                $conn->commit();

                // تسجيل النشاط
                log_activity($user_id, 'register_staff_leave', 'staff_leaves', $conn->insert_id, null, [
                    'staff_id' => $staff_id,
                    'leave_type' => $leave_type,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'total_days' => $total_days
                ]);

                $success_message = 'تم تسجيل الإجازة بنجاح لعدد ' . $total_days . ' أيام';

            } catch (Exception $e) {
                $conn->rollback();
                $error_message = 'حدث خطأ: ' . $e->getMessage();
            }
        }
    }
}

// معالجة حفظ الحضور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_attendance'])) {
    error_log("DEBUG: Processing save_attendance request");
    error_log("DEBUG: CSRF token: " . ($_POST['csrf_token'] ?? 'missing'));

    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
        error_log("DEBUG: CSRF token verification failed");
    } else {
        error_log("DEBUG: CSRF token verified successfully");
        $attendance_date = clean_input($_POST['attendance_date'] ?? '');
        $attendance_data = $_POST['attendance'] ?? [];
        
        if (empty($attendance_date)) {
            $error_message = __('attendance_date_required');
        } else {
            $conn->begin_transaction();
            
            try {
                $updated_count = 0;
                $new_count = 0;
                
                error_log("DEBUG: Processing " . count($attendance_data) . " attendance records");

                foreach ($attendance_data as $user_id_key => $data) {
                    $target_user_id = intval($user_id_key);
                    $status = clean_input($data['status'] ?? 'absent');
                    $check_in_time = clean_input($data['check_in_time'] ?? null);
                    $check_out_time = clean_input($data['check_out_time'] ?? null);
                    $notes = clean_input($data['notes'] ?? null);
                    $user_type = clean_input($data['user_type'] ?? 'teacher');

                    error_log("DEBUG: Processing user $target_user_id with initial status: $status");

                    // التحقق من الحقل المخفي للغياب بالخصم أو الحالة المباشرة
                    if ((isset($data['force_absent_with_deduction']) && $data['force_absent_with_deduction'] == '1') ||
                        $status === 'absent_with_deduction') {
                        $status = 'absent_with_deduction';
                        $check_in_time = null;
                        $check_out_time = null;
                        $notes = null;

                        // تسجيل debug
                        error_log("DEBUG: Absent with deduction applied for user $target_user_id on $attendance_date");
                        error_log("DEBUG: Hidden field: " . (isset($data['force_absent_with_deduction']) ? $data['force_absent_with_deduction'] : 'not set'));
                    }

                    // تسجيل debug للحالة النهائية
                    error_log("DEBUG: Final status for user $target_user_id: $status");

                    // التحقق من أن الموظف ليس في إجازة (للمعلمين والإداريين فقط)
                    if ($user_type !== 'student') {
                        $leave_check_stmt = $conn->prepare("
                            SELECT id FROM staff_leaves
                            WHERE user_id = ?
                            AND status = 'approved'
                            AND ? BETWEEN start_date AND end_date
                        ");
                        $leave_check_stmt->bind_param("is", $target_user_id, $attendance_date);
                        $leave_check_stmt->execute();
                        $leave_result = $leave_check_stmt->get_result();

                        if ($leave_result && $leave_result->num_rows > 0) {
                            // تخطي الموظفين في إجازة
                            continue;
                        }
                    }

                    // تحديد الجدول والحقول المناسبة
                    if ($user_type === 'student') {
                        // للطلاب: استخدام جدول attendance العادي
                        $student_query = $conn->prepare("SELECT id, class_id FROM students WHERE user_id = ?");
                        $student_query->bind_param("i", $target_user_id);
                        $student_query->execute();
                        $student_result = $student_query->get_result()->fetch_assoc();
                        if (!$student_result) {
                            continue; // تخطي إذا لم يتم العثور على الطالب
                        }

                        $student_id = $student_result['id'];
                        $class_id = $student_result['class_id'];

                        // جلب معرف السنة الأكاديمية الحالية
                        $current_year = get_current_academic_year();
                        $year_stmt = $conn->prepare("SELECT id FROM academic_years WHERE year_name = ? OR is_current = 1 LIMIT 1");
                        $year_stmt->bind_param("s", $current_year);
                        $year_stmt->execute();
                        $year_result = $year_stmt->get_result();

                        if ($year_result && $year_result->num_rows > 0) {
                            $academic_year_id = $year_result->fetch_assoc()['id'];
                        } else {
                            // استخدام السنة الحالية كرقم إذا لم توجد في الجدول
                            $academic_year_id = date('Y');
                        }

                        // التحقق من وجود سجل مسبق للطالب
                        $check_stmt = $conn->prepare("SELECT id FROM attendance WHERE student_id = ? AND attendance_date = ? AND class_id = ?");
                        $check_stmt->bind_param("isi", $student_id, $attendance_date, $class_id);
                        $check_stmt->execute();
                        $existing = $check_stmt->get_result()->fetch_assoc();

                        if ($existing) {
                            // تحديث السجل الموجود
                            $update_stmt = $conn->prepare("
                                UPDATE attendance
                                SET status = ?, notes = ?
                                WHERE student_id = ? AND attendance_date = ? AND class_id = ?
                            ");
                            $update_stmt->bind_param("ssisi", $status, $notes, $student_id, $attendance_date, $class_id);
                            $update_stmt->execute();
                            $updated_count++;
                        } else {
                            // إنشاء سجل جديد
                            $insert_stmt = $conn->prepare("
                                INSERT INTO attendance (student_id, class_id, academic_year_id, attendance_date, status, notes)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ");
                            $insert_stmt->bind_param("iiisss", $student_id, $class_id, $academic_year_id, $attendance_date, $status, $notes);
                            $insert_stmt->execute();
                            $new_count++;
                        }

                    } else {
                        // للمعلمين والموظفين - استخدام جدول staff_attendance الموحد

                        // التحقق من وجود سجل مسبق في staff_attendance
                        $check_stmt = $conn->prepare("SELECT id FROM staff_attendance WHERE user_id = ? AND attendance_date = ?");
                        $check_stmt->bind_param("is", $target_user_id, $attendance_date);
                        $check_stmt->execute();
                        $existing = $check_stmt->get_result()->fetch_assoc();

                        // معالجة خاصة للغياب بالخصم
                        if ($status === 'absent_with_deduction') {
                            // للغياب بالخصم: قفل الأوقات وإفراغ الملاحظات
                            $check_in_time = null;
                            $check_out_time = null;
                            $notes = null;

                            // تسجيل debug
                            error_log("Processing absent_with_deduction for user_id: $target_user_id on date: $attendance_date");

                            // التأكد من وجود سجل في staff_absences_with_deduction
                            $deduction_check = $conn->prepare("SELECT id, deduction_amount FROM staff_absences_with_deduction WHERE user_id = ? AND absence_date = ?");
                            $deduction_check->bind_param("is", $target_user_id, $attendance_date);
                            $deduction_check->execute();
                            $deduction_result = $deduction_check->get_result();
                            $deduction_exists = $deduction_result->fetch_assoc();

                            if (!$deduction_exists) {
                                // جلب إعدادات الخصم الافتراضية
                                $default_amount = 100.00; // قيمة افتراضية
                                $default_type = 'daily_wage';

                                // التحقق من وجود جدول الإعدادات
                                $table_check = $conn->query("SHOW TABLES LIKE 'deduction_settings'");
                                if ($table_check && $table_check->num_rows > 0) {
                                    $default_settings = $conn->query("SELECT * FROM deduction_settings WHERE is_default = 1 LIMIT 1");
                                    if (!$default_settings) {
                                        // إذا فشل الاستعلام، جرب بدون شرط is_default
                                        $default_settings = $conn->query("SELECT * FROM deduction_settings ORDER BY id ASC LIMIT 1");
                                    }

                                    if ($default_settings && $default_settings->num_rows > 0) {
                                        $settings = $default_settings->fetch_assoc();
                                        $default_amount = $settings['amount'];
                                        $default_type = $settings['deduction_type'];
                                    }
                                }

                                // إنشاء سجل غياب بالخصم تلقائياً مع مبلغ افتراضي
                                $deduction_stmt = $conn->prepare("
                                    INSERT INTO staff_absences_with_deduction
                                    (user_id, absence_date, reason, deduction_amount, deduction_type, status, recorded_by, processed_by, processed_at)
                                    VALUES (?, ?, 'تم تسجيله من نظام الحضور الموحد', ?, ?, 'processed', ?, ?, NOW())
                                ");
                                $deduction_stmt->bind_param("issdsi", $target_user_id, $attendance_date, $default_amount, $default_type, $user_id, $user_id);
                                $deduction_stmt->execute();

                                error_log("Created absence deduction record for user $target_user_id with amount $default_amount");
                            } else {
                                error_log("Absence deduction record already exists for user $target_user_id with amount {$deduction_exists['deduction_amount']}");
                            }
                        }

                        if ($existing) {
                            // تحديث السجل الموجود
                            error_log("DEBUG: Updating existing record for user $target_user_id with status: $status");

                            $update_stmt = $conn->prepare("
                                UPDATE staff_attendance
                                SET status = ?, check_in_time = ?, check_out_time = ?, notes = ?, updated_at = NOW()
                                WHERE user_id = ? AND attendance_date = ?
                            ");
                            $update_stmt->bind_param("ssssis", $status, $check_in_time, $check_out_time, $notes, $target_user_id, $attendance_date);
                            $update_stmt->execute();

                            // التحقق من التحديث
                            if ($update_stmt->affected_rows > 0) {
                                error_log("DEBUG: Successfully updated record for user $target_user_id");
                                $updated_count++;
                            } else {
                                error_log("DEBUG: No rows affected for user $target_user_id update");
                            }
                        } else {
                            // إنشاء سجل جديد
                            $insert_stmt = $conn->prepare("
                                INSERT INTO staff_attendance (user_id, attendance_date, status, check_in_time, check_out_time, notes, created_at)
                                VALUES (?, ?, ?, ?, ?, ?, NOW())
                            ");
                            $insert_stmt->bind_param("isssss", $target_user_id, $attendance_date, $status, $check_in_time, $check_out_time, $notes);
                            $insert_stmt->execute();
                            $new_count++;
                        }
                    }
                }
                
                $conn->commit();
                
                // تسجيل النشاط
                log_activity($user_id, 'take_staff_attendance', 'attendance', null, null, [
                    'date' => $attendance_date,
                    'new_records' => $new_count,
                    'updated_records' => $updated_count
                ]);
                
                $success_message = sprintf(__('attendance_saved_successfully_details'), $new_count, $updated_count);
                
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = __('error_occurred') . ': ' . $e->getMessage();
            }
        }
    }
}

$selected_date = clean_input($_GET['date'] ?? $_POST['attendance_date'] ?? date('Y-m-d'));

// جلب نوع التبويب من الرابط
$tab = clean_input($_GET['tab'] ?? 'teachers');

// معالجة الفلاتر
$search_filter = clean_input($_GET['search'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');

// تحديد الفئة بناءً على التبويب
$category_condition = '';
$page_title = '';
$back_link = '';

switch ($tab) {
    case 'students':
        $category_condition = "AND u.role = 'student'";
        $page_title = __('smart_student_attendance');
        $back_link = 'index.php?tab=students';
        break;
    case 'teachers':
        $category_condition = "AND u.role = 'teacher'";
        $page_title = __('smart_staff_attendance');
        $back_link = 'index.php?tab=teachers';
        break;
    case 'admins':
        $category_condition = "AND u.role = 'staff'";
        $page_title = __('smart_staff_attendance');
        $back_link = 'index.php?tab=admins';
        break;
    default:
        $category_condition = "AND u.role = 'teacher'";
        $page_title = __('smart_staff_attendance');
        $back_link = 'index.php?tab=teachers';
        $tab = 'teachers';
        break;
}

// بناء شروط الفلاتر
$filter_conditions = [];
$filter_params = [$selected_date];
$filter_types = "s";

// فلتر البحث بالاسم
if (!empty($search_filter)) {
    $filter_conditions[] = "u.full_name LIKE ?";
    $filter_params[] = "%$search_filter%";
    $filter_types .= "s";
}

// ملاحظة: فلتر الحالة سيتم تطبيقه بعد جلب البيانات لأنه يتطلب ربط مع جداول الحضور

// جلب قائمة المستخدمين حسب الفئة المحددة
$staff_query = "
    SELECT
        u.id,
        u.full_name,
        u.role,
        CASE
            WHEN u.role = 'teacher' THEN t.id
            WHEN u.role = 'student' THEN s.id
            ELSE u.id
        END as staff_id,
        CASE
            WHEN u.role = 'student' THEN s.class_id
            ELSE NULL
        END as class_id,
        sl.leave_type,
        sl.status as leave_status,
        sl.start_date,
        sl.end_date
    FROM users u
    LEFT JOIN teachers t ON (u.id = t.user_id AND u.role = 'teacher')
    LEFT JOIN students s ON (u.id = s.user_id AND u.role = 'student')
    LEFT JOIN staff_leaves sl ON (
        u.id = sl.user_id
        AND sl.status = 'approved'
        AND ? BETWEEN sl.start_date AND sl.end_date
    )
    WHERE u.status = 'active'
    AND u.role NOT IN ('admin', 'super_admin', 'system_admin')
    $category_condition
    " . (!empty($filter_conditions) ? " AND " . implode(" AND ", $filter_conditions) : "") . "
    ORDER BY u.full_name
";

$staff_stmt = $conn->prepare($staff_query);
$staff_stmt->bind_param($filter_types, ...$filter_params);
$staff_stmt->execute();
$staff_list = $staff_stmt->get_result();

// جلب حالة الحضور الحالية
$attendance_status = [];

// حضور الطلاب
if ($tab === 'students') {
    $student_attendance_stmt = $conn->prepare("
        SELECT a.*, s.user_id
        FROM attendance a
        JOIN students s ON a.student_id = s.id
        WHERE a.attendance_date = ?
    ");
    $student_attendance_stmt->bind_param("s", $selected_date);
    $student_attendance_stmt->execute();
    $student_attendance_result = $student_attendance_stmt->get_result();
    while ($row = $student_attendance_result->fetch_assoc()) {
        $attendance_status[$row['user_id']] = $row;
    }
}

// حضور المعلمين من جدول staff_attendance الموحد
if ($tab === 'teachers') {
    $staff_attendance_stmt = $conn->prepare("
        SELECT sa.*, sa.user_id
        FROM staff_attendance sa
        JOIN users u ON sa.user_id = u.id
        WHERE sa.attendance_date = ? AND u.role = 'teacher'
    ");
    $staff_attendance_stmt->bind_param("s", $selected_date);
    $staff_attendance_stmt->execute();
    $staff_attendance_result = $staff_attendance_stmt->get_result();
    while ($row = $staff_attendance_result->fetch_assoc()) {
        $attendance_status[$row['user_id']] = $row;
    }
}

// حضور الإداريين من جدول staff_attendance الموحد
if ($tab === 'admins') {
    $staff_attendance_stmt = $conn->prepare("
        SELECT sa.*, sa.user_id
        FROM staff_attendance sa
        JOIN users u ON sa.user_id = u.id
        WHERE sa.attendance_date = ? AND u.role = 'staff'
    ");
    $staff_attendance_stmt->bind_param("s", $selected_date);
    $staff_attendance_stmt->execute();
    $staff_attendance_result = $staff_attendance_stmt->get_result();
    while ($row = $staff_attendance_result->fetch_assoc()) {
        $attendance_status[$row['user_id']] = $row;
    }
}

// تطبيق فلتر الحالة إذا تم تحديده
if (!empty($status_filter)) {
    $filtered_staff = [];
    $staff_list->data_seek(0); // إعادة تعيين المؤشر

    while ($staff = $staff_list->fetch_assoc()) {
        $current_attendance = $attendance_status[$staff['id']] ?? null;
        $staff_status = 'absent'; // الحالة الافتراضية

        // التحقق من حالة الإجازة أولاً
        $is_on_leave = !empty($staff['leave_status']) && $staff['leave_status'] === 'approved';
        $is_absent_with_deduction = $current_attendance && $current_attendance['status'] === 'absent_with_deduction';

        if ($is_on_leave && !empty($staff['leave_type'])) {
            $staff_status = $staff['leave_type'];
        } elseif ($is_absent_with_deduction) {
            $staff_status = 'absent_with_deduction';
        } elseif ($current_attendance && !empty($current_attendance['status'])) {
            // إذا لم يكن في إجازة، تحقق من حالة الحضور
            $staff_status = $current_attendance['status'];
        }

        // فلترة حسب الحالة المطلوبة
        if ($staff_status === $status_filter) {
            $filtered_staff[] = $staff;
        }
    }

    // إنشاء نتيجة مؤقتة للبيانات المفلترة
    $staff_list = new class($filtered_staff) {
        private $data;
        private $position = 0;

        public function __construct($data) {
            $this->data = $data;
        }

        public function fetch_assoc() {
            if ($this->position < count($this->data)) {
                return $this->data[$this->position++];
            }
            return null;
        }

        public function num_rows() {
            return count($this->data);
        }

        public $num_rows;

        public function __get($name) {
            if ($name === 'num_rows') {
                return count($this->data);
            }
            return null;
        }

        public function data_seek($offset) {
            $this->position = $offset;
        }
    };
}

include_once '../includes/header.php';
?>

<style>
/* إصلاح مشاكل النماذج المنبثقة */
.modal {
    z-index: 1055 !important;
}
.modal-backdrop {
    z-index: 1050 !important;
}

/* تمييز حالات الحضور المختلفة */
.attendance-status option[value="present"] {
    background-color: #d4edda;
    color: #155724;
}
.attendance-status option[value="absent"] {
    background-color: #f8d7da;
    color: #721c24;
}
.attendance-status option[value="absent_with_deduction"] {
    background-color: #dc3545;
    color: #ffffff;
    font-weight: bold;
}
.attendance-status option[value="late"] {
    background-color: #fff3cd;
    color: #856404;
}
.attendance-status option[value="sick_leave"] {
    background-color: #f8d7da;
    color: #721c24;
}
.attendance-status option[value="regular_leave"] {
    background-color: #cce7ff;
    color: #004085;
}
.attendance-status option[value="absence_with_deduction"] {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
}
.modal.show {
    display: block !important;
}
.modal-dialog {
    pointer-events: auto !important;
}
.modal-content {
    pointer-events: auto !important;
}
.modal-body, .modal-footer, .modal-header {
    pointer-events: auto !important;
}
.modal input, .modal select, .modal textarea, .modal button {
    pointer-events: auto !important;
    z-index: auto !important;
}

/* تحسين مظهر حقول الأوقات الافتراضية */
.time-controls {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.time-controls .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.time-controls .btn {
    font-size: 0.875rem;
}

.text-sm {
    font-size: 0.875rem;
}
</style>

<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-brain me-2 text-primary"></i>نظام الحضور والغياب الموحد</h2>
                    <p class="text-muted">
                        <?php
                        switch ($tab) {
                            case 'students':
                                echo 'تسجيل وإدارة حضور الطلاب بطريقة سهلة وسريعة';
                                break;
                            case 'teachers':
                                echo 'تسجيل وإدارة حضور المعلمين مع إدارة الإجازات';
                                break;
                            case 'admins':
                                echo 'تسجيل وإدارة حضور الإداريين مع إدارة الإجازات';
                                break;
                            default:
                                echo 'نظام موحد لإدارة حضور جميع فئات المدرسة';
                        }
                        ?>
                    </p>
                </div>
                <div>
                    <!-- تبويبات التنقل السريع -->
                    <div class="btn-group me-3" role="group">
                        <a href="?tab=students&date=<?php echo $selected_date; ?>"
                           class="btn btn-<?php echo $tab === 'students' ? 'primary' : 'outline-primary'; ?>">
                            <i class="fas fa-user-graduate me-1"></i><?php echo __('students'); ?>
                        </a>
                        <a href="?tab=teachers&date=<?php echo $selected_date; ?>"
                           class="btn btn-<?php echo $tab === 'teachers' ? 'primary' : 'outline-primary'; ?>">
                            <i class="fas fa-chalkboard-teacher me-1"></i><?php echo __('teachers'); ?>
                        </a>
                        <a href="?tab=admins&date=<?php echo $selected_date; ?>"
                           class="btn btn-<?php echo $tab === 'admins' ? 'primary' : 'outline-primary'; ?>">
                            <i class="fas fa-user-tie me-1"></i>الإداريين
                        </a>
                    </div>

                    <!-- أزرار إضافية -->
                    <div class="btn-group" role="group">
                        <a href="reports.php" class="btn btn-info">
                            <i class="fas fa-chart-bar me-2"></i><?php echo __('reports'); ?>
                        </a>
                        <?php if (check_permission('admin')): ?>
                        <a href="staff_reports.php" class="btn btn-success">
                            <i class="fas fa-users me-2"></i><?php echo __('staff_reports'); ?>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="tab" value="<?php echo htmlspecialchars($tab); ?>">

                        <div class="col-md-3">
                            <label for="date" class="form-label">
                                <i class="fas fa-calendar me-1"></i><?php echo __('date'); ?>
                            </label>
                            <input type="date" class="form-control" id="date" name="date"
                                   value="<?php echo htmlspecialchars($selected_date); ?>"
                                   max="<?php echo date('Y-m-d'); ?>">
                        </div>

                        <div class="col-md-4">
                            <label for="search" class="form-label">
                                <i class="fas fa-search me-1"></i><?php echo __('search'); ?>
                            </label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($search_filter); ?>"
                                   placeholder="<?php echo $tab === 'students' ? __('search_student') : ($tab === 'teachers' ? __('search_teacher') : __('search_admin')); ?>">
                        </div>

                        <div class="col-md-3">
                            <label for="status" class="form-label">
                                <i class="fas fa-filter me-1"></i><?php echo __('status'); ?>
                            </label>
                            <select class="form-select" id="status" name="status">
                                <option value=""><?php echo __('all_statuses'); ?></option>
                                <option value="present" <?php echo ($status_filter == 'present') ? 'selected' : ''; ?>>
                                    <?php echo __('present'); ?>
                                </option>
                                <option value="absent" <?php echo ($status_filter == 'absent') ? 'selected' : ''; ?>>
                                    <?php echo __('absent'); ?>
                                </option>
                                <option value="absent_with_deduction" <?php echo ($status_filter == 'absent_with_deduction') ? 'selected' : ''; ?>>
                                    غياب بالخصم
                                </option>
                                <option value="late" <?php echo ($status_filter == 'late') ? 'selected' : ''; ?>>
                                    <?php echo __('late'); ?>
                                </option>
                                <option value="sick_leave" <?php echo ($status_filter == 'sick_leave') ? 'selected' : ''; ?>>
                                    <?php echo __('sick_leave'); ?>
                                </option>
                                <option value="regular_leave" <?php echo ($status_filter == 'regular_leave') ? 'selected' : ''; ?>>
                                    <?php echo __('regular_leave'); ?>
                                </option>
                                <option value="emergency_leave" <?php echo ($status_filter == 'emergency_leave') ? 'selected' : ''; ?>>
                                    <?php echo __('emergency_leave'); ?>
                                </option>
                                <option value="absence_with_deduction" <?php echo ($status_filter == 'absence_with_deduction') ? 'selected' : ''; ?>>
                                    <?php echo __('absence_with_deduction_leave'); ?>
                                </option>
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i><?php echo __('search'); ?>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نموذج تسجيل الحضور -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        تسجيل الحضور - <?php echo $tab === 'students' ? 'الطلاب' : ($tab === 'teachers' ? 'المعلمين' : 'الإداريين'); ?>
                        <span class="badge bg-primary ms-2"><?php echo date('Y-m-d', strtotime($selected_date)); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="attendanceForm">
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="attendance_date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i><?php echo __('attendance_date'); ?>
                                </label>
                                <input type="date" class="form-control" id="attendance_date" name="attendance_date"
                                       value="<?php echo htmlspecialchars($selected_date); ?>"
                                       max="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-8">
                                <div class="row g-2">
                                    <div class="col-md-12 mb-2">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-info btn-sm" onclick="loadAttendanceData()">
                                                <i class="fas fa-sync-alt me-1"></i>تحديث البيانات
                                            </button>
                                            <button type="button" class="btn btn-success btn-sm" onclick="markAllPresent()">
                                                <i class="fas fa-check-double me-1"></i>تحديد الكل حاضر
                                            </button>

                                            <?php if ($tab !== 'students'): ?>
                                            <a href="leave_form.php?type=sick" class="btn btn-danger btn-sm">
                                                <i class="fas fa-user-injured me-1"></i>إجازة مرضية
                                            </a>
                                            <a href="leave_form.php?type=regular" class="btn btn-primary btn-sm">
                                                <i class="fas fa-calendar-times me-1"></i>إجازة اعتيادية
                                            </a>
                                            <a href="leave_form.php?type=emergency" class="btn btn-warning btn-sm">
                                                <i class="fas fa-exclamation-triangle me-1"></i>إجازة طارئة
                                            </a>
                                            <?php if (check_permission('admin')): ?>
                                            <a href="new_absence_form.php" class="btn btn-danger btn-sm">
                                                <i class="fas fa-user-times me-1"></i>غياب بالخصم
                                            </a>
                                            <?php endif; ?>
                                            <a href="manage_leaves.php" class="btn btn-info btn-sm">
                                                <i class="fas fa-cog me-1"></i>إدارة الإجازات
                                            </a>
                                            <?php if (check_permission('admin')): ?>
                                            <a href="manage_absences.php" class="btn btn-dark btn-sm">
                                                <i class="fas fa-list me-1"></i>إدارة الغياب بالخصم
                                            </a>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-warning btn-sm" onclick="resetForm()">
                                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                    <?php if ($tab !== 'students'): ?>
                                    <div class="col-md-12">
                                        <div class="row g-2 time-controls">
                                            <div class="col-md-3">
                                                <label for="default_check_in" class="form-label text-sm">
                                                    <i class="fas fa-clock me-1"></i><?php echo __('default_check_in_time'); ?>
                                                </label>
                                                <input type="time" class="form-control form-control-sm" id="default_check_in" value="08:00">
                                            </div>
                                            <div class="col-md-3">
                                                <label for="default_check_out" class="form-label text-sm">
                                                    <i class="fas fa-clock me-1"></i><?php echo __('default_check_out_time'); ?>
                                                </label>
                                                <input type="time" class="form-control form-control-sm" id="default_check_out" value="15:00">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label text-sm">&nbsp;</label>
                                                <div class="d-grid">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="applyDefaultTimes()">
                                                        <i class="fas fa-clock me-1"></i><?php echo __('apply_times_to_all'); ?>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label text-sm">&nbsp;</label>
                                                <div class="d-grid">
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllTimes()">
                                                        <i class="fas fa-eraser me-1"></i><?php echo __('clear_all_times'); ?>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- عداد النتائج -->
                        <?php
                        $total_count = is_object($staff_list) && method_exists($staff_list, 'num_rows') ?
                                      $staff_list->num_rows :
                                      (isset($staff_list->num_rows) ? $staff_list->num_rows : 0);
                        if (!empty($search_filter) || !empty($status_filter)):
                        ?>
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-1"></i>
                            <?php if (!empty($search_filter)): ?>
                                نتائج البحث عن "<?php echo htmlspecialchars($search_filter); ?>"
                            <?php endif; ?>
                            <?php if (!empty($status_filter)): ?>
                                <?php if (!empty($search_filter)): ?> و <?php endif; ?>
                                فلترة حسب الحالة:
                                <strong>
                                <?php
                                    switch($status_filter) {
                                        case 'present': echo 'حاضر'; break;
                                        case 'absent': echo 'غائب'; break;
                                        case 'absent_with_deduction': echo 'غياب بالخصم'; break;
                                        case 'late': echo 'متأخر'; break;
                                        case 'sick_leave': echo 'إجازة مرضية'; break;
                                        case 'regular_leave': echo 'إجازة اعتيادية'; break;
                                        default: echo $status_filter;
                                    }
                                ?>
                                </strong>
                            <?php endif; ?>
                            <br>
                            عرض <?php echo $total_count; ?>
                            <?php echo $tab === 'students' ? 'طالب' : ($tab === 'teachers' ? 'معلم' : 'إداري'); ?>
                            <?php if (!empty($search_filter) || !empty($status_filter)): ?>
                                <a href="?tab=<?php echo $tab; ?>&date=<?php echo $selected_date; ?>" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </a>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <div class="table-responsive">
                            <table class="table table-striped table-bordered align-middle" id="attendanceTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="<?php echo $tab === 'students' ? '30%' : '20%'; ?>">
                                            <?php echo $tab === 'students' ? __('student_name') : __('staff_member'); ?>
                                        </th>
                                        <th width="10%"><?php echo __('role'); ?></th>
                                        <?php if ($tab !== 'students'): ?>
                                        <th width="15%"><?php echo __('leave_status'); ?></th>
                                        <?php endif; ?>
                                        <th width="<?php echo $tab === 'students' ? '20%' : '15%'; ?>"><?php echo __('attendance_status'); ?></th>
                                        <?php if ($tab !== 'students'): ?>
                                        <th width="12%"><?php echo __('check_in'); ?></th>
                                        <th width="12%"><?php echo __('check_out'); ?></th>
                                        <?php endif; ?>
                                        <th width="<?php echo $tab === 'students' ? '35%' : '11%'; ?>"><?php echo __('notes'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $i = 1;
                                    while ($staff = $staff_list->fetch_assoc()): 
                                        $is_on_leave = !empty($staff['leave_status']) && $staff['leave_status'] === 'approved';
                                        $current_attendance = $attendance_status[$staff['id']] ?? null;
                                        $is_absent_with_deduction = $current_attendance && $current_attendance['status'] === 'absent_with_deduction';
                                        $staff_table_id = ($staff['role'] === 'teacher') ? $staff['staff_id'] : $staff['id'];
                                    ?>
                                        <tr class="<?php echo $is_on_leave ? 'table-warning' : ($is_absent_with_deduction ? 'table-danger' : ''); ?>">
                                            <td><?php echo $i++; ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <span class="badge bg-<?php echo $staff['role'] === 'teacher' ? 'primary' : 'success'; ?>">
                                                            <?php echo strtoupper(substr($staff['full_name'], 0, 2)); ?>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($staff['full_name']); ?></strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $staff['role'] === 'teacher' ? 'primary' :
                                                        ($staff['role'] === 'student' ? 'info' : 'success');
                                                ?>">
                                                    <?php echo __($staff['role']); ?>
                                                </span>
                                            </td>
                                            <?php if ($tab !== 'students'): ?>
                                            <td>
                                                <?php if ($is_on_leave): ?>
                                                    <div class="text-center">
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-calendar-times me-1"></i>
                                                            <?php echo __($staff['leave_type'] . '_leave'); ?>
                                                        </span>
                                                        <br>
                                                        <small class="text-muted">
                                                            <?php echo date('m/d', strtotime($staff['start_date'])); ?> -
                                                            <?php echo date('m/d', strtotime($staff['end_date'])); ?>
                                                        </small>
                                                    </div>
                                                <?php elseif ($is_absent_with_deduction): ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-ban me-1"></i>غير متاح
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i><?php echo __('available'); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <?php endif; ?>
                                            <td>
                                                <?php if ($tab !== 'students' && $is_on_leave): ?>
                                                    <!-- الموظفين في إجازة لا يتم تسجيل حضورهم -->
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-calendar-times me-1"></i>
                                                        <?php echo __($staff['leave_type'] . '_leave'); ?>
                                                    </span>
                                                    <br>
                                                    <small class="text-muted"><?php echo __('excluded_from_attendance'); ?></small>
                                                <?php elseif ($tab !== 'students' && $is_absent_with_deduction): ?>
                                                    <!-- الموظفين غائبين بالخصم -->
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-ban me-1"></i>
                                                        غياب بالخصم
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">مع خصم من الراتب</small>
                                                <?php else: ?>
                                                    <select class="form-select form-select-sm attendance-status"
                                                            name="attendance[<?php echo $staff['id']; ?>][status]"
                                                            data-user-id="<?php echo $staff['id']; ?>">
                                                        <option value="present" <?php echo ($current_attendance && $current_attendance['status'] === 'present') ? 'selected' : ''; ?>>
                                                            <?php echo __('present'); ?>
                                                        </option>
                                                        <option value="absent" <?php echo (!$current_attendance || $current_attendance['status'] === 'absent') ? 'selected' : ''; ?>>
                                                            <?php echo __('absent'); ?>
                                                        </option>
                                                        <option value="absent_with_deduction" <?php echo ($current_attendance && $current_attendance['status'] === 'absent_with_deduction') ? 'selected' : ''; ?>>
                                                            غياب بالخصم
                                                        </option>
                                                        <option value="late" <?php echo ($current_attendance && $current_attendance['status'] === 'late') ? 'selected' : ''; ?>>
                                                            <?php echo __('late'); ?>
                                                        </option>
                                                        <option value="sick_leave" <?php echo ($current_attendance && $current_attendance['status'] === 'sick_leave') ? 'selected' : ''; ?>>
                                                            إجازة مرضية
                                                        </option>
                                                        <option value="regular_leave" <?php echo ($current_attendance && $current_attendance['status'] === 'regular_leave') ? 'selected' : ''; ?>>
                                                            إجازة اعتيادية
                                                        </option>
                                                    </select>
                                                    <input type="hidden" name="attendance[<?php echo $staff['id']; ?>][user_type]" value="<?php echo $staff['role']; ?>">
                                                <?php endif; ?>
                                            </td>
                                            <?php if ($tab !== 'students'): ?>
                                            <td>
                                                <input type="time" class="form-control form-control-sm check-in-time"
                                                       name="attendance[<?php echo $staff['id']; ?>][check_in_time]"
                                                       value="<?php echo $current_attendance['check_in_time'] ?? ''; ?>"
                                                       <?php echo $is_on_leave ? 'disabled' : ''; ?>
                                                       <?php echo $is_absent_with_deduction ? 'readonly placeholder="غائب بالخصم"' : ''; ?>>
                                            </td>
                                            <td>
                                                <input type="time" class="form-control form-control-sm check-out-time"
                                                       name="attendance[<?php echo $staff['id']; ?>][check_out_time]"
                                                       value="<?php echo $current_attendance['check_out_time'] ?? ''; ?>"
                                                       <?php echo $is_on_leave ? 'disabled' : ''; ?>
                                                       <?php echo $is_absent_with_deduction ? 'readonly placeholder="غائب بالخروج"' : ''; ?>>
                                            </td>
                                            <?php endif; ?>
                                            <td>
                                                <input type="text" class="form-control form-control-sm"
                                                       name="attendance[<?php echo $staff['id']; ?>][notes]"
                                                       value="<?php echo $is_absent_with_deduction ? '' : htmlspecialchars($current_attendance['notes'] ?? ''); ?>"
                                                       placeholder="<?php echo $is_absent_with_deduction ? 'غياب بالخصم' : __('notes'); ?>"
                                                       <?php echo ($tab !== 'students' && $is_on_leave) ? 'disabled' : ''; ?>
                                                       <?php echo $is_absent_with_deduction ? 'readonly' : ''; ?>>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between mt-3">
                            <div class="text-muted">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    <?php
                                    switch ($tab) {
                                        case 'students':
                                            echo __('students_attendance_info');
                                            break;
                                        case 'teachers':
                                            echo __('teachers_on_leave_excluded');
                                            break;
                                        case 'admins':
                                            echo __('admins_on_leave_excluded');
                                            break;
                                        default:
                                            echo __('staff_on_leave_auto_marked');
                                    }
                                    ?>
                                </small>
                            </div>
                            <button type="submit" name="save_attendance" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('save_attendance'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الإجازة المرضية -->
<div class="modal fade" id="sickLeaveModal" tabindex="-1" aria-labelledby="sickLeaveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="sickLeaveModalLabel">
                    <i class="fas fa-user-injured me-2"></i><?php echo __('sick_leave'); ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="sickLeaveForm" method="post">
                <?php echo csrf_token_field(); ?>
                <input type="hidden" name="leave_action" value="sick">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="sick_staff_id" class="form-label"><?php echo __('staff_member'); ?></label>
                        <select class="form-select" id="sick_staff_id" name="staff_id" required>
                            <option value=""><?php echo __('select_staff_member'); ?></option>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="sick_start_date" class="form-label"><?php echo __('from_date'); ?></label>
                            <input type="date" class="form-control" id="sick_start_date" name="start_date"
                                   max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                            <div class="form-text"><?php echo __('can_select_past_dates'); ?></div>
                        </div>
                        <div class="col-md-6">
                            <label for="sick_end_date" class="form-label"><?php echo __('to_date'); ?></label>
                            <input type="date" class="form-control" id="sick_end_date" name="end_date"
                                   max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="sick_reason" class="form-label"><?php echo __('reason'); ?></label>
                        <textarea class="form-control" id="sick_reason" name="reason" rows="3" placeholder="<?php echo __('enter_leave_reason'); ?>"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('sickLeaveModal')"><?php echo __('cancel'); ?></button>
                    <button type="submit" class="btn btn-danger"><?php echo __('register_sick_leave'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج الإجازة الاعتيادية -->
<div class="modal fade" id="regularLeaveModal" tabindex="-1" aria-labelledby="regularLeaveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="regularLeaveModalLabel">
                    <i class="fas fa-calendar-times me-2"></i><?php echo __('regular_leave'); ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="regularLeaveForm" method="post">
                <?php echo csrf_token_field(); ?>
                <input type="hidden" name="leave_action" value="regular">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="regular_staff_id" class="form-label"><?php echo __('staff_member'); ?></label>
                        <select class="form-select" id="regular_staff_id" name="staff_id" required>
                            <option value=""><?php echo __('select_staff_member'); ?></option>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="regular_start_date" class="form-label"><?php echo __('from_date'); ?></label>
                            <input type="date" class="form-control" id="regular_start_date" name="start_date"
                                   max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                            <div class="form-text"><?php echo __('can_select_past_dates'); ?></div>
                        </div>
                        <div class="col-md-6">
                            <label for="regular_end_date" class="form-label"><?php echo __('to_date'); ?></label>
                            <input type="date" class="form-control" id="regular_end_date" name="end_date"
                                   max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="regular_reason" class="form-label"><?php echo __('reason'); ?></label>
                        <textarea class="form-control" id="regular_reason" name="reason" rows="3" placeholder="<?php echo __('enter_leave_reason'); ?>"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('regularLeaveModal')"><?php echo __('cancel'); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo __('register_regular_leave'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...');

    // ملء قوائم الموظفين في النماذج المنبثقة
    populateStaffLists();

    // التحقق من تحميل Bootstrap
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    console.log('jQuery available:', typeof $ !== 'undefined');

    // إضافة event listeners للنماذج المنبثقة
    const sickModal = document.getElementById('sickLeaveModal');
    const regularModal = document.getElementById('regularLeaveModal');

    if (sickModal) {
        sickModal.addEventListener('show.bs.modal', function() {
            console.log('Sick leave modal opening...');
            populateStaffLists();
            // إصلاح مشاكل التفاعل
            fixModalInteraction('sickLeaveModal');
        });
    }

    if (regularModal) {
        regularModal.addEventListener('show.bs.modal', function() {
            console.log('Regular leave modal opening...');
            populateStaffLists();
            // إصلاح مشاكل التفاعل
            fixModalInteraction('regularLeaveModal');
        });
    }

    // دالة لإصلاح مشاكل التفاعل في النماذج المنبثقة
    function fixModalInteraction(modalId) {
        setTimeout(function() {
            const modal = document.getElementById(modalId);
            if (modal) {
                // إزالة أي خصائص تمنع التفاعل
                modal.style.pointerEvents = 'auto';

                // تفعيل جميع العناصر داخل النموذج
                const inputs = modal.querySelectorAll('input, select, textarea, button');
                inputs.forEach(function(element) {
                    element.style.pointerEvents = 'auto';
                    element.disabled = false;

                    // إضافة event listeners للتأكد من عملها
                    if (element.type === 'date') {
                        element.addEventListener('click', function() {
                            this.showPicker && this.showPicker();
                        });
                    }
                });

                // تفعيل الأزرار
                const buttons = modal.querySelectorAll('button');
                buttons.forEach(function(button) {
                    button.style.pointerEvents = 'auto';
                    button.disabled = false;
                });
            }
        }, 100);
    }

    // دوال فتح النماذج المنبثقة
    window.openSickLeaveModal = function() {
        console.log('Opening sick leave modal...');
        populateStaffLists();

        // فتح النموذج
        const modal = document.getElementById('sickLeaveModal');
        if (modal) {
            // استخدام Bootstrap إذا كان متاح
            if (typeof bootstrap !== 'undefined') {
                const bsModal = new bootstrap.Modal(modal, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                bsModal.show();

                // إصلاح التفاعل بعد الفتح
                modal.addEventListener('shown.bs.modal', function() {
                    fixModalInteraction('sickLeaveModal');
                }, { once: true });
            } else {
                // فتح بطريقة بديلة
                modal.style.display = 'block';
                modal.classList.add('show');
                document.body.classList.add('modal-open');

                // إضافة backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'sickLeaveBackdrop';
                document.body.appendChild(backdrop);

                fixModalInteraction('sickLeaveModal');
            }
        }
    };

    window.openRegularLeaveModal = function() {
        console.log('Opening regular leave modal...');
        populateStaffLists();

        // فتح النموذج
        const modal = document.getElementById('regularLeaveModal');
        if (modal) {
            // استخدام Bootstrap إذا كان متاح
            if (typeof bootstrap !== 'undefined') {
                const bsModal = new bootstrap.Modal(modal, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                bsModal.show();

                // إصلاح التفاعل بعد الفتح
                modal.addEventListener('shown.bs.modal', function() {
                    fixModalInteraction('regularLeaveModal');
                }, { once: true });
            } else {
                // فتح بطريقة بديلة
                modal.style.display = 'block';
                modal.classList.add('show');
                document.body.classList.add('modal-open');

                // إضافة backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'regularLeaveBackdrop';
                document.body.appendChild(backdrop);

                fixModalInteraction('regularLeaveModal');
            }
        }
    };

    // دالة إغلاق النماذج
    window.closeModal = function(modalId) {
        const modal = document.getElementById(modalId);
        const backdrop = document.getElementById(modalId.replace('Modal', 'Backdrop'));

        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        }

        if (backdrop) {
            backdrop.remove();
        }
    };

    // إضافة event listeners لأزرار الإغلاق
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(function(button) {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModal(modal.id);
            }
        });
    });

    function populateStaffLists() {
        const staffOptions = [];

        // جمع بيانات الموظفين من الجدول
        document.querySelectorAll('tbody tr').forEach(function(row) {
            const staffName = row.querySelector('td:nth-child(2) strong');
            const roleSpan = row.querySelector('td:nth-child(3) .badge');
            const leaveStatus = row.querySelector('td:nth-child(4)');

            if (staffName && roleSpan && leaveStatus) {
                const name = staffName.textContent.trim();
                const role = roleSpan.textContent.trim();
                const isOnLeave = leaveStatus.querySelector('.badge.bg-warning');

                // إضافة فقط الموظفين غير المتواجدين في إجازة
                if (!isOnLeave) {
                    const userId = row.querySelector('select[name*="attendance"]')?.name.match(/\[(\d+)\]/)?.[1];
                    if (userId) {
                        staffOptions.push({
                            id: userId,
                            name: name,
                            role: role
                        });
                    }
                }
            }
        });

        // ملء قائمة الإجازة المرضية
        const sickSelect = document.getElementById('sick_staff_id');
        const regularSelect = document.getElementById('regular_staff_id');

        [sickSelect, regularSelect].forEach(select => {
            // مسح الخيارات الموجودة
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // إضافة الخيارات الجديدة
            staffOptions.forEach(staff => {
                const option = document.createElement('option');
                option.value = staff.id;
                option.textContent = `${staff.name} (${staff.role})`;
                select.appendChild(option);
            });
        });
    }





    // التحقق من صحة تواريخ الإجازة
    function validateLeaveDates(startInput, endInput) {
        const startDate = new Date(startInput.value);
        const endDate = new Date(endInput.value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (startDate < today) {
            alert('تاريخ البداية لا يمكن أن يكون في الماضي');
            startInput.focus();
            return false;
        }

        if (endDate < startDate) {
            alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            endInput.focus();
            return false;
        }

        return true;
    }

    // معالجة نموذج الإجازة المرضية
    document.getElementById('sickLeaveForm').addEventListener('submit', function(e) {
        const startInput = document.getElementById('sick_start_date');
        const endInput = document.getElementById('sick_end_date');

        if (!validateLeaveDates(startInput, endInput)) {
            e.preventDefault();
            return false;
        }
    });

    // معالجة نموذج الإجازة الاعتيادية
    document.getElementById('regularLeaveForm').addEventListener('submit', function(e) {
        const startInput = document.getElementById('regular_start_date');
        const endInput = document.getElementById('regular_end_date');

        if (!validateLeaveDates(startInput, endInput)) {
            e.preventDefault();
            return false;
        }
    });
    // تعطيل حقول الوقت للموظفين في إجازة من البداية
    document.querySelectorAll('tr.table-warning').forEach(function(row) {
        const timeInputs = row.querySelectorAll('input[type="time"]');
        timeInputs.forEach(function(input) {
            input.disabled = true;
            input.value = '';
        });
    });

    // استرجاع الأوقات الافتراضية المحفوظة
    const savedCheckIn = localStorage.getItem('default_check_in_time');
    const savedCheckOut = localStorage.getItem('default_check_out_time');

    if (savedCheckIn) {
        document.getElementById('default_check_in').value = savedCheckIn;
    }
    if (savedCheckOut) {
        document.getElementById('default_check_out').value = savedCheckOut;
    }

    // حفظ الأوقات الافتراضية عند التغيير
    document.getElementById('default_check_in').addEventListener('change', function() {
        localStorage.setItem('default_check_in_time', this.value);
    });

    document.getElementById('default_check_out').addEventListener('change', function() {
        localStorage.setItem('default_check_out_time', this.value);
    });

    // تحديث أوقات الدخول والخروج عند تغيير حالة الحضور
    document.querySelectorAll('.attendance-status').forEach(function(select) {
        select.addEventListener('change', function() {
            console.log(`Status changed for user ${this.getAttribute('data-user-id')}: ${this.value}`);
            updateTimeFields(this);
        });

        // تطبيق القواعد الأولية
        updateTimeFields(select);
    });

    function updateTimeFields(statusSelect) {
        const userId = statusSelect.getAttribute('data-user-id');
        const checkInField = document.querySelector(`input[name="attendance[${userId}][check_in_time]"]`);
        const checkOutField = document.querySelector(`input[name="attendance[${userId}][check_out_time]"]`);

        console.log(`updateTimeFields called for user ${userId} with status: ${statusSelect.value}`);

        // التحقق من وجود حقول الأوقات (للمعلمين والإداريين فقط)
        if (!checkInField || !checkOutField) {
            // للطلاب: لا توجد حقول أوقات، لا نحتاج لفعل شيء
            return;
        }

        // التحقق من أن الموظف ليس في إجازة
        const row = statusSelect.closest('tr');
        const isOnLeave = row && row.classList.contains('table-warning');

        if (isOnLeave) {
            // إذا كان في إجازة، لا تملأ أوقات الدخول والخروج
            checkInField.disabled = true;
            checkOutField.disabled = true;
            checkInField.value = '';
            checkOutField.value = '';
            return;
        }

        if (statusSelect.value === 'present' || statusSelect.value === 'late') {
            if (!checkInField.value) {
                checkInField.value = new Date().toTimeString().slice(0, 5);
            }
            checkInField.disabled = false;
            checkOutField.disabled = false;
        } else if (statusSelect.value === 'sick_leave' || statusSelect.value === 'regular_leave' || statusSelect.value === 'absent_with_deduction') {
            // للإجازات والغياب بالخصم: لا حاجة لأوقات دخول وخروج
            checkInField.value = '';
            checkOutField.value = '';
            checkInField.readOnly = true;
            checkOutField.readOnly = true;
            checkInField.disabled = false; // لا نعطل الحقل حتى يتم إرساله
            checkOutField.disabled = false; // لا نعطل الحقل حتى يتم إرساله

            // إضافة الحقل المخفي للغياب بالخصم
            if (statusSelect.value === 'absent_with_deduction') {
                // إزالة أي حقل مخفي موجود أولاً
                const existingHidden = document.querySelector(`input[name="attendance[${userId}][force_absent_with_deduction]"]`);
                if (existingHidden) {
                    existingHidden.remove();
                }

                // إنشاء حقل مخفي جديد
                const hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.name = `attendance[${userId}][force_absent_with_deduction]`;
                hiddenField.value = '1';
                hiddenField.id = `force_absent_${userId}`;

                // إضافة الحقل إلى النموذج
                const form = statusSelect.closest('form');
                if (form) {
                    form.appendChild(hiddenField);
                    console.log(`Added hidden field for user ${userId}: absent_with_deduction`);
                }

                // تعيين placeholder للحقول
                if (checkInField) {
                    checkInField.placeholder = 'غائب بالخصم';
                }
                if (checkOutField) {
                    checkOutField.placeholder = 'غائب بالخصم';
                }
            } else {
                // إزالة الحقل المخفي إذا لم تعد الحالة غياب بالخصم
                const hiddenField = document.querySelector(`input[name="attendance[${userId}][force_absent_with_deduction]"]`);
                if (hiddenField) {
                    hiddenField.remove();
                    console.log(`Removed hidden field for user ${userId}`);
                }

                // إزالة placeholder
                if (checkInField) {
                    checkInField.placeholder = '';
                }
                if (checkOutField) {
                    checkOutField.placeholder = '';
                }
            }
        } else {
            // للغياب العادي
            checkInField.value = '';
            checkOutField.value = '';
            checkInField.disabled = true;
            checkOutField.disabled = true;
        }
    }

    // تحميل بيانات التاريخ المحدد
    window.loadAttendanceData = function() {
        const selectedDate = document.getElementById('attendance_date').value;
        if (selectedDate) {
            window.location.href = `?date=${selectedDate}`;
        }
    };

    // تحديد الكل كحاضر
    window.markAllPresent = function() {
        const defaultCheckIn = document.getElementById('default_check_in');
        const defaultCheckOut = document.getElementById('default_check_out');

        // الحصول على القيم الافتراضية (قد لا تكون موجودة للطلاب)
        const defaultCheckInValue = defaultCheckIn ? defaultCheckIn.value : null;
        const defaultCheckOutValue = defaultCheckOut ? defaultCheckOut.value : null;

        let markedCount = 0;

        document.querySelectorAll('.attendance-status').forEach(function(select) {
            // التحقق من أن الموظف ليس في إجازة
            const row = select.closest('tr');
            const isOnLeave = row && row.classList.contains('table-warning');

            if (!select.disabled && !isOnLeave) {
                select.value = 'present';
                updateTimeFields(select);
                markedCount++;

                // تطبيق الأوقات الافتراضية إذا كانت محددة (للمعلمين والإداريين فقط)
                if (defaultCheckInValue) {
                    const userId = select.getAttribute('data-user-id');
                    const checkInField = document.querySelector(`input[name="attendance[${userId}][check_in_time]"]`);
                    const checkOutField = document.querySelector(`input[name="attendance[${userId}][check_out_time]"]`);

                    if (checkInField && !checkInField.disabled) {
                        checkInField.value = defaultCheckInValue;
                    }

                    if (checkOutField && !checkOutField.disabled && defaultCheckOutValue) {
                        checkOutField.value = defaultCheckOutValue;
                    }
                }
            }
        });

        // إظهار رسالة تأكيد
        if (markedCount > 0) {
            alert(`تم تحديد ${markedCount} شخص كحاضر`);
        } else {
            alert('لا يوجد أشخاص متاحين للتحديد');
        }
    };

    // إعادة تعيين النموذج
    window.resetForm = function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
            document.querySelectorAll('.attendance-status').forEach(function(select) {
                // التحقق من أن الموظف ليس في إجازة
                const row = select.closest('tr');
                const isOnLeave = row && row.classList.contains('table-warning');

                if (!select.disabled && !isOnLeave) {
                    select.value = 'absent';
                    updateTimeFields(select);
                }
            });

            document.querySelectorAll('input[name*="[notes]"]').forEach(function(input) {
                // التحقق من أن الموظف ليس في إجازة
                const row = input.closest('tr');
                const isOnLeave = row && row.classList.contains('table-warning');

                if (!input.disabled && !isOnLeave) {
                    input.value = '';
                }
            });
        }
    };

    // تطبيق الأوقات الافتراضية على جميع الموظفين الحاضرين
    window.applyDefaultTimes = function() {
        const defaultCheckIn = document.getElementById('default_check_in').value;
        const defaultCheckOut = document.getElementById('default_check_out').value;

        if (!defaultCheckIn) {
            alert('يرجى تحديد وقت الدخول الافتراضي');
            return;
        }

        if (defaultCheckOut && defaultCheckIn >= defaultCheckOut) {
            alert('وقت الخروج يجب أن يكون بعد وقت الدخول');
            return;
        }

        let appliedCount = 0;

        document.querySelectorAll('.attendance-status').forEach(function(select) {
            // التحقق من أن الموظف ليس في إجازة وحالته حاضر أو متأخر
            const row = select.closest('tr');
            const isOnLeave = row && row.classList.contains('table-warning');

            if (!isOnLeave && (select.value === 'present' || select.value === 'late')) {
                const userId = select.getAttribute('data-user-id');
                const checkInField = document.querySelector(`input[name="attendance[${userId}][check_in_time]"]`);
                const checkOutField = document.querySelector(`input[name="attendance[${userId}][check_out_time]"]`);

                if (checkInField && !checkInField.disabled) {
                    checkInField.value = defaultCheckIn;
                    appliedCount++;
                }

                if (checkOutField && !checkOutField.disabled && defaultCheckOut) {
                    checkOutField.value = defaultCheckOut;
                }
            }
        });

        if (appliedCount > 0) {
            alert(`تم تطبيق الأوقات على ${appliedCount} موظف`);
        } else {
            alert('لا يوجد موظفين حاضرين لتطبيق الأوقات عليهم');
        }
    };

    // مسح جميع الأوقات
    window.clearAllTimes = function() {
        if (confirm('هل أنت متأكد من مسح جميع أوقات الدخول والخروج؟')) {
            let clearedCount = 0;

            document.querySelectorAll('input[type="time"]').forEach(function(timeInput) {
                // التحقق من أن الموظف ليس في إجازة
                const row = timeInput.closest('tr');
                const isOnLeave = row && row.classList.contains('table-warning');

                if (!isOnLeave && !timeInput.disabled) {
                    timeInput.value = '';
                    clearedCount++;
                }
            });

            alert(`تم مسح ${clearedCount} حقل وقت`);
        }
    };

    // التحقق من صحة النموذج قبل الإرسال
    document.getElementById('attendanceForm').addEventListener('submit', function(e) {
        const attendanceDate = document.getElementById('attendance_date').value;
        if (!attendanceDate) {
            e.preventDefault();
            alert('يرجى تحديد تاريخ الحضور');
            return false;
        }

        // التحقق من صحة أوقات الدخول والخروج
        let hasErrors = false;
        document.querySelectorAll('.check-in-time').forEach(function(checkIn) {
            const userId = checkIn.name.match(/\[(\d+)\]/)[1];
            const checkOut = document.querySelector(`input[name="attendance[${userId}][check_out_time]"]`);

            if (checkIn.value && checkOut.value) {
                if (checkIn.value >= checkOut.value) {
                    hasErrors = true;
                    checkOut.classList.add('is-invalid');
                } else {
                    checkOut.classList.remove('is-invalid');
                }
            }
        });

        if (hasErrors) {
            e.preventDefault();
            alert('وقت الخروج يجب أن يكون بعد وقت الدخول');
            return false;
        }
    });

    // تحديث التاريخ عند تغييره
    document.getElementById('attendance_date').addEventListener('change', function() {
        const selectedDate = this.value;
        const today = new Date().toISOString().split('T')[0];

        if (selectedDate > today) {
            alert('لا يمكن تسجيل الحضور لتاريخ مستقبلي');
            this.value = today;
        }
    });
});
</script>

<?php include_once '../includes/footer.php'; ?>
