<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام تحت الصيانة - System Under Maintenance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .maintenance-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        .maintenance-content {
            position: relative;
            z-index: 2;
        }
        
        .maintenance-icon {
            font-size: 5rem;
            color: #667eea;
            margin-bottom: 2rem;
            animation: bounce 2s ease-in-out infinite;
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .maintenance-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
        }
        
        .maintenance-message {
            font-size: 1rem;
            color: #777;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .progress-container {
            background: #f0f0f0;
            border-radius: 10px;
            height: 8px;
            margin: 2rem 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 10px;
            animation: progress 3s ease-in-out infinite;
        }
        
        .contact-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .contact-info h5 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0.5rem 0;
            color: #555;
        }
        
        .contact-item i {
            margin-left: 0.5rem;
            color: #667eea;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 60px;
            height: 60px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 80px;
            height: 80px;
            top: 70%;
            right: 15%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 30%;
            right: 20%;
            animation-delay: 4s;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes bounce {
            0%, 20%, 60%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            80% {
                transform: translateY(-10px);
            }
        }
        
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-icon {
                font-size: 4rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <div class="maintenance-content">
            <div class="maintenance-icon">
                <i class="fas fa-tools"></i>
            </div>
            
            <h1 class="maintenance-title">
                النظام تحت الصيانة
            </h1>
            
            <h2 class="maintenance-subtitle">
                System Under Maintenance
            </h2>
            
            <div class="maintenance-message">
                <p>
                    <strong>عذراً للإزعاج!</strong> نحن نعمل حالياً على تحسين النظام وإضافة مميزات جديدة.
                </p>
                <p>
                    <strong>Sorry for the inconvenience!</strong> We are currently working on improving the system and adding new features.
                </p>
                <p>
                    سيعود النظام للعمل قريباً. شكراً لصبركم.
                    <br>
                    The system will be back online soon. Thank you for your patience.
                </p>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar"></div>
            </div>
            
            <div class="contact-info">
                <h5>
                    <i class="fas fa-headset me-2"></i>
                    تحتاج مساعدة؟ - Need Help?
                </h5>
                
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>+966-XX-XXX-XXXX</span>
                </div>
                
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <span>متاح 24/7 - Available 24/7</span>
                </div>
            </div>
            
            <a href="mailto:<EMAIL>" class="btn-home">
                <i class="fas fa-envelope me-2"></i>
                اتصل بالدعم - Contact Support
            </a>
        </div>
    </div>
    
    <script>
        // Auto refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes
        
        // Show current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            // You can add a time display element if needed
            console.log('Current time:', timeString);
        }
        
        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add click effect to the maintenance icon
            const icon = document.querySelector('.maintenance-icon');
            icon.addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'bounce 2s ease-in-out infinite';
                }, 100);
            });
            
            // Add hover effect to contact items
            const contactItems = document.querySelectorAll('.contact-item');
            contactItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });
        });
    </script>
</body>
</html>
