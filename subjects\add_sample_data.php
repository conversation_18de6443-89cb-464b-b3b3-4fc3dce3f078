<?php
/**
 * إضافة بيانات تجريبية للمواد
 * Add Sample Data for Subjects
 */

require_once '../includes/config.php';
require_once '../includes/database.php';

if (!$conn) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

echo "<h2>إضافة بيانات تجريبية للمواد</h2>";
echo "<hr>";

// التحقق من وجود مواد
$count_query = "SELECT COUNT(*) as count FROM subjects";
$count_result = $conn->query($count_query);
$current_count = $count_result->fetch_assoc()['count'];

echo "<p>عدد المواد الحالي: <strong>$current_count</strong></p>";

if ($current_count > 0) {
    echo "<p style='color: orange;'>⚠️ توجد مواد في النظام بالفعل. هل تريد إضافة المزيد؟</p>";
    echo "<p><a href='?add=yes' class='btn btn-warning'>نعم، أضف المزيد</a> ";
    echo "<a href='index.php' class='btn btn-secondary'>لا، العودة للقائمة</a></p>";
    
    if (!isset($_GET['add'])) {
        exit;
    }
}

// بيانات المواد التجريبية
$sample_subjects = [
    ['الرياضيات', 'MATH101', 'رياضيات', 'مادة الرياضيات الأساسية', 4, 1, 1],
    ['اللغة العربية', 'ARAB101', 'لغة عربية', 'مادة اللغة العربية', 5, 1, 1],
    ['العلوم', 'SCI101', 'علوم', 'مادة العلوم العامة', 3, 1, 1],
    ['اللغة الإنجليزية', 'ENG101', 'لغة إنجليزية', 'مادة اللغة الإنجليزية', 3, 1, 1],
    ['التربية الإسلامية', 'REL101', 'دين', 'مادة التربية الإسلامية', 2, 1, 1],
    
    ['الفيزياء', 'PHY201', 'فيزياء', 'مادة الفيزياء', 4, 2, 4],
    ['الكيمياء', 'CHEM201', 'كيمياء', 'مادة الكيمياء', 4, 2, 4],
    ['الأحياء', 'BIO201', 'أحياء', 'مادة الأحياء', 4, 2, 4],
    ['التاريخ', 'HIST201', 'تاريخ', 'مادة التاريخ', 2, 2, 4],
    ['الجغرافيا', 'GEO201', 'جغرافيا', 'مادة الجغرافيا', 2, 2, 4],
    
    ['الرياضيات المتقدمة', 'MATH301', 'رياضيات', 'رياضيات متقدمة للثانوية', 5, 3, 7],
    ['الفيزياء المتقدمة', 'PHY301', 'فيزياء', 'فيزياء متقدمة للثانوية', 5, 3, 7],
    ['الكيمياء المتقدمة', 'CHEM301', 'كيمياء', 'كيمياء متقدمة للثانوية', 5, 3, 7],
    ['الأحياء المتقدمة', 'BIO301', 'أحياء', 'أحياء متقدمة للثانوية', 5, 3, 7],
    ['الحاسوب', 'COMP101', 'حاسوب', 'مادة الحاسوب وتكنولوجيا المعلومات', 3, 1, 1],
];

$added = 0;
$errors = 0;

foreach ($sample_subjects as $subject_data) {
    list($name, $code, $department, $description, $credit_hours, $stage_id, $grade_id) = $subject_data;
    
    // التحقق من عدم وجود الكود مسبقاً
    $check_query = "SELECT id FROM subjects WHERE subject_code = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("s", $code);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<p style='color: orange;'>⚠️ المادة $name ($code) موجودة مسبقاً</p>";
        $errors++;
        continue;
    }
    
    // إدراج المادة
    $insert_query = "
        INSERT INTO subjects (
            subject_name, subject_code, department, description, 
            credit_hours, stage_id, grade_id, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())
    ";
    
    $insert_stmt = $conn->prepare($insert_query);
    $insert_stmt->bind_param("ssssiii", $name, $code, $department, $description, $credit_hours, $stage_id, $grade_id);
    
    if ($insert_stmt->execute()) {
        echo "<p style='color: green;'>✅ تم إضافة المادة: $name ($code)</p>";
        $added++;
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة المادة: $name - " . $conn->error . "</p>";
        $errors++;
    }
}

echo "<hr>";
echo "<h3>النتيجة النهائية:</h3>";
echo "<p><strong>تم إضافة:</strong> $added مادة</p>";
echo "<p><strong>الأخطاء:</strong> $errors</p>";

// عرض العدد الجديد
$new_count_result = $conn->query($count_query);
$new_count = $new_count_result->fetch_assoc()['count'];
echo "<p><strong>إجمالي المواد الآن:</strong> $new_count مادة</p>";

echo "<hr>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض صفحة المواد</a></p>";
echo "<p><a href='test_charts.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار الرسوم البيانية</a></p>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #333;
}

p {
    margin: 10px 0;
    padding: 5px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    margin: 5px;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}
</style>
