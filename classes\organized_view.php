<?php
/**
 * عرض منظم للفصول حسب المراحل الدراسية
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

// جلب الفصول مع المعلومات الإضافية
$classes_query = "
    SELECT 
        c.id,
        c.class_name,
        c.grade_level,
        c.capacity,
        c.room_number,
        c.status,
        COUNT(s.id) as student_count,
        COALESCE(c.grade_level, 'غير مصنف') as stage_name
    FROM classes c
    LEFT JOIN students s ON c.id = s.class_id
    GROUP BY c.id, c.class_name, c.grade_level, c.capacity, c.room_number, c.status
    ORDER BY 
        CASE 
            WHEN c.grade_level LIKE '%ما قبل رياض%' THEN 1
            WHEN c.grade_level LIKE '%رياض%' THEN 2
            WHEN c.grade_level LIKE '%KG%' THEN 3
            WHEN c.grade_level LIKE '%تمهيدي%' THEN 4
            WHEN c.grade_level LIKE '%ابتدائي%' THEN 5
            WHEN c.grade_level LIKE '%إعدادي%' OR c.grade_level LIKE '%متوسط%' THEN 6
            WHEN c.grade_level LIKE '%ثانوي%' THEN 7
            WHEN c.grade_level LIKE '%فني%' THEN 8
            ELSE 9
        END,
        c.class_name
";

$classes_result = $conn->query($classes_query);
$classes_data = $classes_result->fetch_all(MYSQLI_ASSOC);

// تجميع الفصول حسب المرحلة
$classes_by_stage = [];
foreach ($classes_data as $class) {
    $stage_key = determineStage($class['grade_level']);
    if (!isset($classes_by_stage[$stage_key])) {
        $classes_by_stage[$stage_key] = [];
    }
    $classes_by_stage[$stage_key][] = $class;
}

function determineStage($grade_level) {
    if (empty($grade_level)) return 'غير مصنف';
    
    $grade_lower = strtolower($grade_level);
    
    if (strpos($grade_lower, 'ما قبل رياض') !== false || 
        strpos($grade_lower, 'رياض') !== false || 
        strpos($grade_lower, 'kg') !== false || 
        strpos($grade_lower, 'تمهيدي') !== false) {
        return 'مرحلة الطفولة المبكرة';
    }
    
    if (strpos($grade_lower, 'ابتدائي') !== false) {
        return 'المرحلة الابتدائية';
    }
    
    if (strpos($grade_lower, 'إعدادي') !== false || strpos($grade_lower, 'متوسط') !== false) {
        return 'المرحلة الإعدادية';
    }
    
    if (strpos($grade_lower, 'ثانوي') !== false) {
        return 'المرحلة الثانوية';
    }
    
    if (strpos($grade_lower, 'فني') !== false) {
        return 'التعليم الفني';
    }
    
    return 'غير مصنف';
}

// ترتيب المراحل
$stage_order = [
    'مرحلة الطفولة المبكرة' => 1,
    'المرحلة الابتدائية' => 2,
    'المرحلة الإعدادية' => 3,
    'المرحلة الثانوية' => 4,
    'التعليم الفني' => 5,
    'غير مصنف' => 99
];

uksort($classes_by_stage, function($a, $b) use ($stage_order) {
    $order_a = $stage_order[$a] ?? 50;
    $order_b = $stage_order[$b] ?? 50;
    return $order_a - $order_b;
});
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفصول مرتبة حسب المراحل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stage-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
        }
        
        .class-card {
            transition: all 0.3s ease;
            border: 1px solid #e3e6f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .class-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #007bff;
        }
        
        .student-progress {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
        }
        
        .progress {
            height: 8px;
            border-radius: 10px;
        }
        
        .stage-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            font-size: 1.5em;
        }
        
        .quick-nav {
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        
        .nav-item {
            transition: all 0.3s ease;
        }
        
        .nav-item:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <!-- العنوان الرئيسي -->
        <div class="text-center mb-5">
            <h1 class="display-5 fw-bold text-primary">
                <i class="fas fa-school me-3"></i>
                الفصول الدراسية مرتبة حسب المراحل
            </h1>
            <p class="lead text-muted">عرض منظم وسهل للوصول إلى جميع الفصول</p>
        </div>

        <!-- التنقل السريع -->
        <div class="quick-nav mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-compass me-2"></i>التنقل السريع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <?php 
                        $stage_icons = [
                            'مرحلة الطفولة المبكرة' => 'fas fa-baby',
                            'المرحلة الابتدائية' => 'fas fa-child',
                            'المرحلة الإعدادية' => 'fas fa-user-graduate',
                            'المرحلة الثانوية' => 'fas fa-graduation-cap',
                            'التعليم الفني' => 'fas fa-tools',
                            'غير مصنف' => 'fas fa-question-circle'
                        ];
                        ?>
                        <?php foreach ($classes_by_stage as $stage_name => $classes): ?>
                            <div class="col-md-4 col-lg-2">
                                <a href="#stage-<?php echo md5($stage_name); ?>" class="text-decoration-none">
                                    <div class="nav-item card border-0 bg-light text-center p-3">
                                        <i class="<?php echo $stage_icons[$stage_name] ?? 'fas fa-school'; ?> fa-2x text-primary mb-2"></i>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($stage_name); ?></h6>
                                        <small class="text-muted"><?php echo count($classes); ?> فصل</small>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض المراحل والفصول -->
        <?php foreach ($classes_by_stage as $stage_name => $classes): ?>
            <div class="mb-5" id="stage-<?php echo md5($stage_name); ?>">
                <!-- عنوان المرحلة -->
                <div class="stage-header text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="stage-icon me-3">
                            <i class="<?php echo $stage_icons[$stage_name] ?? 'fas fa-school'; ?>"></i>
                        </div>
                        <div>
                            <h3 class="mb-1"><?php echo htmlspecialchars($stage_name); ?></h3>
                            <p class="mb-0 opacity-75"><?php echo count($classes); ?> فصل دراسي</p>
                        </div>
                    </div>
                </div>

                <!-- فصول المرحلة -->
                <?php if (empty($classes)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-school fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد فصول في هذه المرحلة</h5>
                        <p class="text-muted">يمكنك إضافة فصول جديدة من صفحة إدارة الفصول</p>
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إدارة الفصول
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($classes as $class): ?>
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card class-card h-100">
                                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0 fw-bold">
                                            <i class="fas fa-door-open me-2 text-primary"></i>
                                            <?php echo htmlspecialchars($class['class_name']); ?>
                                        </h6>
                                        <span class="badge bg-<?php echo $class['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $class['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <!-- معلومات الفصل -->
                                        <div class="mb-3">
                                            <?php if (!empty($class['grade_level'])): ?>
                                                <span class="badge bg-primary mb-2">
                                                    <i class="fas fa-layer-group me-1"></i>
                                                    <?php echo htmlspecialchars($class['grade_level']); ?>
                                                </span>
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($class['room_number'])): ?>
                                                <span class="badge bg-info mb-2 ms-1">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    غرفة <?php echo htmlspecialchars($class['room_number']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>

                                        <!-- إحصائيات الطلاب -->
                                        <div class="student-progress">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-users me-1"></i>الطلاب
                                                </small>
                                                <span class="fw-bold">
                                                    <?php echo $class['student_count']; ?> / <?php echo $class['capacity']; ?>
                                                </span>
                                            </div>
                                            <?php 
                                            $percentage = $class['capacity'] > 0 ? ($class['student_count'] / $class['capacity']) * 100 : 0;
                                            $progress_class = $percentage >= 90 ? 'bg-danger' : ($percentage >= 70 ? 'bg-warning' : 'bg-success');
                                            ?>
                                            <div class="progress mb-2">
                                                <div class="progress-bar <?php echo $progress_class; ?>" 
                                                     style="width: <?php echo min($percentage, 100); ?>%"></div>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo number_format($percentage, 1); ?>% ممتلئ
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-white border-top-0">
                                        <div class="d-flex gap-2">
                                            <a href="view.php?id=<?php echo $class['id']; ?>" class="btn btn-outline-primary btn-sm flex-fill">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </a>
                                            <a href="edit.php?id=<?php echo $class['id']; ?>" class="btn btn-outline-secondary btn-sm flex-fill">
                                                <i class="fas fa-edit me-1"></i>تعديل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>

        <!-- أزرار التنقل -->
        <div class="text-center mt-5">
            <a href="index.php" class="btn btn-primary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>العودة لصفحة الفصول الرئيسية
            </a>
            <a href="../students/index.php" class="btn btn-info btn-lg ms-2">
                <i class="fas fa-users me-2"></i>إدارة الطلاب
            </a>
        </div>
    </div>

    <!-- JavaScript للتنقل السلس -->
    <script>
        // تنقل سلس للروابط
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // تأثير hover للبطاقات
        document.querySelectorAll('.class-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
