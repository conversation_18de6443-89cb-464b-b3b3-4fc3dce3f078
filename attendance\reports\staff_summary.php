<?php
/**
 * تقرير ملخص حضور الموظفين
 * Staff Attendance Summary Report
 */

// جلب إحصائيات الحضور للمعلمين من الجدول الموحد
$teacher_stats_query = "
    SELECT
        u.id,
        u.full_name,
        u.role,
        COUNT(sa.id) as total_days,
        SUM(CASE WHEN sa.status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN sa.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN sa.status = 'late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN sa.status = 'sick_leave' THEN 1 ELSE 0 END) as sick_leave_days,
        SUM(CASE WHEN sa.status = 'regular_leave' THEN 1 ELSE 0 END) as regular_leave_days,
        SUM(CASE WHEN sa.status = 'absent_with_deduction' THEN 1 ELSE 0 END) as absence_with_deduction_days,
        ROUND((SUM(CASE WHEN sa.status = 'present' THEN 1 ELSE 0 END) / NULLIF(COUNT(sa.id), 0)) * 100, 2) as attendance_rate
    FROM users u
    LEFT JOIN staff_attendance sa ON u.id = sa.user_id AND sa.attendance_date BETWEEN ? AND ?
    WHERE u.role = 'teacher' AND u.status = 'active'
    AND u.role NOT IN ('admin', 'super_admin', 'system_admin')
    " . ($user_filter ? "AND u.id = ?" : "") . "
    GROUP BY u.id, u.full_name, u.role
    ORDER BY u.full_name
";

$teacher_params = [$date_from, $date_to];
$teacher_types = 'ss';
if ($user_filter) {
    $teacher_params[] = $user_filter;
    $teacher_types .= 'i';
}

$teacher_stmt = $conn->prepare($teacher_stats_query);
$teacher_stmt->bind_param($teacher_types, ...$teacher_params);
$teacher_stmt->execute();
$teacher_stats = $teacher_stmt->get_result();

// جلب إحصائيات الحضور للإداريين من الجدول الموحد (باستثناء المديرين)
$admin_stats_query = "
    SELECT
        u.id,
        u.full_name,
        u.role,
        COUNT(sa.id) as total_days,
        SUM(CASE WHEN sa.status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN sa.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN sa.status = 'late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN sa.status = 'sick_leave' THEN 1 ELSE 0 END) as sick_leave_days,
        SUM(CASE WHEN sa.status = 'regular_leave' THEN 1 ELSE 0 END) as regular_leave_days,
        SUM(CASE WHEN sa.status = 'absent_with_deduction' THEN 1 ELSE 0 END) as absence_with_deduction_days,
        ROUND((SUM(CASE WHEN sa.status = 'present' THEN 1 ELSE 0 END) / NULLIF(COUNT(sa.id), 0)) * 100, 2) as attendance_rate
    FROM users u
    LEFT JOIN staff_attendance sa ON u.id = sa.user_id AND sa.attendance_date BETWEEN ? AND ?
    WHERE u.role = 'staff' AND u.status = 'active'
    AND u.role NOT IN ('admin', 'super_admin', 'system_admin')
    " . ($user_filter ? "AND u.id = ?" : "") . "
    GROUP BY u.id, u.full_name, u.role
    ORDER BY u.full_name
";

$admin_params = [$date_from, $date_to];
$admin_types = 'ss';
if ($user_filter) {
    $admin_params[] = $user_filter;
    $admin_types .= 'i';
}

$admin_stmt = $conn->prepare($admin_stats_query);
$admin_stmt->bind_param($admin_types, ...$admin_params);
$admin_stmt->execute();
$admin_stats = $admin_stmt->get_result();

// جلب إحصائيات الإجازات
$leaves_stats_query = "
    SELECT 
        u.full_name,
        u.role,
        sl.leave_type,
        COUNT(*) as leave_count,
        SUM(sl.total_days) as total_leave_days
    FROM staff_leaves sl
    JOIN users u ON sl.user_id = u.id
    WHERE sl.status = 'approved' 
    AND ((sl.start_date BETWEEN ? AND ?) OR (sl.end_date BETWEEN ? AND ?) OR (sl.start_date <= ? AND sl.end_date >= ?))
    " . ($user_filter ? "AND u.id = ?" : "") . "
    GROUP BY u.id, u.full_name, u.role, sl.leave_type
    ORDER BY u.full_name, sl.leave_type
";

$leaves_params = [$date_from, $date_to, $date_from, $date_to, $date_from, $date_to];
$leaves_types = 'ssssss';
if ($user_filter) {
    $leaves_params[] = $user_filter;
    $leaves_types .= 'i';
}

$leaves_stmt = $conn->prepare($leaves_stats_query);
$leaves_stmt->bind_param($leaves_types, ...$leaves_params);
$leaves_stmt->execute();
$leaves_stats = $leaves_stmt->get_result();
?>

<div class="row">
    <!-- إحصائيات المعلمين -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chalkboard-teacher me-2"></i>
                    <?php echo __('teachers_attendance_summary'); ?>
                    (<?php echo date('d/m/Y', strtotime($date_from)); ?> - <?php echo date('d/m/Y', strtotime($date_to)); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo __('teacher_name'); ?></th>
                                <th><?php echo __('total_days'); ?></th>
                                <th><?php echo __('present'); ?></th>
                                <th><?php echo __('absent'); ?></th>
                                <th><?php echo __('late'); ?></th>
                                <th>إجازة مرضية</th>
                                <th>إجازة اعتيادية</th>
                                <th>غياب بالخصم</th>
                                <th><?php echo __('attendance_rate'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($teacher = $teacher_stats->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($teacher['full_name']); ?></strong>
                                    </td>
                                    <td><?php echo $teacher['total_days'] ?: 0; ?></td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?php echo $teacher['present_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $teacher['absent_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $teacher['late_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $teacher['sick_leave_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo $teacher['regular_leave_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $teacher['absence_with_deduction_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $rate = $teacher['attendance_rate'] ?: 0;
                                        $color = $rate >= 90 ? 'success' : ($rate >= 75 ? 'warning' : 'danger');
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?>">
                                            <?php echo $rate; ?>%
                                        </span>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الإداريين -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    <?php echo __('admins_attendance_summary'); ?>
                    (<?php echo date('d/m/Y', strtotime($date_from)); ?> - <?php echo date('d/m/Y', strtotime($date_to)); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الموظف</th>
                                <th><?php echo __('total_days'); ?></th>
                                <th><?php echo __('present'); ?></th>
                                <th><?php echo __('absent'); ?></th>
                                <th><?php echo __('late'); ?></th>
                                <th>إجازة مرضية</th>
                                <th>إجازة اعتيادية</th>
                                <th>غياب بالخصم</th>
                                <th><?php echo __('attendance_rate'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($admin = $admin_stats->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($admin['full_name']); ?></strong>
                                    </td>
                                    <td><?php echo $admin['total_days'] ?: 0; ?></td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?php echo $admin['present_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $admin['absent_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $admin['late_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $admin['sick_leave_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo $admin['regular_leave_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $admin['absence_with_deduction_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $rate = $admin['attendance_rate'] ?: 0;
                                        $color = $rate >= 90 ? 'success' : ($rate >= 75 ? 'warning' : 'danger');
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?>">
                                            <?php echo $rate; ?>%
                                        </span>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الإجازات -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-times me-2"></i>
                    <?php echo __('leaves_summary'); ?>
                    (<?php echo date('d/m/Y', strtotime($date_from)); ?> - <?php echo date('d/m/Y', strtotime($date_to)); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo __('staff_member'); ?></th>
                                <th><?php echo __('role'); ?></th>
                                <th><?php echo __('leave_type'); ?></th>
                                <th><?php echo __('leave_count'); ?></th>
                                <th><?php echo __('total_leave_days'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($leave = $leaves_stats->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($leave['full_name']); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $leave['role'] === 'teacher' ? 'primary' : 'success'; ?>">
                                            <?php echo __($leave['role']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $leave['leave_type'] === 'sick' ? 'danger' : 'warning'; ?>">
                                            <?php echo __($leave['leave_type'] . '_leave'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $leave['leave_count']; ?></td>
                                    <td><?php echo $leave['total_leave_days']; ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
