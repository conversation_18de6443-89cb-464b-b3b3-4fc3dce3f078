-- إصلاح مشاكل عرض نظام الغياب والحضور
-- Fix absence and attendance display issues

USE school_management;

-- 1. إعادة إنشاء View الشامل بدون تكرار
DROP VIEW IF EXISTS staff_absences_comprehensive;

CREATE VIEW staff_absences_comprehensive AS
SELECT 
    usa.id,
    usa.user_id,
    usa.absence_date,
    usa.end_date,
    usa.absence_type,
    usa.absence_category,
    usa.days_count,
    usa.hours_count,
    usa.reason,
    usa.medical_certificate,
    usa.has_deduction,
    usa.deduction_type,
    usa.deduction_amount,
    usa.status,
    usa.applied_by,
    usa.approved_by,
    usa.approved_at,
    usa.rejected_by,
    usa.rejected_at,
    usa.rejection_reason,
    usa.replacement_user_id,
    usa.replacement_notes,
    usa.hr_notes,
    usa.manager_notes,
    usa.created_at,
    usa.updated_at,
    u.full_name as user_name,
    u.role as user_role,
    u.email as user_email,
    applied_user.full_name as applied_by_name,
    approved_user.full_name as approved_by_name,
    rejected_user.full_name as rejected_by_name,
    replacement_user.full_name as replacement_name,
    -- ترجمة نوع الغياب/الإجازة بدون تكرار
    CASE 
        WHEN usa.absence_category = 'absence' AND usa.absence_type = 'sick' THEN 'غياب مرضي'
        WHEN usa.absence_category = 'absence' AND usa.absence_type = 'personal' THEN 'غياب شخصي'
        WHEN usa.absence_category = 'absence' AND usa.absence_type = 'emergency' THEN 'غياب طارئ'
        WHEN usa.absence_category = 'absence' AND usa.absence_type = 'unauthorized' THEN 'غياب غير مبرر'
        WHEN usa.absence_category = 'leave' AND usa.absence_type = 'sick' THEN 'إجازة مرضية'
        WHEN usa.absence_category = 'leave' AND usa.absence_type = 'annual' THEN 'إجازة سنوية'
        WHEN usa.absence_category = 'leave' AND usa.absence_type = 'maternity' THEN 'إجازة أمومة'
        WHEN usa.absence_category = 'leave' AND usa.absence_type = 'paternity' THEN 'إجازة أبوة'
        WHEN usa.absence_category = 'leave' AND usa.absence_type = 'study' THEN 'إجازة دراسية'
        WHEN usa.absence_category = 'leave' AND usa.absence_type = 'unpaid' THEN 'إجازة بدون راتب'
        ELSE CONCAT(usa.absence_type, ' - ', usa.absence_category)
    END as absence_type_ar,
    -- ترجمة تصنيف الغياب/الإجازة
    CASE usa.absence_category
        WHEN 'absence' THEN 'غياب'
        WHEN 'leave' THEN 'إجازة'
        ELSE usa.absence_category
    END as absence_category_ar,
    -- ترجمة الحالة
    CASE usa.status
        WHEN 'pending' THEN 'معلق'
        WHEN 'approved' THEN 'مُعتمد'
        WHEN 'rejected' THEN 'مرفوض'
        WHEN 'cancelled' THEN 'ملغي'
        ELSE usa.status
    END as status_ar,
    -- حساب الأيام
    COALESCE(usa.days_count, DATEDIFF(COALESCE(usa.end_date, usa.absence_date), usa.absence_date) + 1) as calculated_days,
    -- ترجمة وجود خصم
    CASE usa.has_deduction WHEN 1 THEN 'نعم' ELSE 'لا' END as has_deduction_ar,
    -- أوقات الحضور (NULL للغياب بالخصم)
    CASE 
        WHEN usa.has_deduction = 1 AND usa.absence_category = 'absence' THEN NULL
        ELSE sa.check_in_time
    END as check_in_time,
    CASE 
        WHEN usa.has_deduction = 1 AND usa.absence_category = 'absence' THEN NULL
        ELSE sa.check_out_time
    END as check_out_time
FROM unified_staff_absences usa
LEFT JOIN users u ON usa.user_id = u.id
LEFT JOIN users applied_user ON usa.applied_by = applied_user.id
LEFT JOIN users approved_user ON usa.approved_by = approved_user.id
LEFT JOIN users rejected_user ON usa.rejected_by = rejected_user.id
LEFT JOIN users replacement_user ON usa.replacement_user_id = replacement_user.id
LEFT JOIN staff_attendance sa ON usa.user_id = sa.user_id AND usa.absence_date = sa.attendance_date;

-- 2. إنشاء View للحضور والغياب الموحد
DROP VIEW IF EXISTS unified_attendance_view;

CREATE VIEW unified_attendance_view AS
SELECT 
    u.id as user_id,
    u.full_name as user_name,
    u.role,
    CURDATE() as attendance_date,
    -- تحديد حالة الإجازة
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM unified_staff_absences usa 
            WHERE usa.user_id = u.id 
            AND usa.absence_date = CURDATE() 
            AND usa.absence_category = 'leave' 
            AND usa.status = 'approved'
        ) THEN 'في إجازة'
        ELSE 'متاح'
    END as leave_status,
    -- تحديد حالة الحضور
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM unified_staff_absences usa 
            WHERE usa.user_id = u.id 
            AND usa.absence_date = CURDATE() 
            AND usa.absence_category = 'absence'
            AND usa.has_deduction = 1
        ) THEN 'غياب بالخصم'
        WHEN EXISTS (
            SELECT 1 FROM staff_attendance sa 
            WHERE sa.user_id = u.id 
            AND sa.attendance_date = CURDATE() 
            AND sa.status = 'present'
        ) THEN 'حاضر'
        WHEN EXISTS (
            SELECT 1 FROM staff_attendance sa 
            WHERE sa.user_id = u.id 
            AND sa.attendance_date = CURDATE() 
            AND sa.status = 'absent'
        ) THEN 'غائب'
        ELSE 'غير محدد'
    END as attendance_status,
    -- أوقات الحضور
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM unified_staff_absences usa 
            WHERE usa.user_id = u.id 
            AND usa.absence_date = CURDATE() 
            AND usa.absence_category = 'absence'
            AND usa.has_deduction = 1
        ) THEN NULL
        ELSE (
            SELECT sa.check_in_time 
            FROM staff_attendance sa 
            WHERE sa.user_id = u.id 
            AND sa.attendance_date = CURDATE() 
            LIMIT 1
        )
    END as check_in_time,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM unified_staff_absences usa 
            WHERE usa.user_id = u.id 
            AND usa.absence_date = CURDATE() 
            AND usa.absence_category = 'absence'
            AND usa.has_deduction = 1
        ) THEN NULL
        ELSE (
            SELECT sa.check_out_time 
            FROM staff_attendance sa 
            WHERE sa.user_id = u.id 
            AND sa.attendance_date = CURDATE() 
            LIMIT 1
        )
    END as check_out_time,
    -- ملاحظات
    COALESCE(
        (SELECT usa.reason 
         FROM unified_staff_absences usa 
         WHERE usa.user_id = u.id 
         AND usa.absence_date = CURDATE() 
         LIMIT 1),
        (SELECT sa.notes 
         FROM staff_attendance sa 
         WHERE sa.user_id = u.id 
         AND sa.attendance_date = CURDATE() 
         LIMIT 1),
        '-'
    ) as notes
FROM users u
WHERE u.role IN ('teacher', 'staff', 'admin')
AND u.status = 'active';

-- 3. إنشاء إجراء لتسجيل غياب بالخصم
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS record_absence_with_deduction(
    IN p_user_id INT,
    IN p_absence_date DATE,
    IN p_absence_type VARCHAR(20),
    IN p_reason TEXT,
    IN p_deduction_amount DECIMAL(10,2),
    IN p_applied_by INT
)
BEGIN
    DECLARE absence_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من عدم وجود سجل مسبق
    SELECT COUNT(*) INTO absence_exists 
    FROM unified_staff_absences 
    WHERE user_id = p_user_id AND absence_date = p_absence_date;
    
    IF absence_exists > 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'يوجد سجل غياب مسبق لهذا التاريخ';
    END IF;
    
    -- إدراج سجل الغياب
    INSERT INTO unified_staff_absences (
        user_id, absence_date, absence_type, absence_category, 
        days_count, reason, has_deduction, deduction_amount, 
        status, applied_by, created_at
    ) VALUES (
        p_user_id, p_absence_date, p_absence_type, 'absence',
        1, p_reason, 1, p_deduction_amount,
        'approved', p_applied_by, NOW()
    );
    
    -- تحديث/إدراج سجل الحضور
    INSERT INTO staff_attendance (
        user_id, attendance_date, status, check_in_time, check_out_time, notes
    ) VALUES (
        p_user_id, p_absence_date, 'absent', NULL, NULL, 
        CONCAT('غياب بالخصم - ', p_absence_type, ' - خصم: ', p_deduction_amount, ' ريال')
    ) ON DUPLICATE KEY UPDATE
        status = 'absent',
        check_in_time = NULL,
        check_out_time = NULL,
        notes = CONCAT('غياب بالخصم - ', p_absence_type, ' - خصم: ', p_deduction_amount, ' ريال');
    
    COMMIT;
    
    SELECT 'تم تسجيل الغياب بالخصم بنجاح' as message;
END//

DELIMITER ;

-- 4. إدراج بيانات تجريبية
-- إنشاء مستخدم سحر سمير إذا لم يكن موجوداً
INSERT IGNORE INTO users (username, email, password, full_name, role, status) 
VALUES ('sahar.samir', '<EMAIL>', 'password', 'سحر سمير', 'staff', 'active');

-- الحصول على ID المستخدم
SET @sahar_id = (SELECT id FROM users WHERE full_name = 'سحر سمير' LIMIT 1);

-- إدراج سجل حضور عادي لأمس
INSERT IGNORE INTO staff_attendance (user_id, attendance_date, status, check_in_time, check_out_time, notes) 
VALUES (@sahar_id, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'present', '08:00:00', '16:00:00', 'حضور عادي');

-- إدراج سجل غياب بالخصم لليوم
CALL record_absence_with_deduction(@sahar_id, CURDATE(), 'personal', 'غياب شخصي للاختبار', 50.00, @sahar_id);

-- 5. تحديث إعدادات الخصومات
INSERT INTO deduction_settings (absence_type, deduction_value, deduction_type, description) VALUES
('unauthorized', 100.00, 'fixed', 'غياب غير مبرر - خصم ثابت'),
('personal', 50.00, 'fixed', 'غياب شخصي - خصم ثابت'),
('emergency', 25.00, 'fixed', 'غياب طارئ - خصم مخفف'),
('sick', 0.00, 'fixed', 'غياب مرضي - بدون خصم مع تقرير طبي')
ON DUPLICATE KEY UPDATE
deduction_value = VALUES(deduction_value),
description = VALUES(description);

-- عرض النتائج
SELECT 'تم إصلاح مشاكل نظام الغياب والحضور بنجاح!' as status;

-- عرض البيانات المحدثة
SELECT 
    user_name,
    role,
    leave_status,
    attendance_status,
    check_in_time,
    check_out_time,
    notes
FROM unified_attendance_view
WHERE user_name LIKE '%سحر%'
ORDER BY user_name;
