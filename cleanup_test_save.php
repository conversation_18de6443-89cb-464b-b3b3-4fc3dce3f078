<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

$test_date = date('Y-m-d');
$conn->query("DELETE FROM staff_absences_with_deduction WHERE reason = 'تم تسجيله من نظام الحضور' AND absence_date = '$test_date'");
$conn->query("DELETE FROM staff_attendance WHERE notes = 'اختبار' AND attendance_date = '$test_date'");
$conn->query("DELETE FROM staff_attendance WHERE status = 'absent_with_deduction' AND attendance_date = '$test_date' AND notes IS NULL");

echo json_encode(['success' => true, 'message' => 'تم حذف السجلات التجريبية']);

if (file_exists('test_save_attendance.php')) {
    unlink('test_save_attendance.php');
}
if (file_exists('cleanup_test_save.php')) {
    unlink('cleanup_test_save.php');
}
?>
