<?php
/**
 * حذف فئة مصروفات
 * Delete Expense Category
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';
$category_id = intval($_GET['id'] ?? 0);

// التحقق من وجود الفئة
if ($category_id <= 0) {
    header('Location: categories.php');
    exit();
}

// جلب بيانات الفئة
$category_query = $conn->prepare("SELECT * FROM expense_categories WHERE id = ?");
$category_query->bind_param("i", $category_id);
$category_query->execute();
$category = $category_query->get_result()->fetch_assoc();

if (!$category) {
    header('Location: categories.php');
    exit();
}

// التحقق من وجود مصروفات مرتبطة
$expenses_check = $conn->prepare("SELECT COUNT(*) as count FROM daily_expenses WHERE category_id = ?");
$expenses_check->bind_param("i", $category_id);
$expenses_check->execute();
$expenses_count = $expenses_check->get_result()->fetch_assoc()['count'];

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirm_delete'])) {
    if ($expenses_count > 0) {
        $error_message = "لا يمكن حذف هذه الفئة لأنها مرتبطة بـ $expenses_count مصروف";
    } else {
        $delete_stmt = $conn->prepare("DELETE FROM expense_categories WHERE id = ?");
        $delete_stmt->bind_param("i", $category_id);
        
        if ($delete_stmt->execute()) {
            header('Location: categories.php?deleted=1');
            exit();
        } else {
            $error_message = 'فشل في حذف الفئة';
        }
    }
}

$page_title = 'حذف الفئة: ' . $category['category_name'];
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-danger">
                <i class="fas fa-trash me-2"></i>حذف الفئة: <?php echo htmlspecialchars($category['category_name']); ?>
            </h1>
            <p class="text-muted mb-0">تأكيد حذف فئة المصروفات</p>
        </div>
        <div>
            <a href="categories.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للفئات
            </a>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <?php if ($expenses_count > 0): ?>
                <!-- لا يمكن الحذف -->
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>لا يمكن حذف هذه الفئة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>السبب:</h6>
                            <p class="mb-0">
                                هذه الفئة مرتبطة بـ <strong><?php echo $expenses_count; ?></strong> مصروف. 
                                لا يمكن حذف الفئات التي تحتوي على مصروفات مسجلة.
                            </p>
                        </div>
                        
                        <h6>البدائل المتاحة:</h6>
                        <ul>
                            <li><strong>إلغاء تفعيل الفئة:</strong> يمكنك إلغاء تفعيل الفئة بدلاً من حذفها</li>
                            <li><strong>تعديل الفئة:</strong> غيّر اسم أو خصائص الفئة لاستخدامها لغرض آخر</li>
                        </ul>
                        
                        <div class="mt-4">
                            <a href="categories.php" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>العودة للفئات
                            </a>
                            <a href="edit_category.php?id=<?php echo $category_id; ?>" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>تعديل الفئة بدلاً من الحذف
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- يمكن الحذف -->
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>تأكيد الحذف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h6>تحذير مهم:</h6>
                            <p class="mb-0">
                                سيتم حذف فئة "<strong><?php echo htmlspecialchars($category['category_name']); ?></strong>" نهائياً.
                                <br><strong>هذا الإجراء لا يمكن التراجع عنه!</strong>
                            </p>
                        </div>
                        
                        <p>هل أنت متأكد من أنك تريد حذف هذه الفئة؟</p>
                        
                        <form method="POST" class="mt-4">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="categories.php" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" name="confirm_delete" class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i>نعم، احذف الفئة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="col-md-4">
            <!-- معلومات الفئة -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="card border">
                        <div class="card-header d-flex align-items-center" style="background-color: <?php echo $category['color']; ?>; color: white;">
                            <i class="<?php echo $category['icon']; ?> me-2"></i>
                            <h6 class="mb-0"><?php echo htmlspecialchars($category['category_name']); ?></h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text text-muted small">
                                <?php echo htmlspecialchars($category['description'] ?: 'لا يوجد وصف'); ?>
                            </p>
                            
                            <?php if ($category['daily_limit'] || $category['monthly_limit']): ?>
                                <div class="alert alert-info py-2">
                                    <small>
                                        <?php if ($category['daily_limit']): ?>
                                            <i class="fas fa-calendar-day me-1"></i>يومي: <?php echo format_currency($category['daily_limit']); ?>
                                        <?php endif; ?>
                                        <?php if ($category['monthly_limit']): ?>
                                            <br><i class="fas fa-calendar-alt me-1"></i>شهري: <?php echo format_currency($category['monthly_limit']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الفئة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <?php
                    $stats_query = $conn->prepare("
                        SELECT 
                            COUNT(*) as expenses_count,
                            COALESCE(SUM(amount), 0) as total_amount
                        FROM daily_expenses 
                        WHERE category_id = ?
                    ");
                    $stats_query->bind_param("i", $category_id);
                    $stats_query->execute();
                    $stats = $stats_query->get_result()->fetch_assoc();
                    ?>
                    
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h5 class="<?php echo ($stats['expenses_count'] > 0) ? 'text-danger' : 'text-success'; ?> mb-0">
                                <?php echo $stats['expenses_count']; ?>
                            </h5>
                            <small class="text-muted">إجمالي المصروفات</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h5 class="text-primary mb-0"><?php echo format_currency($stats['total_amount']); ?></h5>
                            <small class="text-muted">إجمالي المبلغ</small>
                        </div>
                    </div>
                    
                    <?php if ($stats['expenses_count'] > 0): ?>
                        <div class="alert alert-warning mt-3">
                            <small>
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                لا يمكن حذف الفئة لوجود مصروفات مرتبطة بها
                            </small>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success mt-3">
                            <small>
                                <i class="fas fa-check-circle me-1"></i>
                                يمكن حذف الفئة بأمان (لا توجد مصروفات مرتبطة)
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<style>
.card {
    transition: all 0.3s ease;
}

.alert {
    border: none;
    border-radius: 10px;
}

.text-danger {
    color: #dc3545 !important;
}

.text-success {
    color: #28a745 !important;
}
</style>
