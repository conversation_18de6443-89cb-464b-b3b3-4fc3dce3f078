<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// الحصول على المراحل الدراسية
$stages_query = "SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = [];
if ($stages_result) {
    while ($row = $stages_result->fetch_assoc()) {
        $stages[] = $row;
    }
}

// الحصول على الصفوف الدراسية
$grades_query = "
    SELECT g.id, g.grade_name, g.grade_code, g.stage_id, es.stage_name
    FROM grades g
    INNER JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order, g.sort_order
";
$grades_result = $conn->query($grades_query);
$grades = [];
if ($grades_result) {
    while ($row = $grades_result->fetch_assoc()) {
        $grades[] = $row;
    }
}

// الحصول على المعاملات من URL
$selected_stage_id = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;
$selected_grade_id = isset($_GET['grade_id']) ? intval($_GET['grade_id']) : 0;

// معالجة إضافة الفصل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $class_name = clean_input($_POST['class_name'] ?? '');
    $grade_level = clean_input($_POST['grade_level'] ?? '');
    $stage_id = intval($_POST['stage_id'] ?? 0);
    $grade_id = intval($_POST['grade_id'] ?? 0);
    $capacity = intval($_POST['capacity'] ?? 0);
    $section = clean_input($_POST['section'] ?? '');
    $room_number = clean_input($_POST['room_number'] ?? '');
    $description = clean_input($_POST['description'] ?? '');
    $status = 'active';

    $errors = [];
    if (empty($class_name)) {
        $errors[] = __('class_name') . ' ' . __('required_field');
    }
    if (empty($grade_level)) {
        $errors[] = __('grade_level') . ' ' . __('required_field');
    }
    if ($stage_id <= 0) {
        $errors[] = __('stage') . ' ' . __('required_field');
    }
    if ($grade_id <= 0) {
        $errors[] = __('grade') . ' ' . __('required_field');
    }
    if ($capacity <= 0) {
        $errors[] = __('capacity') . ' ' . __('required_field');
    }

    if (empty($errors)) {
        global $conn;

        // التحقق من وجود عمود description
        $check_column_query = "SHOW COLUMNS FROM classes LIKE 'description'";
        $check_result = $conn->query($check_column_query);
        $has_description = $check_result && $check_result->num_rows > 0;

        if ($has_description) {
            // إذا كان العمود موجوداً، استخدم الاستعلام الكامل
            $stmt = $conn->prepare("INSERT INTO classes (class_name, grade_level, stage_id, grade_id, capacity, section, room_number, description, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("ssiiiisss", $class_name, $grade_level, $stage_id, $grade_id, $capacity, $section, $room_number, $description, $status);
        } else {
            // إذا لم يكن العمود موجوداً، استخدم استعلام بدون description
            $stmt = $conn->prepare("INSERT INTO classes (class_name, grade_level, stage_id, grade_id, capacity, section, room_number, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("ssiiiiss", $class_name, $grade_level, $stage_id, $grade_id, $capacity, $section, $room_number, $status);
        }

        if ($stmt->execute()) {
            $_SESSION['success_message'] = __('class_added_successfully');
            header('Location: index.php');
            exit();
        } else {
            $error_message = __('error_occurred') . ': ' . $stmt->error;
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

// تضمين header بعد معالجة النموذج
$page_title = __('add_class');
require_once '../includes/header.php';
?>
<link rel="stylesheet" href="../assets/css/modern-style.css">
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('add_class'); ?></h1>
            <p class="text-muted"><?php echo __('add_new_class_info'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-school me-2"></i><?php echo __('class_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="class_name" class="form-label"><?php echo __('class_name'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="class_name" name="class_name" value="<?php echo htmlspecialchars($_POST['class_name'] ?? ''); ?>" required>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="stage_id" class="form-label"><?php echo __('educational_stage'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="stage_id" name="stage_id" required onchange="updateGrades()">
                            <option value=""><?php echo __('select_stage'); ?></option>
                            <?php foreach ($stages as $stage): ?>
                                <option value="<?php echo $stage['id']; ?>"
                                        <?php echo (isset($_POST['stage_id']) ? $_POST['stage_id'] : $selected_stage_id) == $stage['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($stage['stage_name']); ?> (<?php echo htmlspecialchars($stage['stage_code']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="grade_id" class="form-label"><?php echo __('school_grade'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="grade_id" name="grade_id" required>
                            <option value=""><?php echo __('select_grade_first'); ?></option>
                            <?php foreach ($grades as $grade): ?>
                                <option value="<?php echo $grade['id']; ?>"
                                        data-stage="<?php echo $grade['stage_id']; ?>"
                                        <?php echo (isset($_POST['grade_id']) ? $_POST['grade_id'] : $selected_grade_id) == $grade['id'] ? 'selected' : ''; ?>
                                        style="display: none;">
                                    <?php echo htmlspecialchars($grade['grade_name']); ?> (<?php echo htmlspecialchars($grade['grade_code']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        <div class="form-text"><?php echo __('select_stage_first'); ?></div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="grade_level" class="form-label"><?php echo __('grade_level'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="grade_level" name="grade_level" value="<?php echo htmlspecialchars($_POST['grade_level'] ?? ''); ?>" required>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="section" class="form-label"><?php echo __('section'); ?></label>
                        <input type="text" class="form-control" id="section" name="section" value="<?php echo htmlspecialchars($_POST['section'] ?? ''); ?>" placeholder="<?php echo __('optional'); ?>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="capacity" class="form-label"><?php echo __('capacity'); ?> <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="capacity" name="capacity" value="<?php echo htmlspecialchars($_POST['capacity'] ?? '30'); ?>" required min="1" max="100">
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="room_number" class="form-label"><?php echo __('room_number'); ?></label>
                        <input type="text" class="form-control" id="room_number" name="room_number" value="<?php echo htmlspecialchars($_POST['room_number'] ?? ''); ?>" placeholder="<?php echo __('optional'); ?>">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label"><?php echo __('description'); ?></label>
                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="<?php echo __('optional'); ?>"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                </div>
                <div class="d-flex justify-content-end gap-2">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i><?php echo __('add_class'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateGrades() {
    const stageSelect = document.getElementById('stage_id');
    const gradeSelect = document.getElementById('grade_id');
    const selectedStage = stageSelect.value;

    // إخفاء جميع الخيارات
    const gradeOptions = gradeSelect.querySelectorAll('option[data-stage]');
    gradeOptions.forEach(option => {
        option.style.display = 'none';
        option.selected = false;
    });

    // إعادة تعيين القيمة
    gradeSelect.value = '';

    if (selectedStage) {
        // إظهار الصفوف المرتبطة بالمرحلة المختارة
        const relevantOptions = gradeSelect.querySelectorAll(`option[data-stage="${selectedStage}"]`);
        relevantOptions.forEach(option => {
            option.style.display = 'block';
        });
    }
}

// تشغيل التحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrades();
});
</script>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.forEach.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
<?php require_once '../includes/footer.php'; ?> 