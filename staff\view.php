<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// الحصول على معرف الإداري
$staff_id = intval($_GET['id'] ?? 0);
if ($staff_id <= 0) {
    $_SESSION['error_message'] = 'معرف الإداري غير صالح';
    header('Location: index.php');
    exit();
}

// جلب بيانات الإداري
$query = "
    SELECT 
        s.*,
        u.username,
        u.email,
        u.status as user_status,
        u.last_login,
        u.created_at as user_created_at,
        COALESCE(u.full_name, u.username) as display_name
    FROM staff s
    JOIN users u ON s.user_id = u.id
    WHERE s.id = ?
";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error_message'] = 'الإداري غير موجود';
    header('Location: index.php');
    exit();
}

$staff = $result->fetch_assoc();

// تحميل ملف اللغة
load_language();
$page_title = 'عرض بيانات الإداري - ' . $staff['display_name'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">عرض بيانات الإداري</h1>
                    <p class="text-muted"><?php echo htmlspecialchars($staff['display_name']); ?></p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                    <a href="edit.php?id=<?php echo $staff['id']; ?>" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Personal Information -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>المعلومات الشخصية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 text-center mb-3">
                                    <?php if (!empty($staff['profile_picture'])): ?>
                                        <img src="<?php echo SYSTEM_URL; ?>/uploads/profiles/<?php echo htmlspecialchars($staff['profile_picture']); ?>" 
                                             class="rounded-circle" width="100" height="100" alt="Profile Picture">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                                             style="width: 100px; height: 100px;">
                                            <i class="fas fa-user fa-3x text-white"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">الاسم الكامل:</td>
                                    <td><?php echo htmlspecialchars($staff['display_name']); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الجنس:</td>
                                    <td>
                                        <?php 
                                        echo $staff['gender'] === 'male' ? 'ذكر' : 
                                            ($staff['gender'] === 'female' ? 'أنثى' : 'غير محدد'); 
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ الميلاد:</td>
                                    <td>
                                        <?php 
                                        if (!empty($staff['date_of_birth']) && $staff['date_of_birth'] !== '0000-00-00') {
                                            echo date('Y-m-d', strtotime($staff['date_of_birth']));
                                        } else {
                                            echo '<span class="text-muted">غير محدد</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الرقم القومي:</td>
                                    <td><?php echo !empty($staff['national_id']) ? htmlspecialchars($staff['national_id']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الجنسية:</td>
                                    <td><?php echo !empty($staff['nationality']) ? htmlspecialchars($staff['nationality']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">رقم الهاتف:</td>
                                    <td><?php echo !empty($staff['phone']) ? htmlspecialchars($staff['phone']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">العنوان:</td>
                                    <td><?php echo !empty($staff['address']) ? htmlspecialchars($staff['address']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Job Information -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-briefcase me-2"></i>المعلومات الوظيفية
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">رقم الموظف:</td>
                                    <td>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($staff['employee_id'] ?? 'غير محدد'); ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">القسم:</td>
                                    <td><?php echo !empty($staff['department']) ? htmlspecialchars($staff['department']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">المنصب:</td>
                                    <td><?php echo !empty($staff['position']) ? htmlspecialchars($staff['position']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ التوظيف:</td>
                                    <td>
                                        <?php 
                                        if (!empty($staff['hire_date']) && $staff['hire_date'] !== '0000-00-00') {
                                            echo date('Y-m-d', strtotime($staff['hire_date']));
                                        } else {
                                            echo '<span class="text-muted">غير محدد</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الراتب:</td>
                                    <td>
                                        <?php 
                                        if (!empty($staff['salary']) && $staff['salary'] > 0) {
                                            echo number_format($staff['salary'], 2) . ' جنيه';
                                        } else {
                                            echo '<span class="text-muted">غير محدد</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">رقم الحساب البنكي:</td>
                                    <td><?php echo !empty($staff['bank_account']) ? htmlspecialchars($staff['bank_account']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">حالة الوظيفة:</td>
                                    <td>
                                        <?php
                                        $status_class = match($staff['status']) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-secondary',
                                            'on_leave' => 'bg-warning',
                                            'terminated' => 'bg-danger',
                                            default => 'bg-secondary'
                                        };
                                        $status_text = match($staff['status']) {
                                            'active' => 'نشط',
                                            'inactive' => 'غير نشط',
                                            'on_leave' => 'في إجازة',
                                            'terminated' => 'منتهي الخدمة',
                                            default => 'غير محدد'
                                        };
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Account Information -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-key me-2"></i>معلومات الحساب
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">اسم المستخدم:</td>
                                    <td><?php echo htmlspecialchars($staff['username']); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">البريد الإلكتروني:</td>
                                    <td><?php echo htmlspecialchars($staff['email']); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">حالة الحساب:</td>
                                    <td>
                                        <?php
                                        $user_status_class = match($staff['user_status']) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-secondary',
                                            'suspended' => 'bg-warning',
                                            default => 'bg-secondary'
                                        };
                                        $user_status_text = match($staff['user_status']) {
                                            'active' => 'نشط',
                                            'inactive' => 'غير نشط',
                                            'suspended' => 'معلق',
                                            default => 'غير محدد'
                                        };
                                        ?>
                                        <span class="badge <?php echo $user_status_class; ?>"><?php echo $user_status_text; ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">آخر تسجيل دخول:</td>
                                    <td>
                                        <?php 
                                        if (!empty($staff['last_login'])) {
                                            echo date('Y-m-d H:i:s', strtotime($staff['last_login']));
                                        } else {
                                            echo '<span class="text-muted">لم يسجل دخول من قبل</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ إنشاء الحساب:</td>
                                    <td><?php echo date('Y-m-d H:i:s', strtotime($staff['user_created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-phone me-2"></i>جهة الاتصال في حالات الطوارئ
                            </h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">اسم جهة الاتصال:</td>
                                    <td><?php echo !empty($staff['emergency_contact_name']) ? htmlspecialchars($staff['emergency_contact_name']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">رقم الهاتف:</td>
                                    <td><?php echo !empty($staff['emergency_contact_phone']) ? htmlspecialchars($staff['emergency_contact_phone']) : '<span class="text-muted">غير محدد</span>'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
