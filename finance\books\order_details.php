<?php
/**
 * صفحة تفاصيل طلب الكتب
 * Book Order Details Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$order_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if (!$order_id) {
    header('Location: orders.php');
    exit();
}

// Fetch order details
$order_stmt = $conn->prepare("
    SELECT 
        sbo.id, sbo.order_date, sbo.total_amount, sbo.status,
        s.national_id, u.full_name as student_name,
        c.class_name
    FROM student_book_orders sbo
    JOIN students s ON sbo.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE sbo.id = ?
");
$order_stmt->bind_param('i', $order_id);
$order_stmt->execute();
$order = $order_stmt->get_result()->fetch_assoc();

if (!$order) {
    header('Location: orders.php');
    exit();
}

// Fetch order items
$items_stmt = $conn->prepare("
    SELECT 
        sboi.quantity, sboi.price_per_unit,
        b.book_title
    FROM student_book_order_items sboi
    JOIN books b ON sboi.book_id = b.id
    WHERE sboi.order_id = ?
");
$items_stmt->bind_param('i', $order_id);
$items_stmt->execute();
$items_result = $items_stmt->get_result();

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('order_details'); ?> #<?php echo $order['id']; ?></h1>
        </div>
        <div>
            <a href="orders.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_orders'); ?>
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h5><?php echo __('order_summary'); ?></h5>
        </div>
        <div class="card-body">
            <p><strong><?php echo __('student_name'); ?>:</strong> <?php echo htmlspecialchars($order['student_name']); ?></p>
            <p><strong><?php echo __('national_id'); ?>:</strong> <?php echo htmlspecialchars($order['national_id']); ?></p>
            <p><strong><?php echo __('class'); ?>:</strong> <?php echo htmlspecialchars($order['class_name']); ?></p>
            <p><strong><?php echo __('order_date'); ?>:</strong> <?php echo date_format(date_create($order['order_date']), 'Y-m-d'); ?></p>
            <p><strong><?php echo __('total_amount'); ?>:</strong> <?php echo number_format($order['total_amount'], 2); ?></p>
            <p><strong><?php echo __('status'); ?>:</strong> <span class="badge bg-<?php echo get_status_badge($order['status']); ?>"><?php echo __($order['status']); ?></span></p>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header">
            <h5><?php echo __('ordered_books'); ?></h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th><?php echo __('book_title'); ?></th>
                            <th><?php echo __('quantity'); ?></th>
                            <th><?php echo __('price_per_unit'); ?></th>
                            <th><?php echo __('total'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($item = $items_result->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($item['book_title']); ?></td>
                                <td><?php echo $item['quantity']; ?></td>
                                <td><?php echo number_format($item['price_per_unit'], 2); ?></td>
                                <td><?php echo number_format($item['quantity'] * $item['price_per_unit'], 2); ?></td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
