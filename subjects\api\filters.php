<?php
/**
 * API للفلاتر - متوافق مع هيكل قاعدة البيانات
 */

header("Content-Type: application/json");
require_once "../../includes/config.php";
require_once "../../includes/database.php";

session_start();
if (!isset($_SESSION["user_id"])) {
    http_response_code(401);
    echo json_encode(["error" => "غير مصرح"]);
    exit();
}

try {
    $action = $_GET["action"] ?? "";
    
    switch ($action) {
        case "get_grades":
            $stage_id = intval($_GET["stage_id"] ?? 0);
            if (in_array("grades", ["academic_years", "activity_logs", "admin_attendance", "attendance", "backups", "bank_accounts", "books", "classes", "daily_expenses", "deduction_settings", "discounts", "educational_stages", "error_logs", "exam_attempts", "exam_questions", "exam_results", "exams", "expense_categories", "expenses", "fee_categories", "fee_structures", "fee_type_classes", "fee_types", "grades", "installment_payments", "installment_plans", "installments", "leave_settings", "leave_types", "login_attempts", "notifications", "payments", "permission_types", "remember_tokens", "staff", "staff_absence_deductions", "staff_absences_with_deduction", "staff_attendance", "staff_leaves", "student_book_orders", "student_fees", "student_grades", "student_installments", "student_payments", "students", "subjects", "system_settings", "teacher_assignments", "teacher_attendance", "teachers", "uploaded_files", "user_sessions", "users"])) {
                if ($stage_id > 0) {
                    $query = "SELECT id, grade_name FROM grades WHERE stage_id = ? AND status = \"active\" ORDER BY sort_order";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("i", $stage_id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                } else {
                    $result = $conn->query("SELECT id, grade_name FROM grades WHERE status = \"active\" ORDER BY sort_order");
                }
                $grades = $result->fetch_all(MYSQLI_ASSOC);
                echo json_encode(["success" => true, "data" => $grades]);
            } else {
                echo json_encode(["success" => true, "data" => []]);
            }
            break;
            
        case "search_subjects":
            $filters = [
                "stage_id" => intval($_GET["stage_id"] ?? 0),
                "grade_id" => intval($_GET["grade_id"] ?? 0),
                "department" => trim($_GET["department"] ?? ""),
                "status" => trim($_GET["status"] ?? ""),
                "search" => trim($_GET["search"] ?? "")
            ];
            
            $where_conditions = [];
            $params = [];
            $param_types = "";
            
            if ($filters["stage_id"] > 0) {
                $where_conditions[] = "s.stage_id = ?";
                $params[] = $filters["stage_id"];
                $param_types .= "i";
            }
            
            if ($filters["grade_id"] > 0) {
                $where_conditions[] = "s.grade_id = ?";
                $params[] = $filters["grade_id"];
                $param_types .= "i";
            }
            
            if (!empty($filters["department"])) {
                $where_conditions[] = "s.department = ?";
                $params[] = $filters["department"];
                $param_types .= "s";
            }
            
            if (!empty($filters["status"])) {
                $where_conditions[] = "s.status = ?";
                $params[] = $filters["status"];
                $param_types .= "s";
            }
            
            if (!empty($filters["search"])) {
                $where_conditions[] = "(s.subject_name LIKE ? OR s.subject_code LIKE ?)";
                $search_param = "%{$filters["search"]}%";
                $params[] = $search_param;
                $params[] = $search_param;
                $param_types .= "ss";
            }
            
            $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
            
            $query = "SELECT s.* FROM subjects s {$where_clause} ORDER BY s.subject_name LIMIT 50";
            
            if (!empty($params)) {
                $stmt = $conn->prepare($query);
                $stmt->bind_param($param_types, ...$params);
                $stmt->execute();
                $result = $stmt->get_result();
            } else {
                $result = $conn->query($query);
            }
            
            $subjects = $result->fetch_all(MYSQLI_ASSOC);
            echo json_encode(["success" => true, "data" => $subjects]);
            break;
            
        default:
            echo json_encode(["error" => "إجراء غير صحيح"]);
    }
    
} catch (Exception $e) {
    echo json_encode(["error" => $e->getMessage()]);
}
?>