<?php
/**
 * API لجلب بيانات لوحة التحكم المحدث
 * Updated Dashboard Data API with Full Synchronization
 */

define('SYSTEM_INIT', true);
require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

session_start();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// التحقق من صحة الجلسة
if (!security()->validateSession()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Session expired']);
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

try {
    $data = [];

    // بيانات عامة للجميع
    $data['current_time'] = date('Y-m-d H:i:s');
    $data['current_academic_year'] = get_current_academic_year();
    $data['user_info'] = [
        'id' => $user_id,
        'name' => $_SESSION['full_name'],
        'role' => $user_role,
        'last_activity' => $_SESSION['last_activity'] ?? null
    ];

    // تحديث آخر نشاط
    update_last_activity($user_id);

    // بيانات حسب الدور
    switch ($user_role) {
        case 'admin':
            $data = array_merge($data, getAdminDashboardData());
            break;
        case 'teacher':
            $data = array_merge($data, getTeacherDashboardData($user_id));
            break;
        case 'student':
            $data = array_merge($data, getStudentDashboardData($user_id));
            break;
        case 'staff':
            $data = array_merge($data, getStaffDashboardData($user_id));
            break;
    }

    // إضافة الإشعارات
    $data['notifications'] = get_user_notifications($user_id, 5);
    $data['unread_notifications_count'] = count_unread_notifications($user_id);

    // إضافة إحصائيات النظام العامة
    $data['system_stats'] = getSystemStats();

    echo json_encode(['success' => true, 'data' => $data]);

} catch (Exception $e) {
    error_log("Dashboard API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}

/**
 * دالة جلب بيانات لوحة تحكم المدير
 * Get admin dashboard data
 */
function getAdminDashboardData() {
    global $conn;

    $data = [];

    try {
        // إحصائيات الطلاب
        $result = $conn->query("SELECT
            COUNT(*) as total_students,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_students,
            COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_students
            FROM students");
        $data['students'] = $result->fetch_assoc();

        // إحصائيات المعلمين
        $result = $conn->query("SELECT
            COUNT(*) as total_teachers,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_teachers,
            COUNT(CASE WHEN status = 'on_leave' THEN 1 END) as on_leave_teachers
            FROM teachers");
        $data['teachers'] = $result->fetch_assoc();

        // إحصائيات الفصول
        $result = $conn->query("SELECT
            COUNT(*) as total_classes,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_classes,
            SUM(current_students) as total_enrolled
            FROM classes");
        $data['classes'] = $result->fetch_assoc();

        // إحصائيات الرسوم
        $result = $conn->query("SELECT
            COUNT(*) as total_fees,
            SUM(final_amount) as total_amount,
            SUM(paid_amount) as paid_amount,
            SUM(final_amount - paid_amount) as remaining_amount,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_fees,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_fees,
            COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_fees
            FROM student_fees
            WHERE academic_year_id = (SELECT id FROM academic_years WHERE is_current = 1 LIMIT 1)");
        $data['finance'] = $result->fetch_assoc();

        // الامتحانات القادمة
        $result = $conn->query("SELECT
            e.exam_title, e.exam_date, e.exam_time,
            s.subject_name, c.class_name
            FROM exams e
            JOIN subjects s ON e.subject_id = s.id
            JOIN classes c ON e.class_id = c.id
            WHERE e.exam_date >= CURDATE() AND e.status = 'published'
            ORDER BY e.exam_date ASC
            LIMIT 5");
        $data['upcoming_exams'] = $result->fetch_all(MYSQLI_ASSOC);

        // الأنشطة الأخيرة
        $result = $conn->query("SELECT
            al.action, al.description, al.created_at,
            u.full_name as user_name
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            ORDER BY al.created_at DESC
            LIMIT 10");
        $data['recent_activities'] = $result->fetch_all(MYSQLI_ASSOC);

    } catch (Exception $e) {
        error_log("Admin dashboard data error: " . $e->getMessage());
    }

    return $data;
}

/**
 * دالة جلب بيانات لوحة تحكم المعلم
 * Get teacher dashboard data
 */
function getTeacherDashboardData($user_id) {
    global $conn;

    $data = [];

    try {
        // الحصول على معرف المعلم
        $result = $conn->query("SELECT id FROM teachers WHERE user_id = $user_id");
        $teacher = $result->fetch_assoc();

        if (!$teacher) {
            return $data;
        }

        $teacher_id = $teacher['id'];

        // الفصول المكلف بها
        $result = $conn->query("SELECT
            c.class_name, s.subject_name, ta.weekly_hours,
            COUNT(st.id) as student_count
            FROM teacher_assignments ta
            JOIN classes c ON ta.class_id = c.id
            JOIN subjects s ON ta.subject_id = s.id
            LEFT JOIN students st ON c.id = st.class_id AND st.status = 'active'
            WHERE ta.teacher_id = $teacher_id AND ta.status = 'active'
            GROUP BY ta.id");
        $data['my_classes'] = $result->fetch_all(MYSQLI_ASSOC);

        // الامتحانات القادمة
        $result = $conn->query("SELECT
            e.exam_title, e.exam_date, e.exam_time,
            s.subject_name, c.class_name
            FROM exams e
            JOIN subjects s ON e.subject_id = s.id
            JOIN classes c ON e.class_id = c.id
            WHERE e.teacher_id = $teacher_id AND e.exam_date >= CURDATE()
            ORDER BY e.exam_date ASC
            LIMIT 5");
        $data['upcoming_exams'] = $result->fetch_all(MYSQLI_ASSOC);

        // الامتحانات التي تحتاج تصحيح
        $result = $conn->query("SELECT
            e.exam_title, s.subject_name, c.class_name,
            COUNT(g.id) as graded_count,
            (SELECT COUNT(*) FROM students st WHERE st.class_id = e.class_id AND st.status = 'active') as total_students
            FROM exams e
            JOIN subjects s ON e.subject_id = s.id
            JOIN classes c ON e.class_id = c.id
            LEFT JOIN grades g ON e.id = g.exam_id
            WHERE e.teacher_id = $teacher_id AND e.status = 'completed'
            GROUP BY e.id
            HAVING graded_count < total_students
            LIMIT 5");
        $data['pending_grading'] = $result->fetch_all(MYSQLI_ASSOC);

    } catch (Exception $e) {
        error_log("Teacher dashboard data error: " . $e->getMessage());
    }

    return $data;
}

/**
 * دالة جلب بيانات لوحة تحكم الطالب
 * Get student dashboard data
 */
function getStudentDashboardData($user_id) {
    global $conn;

    $data = [];

    try {
        // الحصول على معرف الطالب
        $result = $conn->query("SELECT id, class_id FROM students WHERE user_id = $user_id");
        $student = $result->fetch_assoc();

        if (!$student) {
            return $data;
        }

        $student_id = $student['id'];
        $class_id = $student['class_id'];

        // معلومات الفصل
        if ($class_id) {
            $result = $conn->query("SELECT class_name, grade_level FROM classes WHERE id = $class_id");
            $data['class_info'] = $result->fetch_assoc();
        }

        // الامتحانات القادمة
        $result = $conn->query("SELECT
            e.exam_title, e.exam_date, e.exam_time,
            s.subject_name, e.total_marks
            FROM exams e
            JOIN subjects s ON e.subject_id = s.id
            WHERE e.class_id = $class_id AND e.exam_date >= CURDATE() AND e.status = 'published'
            ORDER BY e.exam_date ASC
            LIMIT 5");
        $data['upcoming_exams'] = $result->fetch_all(MYSQLI_ASSOC);

        // آخر الدرجات
        $result = $conn->query("SELECT
            e.exam_title, s.subject_name, g.score, g.percentage, g.grade_letter,
            e.total_marks, g.graded_at
            FROM student_grades g
            LEFT JOIN exams e ON g.exam_id = e.id
            LEFT JOIN subjects s ON g.subject_id = s.id
            WHERE g.student_id = $student_id
            ORDER BY g.graded_at DESC
            LIMIT 10");
        $data['recent_grades'] = $result->fetch_all(MYSQLI_ASSOC);

        // إحصائيات الحضور
        $result = $conn->query("SELECT
            COUNT(*) as total_days,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_days,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_days,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_days
            FROM attendance
            WHERE student_id = $student_id
            AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
        $attendance_stats = $result->fetch_assoc();

        if ($attendance_stats['total_days'] > 0) {
            $attendance_stats['attendance_percentage'] = round(
                ($attendance_stats['present_days'] / $attendance_stats['total_days']) * 100, 2
            );
        } else {
            $attendance_stats['attendance_percentage'] = 0;
        }

        $data['attendance_stats'] = $attendance_stats;

        // الرسوم المستحقة
        $result = $conn->query("SELECT
            fc.category_name, sf.final_amount, sf.paid_amount,
            (sf.final_amount - sf.paid_amount) as remaining_amount,
            sf.due_date, sf.status
            FROM student_fees sf
            JOIN fee_structures fs ON sf.fee_structure_id = fs.id
            JOIN fee_categories fc ON fs.fee_category_id = fc.id
            WHERE sf.student_id = $student_id AND sf.status != 'paid'
            ORDER BY sf.due_date ASC");
        $data['pending_fees'] = $result->fetch_all(MYSQLI_ASSOC);

    } catch (Exception $e) {
        error_log("Student dashboard data error: " . $e->getMessage());
    }

    return $data;
}

/**
 * دالة جلب بيانات لوحة تحكم الموظف
 * Get staff dashboard data
 */
function getStaffDashboardData($user_id) {
    global $conn;

    $data = [];

    try {
        // الحصول على معلومات الموظف
        $result = $conn->query("SELECT department, position FROM staff WHERE user_id = $user_id");
        $staff = $result->fetch_assoc();

        if ($staff) {
            $data['staff_info'] = $staff;
        }

        // إحصائيات عامة للنظام
        $data['system_overview'] = getSystemStats();

    } catch (Exception $e) {
        error_log("Staff dashboard data error: " . $e->getMessage());
    }

    return $data;
}

/**
 * دالة جلب إحصائيات النظام العامة
 * Get general system statistics
 */
function getSystemStats() {
    global $conn;

    $stats = [];

    try {
        // إحصائيات سريعة
        $result = $conn->query("SELECT
            (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users,
            (SELECT COUNT(*) FROM students WHERE status = 'active') as active_students,
            (SELECT COUNT(*) FROM teachers WHERE status = 'active') as active_teachers,
            (SELECT COUNT(*) FROM classes WHERE status = 'active') as active_classes,
            (SELECT COUNT(*) FROM exams WHERE exam_date = CURDATE()) as today_exams,
            (SELECT COUNT(*) FROM notifications WHERE is_read = 0) as unread_notifications");

        $stats = $result->fetch_assoc();

        // إضافة الوقت الحالي
        $stats['server_time'] = date('Y-m-d H:i:s');
        $stats['timezone'] = date_default_timezone_get();

    } catch (Exception $e) {
        error_log("System stats error: " . $e->getMessage());
    }

    return $stats;
}
?>

define('SYSTEM_INIT', true);
require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// Start session and check authentication
session_start();

if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$user_role = $_SESSION['role'] ?? 'student';
$user_id = $_SESSION['user_id'];

try {
    $dashboard_data = [];
    
    // Get current academic year
    $current_academic_year = get_current_academic_year();
    
    switch ($user_role) {
        case 'admin':
            $dashboard_data = getAdminDashboardData($conn);
            break;
            
        case 'teacher':
            $dashboard_data = getTeacherDashboardData($conn, $user_id);
            break;
            
        case 'student':
            $dashboard_data = getStudentDashboardData($conn, $user_id);
            break;
            
        case 'staff':
            $dashboard_data = getStaffDashboardData($conn, $user_id);
            break;
            
        default:
            throw new Exception('Invalid user role');
    }
    
    // Add common data
    $dashboard_data['user_info'] = [
        'name' => $_SESSION['full_name'] ?? 'User',
        'role' => $user_role,
        'last_login' => $_SESSION['last_login'] ?? null
    ];
    
    $dashboard_data['system_info'] = [
        'academic_year' => $current_academic_year,
        'current_semester' => get_system_setting('current_semester', 'first'),
        'school_name' => get_system_setting('school_name', 'School Management System')
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $dashboard_data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}

/**
 * Get admin dashboard data
 */
function getAdminDashboardData($conn) {
    $data = [];
    
    // Students statistics
    $students_query = "
        SELECT 
            COUNT(*) as total_students,
            COUNT(CASE WHEN u.status = 'active' THEN 1 END) as active_students,
            COUNT(CASE WHEN s.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_students
        FROM students s 
        JOIN users u ON s.user_id = u.id
    ";
    $students_result = $conn->query($students_query);
    $data['students'] = $students_result->fetch_assoc();
    
    // Teachers statistics
    $teachers_query = "
        SELECT 
            COUNT(*) as total_teachers,
            COUNT(CASE WHEN u.status = 'active' THEN 1 END) as active_teachers
        FROM teachers t 
        JOIN users u ON t.user_id = u.id
    ";
    $teachers_result = $conn->query($teachers_query);
    $data['teachers'] = $teachers_result->fetch_assoc();
    
    // Classes statistics
    $classes_query = "
        SELECT 
            COUNT(*) as total_classes,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_classes
        FROM classes
    ";
    $classes_result = $conn->query($classes_query);
    $data['classes'] = $classes_result->fetch_assoc();
    
    // Financial statistics
    $finance_query = "
        SELECT 
            COALESCE(SUM(sf.final_amount), 0) as total_fees,
            COALESCE(SUM(sp.amount), 0) as total_payments,
            COUNT(CASE WHEN sp.status = 'pending' THEN 1 END) as pending_payments
        FROM student_fees sf
        LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id
        WHERE sf.academic_year = ?
    ";
    $finance_stmt = $conn->prepare($finance_query);
    $current_year = get_current_academic_year();
    $finance_stmt->bind_param("s", $current_year);
    $finance_stmt->execute();
    $data['finance'] = $finance_stmt->get_result()->fetch_assoc();
    
    // Recent activities
    $activities_query = "
        SELECT 
            al.action,
            al.table_name,
            al.created_at,
            u.full_name as user_name
        FROM activity_logs al
        JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT 10
    ";
    $activities_result = $conn->query($activities_query);
    $data['recent_activities'] = $activities_result->fetch_all(MYSQLI_ASSOC);
    
    // Attendance summary for today
    $attendance_query = "
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count
        FROM attendance
        WHERE DATE(attendance_date) = CURDATE()
    ";
    $attendance_result = $conn->query($attendance_query);
    $data['attendance_today'] = $attendance_result->fetch_assoc();
    
    return $data;
}

/**
 * Get teacher dashboard data
 */
function getTeacherDashboardData($conn, $teacher_user_id) {
    $data = [];
    
    // Get teacher ID
    $teacher_query = "SELECT id FROM teachers WHERE user_id = ?";
    $teacher_stmt = $conn->prepare($teacher_query);
    $teacher_stmt->bind_param("i", $teacher_user_id);
    $teacher_stmt->execute();
    $teacher_result = $teacher_stmt->get_result();
    
    if ($teacher_result->num_rows === 0) {
        throw new Exception('Teacher not found');
    }
    
    $teacher_id = $teacher_result->fetch_assoc()['id'];
    
    // Classes assigned to teacher
    $classes_query = "
        SELECT 
            c.id,
            c.class_name,
            c.grade_level,
            COUNT(s.id) as student_count
        FROM classes c
        LEFT JOIN students s ON c.id = s.class_id AND s.status = 'active'
        WHERE c.teacher_id = ?
        GROUP BY c.id
    ";
    $classes_stmt = $conn->prepare($classes_query);
    $classes_stmt->bind_param("i", $teacher_id);
    $classes_stmt->execute();
    $data['my_classes'] = $classes_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Subjects taught
    $subjects_query = "
        SELECT DISTINCT
            s.id,
            s.subject_name,
            s.subject_name_en
        FROM subjects s
        JOIN class_subjects cs ON s.id = cs.subject_id
        JOIN classes c ON cs.class_id = c.id
        WHERE c.teacher_id = ?
    ";
    $subjects_stmt = $conn->prepare($subjects_query);
    $subjects_stmt->bind_param("i", $teacher_id);
    $subjects_stmt->execute();
    $data['my_subjects'] = $subjects_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Upcoming exams
    $exams_query = "
        SELECT 
            e.id,
            e.exam_title,
            e.exam_date,
            e.exam_time,
            s.subject_name,
            c.class_name
        FROM exams e
        JOIN subjects s ON e.subject_id = s.id
        JOIN classes c ON e.class_id = c.id
        WHERE c.teacher_id = ? AND e.exam_date >= CURDATE()
        ORDER BY e.exam_date ASC
        LIMIT 5
    ";
    $exams_stmt = $conn->prepare($exams_query);
    $exams_stmt->bind_param("i", $teacher_id);
    $exams_stmt->execute();
    $data['upcoming_exams'] = $exams_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Recent grades entered
    $grades_query = "
        SELECT 
            g.score,
            g.created_at,
            u.full_name as student_name,
            s.subject_name,
            e.exam_title
        FROM grades g
        JOIN students st ON g.student_id = st.id
        JOIN users u ON st.user_id = u.id
        JOIN exams e ON g.exam_id = e.id
        JOIN subjects s ON e.subject_id = s.id
        JOIN classes c ON e.class_id = c.id
        WHERE c.teacher_id = ?
        ORDER BY g.created_at DESC
        LIMIT 10
    ";
    $grades_stmt = $conn->prepare($grades_query);
    $grades_stmt->bind_param("i", $teacher_id);
    $grades_stmt->execute();
    $data['recent_grades'] = $grades_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    return $data;
}

/**
 * Get student dashboard data
 */
function getStudentDashboardData($conn, $student_user_id) {
    $data = [];
    
    // Get student ID
    $student_query = "SELECT id, class_id FROM students WHERE user_id = ?";
    $student_stmt = $conn->prepare($student_query);
    $student_stmt->bind_param("i", $student_user_id);
    $student_stmt->execute();
    $student_result = $student_stmt->get_result();
    
    if ($student_result->num_rows === 0) {
        throw new Exception('Student not found');
    }
    
    $student_data = $student_result->fetch_assoc();
    $student_id = $student_data['id'];
    $class_id = $student_data['class_id'];
    
    // Class information
    if ($class_id) {
        $class_query = "
            SELECT 
                c.class_name,
                c.grade_level,
                u.full_name as teacher_name
            FROM classes c
            LEFT JOIN teachers t ON c.teacher_id = t.id
            LEFT JOIN users u ON t.user_id = u.id
            WHERE c.id = ?
        ";
        $class_stmt = $conn->prepare($class_query);
        $class_stmt->bind_param("i", $class_id);
        $class_stmt->execute();
        $data['class_info'] = $class_stmt->get_result()->fetch_assoc();
    }
    
    // Recent grades
    $grades_query = "
        SELECT 
            g.score,
            g.grade_letter,
            g.created_at,
            s.subject_name,
            e.exam_title,
            e.total_marks
        FROM grades g
        JOIN exams e ON g.exam_id = e.id
        JOIN subjects s ON e.subject_id = s.id
        WHERE g.student_id = ?
        ORDER BY g.created_at DESC
        LIMIT 10
    ";
    $grades_stmt = $conn->prepare($grades_query);
    $grades_stmt->bind_param("i", $student_id);
    $grades_stmt->execute();
    $data['recent_grades'] = $grades_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Attendance summary
    $attendance_query = "
        SELECT 
            COUNT(*) as total_days,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_days,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_days,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_days
        FROM attendance
        WHERE student_id = ? AND MONTH(attendance_date) = MONTH(CURDATE())
    ";
    $attendance_stmt = $conn->prepare($attendance_query);
    $attendance_stmt->bind_param("i", $student_id);
    $attendance_stmt->execute();
    $data['attendance_summary'] = $attendance_stmt->get_result()->fetch_assoc();
    
    // Upcoming exams
    $upcoming_exams_query = "
        SELECT 
            e.id,
            e.exam_title,
            e.exam_date,
            e.exam_time,
            s.subject_name
        FROM exams e
        JOIN subjects s ON e.subject_id = s.id
        WHERE e.class_id = ? AND e.exam_date >= CURDATE()
        ORDER BY e.exam_date ASC
        LIMIT 5
    ";
    $upcoming_exams_stmt = $conn->prepare($upcoming_exams_query);
    $upcoming_exams_stmt->bind_param("i", $class_id);
    $upcoming_exams_stmt->execute();
    $data['upcoming_exams'] = $upcoming_exams_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Financial summary
    $finance_query = "
        SELECT 
            COALESCE(SUM(sf.final_amount), 0) as total_fees,
            COALESCE(SUM(sp.amount), 0) as total_paid,
            COUNT(CASE WHEN sf.status = 'pending' THEN 1 END) as pending_fees
        FROM student_fees sf
        LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id
        WHERE sf.student_id = ? AND sf.academic_year = ?
    ";
    $finance_stmt = $conn->prepare($finance_query);
    $current_year = get_current_academic_year();
    $finance_stmt->bind_param("is", $student_id, $current_year);
    $finance_stmt->execute();
    $data['finance_summary'] = $finance_stmt->get_result()->fetch_assoc();
    
    return $data;
}

/**
 * Get staff dashboard data
 */
function getStaffDashboardData($conn, $staff_user_id) {
    $data = [];
    
    // Basic statistics
    $stats_query = "
        SELECT 
            (SELECT COUNT(*) FROM students s JOIN users u ON s.user_id = u.id WHERE u.status = 'active') as total_students,
            (SELECT COUNT(*) FROM teachers t JOIN users u ON t.user_id = u.id WHERE u.status = 'active') as total_teachers,
            (SELECT COUNT(*) FROM classes WHERE status = 'active') as total_classes
    ";
    $stats_result = $conn->query($stats_query);
    $data['statistics'] = $stats_result->fetch_assoc();
    
    // Recent activities (limited view for staff)
    $activities_query = "
        SELECT 
            al.action,
            al.table_name,
            al.created_at,
            u.full_name as user_name
        FROM activity_logs al
        JOIN users u ON al.user_id = u.id
        WHERE al.action IN ('create_student', 'update_student', 'create_teacher', 'update_teacher')
        ORDER BY al.created_at DESC
        LIMIT 10
    ";
    $activities_result = $conn->query($activities_query);
    $data['recent_activities'] = $activities_result->fetch_all(MYSQLI_ASSOC);
    
    return $data;
}
?>
