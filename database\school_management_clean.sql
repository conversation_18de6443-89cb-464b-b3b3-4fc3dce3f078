-- php<PERSON>y<PERSON>dmin SQL Dump
-- نظام إدارة المدارس الكامل والمحدث
-- Complete and Updated School Management System
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 31, 2025 at 12:00 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `school_management`
--

-- --------------------------------------------------------

--
-- Table structure for table `academic_years`
--

CREATE TABLE `academic_years` (
  `id` int(10) UNSIGNED NOT NULL,
  `year_name` varchar(20) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_current` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive','archived') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `academic_years`
--

INSERT INTO `academic_years` (`id`, `year_name`, `start_date`, `end_date`, `is_current`, `status`, `created_at`, `updated_at`) VALUES
(1, '2024-2025', '2024-09-01', '2025-06-30', 1, 'active', '2025-07-15 04:55:07', '2025-07-15 04:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(10) UNSIGNED NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','teacher','student','staff','parent') NOT NULL DEFAULT 'student',
  `status` enum('active','inactive','suspended','pending') DEFAULT 'active',
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `status`, `phone`, `address`, `profile_picture`, `last_login`, `email_verified_at`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 'active', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(2, 'teacher1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'معلم تجريبي', 'teacher', 'active', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(12, 'staff1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف إداري', 'staff', 'active', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(15, 'admin2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير مساعد', 'admin', 'active', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(18, 'staff2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف إداري 2', 'staff', 'active', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-15 04:55:07', '2025-07-15 04:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `leave_types`
--

CREATE TABLE `leave_types` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `name_en` varchar(100) DEFAULT NULL,
  `code` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `max_days_per_year` int(10) UNSIGNED DEFAULT NULL,
  `max_days_per_request` int(10) UNSIGNED DEFAULT NULL,
  `min_days_notice` int(10) UNSIGNED DEFAULT 1,
  `requires_medical_certificate` tinyint(1) DEFAULT 0,
  `requires_replacement` tinyint(1) DEFAULT 0,
  `approval_levels` tinyint(3) UNSIGNED DEFAULT 1,
  `is_paid` tinyint(1) DEFAULT 1,
  `affects_salary` tinyint(1) DEFAULT 0,
  `can_be_carried_forward` tinyint(1) DEFAULT 0,
  `color_code` varchar(7) DEFAULT '#007bff',
  `icon` varchar(50) DEFAULT 'fas fa-calendar',
  `sort_order` tinyint(3) UNSIGNED DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `leave_types`
--

INSERT INTO `leave_types` (`id`, `name`, `name_en`, `code`, `description`, `max_days_per_year`, `max_days_per_request`, `min_days_notice`, `requires_medical_certificate`, `requires_replacement`, `approval_levels`, `is_paid`, `affects_salary`, `can_be_carried_forward`, `color_code`, `icon`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'إجازة سنوية', 'Annual Leave', 'ANNUAL', 'الإجازة السنوية المستحقة للموظف', 30, 15, 7, 0, 1, 2, 1, 0, 1, '#28a745', 'fas fa-calendar-check', 1, 1, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(2, 'إجازة مرضية', 'Sick Leave', 'SICK', 'إجازة مرضية بتقرير طبي', 90, 30, 0, 1, 0, 1, 1, 0, 0, '#dc3545', 'fas fa-user-injured', 2, 1, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(3, 'إجازة طارئة', 'Emergency Leave', 'EMERGENCY', 'إجازة طارئة لظروف استثنائية', 7, 3, 0, 0, 0, 1, 1, 0, 0, '#ffc107', 'fas fa-exclamation-triangle', 3, 1, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(4, 'إجازة أمومة', 'Maternity Leave', 'MATERNITY', 'إجازة أمومة للموظفات', 90, 90, 30, 1, 0, 2, 1, 0, 0, '#e83e8c', 'fas fa-baby', 4, 1, '2025-07-15 04:55:07', '2025-07-15 04:55:07'),
(5, 'إجازة أبوة', 'Paternity Leave', 'PATERNITY', 'إجازة أبوة للموظفين', 7, 7, 7, 0, 0, 1, 1, 0, 0, '#6f42c1', 'fas fa-male', 5, 1, '2025-07-15 04:55:07', '2025-07-15 04:55:07');

-- --------------------------------------------------------

--
-- Table structure for table `staff_leaves`
--

CREATE TABLE `staff_leaves` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `leave_type_id` int(10) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `total_days` decimal(5,2) NOT NULL,
  `working_days` decimal(5,2) NOT NULL,
  `reason` text NOT NULL,
  `medical_certificate` varchar(255) DEFAULT NULL,
  `replacement_user_id` int(10) UNSIGNED DEFAULT NULL,
  `replacement_notes` text DEFAULT NULL,
  `status` enum('pending','approved','rejected','cancelled') DEFAULT 'pending',
  `workflow_step` tinyint(3) UNSIGNED DEFAULT 1,
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `applied_by` int(10) UNSIGNED NOT NULL,
  `approved_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_by` int(10) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `cancelled_by` int(10) UNSIGNED DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` text DEFAULT NULL,
  `hr_notes` text DEFAULT NULL,
  `manager_notes` text DEFAULT NULL,
  `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
  `notification_sent` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `staff_leaves`
--

INSERT INTO `staff_leaves` (`id`, `user_id`, `leave_type_id`, `start_date`, `end_date`, `total_days`, `working_days`, `reason`, `status`, `applied_by`, `created_at`, `updated_at`) VALUES
(8, 12, 2, '2025-07-20', '2025-07-26', 7.00, 7.00, 'مرض', 'approved', 15, '2025-07-20 09:55:50', '2025-07-20 09:57:02'),
(11, 18, 2, '2025-07-20', '2025-07-23', 4.00, 4.00, '', 'approved', 15, '2025-07-20 10:04:08', '2025-07-20 10:04:08');

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `description` text,
  `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
  `is_public` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('system_name', 'نظام إدارة المدارس', 'اسم النظام'),
('system_version', '2.0.0', 'إصدار النظام'),
('timezone', 'Asia/Riyadh', 'المنطقة الزمنية'),
('language', 'ar', 'اللغة الافتراضية'),
('currency', 'ريال سعودي', 'العملة الافتراضية'),
('currency_symbol', 'ر.س', 'رمز العملة'),
('academic_year_start', '09-01', 'بداية العام الدراسي'),
('academic_year_end', '06-30', 'نهاية العام الدراسي');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `academic_years`
--
ALTER TABLE `academic_years`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_year_name` (`year_name`),
  ADD KEY `idx_is_current` (`is_current`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `leave_types`
--
ALTER TABLE `leave_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `idx_name` (`name`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `staff_leaves`
--
ALTER TABLE `staff_leaves`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_leave_type_id` (`leave_type_id`),
  ADD KEY `idx_start_date` (`start_date`),
  ADD KEY `idx_end_date` (`end_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_applied_by` (`applied_by`),
  ADD KEY `idx_approved_by` (`approved_by`),
  ADD KEY `idx_date_range` (`start_date`,`end_date`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `academic_years`
--
ALTER TABLE `academic_years`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `leave_types`
--
ALTER TABLE `leave_types`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `staff_leaves`
--
ALTER TABLE `staff_leaves`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `staff_leaves`
--
ALTER TABLE `staff_leaves`
  ADD CONSTRAINT `staff_leaves_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_leaves_leave_type_id_foreign` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types` (`id`) ON DELETE RESTRICT,
  ADD CONSTRAINT `staff_leaves_applied_by_foreign` FOREIGN KEY (`applied_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  ADD CONSTRAINT `staff_leaves_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_leaves_replacement_user_id_foreign` FOREIGN KEY (`replacement_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
