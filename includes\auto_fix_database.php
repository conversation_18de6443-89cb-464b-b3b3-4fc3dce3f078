<?php
/**
 * إصلاح تلقائي لقاعدة البيانات
 * Auto fix database issues
 */

function auto_fix_classes_table($conn) {
    try {
        // التحقق من وجود عمود description في جدول classes
        $check_query = "SHOW COLUMNS FROM classes LIKE 'description'";
        $result = $conn->query($check_query);
        
        if (!$result || $result->num_rows == 0) {
            // إضافة العمود إذا لم يكن موجوداً
            $alter_query = "ALTER TABLE classes ADD COLUMN description TEXT DEFAULT NULL AFTER academic_year_id";
            
            if ($conn->query($alter_query)) {
                error_log("Auto-fix: Added description column to classes table");
                return true;
            } else {
                error_log("Auto-fix failed: Could not add description column to classes table - " . $conn->error);
                return false;
            }
        }
        
        return true; // العمود موجود مسبقاً
        
    } catch (Exception $e) {
        error_log("Auto-fix error: " . $e->getMessage());
        return false;
    }
}

function check_and_fix_database($conn) {
    $fixes_applied = [];
    
    // إصلاح جدول classes
    if (auto_fix_classes_table($conn)) {
        $fixes_applied[] = 'classes_description_column';
    }
    
    return $fixes_applied;
}

// تشغيل الإصلاح التلقائي إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'auto_fix_database.php') {
    require_once 'config.php';
    require_once 'database.php';
    
    if ($conn) {
        $fixes = check_and_fix_database($conn);
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'fixes_applied' => $fixes,
            'message' => 'تم تطبيق ' . count($fixes) . ' إصلاح'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'فشل في الاتصال بقاعدة البيانات'
        ], JSON_UNESCAPED_UNICODE);
    }
}
?>
