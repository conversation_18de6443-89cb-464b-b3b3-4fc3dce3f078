<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

$id = intval($_GET['id'] ?? 0);
$error = '';
$success = '';

if ($id) {
    $stmt = $conn->prepare("SELECT * FROM attendance WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $record = $stmt->get_result()->fetch_assoc();
    if (!$record) {
        die('سجل الحضور غير موجود');
    }
} else {
    die('معرف السجل غير صحيح');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $status = $_POST['status'] ?? '';
    $notes = trim($_POST['notes'] ?? '');
    if ($status) {
        $stmt = $conn->prepare("UPDATE attendance SET status=?, notes=? WHERE id=?");
        $stmt->bind_param("ssi", $status, $notes, $id);
        if ($stmt->execute()) {
            $success = 'تم التعديل بنجاح';
        } else {
            $error = 'حدث خطأ أثناء التعديل';
        }
    } else {
        $error = 'يرجى اختيار الحالة';
    }
}
?>
<div class="container">
    <h2>تعديل سجل الحضور</h2>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label>الحالة</label>
            <select name="status" class="form-control" required>
                <option value="present" <?= $record['status']=='present'?'selected':'' ?>>حاضر</option>
                <option value="absent" <?= $record['status']=='absent'?'selected':'' ?>>غائب</option>
                <option value="late" <?= $record['status']=='late'?'selected':'' ?>>متأخر</option>
                <option value="excused" <?= $record['status']=='excused'?'selected':'' ?>>بعذر</option>
            </select>
        </div>
        <div class="mb-3">
            <label>ملاحظات</label>
            <textarea name="notes" class="form-control"><?= htmlspecialchars($record['notes']) ?></textarea>
        </div>
        <button type="submit" class="btn btn-primary">حفظ التعديلات</button>
        <a href="index.php" class="btn btn-secondary">رجوع</a>
    </form>
</div>
<?php require_once '../includes/footer.php'; ?> 