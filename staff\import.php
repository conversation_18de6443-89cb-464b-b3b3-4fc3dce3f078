<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';
$import_results = [];

// معالجة رفع الملف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $file = $_FILES['csv_file'];
        
        // التحقق من الملف
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $error_message = 'حدث خطأ أثناء رفع الملف';
        } elseif ($file['size'] > 5 * 1024 * 1024) { // 5MB max
            $error_message = 'حجم الملف كبير جداً (الحد الأقصى 5 ميجابايت)';
        } elseif (!in_array(strtolower(pathinfo($file['name'], PATHINFO_EXTENSION)), ['csv'])) {
            $error_message = 'نوع الملف غير مدعوم. يرجى رفع ملف CSV فقط';
        } else {
            // قراءة الملف
            $csv_data = array_map('str_getcsv', file($file['tmp_name']));
            $header = array_shift($csv_data);
            
            // التحقق من الأعمدة المطلوبة
            $required_columns = ['full_name', 'username', 'email', 'password', 'employee_id'];
            $missing_columns = array_diff($required_columns, $header);
            
            if (!empty($missing_columns)) {
                $error_message = 'الأعمدة التالية مفقودة: ' . implode(', ', $missing_columns);
            } else {
                // معالجة البيانات
                $success_count = 0;
                $error_count = 0;
                $import_results = [];
                
                global $conn;
                
                foreach ($csv_data as $row_index => $row) {
                    $row_number = $row_index + 2; // +2 because we removed header and arrays are 0-indexed
                    
                    if (count($row) !== count($header)) {
                        $import_results[] = [
                            'row' => $row_number,
                            'status' => 'error',
                            'message' => 'عدد الأعمدة غير صحيح'
                        ];
                        $error_count++;
                        continue;
                    }
                    
                    $data = array_combine($header, $row);
                    
                    // تنظيف البيانات
                    $full_name = clean_input($data['full_name'] ?? '');
                    $username = clean_input($data['username'] ?? '');
                    $email = clean_input($data['email'] ?? '');
                    $password = $data['password'] ?? '';
                    $employee_id = clean_input($data['employee_id'] ?? '');
                    $phone = clean_input($data['phone'] ?? '');
                    $department = clean_input($data['department'] ?? '');
                    $position = clean_input($data['position'] ?? '');
                    $gender = clean_input($data['gender'] ?? 'male');
                    $salary = floatval($data['salary'] ?? 0);
                    
                    // التحقق من البيانات
                    $row_errors = [];
                    if (empty($full_name)) $row_errors[] = 'الاسم الكامل مطلوب';
                    if (empty($username)) $row_errors[] = 'اسم المستخدم مطلوب';
                    if (empty($email)) $row_errors[] = 'البريد الإلكتروني مطلوب';
                    if (empty($password)) $row_errors[] = 'كلمة المرور مطلوبة';
                    if (empty($employee_id)) $row_errors[] = 'رقم الموظف مطلوب';
                    
                    if (!validate_email($email)) $row_errors[] = 'البريد الإلكتروني غير صالح';
                    if (!validate_password($password)) $row_errors[] = 'كلمة المرور قصيرة جداً';
                    if (!in_array($gender, ['male', 'female'])) $row_errors[] = 'الجنس غير صالح';
                    
                    // التحقق من التكرار
                    if (get_user_by_username($username)) $row_errors[] = 'اسم المستخدم موجود بالفعل';
                    if (get_user_by_email($email)) $row_errors[] = 'البريد الإلكتروني موجود بالفعل';
                    
                    $stmt = $conn->prepare("SELECT id FROM staff WHERE employee_id = ?");
                    $stmt->bind_param("s", $employee_id);
                    $stmt->execute();
                    if ($stmt->get_result()->num_rows > 0) {
                        $row_errors[] = 'رقم الموظف موجود بالفعل';
                    }
                    
                    if (!empty($row_errors)) {
                        $import_results[] = [
                            'row' => $row_number,
                            'status' => 'error',
                            'message' => implode(', ', $row_errors),
                            'data' => $full_name
                        ];
                        $error_count++;
                        continue;
                    }
                    
                    // إدراج البيانات
                    $conn->begin_transaction();
                    try {
                        // إنشاء المستخدم
                        $hashed_password = hash_password($password);
                        $stmt = $conn->prepare("
                            INSERT INTO users (full_name, username, email, password, role, status, phone, gender, created_at) 
                            VALUES (?, ?, ?, ?, 'staff', 'active', ?, ?, NOW())
                        ");
                        $stmt->bind_param("ssssss", $full_name, $username, $email, $hashed_password, $phone, $gender);
                        $stmt->execute();
                        $user_id = $conn->insert_id;
                        $stmt->close();

                        // إنشاء الإداري
                        $stmt = $conn->prepare("
                            INSERT INTO staff (
                                user_id, employee_id, phone, department, position, 
                                gender, salary, status, created_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())
                        ");
                        $stmt->bind_param("isssssd", 
                            $user_id, $employee_id, $phone, $department, $position, $gender, $salary
                        );
                        $stmt->execute();
                        $staff_id = $conn->insert_id;
                        $stmt->close();

                        $conn->commit();
                        
                        $import_results[] = [
                            'row' => $row_number,
                            'status' => 'success',
                            'message' => 'تم إضافة الإداري بنجاح',
                            'data' => $full_name
                        ];
                        $success_count++;
                        
                        // تسجيل النشاط
                        log_activity($_SESSION['user_id'], 'import_staff', 'staff', $staff_id, null, [
                            'staff_name' => $full_name,
                            'employee_id' => $employee_id
                        ]);
                        
                    } catch (Exception $e) {
                        $conn->rollback();
                        $import_results[] = [
                            'row' => $row_number,
                            'status' => 'error',
                            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
                            'data' => $full_name
                        ];
                        $error_count++;
                        log_error("Error importing staff row $row_number: " . $e->getMessage());
                    }
                }
                
                $success_message = "تم استيراد $success_count إداري بنجاح";
                if ($error_count > 0) {
                    $success_message .= " مع $error_count خطأ";
                }
            }
        }
    }
}

// تحميل ملف اللغة
load_language();
$page_title = 'استيراد الإداريين';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">استيراد الإداريين</h1>
                    <p class="text-muted">استيراد بيانات الإداريين من ملف CSV</p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                    <a href="generate_csv_sample.php" class="btn btn-info">
                        <i class="fas fa-download me-2"></i>تحميل نموذج CSV
                    </a>
                </div>
            </div>

            <!-- Error/Success Messages -->
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Import Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>رفع ملف CSV
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <label for="csv_file" class="form-label">ملف CSV <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="csv_file" name="csv_file" 
                                       accept=".csv" required>
                                <div class="form-text">
                                    الحد الأقصى لحجم الملف: 5 ميجابايت. الصيغة المدعومة: CSV فقط
                                </div>
                                <div class="invalid-feedback">يرجى اختيار ملف CSV</div>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-upload me-2"></i>استيراد البيانات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- CSV Format Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>تعليمات تنسيق ملف CSV
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">الأعمدة المطلوبة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i><strong>full_name</strong> - الاسم الكامل</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>username</strong> - اسم المستخدم</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>email</strong> - البريد الإلكتروني</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>password</strong> - كلمة المرور</li>
                                <li><i class="fas fa-check text-success me-2"></i><strong>employee_id</strong> - رقم الموظف</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">الأعمدة الاختيارية:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-minus text-muted me-2"></i><strong>phone</strong> - رقم الهاتف</li>
                                <li><i class="fas fa-minus text-muted me-2"></i><strong>department</strong> - القسم</li>
                                <li><i class="fas fa-minus text-muted me-2"></i><strong>position</strong> - المنصب</li>
                                <li><i class="fas fa-minus text-muted me-2"></i><strong>gender</strong> - الجنس (male/female)</li>
                                <li><i class="fas fa-minus text-muted me-2"></i><strong>salary</strong> - الراتب</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ملاحظات مهمة:</strong>
                        <ul class="mb-0 mt-2">
                            <li>يجب أن يكون الصف الأول يحتوي على أسماء الأعمدة</li>
                            <li>اسم المستخدم والبريد الإلكتروني ورقم الموظف يجب أن تكون فريدة</li>
                            <li>كلمة المرور يجب أن تكون 6 أحرف على الأقل</li>
                            <li>الجنس يجب أن يكون male أو female</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Import Results -->
            <?php if (!empty($import_results)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>نتائج الاستيراد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>الصف</th>
                                        <th>الحالة</th>
                                        <th>البيانات</th>
                                        <th>الرسالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($import_results as $result): ?>
                                        <tr>
                                            <td><?php echo $result['row']; ?></td>
                                            <td>
                                                <?php if ($result['status'] === 'success'): ?>
                                                    <span class="badge bg-success">نجح</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">فشل</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($result['data'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($result['message']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// File validation
document.getElementById('csv_file').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const fileSize = file.size / 1024 / 1024; // Convert to MB
        const fileExtension = file.name.split('.').pop().toLowerCase();

        if (fileSize > 5) {
            this.setCustomValidity('حجم الملف كبير جداً (الحد الأقصى 5 ميجابايت)');
        } else if (fileExtension !== 'csv') {
            this.setCustomValidity('نوع الملف غير مدعوم. يرجى رفع ملف CSV فقط');
        } else {
            this.setCustomValidity('');
        }
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
