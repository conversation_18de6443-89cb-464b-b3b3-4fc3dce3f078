<div class="text-center">
    <div class="mb-4">
        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
    </div>
    
    <h3 class="text-success mb-4">Installation Completed Successfully!</h3>
    
    <p class="lead text-muted mb-4">
        Your School Management System has been installed and configured successfully.
    </p>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>Admin Login Details
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless mb-0">
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['admin_email'] ?? '<EMAIL>'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Password:</strong></td>
                        <td>The password you set during installation</td>
                    </tr>
                    <tr>
                        <td><strong>Role:</strong></td>
                        <td>System Administrator</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-school me-2"></i>School Information
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless mb-0">
                    <tr>
                        <td><strong>Name:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['school_name'] ?? 'School Name'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Academic Year:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['academic_year'] ?? '2024-2025'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Currency:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['currency_symbol'] ?? 'ر.س'); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="alert alert-warning mt-4">
    <i class="fas fa-shield-alt me-2"></i>
    <strong>Important Security Steps:</strong>
    <ol class="mb-0 mt-2">
        <li><strong>Delete the install directory</strong> - Remove the entire <code>/install</code> folder for security</li>
        <li><strong>Change default passwords</strong> - Update the admin password from the settings page</li>
        <li><strong>Configure SSL</strong> - Enable HTTPS for secure connections</li>
        <li><strong>Set up backups</strong> - Configure automatic database backups</li>
        <li><strong>Review permissions</strong> - Ensure file and folder permissions are secure</li>
    </ol>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Next Steps:</strong>
    <ul class="mb-0 mt-2">
        <li>Login to the admin panel and explore the system</li>
        <li>Add teachers, students, and classes</li>
        <li>Configure fee types and payment methods</li>
        <li>Set up subjects and exam schedules</li>
        <li>Customize system settings to match your school's needs</li>
    </ul>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-book me-2"></i>Documentation
                </h5>
                <p class="card-text">
                    Read the user manual and documentation for detailed instructions.
                </p>
                <a href="../docs/" class="btn btn-outline-primary" target="_blank">
                    View Documentation
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-life-ring me-2"></i>Support
                </h5>
                <p class="card-text">
                    Need help? Contact our support team for assistance.
                </p>
                <a href="mailto:<EMAIL>" class="btn btn-outline-info">
                    Get Support
                </a>
            </div>
        </div>
    </div>
</div>

<div class="text-center mt-5">
    <a href="../index.php" class="btn btn-primary btn-lg">
        <i class="fas fa-sign-in-alt me-2"></i>Go to Login Page
    </a>
    <a href="../dashboard/" class="btn btn-success btn-lg ms-3">
        <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
    </a>
</div>

<div class="text-center mt-4">
    <small class="text-muted">
        School Management System v2.0.0 - Installed on <?php echo date('Y-m-d H:i:s'); ?>
    </small>
</div>

<script>
// Clear installation session data
<?php 
session_destroy();
?>

// Show success animation
document.addEventListener('DOMContentLoaded', function() {
    // Add some celebration animation
    const icon = document.querySelector('.fa-check-circle');
    icon.style.animation = 'bounce 1s ease-in-out';
    
    // Add CSS for animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes bounce {
            0%, 20%, 60%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            80% {
                transform: translateY(-10px);
            }
        }
    `;
    document.head.appendChild(style);
});
</script>
