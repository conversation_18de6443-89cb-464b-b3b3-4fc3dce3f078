<?php
/**
 * نظام الأمان المتقدم
 * Advanced Security System
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

/**
 * فئة الأمان الرئيسية
 * Main Security Class
 */
class SecurityManager {
    
    private static $instance = null;
    private $conn;
    
    private function __construct() {
        global $conn;
        $this->conn = $conn;
    }

    private function ensureConnection() {
        if (!$this->conn) {
            global $conn;
            $this->conn = $conn;
        }
        if (!$this->conn) {
            throw new Exception('Database connection not available in SecurityManager.');
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تنظيف المدخلات من الهجمات
     * Clean input from attacks
     */
    public function cleanInput($input, $type = 'string') {
        if (is_array($input)) {
            return array_map([$this, 'cleanInput'], $input);
        }

        // التحقق من القيم الفارغة
        if ($input === null || $input === '') {
            return '';
        }

        // تحويل إلى string إذا لم يكن كذلك
        $input = (string) $input;

        // Remove null bytes
        $input = str_replace(chr(0), '', $input);

        // Trim whitespace
        $input = trim($input);
        
        switch ($type) {
            case 'email':
                return filter_var($input, FILTER_SANITIZE_EMAIL);
                
            case 'url':
                return filter_var($input, FILTER_SANITIZE_URL);
                
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
                
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                
            case 'html':
                return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                
            case 'sql':
                return $this->conn ? $this->conn->real_escape_string($input) : addslashes($input);
                
            case 'filename':
                // Remove dangerous characters from filenames
                $input = preg_replace('/[^a-zA-Z0-9._-]/', '', $input);
                return substr($input, 0, 255);
                
            default:
                // Default string cleaning
                $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                return $input;
        }
    }
    
    /**
     * التحقق من صحة المدخلات
     * Validate input data
     */
    public function validateInput($input, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule_set) {
            $value = $input[$field] ?? null;
            $rules_array = explode('|', $rule_set);
            
            foreach ($rules_array as $rule) {
                $rule_parts = explode(':', $rule);
                $rule_name = $rule_parts[0];
                $rule_param = $rule_parts[1] ?? null;
                
                switch ($rule_name) {
                    case 'required':
                        if (empty($value)) {
                            $errors[$field][] = "Field {$field} is required";
                        }
                        break;
                        
                    case 'email':
                        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[$field][] = "Field {$field} must be a valid email";
                        }
                        break;
                        
                    case 'min':
                        if (!empty($value) && strlen($value) < $rule_param) {
                            $errors[$field][] = "Field {$field} must be at least {$rule_param} characters";
                        }
                        break;
                        
                    case 'max':
                        if (!empty($value) && strlen($value) > $rule_param) {
                            $errors[$field][] = "Field {$field} must not exceed {$rule_param} characters";
                        }
                        break;
                        
                    case 'numeric':
                        if (!empty($value) && !is_numeric($value)) {
                            $errors[$field][] = "Field {$field} must be numeric";
                        }
                        break;
                        
                    case 'alpha':
                        if (!empty($value) && !ctype_alpha($value)) {
                            $errors[$field][] = "Field {$field} must contain only letters";
                        }
                        break;
                        
                    case 'alphanumeric':
                        if (!empty($value) && !ctype_alnum($value)) {
                            $errors[$field][] = "Field {$field} must contain only letters and numbers";
                        }
                        break;
                        
                    case 'url':
                        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                            $errors[$field][] = "Field {$field} must be a valid URL";
                        }
                        break;
                        
                    case 'date':
                        if (!empty($value) && !$this->validateDate($value)) {
                            $errors[$field][] = "Field {$field} must be a valid date";
                        }
                        break;
                        
                    case 'phone':
                        if (!empty($value) && !$this->validatePhone($value)) {
                            $errors[$field][] = "Field {$field} must be a valid phone number";
                        }
                        break;
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * التحقق من صحة التاريخ
     * Validate date format
     */
    private function validateDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     * Validate phone number
     */
    private function validatePhone($phone) {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Check if it's a valid length (7-15 digits)
        return strlen($phone) >= 7 && strlen($phone) <= 15;
    }
    
    /**
     * حماية من هجمات CSRF
     * CSRF Protection
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_tokens'])) {
            $_SESSION['csrf_tokens'] = [];
        }
        
        $token = bin2hex(random_bytes(32));
        $expires = time() + CSRF_TOKEN_EXPIRE;
        
        $_SESSION['csrf_tokens'][$token] = $expires;
        
        // Clean expired tokens
        $this->cleanExpiredCSRFTokens();
        
        return $token;
    }
    
    /**
     * التحقق من صحة رمز CSRF
     * Verify CSRF token
     */
    public function verifyCSRFToken($token) {
        if (!isset($_SESSION['csrf_tokens'][$token])) {
            return false;
        }
        
        $expires = $_SESSION['csrf_tokens'][$token];
        
        if (time() > $expires) {
            unset($_SESSION['csrf_tokens'][$token]);
            return false;
        }
        
        // Remove token after use (one-time use)
        unset($_SESSION['csrf_tokens'][$token]);
        return true;
    }
    
    /**
     * تنظيف رموز CSRF المنتهية الصلاحية
     * Clean expired CSRF tokens
     */
    private function cleanExpiredCSRFTokens() {
        if (!isset($_SESSION['csrf_tokens'])) {
            return;
        }
        
        $current_time = time();
        foreach ($_SESSION['csrf_tokens'] as $token => $expires) {
            if ($current_time > $expires) {
                unset($_SESSION['csrf_tokens'][$token]);
            }
        }
    }
    
    /**
     * تشفير البيانات
     * Encrypt data
     */
    public function encrypt($data, $key = null) {
        if ($key === null) {
            $key = $this->getEncryptionKey();
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, ENCRYPTION_METHOD, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     * Decrypt data
     */
    public function decrypt($data, $key = null) {
        if ($key === null) {
            $key = $this->getEncryptionKey();
        }
        
        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, ENCRYPTION_METHOD, $key, 0, $iv);
    }
    
    /**
     * الحصول على مفتاح التشفير
     * Get encryption key
     */
    private function getEncryptionKey() {
        // In production, this should be stored securely (environment variable, etc.)
        return hash('sha256', DB_PASSWORD . DB_NAME . 'school_system_secret_key');
    }
    
    /**
     * تشفير كلمة المرور
     * Hash password
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * التحقق من كلمة المرور
     * Verify password
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * التحقق من قوة كلمة المرور
     * Check password strength
     */
    public function checkPasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < PASSWORD_MIN_LENGTH) {
            $errors[] = "Password must be at least " . PASSWORD_MIN_LENGTH . " characters long";
        }
        
        if (PASSWORD_REQUIRE_UPPERCASE && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }
        
        if (PASSWORD_REQUIRE_NUMBERS && !preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }
        
        if (PASSWORD_REQUIRE_SPECIAL && !preg_match('/[^a-zA-Z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
        }
        
        // Check for common passwords
        $common_passwords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        
        if (in_array(strtolower($password), $common_passwords)) {
            $errors[] = "Password is too common";
        }
        
        return $errors;
    }
    
    /**
     * تسجيل محاولة تسجيل الدخول
     * Log login attempt
     */
    public function logLoginAttempt($username, $ip_address, $user_agent, $success, $failure_reason = null) {
        if (!$this->conn) {
            return false;
        }
        
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO login_attempts (username, ip_address, user_agent, success, failure_reason, attempted_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            if (!$stmt) {
                error_log('SecurityManager: prepare() failed in logLoginAttempt: ' . $this->conn->error);
                throw new Exception('Database error: failed to prepare statement for logLoginAttempt.');
            }
            $stmt->bind_param("sssbs", $username, $ip_address, $user_agent, $success, $failure_reason);
            return $stmt->execute();
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * التحقق من محاولات تسجيل الدخول
     * Check login attempts
     */
    public function checkLoginAttempts($username, $ip_address) {
        try {
            $this->ensureConnection();
            // Check attempts in the last lockout period
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as attempts, MAX(attempted_at) as last_attempt
                FROM login_attempts 
                WHERE (username = ? OR ip_address = ?) 
                AND success = 0 
                AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            if (!$stmt) {
                error_log('SecurityManager: prepare() failed in checkLoginAttempts: ' . $this->conn->error);
                throw new Exception('Database error: failed to prepare statement for login attempts.');
            }
            $username_var = $username;
            $ip_address_var = $ip_address;
            $lockout_time = LOGIN_LOCKOUT_TIME;
            $stmt->bind_param("ssi", $username_var, $ip_address_var, $lockout_time);
            $stmt->execute();
            $result = $stmt->get_result()->fetch_assoc();
            $attempts = $result['attempts'];
            $last_attempt = $result['last_attempt'];
            if ($attempts >= LOGIN_MAX_ATTEMPTS) {
                $lockout_remaining = LOGIN_LOCKOUT_TIME - (time() - strtotime($last_attempt));
                return [
                    'allowed' => false,
                    'attempts' => $attempts,
                    'lockout_time' => max(0, $lockout_remaining)
                ];
            }
            return [
                'allowed' => true,
                'attempts' => $attempts,
                'lockout_time' => 0
            ];
        } catch (Exception $e) {
            error_log('SecurityManager DB error: ' . $e->getMessage());
            return ['allowed' => true, 'attempts' => 0, 'lockout_time' => 0];
        }
    }
    
    /**
     * تنظيف محاولات تسجيل الدخول القديمة
     * Clean old login attempts
     */
    public function cleanOldLoginAttempts() {
        if (!$this->conn) {
            return false;
        }
        
        try {
            $stmt = $this->conn->prepare("
                DELETE FROM login_attempts 
                WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
            ");
            return $stmt->execute();
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * التحقق من عنوان IP المشبوه
     * Check suspicious IP
     */
    public function checkSuspiciousIP($ip_address) {
        // List of known malicious IP ranges or patterns
        $suspicious_patterns = [
            '/^10\.0\.0\./', // Example: block certain ranges
            '/^192\.168\./', // Example: block local networks from external access
        ];
        
        foreach ($suspicious_patterns as $pattern) {
            if (preg_match($pattern, $ip_address)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * إنشاء رمز أمان عشوائي
     * Generate secure random token
     */
    public function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * التحقق من صحة الجلسة
     * Validate session security
     */
    public function validateSession() {
        // Check if session exists
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
                session_destroy();
                return false;
            }
        }
        
        // Check IP address consistency (optional, can cause issues with mobile users)
        if (isset($_SESSION['ip_address'])) {
            if ($_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
                // Log suspicious activity
                $this->logSecurityEvent('session_ip_mismatch', [
                    'user_id' => $_SESSION['user_id'],
                    'original_ip' => $_SESSION['ip_address'],
                    'current_ip' => $_SERVER['REMOTE_ADDR']
                ]);
                
                // Optionally destroy session
                // session_destroy();
                // return false;
            }
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * تسجيل حدث أمني
     * Log security event
     */
    public function logSecurityEvent($event_type, $data = []) {
        if (!$this->conn) {
            return false;
        }
        
        try {
            $user_id = $_SESSION['user_id'] ?? null;
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $data_json = json_encode($data);
            
            $stmt = $this->conn->prepare("
                INSERT INTO security_events (user_id, event_type, ip_address, user_agent, event_data, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $stmt->bind_param("issss", $user_id, $event_type, $ip_address, $user_agent, $data_json);
            return $stmt->execute();
        } catch (Exception $e) {
            return false;
        }
    }
}

// ===================================
// HELPER FUNCTIONS
// ===================================

/**
 * Get security manager instance
 */
function security() {
    return SecurityManager::getInstance();
}

/**
 * Clean input (shorthand)
 */
if (!function_exists('clean_input')) {
    function clean_input($input, $type = 'string') {
        return security()->cleanInput($input, $type);
    }
}

/**
 * Generate CSRF token (shorthand)
 */
if (!function_exists('generate_csrf_token')) {
    function generate_csrf_token() {
        return security()->generateCSRFToken();
    }
}

/**
 * Verify CSRF token (shorthand)
 */
if (!function_exists('verify_csrf_token')) {
    function verify_csrf_token($token) {
        return security()->verifyCSRFToken($token);
    }
}

/**
 * Generate CSRF token field for forms
 */
function csrf_token_field() {
    $token = generate_csrf_token();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
}

/**
 * Hash password (shorthand)
 */
function hash_password($password) {
    return security()->hashPassword($password);
}

/**
 * Verify password (shorthand)
 */
function verify_password($password, $hash) {
    return security()->verifyPassword($password, $hash);
}

/**
 * Check if request is from AJAX
 */
function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Get client IP address
 */
function get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Rate limiting check
 */
function check_rate_limit($action, $limit = 60, $window = 3600) {
    $ip = get_client_ip();
    $key = "rate_limit_{$action}_{$ip}";
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'window_start' => time()];
    }
    
    $data = $_SESSION[$key];
    
    // Reset if window expired
    if (time() - $data['window_start'] > $window) {
        $_SESSION[$key] = ['count' => 1, 'window_start' => time()];
        return true;
    }
    
    // Check limit
    if ($data['count'] >= $limit) {
        return false;
    }
    
    // Increment counter
    $_SESSION[$key]['count']++;
    return true;
}
?>
