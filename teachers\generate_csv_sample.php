<?php
/**
 * توليد نموذج CSV للمعلمين بالترميز الصحيح
 * Generate Teachers CSV Sample with Correct Encoding
 */

// تعيين الترميز
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="sample_teachers_import.csv"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// إضافة BOM لدعم UTF-8 في Excel
echo "\xEF\xBB\xBF";

// رؤوس الأعمدة
$headers = [
    'full_name',
    'username',
    'email',
    'phone',
    'national_id',
    'date_of_birth',
    'gender',
    'address',
    'qualification',
    'specialization',
    'experience_years',
    'hire_date',
    'salary',
    'status',
    'password'
];

// البيانات النموذجية
$sample_data = [
    [
        'أحمد محمد علي',
        'ahmed_teacher',
        '<EMAIL>',
        '01234567890',
        '12345678901234',
        '1985-05-15',
        'male',
        'القاهرة - المعادي',
        'بكالوريوس تربية',
        'رياضيات',
        '10',
        '2020-09-01',
        '5000.00',
        'active',
        '123456'
    ],
    [
        'فاطمة أحمد حسن',
        'fatma_teacher',
        '<EMAIL>',
        '01234567891',
        '12345678901235',
        '1988-08-20',
        'female',
        'الجيزة - الدقي',
        'ليسانس آداب',
        'لغة عربية',
        '8',
        '2021-09-01',
        '4800.00',
        'active',
        '123456'
    ],
    [
        'محمد سامي عبدالله',
        'mohamed_teacher',
        '<EMAIL>',
        '01234567892',
        '12345678901236',
        '1982-12-10',
        'male',
        'القاهرة - مصر الجديدة',
        'بكالوريوس علوم',
        'فيزياء',
        '12',
        '2019-09-01',
        '5200.00',
        'active',
        '123456'
    ],
    [
        'نور الدين خالد',
        'nour_teacher',
        '<EMAIL>',
        '01234567893',
        '12345678901237',
        '1990-03-25',
        'male',
        'الإسكندرية - سموحة',
        'بكالوريوس تربية',
        'كيمياء',
        '5',
        '2022-09-01',
        '4500.00',
        'active',
        '123456'
    ],
    [
        'سارة عمر يوسف',
        'sara_teacher',
        '<EMAIL>',
        '01234567894',
        '12345678901238',
        '1987-07-18',
        'female',
        'القاهرة - النزهة',
        'ليسانس آداب',
        'لغة إنجليزية',
        '9',
        '2020-09-01',
        '4900.00',
        'active',
        '123456'
    ]
];

// فتح مخرج CSV
$output = fopen('php://output', 'w');

// كتابة الرؤوس
fputcsv($output, $headers);

// كتابة البيانات النموذجية
foreach ($sample_data as $row) {
    fputcsv($output, $row);
}

// إغلاق المخرج
fclose($output);
exit();
?>
