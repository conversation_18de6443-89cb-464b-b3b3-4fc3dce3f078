<?php
/**
 * API للحصول على الإشعارات
 * Notifications API
 */

define('SYSTEM_INIT', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    $user_id = $_SESSION['user_id'];
    $limit = intval($_GET['limit'] ?? 10);
    $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === '1';
    
    // بناء الاستعلام
    $where_clause = "user_id = ?";
    $params = [$user_id];
    $types = "i";
    
    if ($unread_only) {
        $where_clause .= " AND is_read = 0";
    }
    
    $query = "
        SELECT 
            id,
            title,
            message,
            type,
            action_url,
            is_read,
            created_at
        FROM notifications 
        WHERE {$where_clause}
        ORDER BY created_at DESC 
        LIMIT ?
    ";
    
    $params[] = $limit;
    $types .= "i";
    
    global $conn;
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    
    $result = $stmt->get_result();
    $notifications = [];
    
    while ($row = $result->fetch_assoc()) {
        $notifications[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'message' => $row['message'],
            'type' => $row['type'],
            'action_url' => $row['action_url'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => format_datetime($row['created_at'], 'Y/m/d H:i'),
            'time_ago' => time_ago($row['created_at'])
        ];
    }
    
    echo json_encode($notifications);
    
} catch (Exception $e) {
    log_error("Error fetching notifications: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * دالة لحساب الوقت المنقضي
 */
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return __('just_now');
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' ' . __('minutes_ago');
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' ' . __('hours_ago');
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ' ' . __('days_ago');
    } else {
        return format_date($datetime);
    }
}
?>
