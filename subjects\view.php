<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

$id = intval($_GET['id'] ?? 0);
if ($id) {
    $stmt = $conn->prepare("SELECT * FROM subjects WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $subject = $stmt->get_result()->fetch_assoc();
    if (!$subject) {
        die('المادة غير موجودة');
    }
} else {
    die('معرف المادة غير صحيح');
}
?>
<div class="container">
    <h2>بيانات المادة</h2>
    <table class="table table-bordered">
        <tr><th>اسم المادة</th><td><?= htmlspecialchars($subject['subject_name']) ?></td></tr>
    </table>
    <a href="edit.php?id=<?= $subject['id'] ?>" class="btn btn-primary">تعديل</a>
    <a href="index.php" class="btn btn-secondary">رجوع</a>
</div>
<?php require_once '../includes/footer.php'; ?> 