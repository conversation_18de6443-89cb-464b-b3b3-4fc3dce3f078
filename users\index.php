<?php
// صفحة عرض المستخدمين
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

// جلب المستخدمين
$users = [];
if ($conn) {
    $result = $conn->query("SELECT * FROM users");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $users[] = $row;
        }
    }
}
?>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>المستخدمون</h2>
        <a href="add.php" class="btn btn-primary">إضافة مستخدم</a>
    </div>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>الاسم الكامل</th>
                <th>البريد الإلكتروني</th>
                <th>الدور</th>
                <th>الحالة</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($users as $user): ?>
            <tr>
                <td><?= htmlspecialchars($user['full_name']) ?></td>
                <td><?= htmlspecialchars($user['email']) ?></td>
                <td><?= htmlspecialchars($user['role']) ?></td>
                <td><?= htmlspecialchars($user['status']) ?></td>
                <td>
                    <a href="view.php?id=<?= $user['id'] ?>" class="btn btn-info btn-sm">عرض</a>
                    <a href="edit.php?id=<?= $user['id'] ?>" class="btn btn-warning btn-sm">تعديل</a>
                    <a href="delete.php?id=<?= $user['id'] ?>" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</a>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?php require_once '../includes/footer.php'; ?> 