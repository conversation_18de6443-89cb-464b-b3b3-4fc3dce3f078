<?php
/**
 * صفحة إدارة المدفوعات
 * Payments Management Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة تغيير اللغة من URL
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $_GET['lang'];
}


// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$student_filter = clean_input($_GET['student_id'] ?? '');
$payment_method_filter = clean_input($_GET['payment_method'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');
$date_from = clean_input($_GET['date_from'] ?? '');
$date_to = clean_input($_GET['date_to'] ?? '');
$show_unpaid_fees = clean_input($_GET['show_unpaid_fees'] ?? '');

// بناء استعلام البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(u.full_name LIKE ? OR sp.payment_reference LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
    $types .= "ss";
}

if (!empty($student_filter)) {
    $where_conditions[] = "sp.student_id = ?";
    $params[] = $student_filter;
    $types .= "i";
}

if (!empty($payment_method_filter)) {
    $where_conditions[] = "sp.payment_method = ?";
    $params[] = $payment_method_filter;
    $types .= "s";
}

if (!empty($status_filter)) {
    $where_conditions[] = "sp.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

if (!empty($date_from)) {
    $where_conditions[] = "sp.payment_date >= ?";
    $params[] = $date_from;
    $types .= "s";
}

if (!empty($date_to)) {
    $where_conditions[] = "sp.payment_date <= ?";
    $params[] = $date_to;
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    WHERE $where_clause
";
$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}

$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب المدفوعات فقط (لأن جميع الرسوم مدفوعة عند الإضافة)
$query = "
    SELECT
        sp.*,
        'payment' as payment_type,
        u.full_name as student_name,
        s.id as student_id,
        c.class_name,
        c.grade_level,
        sf.final_amount as fee_amount,
        ft.type_name as fee_type_name,
        pu.full_name as processed_by_name
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN users pu ON sp.processed_by = pu.id
    WHERE $where_clause
    ORDER BY sp.payment_date DESC, sp.id DESC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);

// المعاملات + LIMIT و OFFSET
$params[] = $records_per_page;
$params[] = $offset;
$types .= "ii";

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$payments = $stmt->get_result();

// جلب قوائم الفلترة
$students = $conn->query("
    SELECT s.id, u.full_name, s.student_id as student_number
    FROM students s
    JOIN users u ON s.user_id = u.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
");

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_payments,
        SUM(CASE WHEN status = 'confirmed' THEN amount ELSE 0 END) as confirmed_amount,
        COUNT(CASE WHEN payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as recent_payments,
        SUM(CASE WHEN payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN amount ELSE 0 END) as recent_amount
    FROM student_payments
    WHERE YEAR(payment_date) = YEAR(CURDATE())
";

$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('payments'); ?></h1>
            <p class="text-muted"><?php echo __('manage_student_payments'); ?></p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('record_payment'); ?>
            </a>
            <a href="bulk_import.php" class="btn btn-success">
                <i class="fas fa-upload me-2"></i><?php echo __('bulk_import'); ?>
            </a>
            <a href="export.php" class="btn btn-info">
                <i class="fas fa-download me-2"></i><?php echo __('export'); ?>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-receipt text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_payments']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_payments'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-dollar-sign text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['confirmed_amount'], 2); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('confirmed_amount'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient p-3 rounded-3">
                                <i class="fas fa-calendar text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['recent_payments']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('recent_payments'); ?></p>
                            <small class="text-info"><?php echo __('last_30_days'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-chart-line text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['recent_amount'], 2); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('monthly_revenue'); ?></p>
                            <small class="text-warning"><?php echo __('last_30_days'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="search" class="form-label"><?php echo __('search'); ?></label>
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="<?php echo __('search_payment'); ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="student_id" class="form-label"><?php echo __('student'); ?></label>
                    <select class="form-select" id="student_id" name="student_id">
                        <option value=""><?php echo __('all_students'); ?></option>
                        <?php while ($student = $students->fetch_assoc()): ?>
                            <option value="<?php echo $student['id']; ?>"
                                    <?php echo ($student_filter == $student['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($student['full_name'] . ' (ID: ' . $student['student_number'] . ')'); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="payment_method" class="form-label"><?php echo __('payment_method'); ?></label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value=""><?php echo __('all_methods'); ?></option>
                        <option value="cash" <?php echo ($payment_method_filter == 'cash') ? 'selected' : ''; ?>>
                            <?php echo __('cash'); ?>
                        </option>
                        <option value="bank_transfer" <?php echo ($payment_method_filter == 'bank_transfer') ? 'selected' : ''; ?>>
                            <?php echo __('bank_transfer'); ?>
                        </option>
                        <option value="check" <?php echo ($payment_method_filter == 'check') ? 'selected' : ''; ?>>
                            <?php echo __('check'); ?>
                        </option>
                        <option value="card" <?php echo ($payment_method_filter == 'card') ? 'selected' : ''; ?>>
                            <?php echo __('card'); ?>
                        </option>
                        <option value="online" <?php echo ($payment_method_filter == 'online') ? 'selected' : ''; ?>>
                            <?php echo __('online'); ?>
                        </option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>
                            <?php echo __('pending'); ?>
                        </option>
                        <option value="confirmed" <?php echo ($status_filter == 'confirmed') ? 'selected' : ''; ?>>
                            <?php echo __('confirmed'); ?>
                        </option>
                        <option value="cancelled" <?php echo ($status_filter == 'cancelled') ? 'selected' : ''; ?>>
                            <?php echo __('cancelled'); ?>
                        </option>
                        <option value="refunded" <?php echo ($status_filter == 'refunded') ? 'selected' : ''; ?>>
                            <?php echo __('refunded'); ?>
                        </option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="date_from" class="form-label"><?php echo __('date_from'); ?></label>
                    <input type="date" 
                           class="form-control" 
                           id="date_from" 
                           name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="date_to" class="form-label"><?php echo __('date_to'); ?></label>
                    <input type="date" 
                           class="form-control" 
                           id="date_to" 
                           name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                


                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                    </button>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i><?php echo __('clear'); ?>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0"><?php echo __('payments_list'); ?></h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printTable()">
                    <i class="fas fa-print me-1"></i><?php echo __('print'); ?>
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-1"></i><?php echo __('export'); ?>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php if ($payments->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="paymentsTable">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo __('reference'); ?></th>
                                <th><?php echo __('student'); ?></th>
                                <th><?php echo __('amount'); ?></th>
                                <th><?php echo __('method'); ?></th>
                                <th><?php echo __('date'); ?></th>
                                <th><?php echo __('fee_type'); ?></th>
                                <th><?php echo __('status'); ?></th>
                                <th><?php echo __('processed_by'); ?></th>
                                <th class="no-print"><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($payment = $payments->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo htmlspecialchars($payment['payment_reference']); ?>
                                        </span>
                                        <?php if (!empty($payment['receipt_number'])): ?>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo __('receipt'); ?>: <?php echo htmlspecialchars($payment['receipt_number']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($payment['student_name']); ?></strong>
                                            <br>
                                            <small class="text-muted">ID: <?php echo htmlspecialchars($payment['student_id']); ?></small>
                                            <?php if (!empty($payment['class_name'])): ?>
                                                <br>
                                                <small class="text-info"><?php echo htmlspecialchars($payment['class_name']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success">
                                            <?php echo number_format($payment['amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $method_icons = [
                                            'cash' => 'fas fa-money-bill text-success',
                                            'bank_transfer' => 'fas fa-university text-primary',
                                            'check' => 'fas fa-money-check text-info',
                                            'card' => 'fas fa-credit-card text-warning',
                                            'online' => 'fas fa-globe text-secondary'
                                        ];
                                        $icon = $method_icons[$payment['payment_method']] ?? 'fas fa-question';
                                        ?>
                                        <i class="<?php echo $icon; ?> me-1"></i>
                                        <?php echo __(strtolower($payment['payment_method'])); ?>

                                        <?php if (!empty($payment['bank_name'])): ?>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($payment['bank_name']); ?></small>
                                        <?php endif; ?>

                                        <?php if (!empty($payment['check_number'])): ?>
                                            <br>
                                            <small class="text-muted"><?php echo __('check'); ?>: <?php echo htmlspecialchars($payment['check_number']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo format_date($payment['payment_date']); ?>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo format_datetime($payment['processed_at'], 'H:i'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if (!empty($payment['fee_type_name'])): ?>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($payment['fee_type_name']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo __('general_payment'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($payment['status']) {
                                            case 'pending':
                                                $status_class = 'bg-warning';
                                                $status_text = __('pending');
                                                break;
                                            case 'confirmed':
                                                $status_class = 'bg-success';
                                                $status_text = __('confirmed');
                                                break;
                                            case 'cancelled':
                                                $status_class = 'bg-danger';
                                                $status_text = __('cancelled');
                                                break;
                                            case 'refunded':
                                                $status_class = 'bg-secondary';
                                                $status_text = __('refunded');
                                                break;
                                            default:
                                                $status_class = 'bg-secondary';
                                                $status_text = $payment['status'];
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($payment['processed_by_name'])): ?>
                                            <small><?php echo htmlspecialchars($payment['processed_by_name']); ?></small>
                                        <?php else: ?>
                                            <small class="text-muted"><?php echo __('system'); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?php echo $payment['id']; ?>"
                                               class="btn btn-outline-info"
                                               title="<?php echo __('view_details'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="receipt_improved.php?id=<?php echo $payment['id']; ?>"
                                               class="btn btn-outline-primary"
                                               title="<?php echo __('print_receipt'); ?>"
                                               target="_blank">
                                                <i class="fas fa-receipt"></i>
                                            </a>
                                            <?php if ($payment['status'] === 'pending'): ?>
                                            <a href="edit.php?id=<?php echo $payment['id']; ?>"
                                               class="btn btn-outline-warning"
                                               title="<?php echo __('edit'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="delete.php?id=<?php echo $payment['id']; ?>"
                                               class="btn btn-outline-danger"
                                               title="<?php echo __('delete'); ?>"
                                               onclick="return confirm('<?php echo __('confirm_delete_payment'); ?>');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Payments pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>&student_id=<?php echo urlencode($student_filter); ?>&payment_method=<?php echo urlencode($payment_method_filter); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>">
                                        <?php echo __('previous'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $start = max(1, $page - 2);
                            $end = min($total_pages, $page + 2);
                            
                            for ($i = $start; $i <= $end; $i++):
                            ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&student_id=<?php echo urlencode($student_filter); ?>&payment_method=<?php echo urlencode($payment_method_filter); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>&student_id=<?php echo urlencode($student_filter); ?>&payment_method=<?php echo urlencode($payment_method_filter); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>">
                                        <?php echo __('next'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo __('no_payments_found'); ?></h5>
                    <p class="text-muted"><?php echo __('try_different_search'); ?></p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('record_first_payment'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    // Print table
    function printTable() {
        window.print();
    }

    // Export to Excel
    function exportToExcel() {
        const table = document.getElementById('paymentsTable');
        const wb = XLSX.utils.table_to_book(table);
        XLSX.writeFile(wb, 'payments_<?php echo date('Y-m-d'); ?>.xlsx');
    }

    // Auto-submit search form on input
    document.getElementById('search').addEventListener('input', function() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });
</script>

<!-- Include XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<?php require_once '../../includes/footer.php'; ?>
