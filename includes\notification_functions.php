<?php
/**
 * وظائف الإشعارات
 * Notification Functions
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

/**
 * إضافة إشعار جديد
 * Add new notification
 */
function add_notification($user_id, $title, $message, $type = 'info', $action_url = null, $related_table = null, $related_id = null) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type, action_url, related_table, related_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->bind_param("isssssi", $user_id, $title, $message, $type, $action_url, $related_table, $related_id);
        $result = $stmt->execute();
        
        if ($result) {
            // إرسال إشعار فوري عبر WebSocket إذا كان متاحاً
            send_realtime_notification($user_id, [
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'action_url' => $action_url
            ]);
        }
        
        return $result;
    } catch (Exception $e) {
        log_error("Error adding notification: " . $e->getMessage());
        return false;
    }
}

/**
 * إضافة إشعار من قالب
 * Add notification from template
 */
function add_notification_from_template($user_id, $template_key, $variables = [], $action_url = null, $related_table = null, $related_id = null) {
    global $conn;
    
    try {
        // جلب القالب
        $stmt = $conn->prepare("SELECT * FROM notification_templates WHERE template_key = ? AND is_active = 1");
        $stmt->bind_param("s", $template_key);
        $stmt->execute();
        $template = $stmt->get_result()->fetch_assoc();
        
        if (!$template) {
            log_error("Notification template not found: " . $template_key);
            return false;
        }
        
        // تحديد اللغة
        $language = get_user_language($user_id);
        $title = $language === 'en' && !empty($template['title_en']) ? $template['title_en'] : $template['title_ar'];
        $message = $language === 'en' && !empty($template['message_en']) ? $template['message_en'] : $template['message_ar'];
        
        // استبدال المتغيرات
        foreach ($variables as $key => $value) {
            $title = str_replace('{' . $key . '}', $value, $title);
            $message = str_replace('{' . $key . '}', $value, $message);
        }
        
        return add_notification($user_id, $title, $message, $template['type'], $action_url, $related_table, $related_id);
        
    } catch (Exception $e) {
        log_error("Error adding notification from template: " . $e->getMessage());
        return false;
    }
}

/**
 * إضافة إشعار جماعي
 * Add bulk notifications
 */
function add_bulk_notification($user_ids, $title, $message, $type = 'info', $action_url = null, $related_table = null, $related_id = null) {
    global $conn;
    
    if (empty($user_ids) || !is_array($user_ids)) {
        return false;
    }
    
    try {
        $conn->begin_transaction();
        
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type, action_url, related_table, related_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $success_count = 0;
        foreach ($user_ids as $user_id) {
            $stmt->bind_param("isssssi", $user_id, $title, $message, $type, $action_url, $related_table, $related_id);
            if ($stmt->execute()) {
                $success_count++;
                
                // إرسال إشعار فوري
                send_realtime_notification($user_id, [
                    'title' => $title,
                    'message' => $message,
                    'type' => $type,
                    'action_url' => $action_url
                ]);
            }
        }
        
        $conn->commit();
        return $success_count;
        
    } catch (Exception $e) {
        $conn->rollback();
        log_error("Error adding bulk notifications: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب إشعارات المستخدم
 * Get user notifications
 */
function get_user_notifications($user_id, $limit = 10, $unread_only = false) {
    global $conn;
    
    try {
        $where_clause = "user_id = ?";
        $params = [$user_id];
        $types = "i";
        
        if ($unread_only) {
            $where_clause .= " AND is_read = 0";
        }
        
        $stmt = $conn->prepare("
            SELECT * FROM notifications 
            WHERE $where_clause 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $params[] = $limit;
        $types .= "i";
        
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        
    } catch (Exception $e) {
        log_error("Error getting user notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * عدد الإشعارات غير المقروءة
 * Count unread notifications
 */
function get_unread_notifications_count($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        return intval($result['count']);
        
    } catch (Exception $e) {
        log_error("Error counting unread notifications: " . $e->getMessage());
        return 0;
    }
}

/**
 * تحديد إشعار كمقروء
 * Mark notification as read
 */
function mark_notification_read($notification_id, $user_id = null) {
    global $conn;
    
    try {
        $where_clause = "id = ?";
        $params = [$notification_id];
        $types = "i";
        
        if ($user_id) {
            $where_clause .= " AND user_id = ?";
            $params[] = $user_id;
            $types .= "i";
        }
        
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE $where_clause");
        $stmt->bind_param($types, ...$params);
        
        return $stmt->execute();
        
    } catch (Exception $e) {
        log_error("Error marking notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديد جميع إشعارات المستخدم كمقروءة
 * Mark all user notifications as read
 */
function mark_all_notifications_read($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE user_id = ? AND is_read = 0");
        $stmt->bind_param("i", $user_id);
        
        return $stmt->execute();
        
    } catch (Exception $e) {
        log_error("Error marking all notifications as read: " . $e->getMessage());
        return false;
    }
}

/**
 * حذف الإشعارات القديمة
 * Delete old notifications
 */
function cleanup_old_notifications($days = 30) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
        $stmt->bind_param("i", $days);
        $result = $stmt->execute();
        
        $deleted_count = $conn->affected_rows;
        log_activity(0, 'cleanup_notifications', 'notifications', null, null, ['deleted_count' => $deleted_count]);
        
        return $deleted_count;
        
    } catch (Exception $e) {
        log_error("Error cleaning up old notifications: " . $e->getMessage());
        return false;
    }
}

/**
 * إرسال إشعار فوري عبر WebSocket
 * Send real-time notification via WebSocket
 */
function send_realtime_notification($user_id, $data) {
    // هذه الوظيفة يمكن تطويرها لاحقاً لدعم WebSocket
    // يمكن استخدام مكتبات مثل ReactPHP أو Ratchet
    
    // حالياً نحفظ في ملف مؤقت للمعاينة
    $notification_data = [
        'user_id' => $user_id,
        'data' => $data,
        'timestamp' => time()
    ];
    
    // يمكن إضافة منطق إرسال فوري هنا
    return true;
}

/**
 * جلب لغة المستخدم
 * Get user language
 */
function get_user_language($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("SELECT language FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        return $result['language'] ?? get_system_setting('language', 'ar');
        
    } catch (Exception $e) {
        return get_system_setting('language', 'ar');
    }
}

/**
 * إرسال إشعار بريد إلكتروني
 * Send email notification
 */
function send_email_notification($user_id, $subject, $message, $template = null) {
    global $conn;
    
    // التحقق من تفعيل إشعارات البريد الإلكتروني
    if (!get_system_setting('email_notifications', false)) {
        return false;
    }
    
    try {
        // جلب بيانات المستخدم
        $stmt = $conn->prepare("SELECT email, full_name, language FROM users WHERE id = ? AND status = 'active'");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $user = $stmt->get_result()->fetch_assoc();
        
        if (!$user || empty($user['email'])) {
            return false;
        }
        
        // إعداد البريد الإلكتروني
        $to = $user['email'];
        $headers = [
            'From: ' . get_system_setting('school_email', '<EMAIL>'),
            'Reply-To: ' . get_system_setting('school_email', '<EMAIL>'),
            'Content-Type: text/html; charset=UTF-8',
            'X-Mailer: School Management System'
        ];
        
        // تحضير المحتوى
        $html_message = prepare_email_template($message, $user, $template);
        
        // إرسال البريد
        $result = mail($to, $subject, $html_message, implode("\r\n", $headers));
        
        // تسجيل محاولة الإرسال
        log_activity($user_id, 'send_email', 'notifications', null, null, [
            'to' => $to,
            'subject' => $subject,
            'success' => $result
        ]);
        
        return $result;
        
    } catch (Exception $e) {
        log_error("Error sending email notification: " . $e->getMessage());
        return false;
    }
}

/**
 * تحضير قالب البريد الإلكتروني
 * Prepare email template
 */
function prepare_email_template($message, $user, $template = null) {
    $school_name = get_system_setting('school_name', 'School Management System');
    $school_logo = get_system_setting('school_logo', '');
    
    $html = '
    <!DOCTYPE html>
    <html dir="' . ($user['language'] === 'ar' ? 'rtl' : 'ltr') . '">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . htmlspecialchars($school_name) . '</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . htmlspecialchars($school_name) . '</h1>
            </div>
            <div class="content">
                <h2>' . __('dear') . ' ' . htmlspecialchars($user['full_name']) . '</h2>
                <div>' . $message . '</div>
            </div>
            <div class="footer">
                <p>' . htmlspecialchars($school_name) . ' - ' . __('all_rights_reserved') . '</p>
                <p>' . __('automated_message') . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

/**
 * إشعارات خاصة بالنظام المالي
 * Financial system notifications
 */
function notify_fee_assigned($student_id, $fee_amount, $fee_type, $due_date = null) {
    $variables = [
        'amount' => number_format($fee_amount, 2) . ' ' . get_system_setting('currency_symbol', 'ر.س'),
        'fee_type' => $fee_type,
        'due_date' => $due_date ? format_date($due_date) : ''
    ];
    
    return add_notification_from_template($student_id, 'new_fee_assigned', $variables, 'finance/fees/');
}

function notify_payment_received($student_id, $payment_amount, $payment_method) {
    $variables = [
        'amount' => number_format($payment_amount, 2) . ' ' . get_system_setting('currency_symbol', 'ر.س'),
        'method' => __($payment_method)
    ];
    
    return add_notification_from_template($student_id, 'payment_received', $variables, 'finance/payments/');
}

function notify_fee_overdue($student_id, $fee_amount, $days_overdue) {
    $variables = [
        'amount' => number_format($fee_amount, 2) . ' ' . get_system_setting('currency_symbol', 'ر.س'),
        'days' => $days_overdue
    ];
    
    return add_notification_from_template($student_id, 'fee_overdue', $variables, 'finance/fees/');
}

/**
 * إشعارات خاصة بالامتحانات
 * Exam system notifications
 */
function notify_exam_scheduled($student_id, $subject_name, $exam_date) {
    $variables = [
        'subject' => $subject_name,
        'date' => format_date($exam_date)
    ];
    
    return add_notification_from_template($student_id, 'exam_scheduled', $variables, 'exams/');
}

function notify_grade_published($student_id, $subject_name, $grade) {
    $variables = [
        'subject' => $subject_name,
        'grade' => $grade
    ];
    
    return add_notification_from_template($student_id, 'grade_published', $variables, 'grades/');
}

/**
 * إشعارات خاصة بالحضور
 * Attendance system notifications
 */
function notify_attendance_warning($student_id, $attendance_percentage) {
    $variables = [
        'percentage' => number_format($attendance_percentage, 1)
    ];
    
    return add_notification_from_template($student_id, 'attendance_warning', $variables, 'attendance/');
}
?>
