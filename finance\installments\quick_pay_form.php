<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// جلب معرف القسط
$installment_id = intval($_GET['id'] ?? 0);

if ($installment_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف القسط غير صحيح'));
    exit();
}

// جلب بيانات القسط
$stmt = $conn->prepare("
    SELECT
        si.id,
        si.student_id,
        si.installment_number,
        si.total_amount,
        si.paid_amount,
        si.due_date,
        si.status,
        si.notes,
        u.full_name as student_name,
        s.student_id as student_number,
        c.class_name
    FROM student_installments si
    JOIN students s ON si.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE si.id = ?
");

$stmt->bind_param("i", $installment_id);
$stmt->execute();
$installment = $stmt->get_result()->fetch_assoc();

if (!$installment) {
    header('Location: index.php?error=' . urlencode('القسط غير موجود'));
    exit();
}

$remaining_amount = $installment['total_amount'] - $installment['paid_amount'];

// معالجة الدفع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // نفس كود المعالجة من quick_pay.php
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $pay_amount = floatval($_POST['pay_amount'] ?? 0);
        $payment_method = clean_input($_POST['payment_method'] ?? 'cash');
        $payment_date = clean_input($_POST['payment_date'] ?? date('Y-m-d'));
        $payment_reference = clean_input($_POST['payment_reference'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        
        if ($pay_amount <= 0) {
            $error_message = __('invalid_amount');
        } elseif ($pay_amount > $remaining_amount) {
            $error_message = __('amount_exceeds_remaining');
        } else {
            try {
                $conn->begin_transaction();
                
                $new_paid_amount = $installment['paid_amount'] + $pay_amount;

                // تحديد الحالة الجديدة بناءً على المبلغ المدفوع وتاريخ الاستحقاق
                if ($new_paid_amount >= $installment['total_amount']) {
                    $new_status = 'paid';
                } elseif ($new_paid_amount > 0) {
                    $new_status = 'partial';
                } elseif ($installment['due_date'] < date('Y-m-d')) {
                    $new_status = 'overdue';
                } else {
                    $new_status = 'pending';
                }
                
                $update_stmt = $conn->prepare("UPDATE student_installments SET paid_amount = ?, status = ?, paid_date = ? WHERE id = ?");
                $paid_date = ($new_status === 'paid') ? $payment_date : null;
                $update_stmt->bind_param("dssi", $new_paid_amount, $new_status, $paid_date, $installment_id);
                
                if (!$update_stmt->execute()) {
                    throw new Exception(__('database_error') . ': ' . $conn->error);
                }
                
                // إضافة سجل الدفعة في جدول student_payments
                $receipt_number = 'PAY-' . date('Ymd') . '-' . $installment_id . rand(1000, 9999);
                $payment_stmt = $conn->prepare("INSERT INTO student_payments (student_id, installment_id, payment_method, amount, payment_reference, payment_date, receipt_number, status, notes, processed_by, processed_at, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'confirmed', ?, ?, NOW(), NOW())");
                if ($payment_stmt) {
                    $processed_by = $_SESSION['user_id'] ?? 1;
                    $payment_stmt->bind_param("iisdssssi", $installment['student_id'], $installment_id, $payment_method, $pay_amount, $payment_reference, $payment_date, $receipt_number, $notes, $processed_by);
                    $payment_stmt->execute();
                }
                
                $conn->commit();
                
                header("Location: index.php?payment_success=1&amount=" . $pay_amount);
                exit();
                
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = $e->getMessage();
            }
        }
    }
}

$page_title = __('quick_payment');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i><?php echo __('quick_payment'); ?></h4>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- معلومات القسط -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i><?php echo __('installment_details'); ?></h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong><?php echo __('student'); ?>:</strong> <?php echo htmlspecialchars($installment['student_name']); ?><br>
                                <strong><?php echo __('student_number'); ?>:</strong> <?php echo htmlspecialchars($installment['student_id']); ?><br>
                                <strong><?php echo __('class'); ?>:</strong> <?php echo htmlspecialchars($installment['class_name'] ?? 'غير محدد'); ?>
                            </div>
                            <div class="col-md-6">
                                <strong><?php echo __('installment_number'); ?>:</strong> #<?php echo $installment['installment_number']; ?><br>
                                <strong><?php echo __('due_date'); ?>:</strong> <?php echo date('Y-m-d', strtotime($installment['due_date'])); ?><br>
                                <strong><?php echo __('status'); ?>:</strong>
                                <?php
                                // تحديد الحالة الفعلية
                                $actual_status = $installment['status'];
                                if ($installment['paid_amount'] >= $installment['total_amount']) {
                                    $actual_status = 'paid';
                                } elseif ($installment['paid_amount'] > 0) {
                                    $actual_status = 'partial';
                                } elseif ($installment['due_date'] < date('Y-m-d')) {
                                    $actual_status = 'overdue';
                                } else {
                                    $actual_status = 'pending';
                                }

                                $status_class = '';
                                $status_text = '';
                                $status_icon = '';

                                switch ($actual_status) {
                                    case 'paid':
                                        $status_class = 'success';
                                        $status_text = 'مدفوع';
                                        $status_icon = 'fas fa-check';
                                        break;
                                    case 'partial':
                                        $status_class = 'info';
                                        $status_text = 'مدفوع جزئياً';
                                        $status_icon = 'fas fa-adjust';
                                        break;
                                    case 'overdue':
                                        $status_class = 'danger';
                                        $status_text = 'متأخر';
                                        $status_icon = 'fas fa-exclamation-triangle';
                                        break;
                                    case 'pending':
                                        $status_class = 'warning';
                                        $status_text = 'معلق';
                                        $status_icon = 'fas fa-clock';
                                        break;
                                }
                                ?>
                                <span class="badge bg-<?php echo $status_class; ?>">
                                    <i class="<?php echo $status_icon; ?> me-1"></i><?php echo $status_text; ?>
                                </span>

                                <?php if ($actual_status == 'overdue'): ?>
                                    <?php
                                    $days_overdue = (strtotime(date('Y-m-d')) - strtotime($installment['due_date'])) / (60 * 60 * 24);
                                    ?>
                                    <br><small class="text-danger">متأخر <?php echo floor($days_overdue); ?> يوم</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- المبالغ المالية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h5 class="text-primary"><?php echo format_currency($installment['total_amount']); ?></h5>
                                    <small class="text-muted"><?php echo __('total_amount'); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h5 class="text-success"><?php echo format_currency($installment['paid_amount']); ?></h5>
                                    <small class="text-muted"><?php echo __('paid_amount'); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h5 class="text-danger"><?php echo format_currency($remaining_amount); ?></h5>
                                    <small class="text-muted"><?php echo __('remaining_amount'); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نموذج الدفع -->
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><?php echo __('payment_details'); ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('pay_amount'); ?> <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="pay_amount" id="pay_amount" min="0.01" max="<?php echo $remaining_amount; ?>" step="0.01" value="<?php echo $remaining_amount; ?>" required onchange="updateRemaining()">
                                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                            </div>
                                            <div class="d-flex gap-2 mt-2">
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="setAmount(<?php echo $remaining_amount; ?>)"><?php echo __('pay_full_amount'); ?></button>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="setAmount(<?php echo $remaining_amount / 2; ?>)"><?php echo __('pay_half_amount'); ?></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('payment_method'); ?> <span class="text-danger">*</span></label>
                                            <select class="form-select" name="payment_method" required>
                                                <option value="cash"><?php echo __('cash'); ?></option>
                                                <option value="bank_transfer"><?php echo __('bank_transfer'); ?></option>
                                                <option value="check"><?php echo __('check'); ?></option>
                                                <option value="card"><?php echo __('card'); ?></option>
                                                <option value="online"><?php echo __('online'); ?></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('payment_date'); ?></label>
                                            <input type="date" class="form-control" name="payment_date" value="<?php echo date('Y-m-d'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('payment_reference'); ?></label>
                                            <input type="text" class="form-control" name="payment_reference" placeholder="<?php echo __('optional'); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('notes'); ?></label>
                                    <textarea class="form-control" name="notes" rows="2" placeholder="<?php echo __('payment_notes_placeholder'); ?>"></textarea>
                                </div>
                                
                                <!-- عرض المبلغ المتبقي بعد الدفع -->
                                <div class="alert alert-warning" id="remaining_alert">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><strong><?php echo __('remaining_after_payment'); ?>:</strong></span>
                                        <span class="fs-6 fw-bold" id="new_remaining">0.00 <?php echo get_currency_symbol(); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-money-bill-wave me-2"></i><?php echo __('process_payment'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateRemaining() {
    const totalAmount = <?php echo $installment['total_amount']; ?>;
    const paidAmount = <?php echo $installment['paid_amount']; ?>;
    const payAmount = parseFloat(document.getElementById('pay_amount').value) || 0;
    
    const newRemaining = totalAmount - paidAmount - payAmount;
    const alert = document.getElementById('remaining_alert');
    const remainingSpan = document.getElementById('new_remaining');
    
    remainingSpan.textContent = newRemaining.toFixed(2) + ' <?php echo get_currency_symbol(); ?>';
    
    alert.className = 'alert ';
    if (newRemaining <= 0) {
        alert.className += 'alert-success';
    } else {
        alert.className += 'alert-warning';
    }
}

function setAmount(amount) {
    document.getElementById('pay_amount').value = amount.toFixed(2);
    updateRemaining();
}

document.addEventListener('DOMContentLoaded', function() {
    updateRemaining();
});
</script>

<?php include_once '../../includes/footer.php'; ?>
