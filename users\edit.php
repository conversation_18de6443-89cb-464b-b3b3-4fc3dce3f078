<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

$id = intval($_GET['id'] ?? 0);
$error = '';
$success = '';

if ($id) {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $user = $stmt->get_result()->fetch_assoc();
    if (!$user) {
        die('المستخدم غير موجود');
    }
} else {
    die('معرف المستخدم غير صحيح');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $role = $_POST['role'] ?? 'student';
    $status = $_POST['status'] ?? 'active';
    if ($full_name && $email) {
        $stmt = $conn->prepare("UPDATE users SET full_name=?, email=?, role=?, status=? WHERE id=?");
        $stmt->bind_param("ssssi", $full_name, $email, $role, $status, $id);
        if ($stmt->execute()) {
            $success = 'تم التعديل بنجاح';
        } else {
            $error = 'حدث خطأ أثناء التعديل';
        }
    } else {
        $error = 'يرجى تعبئة جميع الحقول';
    }
}
?>
<div class="container">
    <h2>تعديل مستخدم</h2>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label>الاسم الكامل</label>
            <input type="text" name="full_name" class="form-control" value="<?= htmlspecialchars($user['full_name']) ?>" required>
        </div>
        <div class="mb-3">
            <label>البريد الإلكتروني</label>
            <input type="email" name="email" class="form-control" value="<?= htmlspecialchars($user['email']) ?>" required>
        </div>
        <div class="mb-3">
            <label>الدور</label>
            <select name="role" class="form-control">
                <option value="admin" <?= $user['role']=='admin'?'selected':'' ?>>مدير</option>
                <option value="teacher" <?= $user['role']=='teacher'?'selected':'' ?>>معلم</option>
                <option value="student" <?= $user['role']=='student'?'selected':'' ?>>طالب</option>
                <option value="staff" <?= $user['role']=='staff'?'selected':'' ?>>موظف</option>
            </select>
        </div>
        <div class="mb-3">
            <label>الحالة</label>
            <select name="status" class="form-control">
                <option value="active" <?= $user['status']=='active'?'selected':'' ?>>نشط</option>
                <option value="inactive" <?= $user['status']=='inactive'?'selected':'' ?>>غير نشط</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">حفظ التعديلات</button>
        <a href="index.php" class="btn btn-secondary">رجوع</a>
    </form>
</div>
<?php require_once '../includes/footer.php'; ?> 