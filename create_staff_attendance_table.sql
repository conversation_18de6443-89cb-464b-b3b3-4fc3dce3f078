-- إن<PERSON><PERSON><PERSON> جدول staff_attendance للموظفين (المعلمين والإداريين)
-- Create staff_attendance table for staff (teachers and admins)

USE school_management;

-- إن<PERSON>اء جدول staff_attendance
CREATE TABLE IF NOT EXISTS `staff_attendance` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL,
  `staff_type` enum('teacher','admin','staff') NOT NULL DEFAULT 'staff',
  `attendance_date` date NOT NULL,
  `status` enum('present','absent','late','excused','sick_leave','regular_leave','half_day') NOT NULL DEFAULT 'present',
  `check_in_time` time DEFAULT NULL,
  `check_out_time` time DEFAULT NULL,
  `break_start_time` time DEFAULT NULL,
  `break_end_time` time DEFAULT NULL,
  `total_hours` decimal(4,2) DEFAULT NULL,
  `overtime_hours` decimal(4,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `device_info` varchar(255) DEFAULT NULL,
  `recorded_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_by` int(10) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_date` (`user_id`, `attendance_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_staff_type` (`staff_type`),
  KEY `idx_attendance_date` (`attendance_date`),
  KEY `idx_status` (`status`),
  KEY `idx_recorded_by` (`recorded_by`),
  KEY `idx_approved_by` (`approved_by`),
  KEY `idx_date_status` (`attendance_date`, `status`),
  KEY `idx_user_date_status` (`user_id`, `attendance_date`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة القيود الخارجية
ALTER TABLE `staff_attendance`
  ADD CONSTRAINT `staff_attendance_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_attendance_recorded_by_foreign` FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `staff_attendance_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- نقل البيانات من جدول admin_attendance إلى staff_attendance
INSERT IGNORE INTO staff_attendance (
    user_id, staff_type, attendance_date, status, check_in_time, check_out_time, 
    notes, recorded_by, created_at, updated_at
)
SELECT 
    admin_id as user_id,
    'admin' as staff_type,
    attendance_date,
    status,
    check_in_time,
    check_out_time,
    notes,
    recorded_by,
    created_at,
    updated_at
FROM admin_attendance;

-- إنشاء view موحدة لجميع أنواع الحضور
CREATE OR REPLACE VIEW all_attendance AS
SELECT 
    'student' as attendance_type,
    a.id,
    a.student_id as user_id,
    u.full_name as user_name,
    u.role as user_role,
    a.attendance_date,
    a.status,
    a.arrival_time as check_in_time,
    a.departure_time as check_out_time,
    NULL as total_hours,
    a.notes,
    a.marked_by as recorded_by,
    a.marked_at as created_at,
    a.updated_at
FROM attendance a
LEFT JOIN students s ON a.student_id = s.id
LEFT JOIN users u ON s.user_id = u.id

UNION ALL

SELECT 
    'staff' as attendance_type,
    sa.id,
    sa.user_id,
    u.full_name as user_name,
    u.role as user_role,
    sa.attendance_date,
    sa.status,
    sa.check_in_time,
    sa.check_out_time,
    sa.total_hours,
    sa.notes,
    sa.recorded_by,
    sa.created_at,
    sa.updated_at
FROM staff_attendance sa
LEFT JOIN users u ON sa.user_id = u.id;

-- إنشاء view خاصة بحضور الموظفين مع تفاصيل إضافية
CREATE OR REPLACE VIEW staff_attendance_detailed AS
SELECT 
    sa.*,
    u.full_name as user_name,
    u.role as user_role,
    u.email as user_email,
    recorder.full_name as recorded_by_name,
    approver.full_name as approved_by_name,
    CASE 
        WHEN sa.status = 'present' AND sa.check_in_time IS NOT NULL AND sa.check_out_time IS NOT NULL THEN
            TIMESTAMPDIFF(MINUTE, sa.check_in_time, sa.check_out_time) / 60.0
        ELSE NULL
    END as calculated_hours,
    CASE 
        WHEN sa.check_in_time > '09:00:00' THEN 'late'
        WHEN sa.check_in_time <= '09:00:00' THEN 'on_time'
        ELSE 'unknown'
    END as punctuality_status
FROM staff_attendance sa
LEFT JOIN users u ON sa.user_id = u.id
LEFT JOIN users recorder ON sa.recorded_by = recorder.id
LEFT JOIN users approver ON sa.approved_by = approver.id;

-- إدراج بيانات تجريبية للاختبار
INSERT IGNORE INTO staff_attendance (user_id, staff_type, attendance_date, status, check_in_time, check_out_time, recorded_by) VALUES
(1, 'admin', CURDATE(), 'present', '08:30:00', '16:30:00', 1),
(2, 'teacher', CURDATE(), 'present', '08:45:00', '15:45:00', 1),
(12, 'staff', CURDATE(), 'present', '09:00:00', '17:00:00', 1),
(15, 'admin', CURDATE(), 'present', '08:15:00', '16:15:00', 1),
(18, 'staff', CURDATE(), 'late', '09:30:00', '17:30:00', 1);

-- إنشاء إجراء مخزن لتسجيل الحضور
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS record_staff_attendance(
    IN p_user_id INT,
    IN p_attendance_date DATE,
    IN p_status VARCHAR(20),
    IN p_check_in_time TIME,
    IN p_check_out_time TIME,
    IN p_notes TEXT,
    IN p_recorded_by INT
)
BEGIN
    DECLARE v_staff_type VARCHAR(20);
    
    -- تحديد نوع الموظف
    SELECT role INTO v_staff_type FROM users WHERE id = p_user_id;
    
    -- إدراج أو تحديث سجل الحضور
    INSERT INTO staff_attendance (
        user_id, staff_type, attendance_date, status, 
        check_in_time, check_out_time, notes, recorded_by
    ) VALUES (
        p_user_id, v_staff_type, p_attendance_date, p_status,
        p_check_in_time, p_check_out_time, p_notes, p_recorded_by
    )
    ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        check_in_time = VALUES(check_in_time),
        check_out_time = VALUES(check_out_time),
        notes = VALUES(notes),
        recorded_by = VALUES(recorded_by),
        updated_at = CURRENT_TIMESTAMP;
END//

DELIMITER ;

-- إنشاء دالة للحصول على إحصائيات الحضور
DELIMITER //

CREATE FUNCTION IF NOT EXISTS get_staff_attendance_rate(
    p_user_id INT,
    p_start_date DATE,
    p_end_date DATE
) RETURNS DECIMAL(5,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE total_days INT DEFAULT 0;
    DECLARE present_days INT DEFAULT 0;
    DECLARE attendance_rate DECIMAL(5,2) DEFAULT 0.00;
    
    -- حساب إجمالي الأيام
    SELECT COUNT(*) INTO total_days
    FROM staff_attendance
    WHERE user_id = p_user_id
    AND attendance_date BETWEEN p_start_date AND p_end_date;
    
    -- حساب أيام الحضور
    SELECT COUNT(*) INTO present_days
    FROM staff_attendance
    WHERE user_id = p_user_id
    AND attendance_date BETWEEN p_start_date AND p_end_date
    AND status IN ('present', 'late');
    
    -- حساب معدل الحضور
    IF total_days > 0 THEN
        SET attendance_rate = (present_days / total_days) * 100;
    END IF;
    
    RETURN attendance_rate;
END//

DELIMITER ;

-- إضافة فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_staff_attendance_month ON staff_attendance(user_id, YEAR(attendance_date), MONTH(attendance_date));
CREATE INDEX IF NOT EXISTS idx_staff_attendance_week ON staff_attendance(user_id, YEARWEEK(attendance_date));

SELECT 'Staff attendance table created successfully!' as message;
