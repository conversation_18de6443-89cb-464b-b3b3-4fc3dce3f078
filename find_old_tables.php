<?php
/**
 * البحث عن استخدامات الجداول القديمة
 */

echo "<h1>🔍 البحث عن استخدامات الجداول القديمة</h1>";

// الجداول القديمة المطلوب استبدالها
$old_tables = [
    'staff_attendance' => 'staff_attendance',
    'staff_attendance' => 'staff_attendance'
];

// المجلدات المطلوب البحث فيها
$directories = [
    'attendance/',
    'includes/',
    'dashboard/',
    'reports/',
    'api/'
];

$found_files = [];
$total_matches = 0;

echo "<div class='alert alert-info'>";
echo "<h4>🎯 البحث عن:</h4>";
echo "<ul>";
foreach ($old_tables as $old => $new) {
    echo "<li><code>$old</code> → <code>$new</code></li>";
}
echo "</ul>";
echo "</div>";

// البحث في الملفات
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "<h3>📁 البحث في مجلد: $dir</h3>";
        
        $files = glob($dir . '*.php');
        foreach ($files as $file) {
            $content = file_get_contents($file);
            $file_matches = [];
            
            foreach ($old_tables as $old_table => $new_table) {
                // البحث عن الجدول القديم
                if (stripos($content, $old_table) !== false) {
                    // عد المطابقات
                    $matches = substr_count(strtolower($content), strtolower($old_table));
                    $file_matches[$old_table] = $matches;
                    $total_matches += $matches;
                }
            }
            
            if (!empty($file_matches)) {
                $found_files[$file] = $file_matches;
            }
        }
    }
}

// البحث في الملفات في المجلد الجذر
echo "<h3>📁 البحث في المجلد الجذر:</h3>";
$root_files = glob('*.php');
foreach ($root_files as $file) {
    $content = file_get_contents($file);
    $file_matches = [];
    
    foreach ($old_tables as $old_table => $new_table) {
        if (stripos($content, $old_table) !== false) {
            $matches = substr_count(strtolower($content), strtolower($old_table));
            $file_matches[$old_table] = $matches;
            $total_matches += $matches;
        }
    }
    
    if (!empty($file_matches)) {
        $found_files[$file] = $file_matches;
    }
}

// عرض النتائج
if (!empty($found_files)) {
    echo "<div class='alert alert-warning'>";
    echo "<h4>⚠️ وُجدت $total_matches مطابقة في " . count($found_files) . " ملف</h4>";
    echo "</div>";
    
    echo "<table class='table table-striped'>";
    echo "<tr><th>الملف</th><th>الجدول القديم</th><th>عدد المطابقات</th><th>الإجراء المطلوب</th></tr>";
    
    foreach ($found_files as $file => $matches) {
        $row_span = count($matches);
        $first = true;
        
        foreach ($matches as $old_table => $count) {
            echo "<tr>";
            if ($first) {
                echo "<td rowspan='$row_span'><strong>" . htmlspecialchars($file) . "</strong></td>";
                $first = false;
            }
            echo "<td><code class='text-danger'>$old_table</code></td>";
            echo "<td><span class='badge bg-warning'>$count</span></td>";
            echo "<td><code class='text-success'>→ staff_attendance</code></td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    // زر الإصلاح التلقائي
    echo "<div class='mt-4'>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='auto_fix'>";
    echo "<button type='submit' class='btn btn-danger btn-lg' onclick='return confirm(\"هل أنت متأكد من تطبيق الإصلاح التلقائي؟\\n\\nسيتم استبدال جميع المراجع للجداول القديمة بالجدول الموحد الجديد.\")'>🔧 إصلاح تلقائي لجميع الملفات</button>";
    echo "</form>";
    echo "</div>";
    
} else {
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ ممتاز! لا توجد مراجع للجداول القديمة</h4>";
    echo "<p>جميع الملفات تستخدم الجدول الموحد الجديد <code>staff_attendance</code></p>";
    echo "</div>";
}

// معالجة الإصلاح التلقائي
if ($_POST['action'] ?? '' === 'auto_fix') {
    echo "<hr>";
    echo "<h3>🔧 تطبيق الإصلاح التلقائي:</h3>";
    
    $fixed_files = 0;
    $total_replacements = 0;
    
    foreach ($found_files as $file => $matches) {
        echo "<h4>📝 إصلاح ملف: $file</h4>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        $file_replacements = 0;
        
        foreach ($old_tables as $old_table => $new_table) {
            if (isset($matches[$old_table])) {
                // استبدال مراجع الجدول
                $patterns = [
                    // FROM table_name
                    "/FROM\s+$old_table(\s|$)/i" => "FROM $new_table$1",
                    // JOIN table_name
                    "/JOIN\s+$old_table(\s|$)/i" => "JOIN $new_table$1",
                    // LEFT JOIN table_name
                    "/LEFT\s+JOIN\s+$old_table(\s|$)/i" => "LEFT JOIN $new_table$1",
                    // RIGHT JOIN table_name
                    "/RIGHT\s+JOIN\s+$old_table(\s|$)/i" => "RIGHT JOIN $new_table$1",
                    // INNER JOIN table_name
                    "/INNER\s+JOIN\s+$old_table(\s|$)/i" => "INNER JOIN $new_table$1",
                    // INSERT INTO table_name
                    "/INSERT\s+INTO\s+$old_table(\s|$)/i" => "INSERT INTO $new_table$1",
                    // UPDATE table_name
                    "/UPDATE\s+$old_table(\s|$)/i" => "UPDATE $new_table$1",
                    // DELETE FROM table_name
                    "/DELETE\s+FROM\s+$old_table(\s|$)/i" => "DELETE FROM $new_table$1",
                    // table_name alias
                    "/\b$old_table\s+(\w+)\s+ON/i" => "$new_table $1 ON",
                ];
                
                foreach ($patterns as $pattern => $replacement) {
                    $new_content = preg_replace($pattern, $replacement, $content);
                    if ($new_content !== $content) {
                        $content = $new_content;
                        $file_replacements++;
                    }
                }
                
                // استبدال بسيط للحالات المتبقية
                $simple_replacements = str_ireplace($old_table, $new_table, $content);
                if ($simple_replacements !== $content) {
                    $content = $simple_replacements;
                    $file_replacements++;
                }
            }
        }
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "<p>✅ تم إصلاح الملف ($file_replacements تغيير)</p>";
                $fixed_files++;
                $total_replacements += $file_replacements;
            } else {
                echo "<p>❌ فشل في حفظ الملف</p>";
            }
        } else {
            echo "<p>ℹ️ لا توجد تغييرات مطلوبة</p>";
        }
    }
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h4>🎉 تم الإصلاح التلقائي!</h4>";
    echo "<ul>";
    echo "<li><strong>الملفات المُصلحة:</strong> $fixed_files</li>";
    echo "<li><strong>إجمالي التغييرات:</strong> $total_replacements</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<h4>🔄 يُنصح بإعادة تشغيل الاختبار للتأكد</h4>";
    echo '<a href="find_old_tables.php" class="btn btn-primary">🔍 إعادة الفحص</a>';
    echo "</div>";
}

echo "<hr>";
echo "<div class='alert alert-info'>";
echo "<h4>📋 ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>تأكد من عمل نسخة احتياطية قبل التطبيق</li>";
echo "<li>الإصلاح التلقائي يغطي معظم الحالات الشائعة</li>";
echo "<li>قد تحتاج بعض الحالات المعقدة لإصلاح يدوي</li>";
echo "<li>اختبر النظام بعد الإصلاح للتأكد من عمله</li>";
echo "</ul>";
echo "</div>";

// حذف الملف بعد 5 دقائق
echo "<script>
setTimeout(function() {
    if (confirm('هل تريد حذف ملف البحث؟')) {
        fetch('cleanup_find_tables.php');
    }
}, 300000);
</script>";
?>
