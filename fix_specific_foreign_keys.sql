-- إصلاح القيود الخارجية المُشكلة تحديداً
-- Fix specific problematic foreign key constraints

USE school_management;

-- تعطيل فحص القيود الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- 1. إص<PERSON><PERSON><PERSON> جدول unified_staff_absences
-- حذف جميع القيود الخارجية الموجودة
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_user_id_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_applied_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_approved_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_rejected_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_replacement_foreign;

-- تنظيف البيانات غير الصحيحة
-- حذ<PERSON> السجلات التي تشير لمستخدمين غير موجودين
DELETE FROM unified_staff_absences WHERE user_id NOT IN (SELECT id FROM users);

-- تحديث الحقول الاختيارية لتشير لمستخدمين موجودين أو NULL
UPDATE unified_staff_absences 
SET applied_by = (SELECT MIN(id) FROM users WHERE role IN ('admin', 'manager') LIMIT 1)
WHERE applied_by NOT IN (SELECT id FROM users);

UPDATE unified_staff_absences 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

UPDATE unified_staff_absences 
SET rejected_by = NULL 
WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);

UPDATE unified_staff_absences 
SET replacement_user_id = NULL 
WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود الخارجية بالإعدادات الصحيحة
ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_applied_by_foreign 
FOREIGN KEY (applied_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_approved_by_foreign 
FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_rejected_by_foreign 
FOREIGN KEY (rejected_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_replacement_foreign 
FOREIGN KEY (replacement_user_id) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- 2. إصلاح جدول staff_absences_with_deduction
-- حذف القيود الموجودة
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_user_id_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_approved_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_recorded_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_processed_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_created_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_rejected_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_with_deduction_user_id_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_with_deduction_approved_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_with_deduction_recorded_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_with_deduction_processed_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_with_deduction_created_by_foreign;

-- تنظيف البيانات
DELETE FROM staff_absences_with_deduction WHERE user_id NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET recorded_by = (SELECT MIN(id) FROM users LIMIT 1)
WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET created_by = (SELECT MIN(id) FROM users LIMIT 1)
WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET processed_by = NULL 
WHERE processed_by IS NOT NULL AND processed_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET rejected_by = NULL 
WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود
ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT staff_absences_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT staff_absences_recorded_by_foreign 
FOREIGN KEY (recorded_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT staff_absences_created_by_foreign 
FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT staff_absences_approved_by_foreign 
FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT staff_absences_processed_by_foreign 
FOREIGN KEY (processed_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT staff_absences_rejected_by_foreign 
FOREIGN KEY (rejected_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- 3. إصلاح جدول staff_leaves
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_user_id_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_applied_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_approved_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_rejected_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_cancelled_by_foreign;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_replacement_user_id_foreign;

-- تنظيف البيانات
DELETE FROM staff_leaves WHERE user_id NOT IN (SELECT id FROM users);

UPDATE staff_leaves 
SET applied_by = (SELECT MIN(id) FROM users LIMIT 1)
WHERE applied_by IS NOT NULL AND applied_by NOT IN (SELECT id FROM users);

UPDATE staff_leaves 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

UPDATE staff_leaves 
SET rejected_by = NULL 
WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);

UPDATE staff_leaves 
SET cancelled_by = NULL 
WHERE cancelled_by IS NOT NULL AND cancelled_by NOT IN (SELECT id FROM users);

UPDATE staff_leaves 
SET replacement_user_id = NULL 
WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود
ALTER TABLE staff_leaves
ADD CONSTRAINT staff_leaves_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE staff_leaves
ADD CONSTRAINT staff_leaves_applied_by_foreign 
FOREIGN KEY (applied_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_leaves
ADD CONSTRAINT staff_leaves_approved_by_foreign 
FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_leaves
ADD CONSTRAINT staff_leaves_rejected_by_foreign 
FOREIGN KEY (rejected_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_leaves
ADD CONSTRAINT staff_leaves_cancelled_by_foreign 
FOREIGN KEY (cancelled_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_leaves
ADD CONSTRAINT staff_leaves_replacement_user_id_foreign 
FOREIGN KEY (replacement_user_id) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- 4. إصلاح جدول staff_attendance
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_user_id_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_recorded_by_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_approved_by_foreign;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_absence_id_foreign;

-- تنظيف البيانات
DELETE FROM staff_attendance WHERE user_id NOT IN (SELECT id FROM users);

UPDATE staff_attendance 
SET recorded_by = (SELECT MIN(id) FROM users LIMIT 1)
WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

UPDATE staff_attendance 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

UPDATE staff_attendance 
SET absence_id = NULL 
WHERE absence_id IS NOT NULL AND absence_id NOT IN (SELECT id FROM unified_staff_absences);

-- إعادة إضافة القيود
ALTER TABLE staff_attendance
ADD CONSTRAINT staff_attendance_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE staff_attendance
ADD CONSTRAINT staff_attendance_recorded_by_foreign 
FOREIGN KEY (recorded_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_attendance
ADD CONSTRAINT staff_attendance_approved_by_foreign 
FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE staff_attendance
ADD CONSTRAINT staff_attendance_absence_id_foreign 
FOREIGN KEY (absence_id) REFERENCES unified_staff_absences (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- 5. إصلاح جدول admin_attendance
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_ibfk_1;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_ibfk_2;
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_user_id_foreign;

-- تنظيف البيانات
DELETE FROM admin_attendance WHERE admin_id NOT IN (SELECT id FROM users);

UPDATE admin_attendance 
SET recorded_by = (SELECT MIN(id) FROM users LIMIT 1)
WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود
ALTER TABLE admin_attendance
ADD CONSTRAINT admin_attendance_user_id_foreign 
FOREIGN KEY (admin_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE admin_attendance
ADD CONSTRAINT admin_attendance_recorded_by_foreign 
FOREIGN KEY (recorded_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- إعادة تفعيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- إنشاء إجراء لحذف المستخدمين بأمان تام
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS safe_delete_user_complete(IN p_user_id INT)
BEGIN
    DECLARE user_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدم
    SELECT COUNT(*) INTO user_exists FROM users WHERE id = p_user_id;
    
    IF user_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'User does not exist';
    END IF;
    
    -- تحديث الحقول التي تشير للمستخدم كـ applied_by, recorded_by, etc. إلى NULL
    UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by = p_user_id;
    UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by = p_user_id;
    
    UPDATE staff_leaves SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE staff_leaves SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by = p_user_id;
    UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_attendance SET approved_by = NULL WHERE approved_by = p_user_id;
    
    UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    
    -- الآن يمكن حذف المستخدم بأمان
    DELETE FROM users WHERE id = p_user_id;
    
    COMMIT;
    
    SELECT CONCAT('User ', p_user_id, ' deleted successfully with all references cleaned') as message;
END//

DELIMITER ;

-- اختبار سريع للتأكد من إصلاح المشكلة
SELECT 'All foreign key constraints have been fixed with proper CASCADE and SET NULL actions!' as status;

-- عرض القيود الخارجية المُحدثة
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND TABLE_NAME IN ('unified_staff_absences', 'staff_absences_with_deduction', 'staff_leaves', 'staff_attendance', 'admin_attendance')
AND REFERENCED_TABLE_NAME = 'users'
ORDER BY TABLE_NAME, COLUMN_NAME;
