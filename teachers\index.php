<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين اللغة الافتراضية إذا لم تكن محددة
if (!isset($_SESSION['system_language'])) {
    $_SESSION['system_language'] = 'ar';
}

/**
 * صفحة إدارة المعلمين
 * Teachers Management Page
 */

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// معالجة الحذف
if (isset($_POST['delete_teacher'])) {
    $teacher_id = intval($_POST['teacher_id']);

    global $conn;
    $conn->begin_transaction();

    try {
        // الحصول على user_id قبل حذف المعلم
        $stmt = $conn->prepare("SELECT user_id FROM teachers WHERE id = ?");
        $stmt->bind_param("i", $teacher_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $teacher_data = $result->fetch_assoc();
        $user_id = $teacher_data['user_id'] ?? null;
        $stmt->close();

        // حذف المعلم
        $stmt = $conn->prepare("DELETE FROM teachers WHERE id = ?");
        $stmt->bind_param("i", $teacher_id);
        $stmt->execute();
        $stmt->close();

        // حذف المستخدم إذا كان موجوداً
        if ($user_id) {
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $stmt->close();
        }

        $conn->commit();

        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'delete_teacher', 'teachers', $teacher_id, null, [
            'teacher_id' => $teacher_id,
            'user_id' => $user_id
        ]);

        $_SESSION['success_message'] = __('deleted_successfully');
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = __('error_occurred');
        log_error("Error deleting teacher: " . $e->getMessage());
    }

    header('Location: index.php');
    exit();
}

// تحميل ملف اللغة قبل استخدام دالة الترجمة
load_language();

$page_title = __('teachers');
require_once '../includes/header.php';

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$department_filter = clean_input($_GET['department'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');

// بناء استعلام البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(u.username LIKE ? OR t.employee_id LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
    $types .= "ss";
}

if (!empty($department_filter)) {
    $where_conditions[] = "t.department = ?";
    $params[] = $department_filter;
    $types .= "s";
}

if (!empty($status_filter)) {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب المعلمين
$query = "
    SELECT
        t.*,
        u.username,
        u.email,
        u.status,
        u.last_login,
        u.created_at as user_created_at,
        COALESCE(u.full_name, u.username) as display_name
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE $where_clause
    ORDER BY u.username ASC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
if (!$stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($query));
}
$params[] = $records_per_page;
$params[] = $offset;
$types .= "ii";

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$teachers = $stmt->get_result();

// جلب قائمة الأقسام للفلترة
$departments = $conn->query("SELECT DISTINCT department FROM teachers WHERE department IS NOT NULL ORDER BY department");

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_teachers,
        SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_teachers,
        COUNT(DISTINCT t.department) as total_departments,
        AVG(DATEDIFF(CURDATE(), t.hire_date) / 365.25) as avg_experience
    FROM teachers t
    JOIN users u ON t.user_id = u.id
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('teachers'); ?></h1>
            <p class="text-muted"><?php echo __('manage_teachers_info'); ?></p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('add_teacher'); ?>
            </a>
            <a href="import.php" class="btn btn-success">
                <i class="fas fa-upload me-2"></i><?php echo __('import_teachers'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({
                    title: '<?php echo __('success'); ?>',
                    text: '<?php echo $_SESSION['success_message']; ?>',
                    icon: 'success',
                    confirmButtonText: '<?php echo __('ok'); ?>',
                    customClass: {
                        confirmButton: 'btn btn-success'
                    },
                    buttonsStyling: false
                });
            });
        </script>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({
                    title: '<?php echo __('error'); ?>',
                    text: '<?php echo $_SESSION['error_message']; ?>',
                    icon: 'error',
                    confirmButtonText: '<?php echo __('ok'); ?>',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    },
                    buttonsStyling: false
                });
            });
        </script>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-chalkboard-teacher text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_teachers']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_teachers'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-user-check text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['active_teachers']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('active_teachers'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient p-3 rounded-3">
                                <i class="fas fa-building text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_departments']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('departments'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-clock text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['avg_experience'] ?? 0, 1); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('avg_experience_years'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label"><?php echo __('search'); ?></label>
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="<?php echo __('search_by_name_number'); ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="department" class="form-label"><?php echo __('department'); ?></label>
                    <select class="form-select" id="department" name="department">
                        <option value=""><?php echo __('all_departments'); ?></option>
                        <?php while ($dept = $departments->fetch_assoc()): ?>
                            <option value="<?php echo htmlspecialchars($dept['department']); ?>" 
                                    <?php echo ($department_filter == $dept['department']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($dept['department']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>
                            <?php echo __('active'); ?>
                        </option>
                        <option value="inactive" <?php echo ($status_filter == 'inactive') ? 'selected' : ''; ?>>
                            <?php echo __('inactive'); ?>
                        </option>
                        <option value="suspended" <?php echo ($status_filter == 'suspended') ? 'selected' : ''; ?>>
                            <?php echo __('suspended'); ?>
                        </option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Teachers Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0"><?php echo __('teachers_list'); ?></h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printTable()">
                    <i class="fas fa-print me-1"></i><?php echo __('print'); ?>
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-1"></i><?php echo __('export'); ?>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php if ($teachers->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="teachersTable">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo __('photo'); ?></th>
                                <th><?php echo __('teacher_info'); ?></th>
                                <th><?php echo __('specialization'); ?></th>
                                <th><?php echo __('department'); ?></th>
                                <th><?php echo __('contact_info'); ?></th>
                                <th><?php echo __('hire_date'); ?></th>
                                <th><?php echo __('status'); ?></th>
                                <th><?php echo __('last_login'); ?></th>
                                <th class="no-print"><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($teacher = $teachers->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($teacher['profile_picture'])): ?>
                                            <img src="../uploads/profiles/<?php echo $teacher['profile_picture']; ?>" 
                                                 alt="<?php echo htmlspecialchars($teacher['display_name']); ?>"
                                                 class="rounded-circle" 
                                                 width="40" 
                                                 height="40">
                                        <?php else: ?>
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($teacher['display_name']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo __('employee_id'); ?>: <?php echo htmlspecialchars($teacher['employee_id']); ?>
                                            </small>
                                            <?php if (!empty($teacher['national_id'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo __('national_id'); ?>: <?php echo htmlspecialchars($teacher['national_id']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($teacher['specialization'])): ?>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($teacher['specialization']); ?>
                                            </span>
                                            <?php if (!empty($teacher['qualification'])): ?>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($teacher['qualification']); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo __('not_specified'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($teacher['department'])): ?>
                                            <?php echo htmlspecialchars($teacher['department']); ?>
                                            <?php if (!empty($teacher['office_location'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?php echo htmlspecialchars($teacher['office_location']); ?>
                                                </small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo __('not_assigned'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($teacher['email'])): ?>
                                            <div><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($teacher['email']); ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($teacher['phone'])): ?>
                                            <div><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($teacher['phone']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($teacher['hire_date']): ?>
                                            <?php echo format_date($teacher['hire_date']); ?>
                                            <br>
                                            <small class="text-muted">
                                                <?php 
                                                $years = floor((time() - strtotime($teacher['hire_date'])) / (365.25 * 24 * 60 * 60));
                                                echo $years . ' ' . __('years');
                                                ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo __('not_specified'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($teacher['status']) {
                                            case 'active':
                                                $status_class = 'bg-success';
                                                $status_text = __('active');
                                                break;
                                            case 'inactive':
                                                $status_class = 'bg-warning';
                                                $status_text = __('inactive');
                                                break;
                                            case 'suspended':
                                                $status_class = 'bg-danger';
                                                $status_text = __('suspended');
                                                break;
                                            default:
                                                $status_class = 'bg-secondary';
                                                $status_text = $teacher['status'];
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($teacher['last_login']): ?>
                                            <small class="text-muted">
                                                <?php echo format_datetime($teacher['last_login'], 'Y/m/d H:i'); ?>
                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted"><?php echo __('never_logged_in'); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?php echo $teacher['id']; ?>" 
                                               class="btn btn-outline-info" 
                                               title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <a href="edit.php?id=<?php echo $teacher['id']; ?>" 
                                               class="btn btn-outline-primary" 
                                               title="<?php echo __('edit'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <a href="assignments.php?teacher_id=<?php echo $teacher['id']; ?>"
                                               class="btn btn-outline-success"
                                               title="<?php echo __('assignments'); ?>">
                                                <i class="fas fa-tasks"></i>
                                            </a>
                                            
                                            <button type="button"
                                                    class="btn btn-outline-danger"
                                                    onclick="confirmDelete(<?php echo $teacher['id']; ?>, '<?php echo htmlspecialchars($teacher['display_name'], ENT_QUOTES); ?>')"
                                                    title="<?php echo __('delete'); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Teachers pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo __('previous'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $start = max(1, $page - 2);
                            $end = min($total_pages, $page + 2);
                            
                            for ($i = $start; $i <= $end; $i++):
                            ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo __('next'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo __('no_teachers_found'); ?></h5>
                    <p class="text-muted"><?php echo __('try_different_search'); ?></p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_first_teacher'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Hidden form for deletion -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="teacher_id" id="deleteTeacherId">
    <input type="hidden" name="delete_teacher" value="1">
</form>

<script>
function confirmDelete(teacherId, teacherName) {
    // استخدام SweetAlert للتأكيد
    Swal.fire({
        title: '<?php echo __('confirm_delete'); ?>',
        html: `
            <div class="text-center mb-3">
                <i class="fas fa-user-times fa-3x text-danger"></i>
            </div>
            <p><?php echo __('are_you_sure_delete_teacher'); ?></p>
            <div class="alert alert-warning">
                <strong><?php echo __('teacher_name'); ?>:</strong> ${teacherName}
            </div>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong><?php echo __('warning'); ?>:</strong> <?php echo __('delete_teacher_warning'); ?>
            </div>
            <p class="text-muted small"><?php echo __('this_action_cannot_be_undone'); ?></p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>',
        cancelButtonText: '<i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار رسالة تحميل
            Swal.fire({
                title: '<?php echo __('deleting'); ?>...',
                text: '<?php echo __('please_wait'); ?>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // إرسال النموذج
            document.getElementById('deleteTeacherId').value = teacherId;
            document.getElementById('deleteForm').submit();
        }
    });
}

    // Print table
    function printTable() {
        window.print();
    }

    // Export to Excel
    function exportToExcel() {
        const table = document.getElementById('teachersTable');
        const wb = XLSX.utils.table_to_book(table);
        XLSX.writeFile(wb, 'teachers_<?php echo date('Y-m-d'); ?>.xlsx');
    }

    // Auto-submit search form on input
    document.getElementById('search').addEventListener('input', function() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });
</script>

<!-- Include XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<?php require_once '../includes/footer.php'; ?>
