<?php
/**
 * تعديل الإجازة
 * Edit Leave
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$leave_id = intval($_GET['id'] ?? 0);

if (!$leave_id) {
    header('Location: manage_leaves.php');
    exit();
}

// جلب تفاصيل الإجازة
$query = "SELECT sl.*, u.full_name FROM staff_leaves sl JOIN users u ON sl.user_id = u.id WHERE sl.id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $leave_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: manage_leaves.php');
    exit();
}

$leave = $result->fetch_assoc();

// معالجة التحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $leave_type = clean_input($_POST['leave_type']);
        $start_date = clean_input($_POST['start_date']);
        $end_date = clean_input($_POST['end_date']);
        $reason = clean_input($_POST['reason']);
        $status = clean_input($_POST['status']);

        if (empty($leave_type) || empty($start_date) || empty($end_date)) {
            $error_message = 'جميع الحقول مطلوبة';
        } elseif ($start_date > $end_date) {
            $error_message = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
        } else {
            try {
                // حساب عدد الأيام
                $start = new DateTime($start_date);
                $end = new DateTime($end_date);
                $interval = $start->diff($end);
                $total_days = $interval->days + 1;

                // تحديث الإجازة
                $stmt = $conn->prepare("
                    UPDATE staff_leaves 
                    SET leave_type = ?, start_date = ?, end_date = ?, total_days = ?, reason = ?, status = ?
                    WHERE id = ?
                ");
                $stmt->bind_param("sssissi", $leave_type, $start_date, $end_date, $total_days, $reason, $status, $leave_id);
                
                if ($stmt->execute()) {
                    $success_message = 'تم تحديث الإجازة بنجاح';
                    
                    // تسجيل النشاط
                    log_activity($user_id, 'update_leave', 'staff_leaves', $leave_id);
                    
                    // إعادة جلب البيانات المحدثة
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("i", $leave_id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $leave = $result->fetch_assoc();
                } else {
                    $error_message = 'حدث خطأ أثناء التحديث';
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ: ' . $e->getMessage();
            }
        }
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الإجازة - <?php echo htmlspecialchars($leave['full_name']); ?>
                        </h5>
                        <a href="manage_leaves.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="leave_type" class="form-label">نوع الإجازة *</label>
                                    <select class="form-select" id="leave_type" name="leave_type" required>
                                        <option value="sick" <?php echo $leave['leave_type'] === 'sick' ? 'selected' : ''; ?>>
                                            إجازة مرضية
                                        </option>
                                        <option value="regular" <?php echo $leave['leave_type'] === 'regular' ? 'selected' : ''; ?>>
                                            إجازة اعتيادية
                                        </option>
                                        <option value="emergency" <?php echo $leave['leave_type'] === 'emergency' ? 'selected' : ''; ?>>
                                            إجازة طارئة
                                        </option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة *</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="pending" <?php echo $leave['status'] === 'pending' ? 'selected' : ''; ?>>
                                            في الانتظار
                                        </option>
                                        <option value="approved" <?php echo $leave['status'] === 'approved' ? 'selected' : ''; ?>>
                                            موافق عليها
                                        </option>
                                        <option value="rejected" <?php echo $leave['status'] === 'rejected' ? 'selected' : ''; ?>>
                                            مرفوضة
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية *</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="<?php echo $leave['start_date']; ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">تاريخ النهاية *</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="<?php echo $leave['end_date']; ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">السبب</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3"><?php echo htmlspecialchars($leave['reason']); ?></textarea>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="manage_leaves.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
