{"name": "school-system/management-v2", "description": "Comprehensive School Management System - نظام إدارة المدارس الشامل", "type": "project", "version": "2.0.0", "keywords": ["school", "management", "education", "student", "teacher", "grades", "attendance", "finance", "php", "mysql"], "homepage": "https://github.com/school-system/management-v2", "license": "MIT", "authors": [{"name": "School System Team", "email": "<EMAIL>", "homepage": "https://schoolsystem.com", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/school-system/management-v2/issues", "forum": "https://forum.schoolsystem.com", "docs": "https://docs.schoolsystem.com"}, "require": {"php": ">=7.4", "ext-mysqli": "*", "ext-json": "*", "ext-openssl": "*", "ext-curl": "*", "ext-gd": "*", "ext-zip": "*", "ext-mbstring": "*", "phpmailer/phpmailer": "^6.8", "phpoffice/phpspreadsheet": "^1.29", "tecnickcom/tcpdf": "^6.6", "endroid/qr-code": "^4.8", "intervention/image": "^2.7", "league/csv": "^9.8", "monolog/monolog": "^3.4", "vlucas/phpdotenv": "^5.5", "ramsey/uuid": "^4.7", "nesbot/carbon": "^2.68"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "phpstan/phpstan": "^1.10", "friendsofphp/php-cs-fixer": "^3.21", "phpmd/phpmd": "^2.13", "sebastian/phpcpd": "^6.0"}, "suggest": {"ext-imagick": "For advanced image processing", "ext-redis": "For caching and session storage", "ext-memcached": "Alternative caching solution", "ext-intl": "For internationalization support", "ext-ldap": "For LDAP authentication", "ext-soap": "For SOAP web services integration"}, "autoload": {"psr-4": {"SchoolSystem\\": "src/", "SchoolSystem\\Core\\": "src/Core/", "SchoolSystem\\Models\\": "src/Models/", "SchoolSystem\\Controllers\\": "src/Controllers/", "SchoolSystem\\Services\\": "src/Services/", "SchoolSystem\\Utils\\": "src/Utils/"}, "files": ["includes/functions.php", "includes/constants.php"]}, "autoload-dev": {"psr-4": {"SchoolSystem\\Tests\\": "tests/"}}, "scripts": {"post-install-cmd": ["@php -r \"if (!file_exists('config/installed.lock')) { echo 'Please run the installation wizard at /install/setup.php\\n'; }\""], "post-update-cmd": ["@php -r \"echo 'School Management System updated successfully!\\n';\""], "test": ["phpunit"], "test-coverage": ["phpunit --coverage-html coverage"], "cs-check": ["php-cs-fixer fix --dry-run --diff"], "cs-fix": ["php-cs-fixer fix"], "phpstan": ["phpstan analyse"], "phpmd": ["phpmd src text phpmd.xml"], "quality": ["@cs-check", "@phpstan", "@phpmd", "@test"], "install-system": ["@php install/cli-installer.php"], "backup": ["@php scripts/backup.php"], "restore": ["@php scripts/restore.php"], "migrate": ["@php scripts/migrate.php"], "seed": ["@php scripts/seed.php"], "clear-cache": ["@php scripts/clear-cache.php"], "optimize": ["@php scripts/optimize.php"]}, "scripts-descriptions": {"test": "Run the test suite", "test-coverage": "Run tests with coverage report", "cs-check": "Check coding standards", "cs-fix": "Fix coding standards violations", "phpstan": "Run static analysis", "phpmd": "Run mess detector", "quality": "Run all quality checks", "install-system": "Install the system via CLI", "backup": "Create system backup", "restore": "Restore system from backup", "migrate": "Run database migrations", "seed": "Seed database with sample data", "clear-cache": "Clear system cache", "optimize": "Optimize system performance"}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/installers": true}, "platform": {"php": "7.4"}}, "extra": {"branch-alias": {"dev-main": "2.0-dev"}, "installer-paths": {"vendor/{$vendor}/{$name}/": ["type:library"]}}, "repositories": [{"type": "composer", "url": "https://packagist.org"}], "minimum-stability": "stable", "prefer-stable": true, "archive": {"exclude": ["/tests", "/docs", "/.github", "/.giti<PERSON>re", "/phpunit.xml", "/phpcs.xml", "/phpstan.neon", "/phpmd.xml", "/.php-cs-fixer.php"]}}