<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// إعداد headers للتحميل
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="staff_import_template.csv"');
header('Pragma: no-cache');
header('Expires: 0');

// إنشاء output stream
$output = fopen('php://output', 'w');

// إضافة BOM للدعم العربي في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// كتابة header
$header = [
    'full_name',
    'username', 
    'email',
    'password',
    'employee_id',
    'phone',
    'department',
    'position',
    'gender',
    'salary'
];

fputcsv($output, $header);

// إضافة بيانات نموذجية
$sample_data = [
    [
        'أحمد محمد علي',
        'ahmed_mohamed',
        '<EMAIL>',
        'password123',
        'EMP001',
        '01234567890',
        'الشؤون الإدارية',
        'مدير إداري',
        'male',
        '5000'
    ],
    [
        'فاطمة أحمد حسن',
        'fatma_ahmed',
        '<EMAIL>',
        'password123',
        'EMP002',
        '01234567891',
        'المحاسبة',
        'محاسب',
        'female',
        '4500'
    ],
    [
        'محمد سعد إبراهيم',
        'mohamed_saad',
        '<EMAIL>',
        'password123',
        'EMP003',
        '01234567892',
        'الموارد البشرية',
        'أخصائي موارد بشرية',
        'male',
        '4000'
    ]
];

foreach ($sample_data as $row) {
    fputcsv($output, $row);
}

fclose($output);
exit();
?>
