<?php
/**
 * صفحة المواد الدراسية المبسطة
 */

require_once "../includes/config.php";
require_once "../includes/functions.php";
require_once "../includes/database.php";
require_once "../includes/security.php";

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission("admin")) {
    header("Location: ../dashboard/");
    exit();
}

// جلب المواد
$subjects_query = "SELECT * FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);
$subjects = $subjects_result->fetch_all(MYSQLI_ASSOC);

$page_title = "المواد الدراسية";
include_once "../includes/header.php";
?>

<!-- SweetAlert2 للحذف -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-book-open me-2 text-primary"></i>المواد الدراسية
        </h1>
        <a href="add.php" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة مادة
        </a>
    </div>

    <?php if (isset($_SESSION["success_message"])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION["success_message"]; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION["success_message"]); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION["error_message"])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION["error_message"]; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION["error_message"]); ?>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم المادة</th>
                            <th>كود المادة</th>
                            <th>القسم</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($subjects as $subject): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($subject["subject_name"]); ?></td>
                            <td><?php echo htmlspecialchars($subject["subject_code"] ?? "غير محدد"); ?></td>
                            <td><?php echo htmlspecialchars($subject["department"] ?? "غير محدد"); ?></td>
                            <td>
                                <?php if ($subject["status"] == "active"): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="view.php?id=<?php echo $subject["id"]; ?>" class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?php echo $subject["id"]; ?>" class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" title="حذف"
                                            onclick="confirmDelete(<?php echo $subject["id"]; ?>, '<?php echo htmlspecialchars($subject["subject_name"], ENT_QUOTES); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(subjectId, subjectName) {
    if (confirm("هل أنت متأكد من حذف المادة: " + subjectName + "؟\n\nلا يمكن التراجع عن هذا الإجراء.")) {
        var form = document.createElement("form");
        form.method = "POST";
        form.action = "delete.php?id=" + subjectId;
        
        var confirmInput = document.createElement("input");
        confirmInput.type = "hidden";
        confirmInput.name = "confirm_delete";
        confirmInput.value = "1";
        
        form.appendChild(confirmInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include_once "../includes/footer.php"; ?>