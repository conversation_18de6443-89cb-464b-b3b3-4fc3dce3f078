<?php
/**
 * ملف الفوتر الأساسي
 * Main Footer File
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }
?>

<?php if (is_logged_in()): ?>
                </div> <!-- End main-content -->
            </div> <!-- End col-lg-10 -->
        </div> <!-- End row -->
    </div> <!-- End container-fluid -->
    
    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5 no-print">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-start">
                    <small class="text-muted">
                        &copy; <?php echo date('Y'); ?> <?php echo get_system_setting('school_name', __('system_name')); ?>. 
                        <?php echo __('all_rights_reserved'); ?>
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <?php echo __('version'); ?> <?php echo SYSTEM_VERSION; ?> | 
                        <a href="<?php echo SYSTEM_URL; ?>/help/" class="text-decoration-none"><?php echo __('help'); ?></a> |
                        <a href="<?php echo SYSTEM_URL; ?>/contact/" class="text-decoration-none"><?php echo __('contact'); ?></a>
                    </small>
                </div>
            </div>
        </div>
    </footer>
<?php endif; ?>

<!-- Additional JavaScript -->
<script>
    // Auto-hide alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    });
    
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            const forms = document.getElementsByClassName('needs-validation');
            Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
    
    // Confirm delete actions
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
            e.preventDefault();
            const button = e.target.classList.contains('btn-delete') ? e.target : e.target.closest('.btn-delete');
            const message = button.getAttribute('data-message') || '<?php echo __('confirm_delete'); ?>';
            
            confirmAction(message, function() {
                if (button.tagName === 'A') {
                    window.location.href = button.href;
                } else if (button.tagName === 'BUTTON' && button.form) {
                    button.form.submit();
                }
            });
        }
    });
    
    // Auto-save forms
    function autoSave(formId, saveUrl) {
        const form = document.getElementById(formId);
        if (!form) return;
        
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(function(input) {
            input.addEventListener('change', function() {
                const formData = new FormData(form);
                formData.append('auto_save', '1');
                
                fetch(saveUrl, {
                    method: 'POST',
                    body: formData
                }).then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show subtle success indicator
                        const indicator = document.createElement('span');
                        indicator.className = 'text-success ms-2';
                        indicator.innerHTML = '<i class="fas fa-check"></i>';
                        input.parentNode.appendChild(indicator);
                        
                        setTimeout(() => {
                            indicator.remove();
                        }, 2000);
                    }
                });
            });
        });
    }
    
    // File upload preview
    function previewFile(input, previewId) {
        const file = input.files[0];
        const preview = document.getElementById(previewId);
        
        if (file && preview) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                if (file.type.startsWith('image/')) {
                    preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">`;
                } else {
                    preview.innerHTML = `<div class="alert alert-info">
                        <i class="fas fa-file me-2"></i>${file.name}
                    </div>`;
                }
            };
            
            reader.readAsDataURL(file);
        }
    }
    
    // Data tables initialization
    function initDataTable(tableId, options = {}) {
        const defaultOptions = {
            responsive: true,
            pageLength: <?php echo ITEMS_PER_PAGE; ?>,
            language: {
                <?php if ($current_language === 'ar'): ?>
                url: 'https://cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                <?php endif; ?>
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            ...options
        };
        
        if (typeof $.fn.DataTable !== 'undefined') {
            return $('#' + tableId).DataTable(defaultOptions);
        }
    }
    
    // Chart initialization
    function initChart(canvasId, type, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx || typeof Chart === 'undefined') return;
        
        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: data.title || ''
                }
            },
            ...options
        };
        
        return new Chart(ctx, {
            type: type,
            data: data,
            options: defaultOptions
        });
    }
    
    // Print functionality
    function printElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title><?php echo __('print'); ?></title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; }
                    @media print { .no-print { display: none !important; } }
                </style>
            </head>
            <body>
                ${element.outerHTML}
                <script>
                    window.onload = function() {
                        window.print();
                        window.close();
                    };
                <\/script>
            </body>
            </html>
        `);
        printWindow.document.close();
    }
    
    // Export to Excel
    function exportToExcel(tableId, filename = 'export') {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        const wb = XLSX.utils.table_to_book(table);
        XLSX.writeFile(wb, filename + '.xlsx');
    }
    
    // Real-time notifications
    function startNotificationPolling() {
        setInterval(function() {
            fetch('<?php echo SYSTEM_URL; ?>/api/get_unread_count.php')
                .then(response => response.json())
                .then(data => {
                    const badge = document.querySelector('.notification-badge');
                    if (data.count > 0) {
                        if (badge) {
                            badge.textContent = data.count;
                        } else {
                            const button = document.querySelector('[data-bs-toggle="dropdown"] .fas.fa-bell').parentNode;
                            const newBadge = document.createElement('span');
                            newBadge.className = 'notification-badge';
                            newBadge.textContent = data.count;
                            button.appendChild(newBadge);
                        }
                    } else if (badge) {
                        badge.remove();
                    }
                });
        }, 30000); // Check every 30 seconds
    }
    
    // Start notification polling if user is logged in
    <?php if (is_logged_in()): ?>
    startNotificationPolling();
    <?php endif; ?>
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S to save forms
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('button[type="submit"], input[type="submit"]');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) modal.hide();
            }
        }
    });
    
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // Service Worker for offline functionality
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('<?php echo SYSTEM_URL; ?>/sw.js')
                .then(function(registration) {
                    console.log('ServiceWorker registration successful');
                })
                .catch(function(err) {
                    console.log('ServiceWorker registration failed');
                });
        });
    }
    
    // Performance monitoring
    window.addEventListener('load', function() {
        if ('performance' in window) {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            if (loadTime > 3000) { // If page takes more than 3 seconds
                console.warn('Page load time is slow:', loadTime + 'ms');
            }
        }
    });
    
    // Error handling
    window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error);
        
        // Send error to server for logging
        fetch('<?php echo SYSTEM_URL; ?>/api/log_error.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error ? e.error.stack : null,
                url: window.location.href,
                userAgent: navigator.userAgent
            })
        });
    });
    
    // Hide loading spinner when page is fully loaded
    function hideLoading() {
        var spinner = document.querySelector('.loading-spinner');
        if (spinner) spinner.style.display = 'none';
    }
    window.addEventListener('load', function() {
        hideLoading();
    });
</script>

<!-- Additional CSS for animations -->
<style>
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }
    
    .slide-in {
        animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .pulse {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }
    
    .lazy {
        opacity: 0;
        transition: opacity 0.3s;
    }
    
    .lazy.loaded {
        opacity: 1;
    }
    
    /* Scrollbar styling */
    ::-webkit-scrollbar {
        width: 8px;
    }
    
    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: var(--secondary-color);
    }
    
    /* Selection styling */
    ::selection {
        background: var(--primary-color);
        color: white;
    }
    
    ::-moz-selection {
        background: var(--primary-color);
        color: white;
    }
</style>

</body>
</html>
