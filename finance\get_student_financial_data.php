<?php
/**
 * جلب البيانات المالية للطالب عبر AJAX
 * Get Student Financial Data via AJAX
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول
check_session();

header('Content-Type: application/json');

if (!isset($_GET['student_id']) || !is_numeric($_GET['student_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الطالب غير صحيح']);
    exit();
}

$student_id = intval($_GET['student_id']);

try {
    // جلب جميع رسوم الطالب
    $fees_query = "
        SELECT 
            sf.id,
            sf.final_amount,
            sf.remaining_amount,
            sf.status,
            sf.due_date,
            sf.semester,
            ft.type_name as fee_type_name,
            sf.created_at
        FROM student_fees sf
        LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
        WHERE sf.student_id = ?
        ORDER BY sf.created_at DESC
    ";
    
    $fees_stmt = $conn->prepare($fees_query);
    $fees_stmt->bind_param("i", $student_id);
    $fees_stmt->execute();
    $fees_result = $fees_stmt->get_result();
    
    $all_fees = [];
    $pending_fees = [];
    
    while ($fee = $fees_result->fetch_assoc()) {
        $all_fees[] = $fee;
        
        // إضافة الرسوم المستحقة (غير مدفوعة بالكامل)
        if ($fee['status'] !== 'paid' && $fee['remaining_amount'] > 0) {
            $pending_fees[] = $fee;
        }
    }
    
    // جلب آخر المدفوعات
    $payments_query = "
        SELECT 
            sp.id,
            sp.amount,
            sp.payment_date,
            sp.payment_method,
            sp.payment_type,
            sp.receipt_number,
            sp.notes,
            ft.type_name as fee_type_name
        FROM student_payments sp
        LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
        LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
        WHERE sp.student_id = ? AND sp.status = 'confirmed'
        ORDER BY sp.payment_date DESC, sp.id DESC
        LIMIT 10
    ";
    
    $payments_stmt = $conn->prepare($payments_query);
    $payments_stmt->bind_param("i", $student_id);
    $payments_stmt->execute();
    $payments_result = $payments_stmt->get_result();
    
    $recent_payments = [];
    while ($payment = $payments_result->fetch_assoc()) {
        $recent_payments[] = $payment;
    }
    
    // حساب الإحصائيات
    $total_fees = 0;
    $total_paid = 0;
    $total_pending = 0;
    
    foreach ($all_fees as $fee) {
        $total_fees += $fee['final_amount'];
        $total_paid += ($fee['final_amount'] - $fee['remaining_amount']);
        $total_pending += $fee['remaining_amount'];
    }
    
    $response = [
        'success' => true,
        'fees' => $all_fees,
        'pending_fees' => $pending_fees,
        'recent_payments' => $recent_payments,
        'statistics' => [
            'total_fees' => $total_fees,
            'total_paid' => $total_paid,
            'total_pending' => $total_pending,
            'fees_count' => count($all_fees),
            'pending_count' => count($pending_fees),
            'payments_count' => count($recent_payments)
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'خطأ في جلب البيانات: ' . $e->getMessage()
    ]);
}
?>
