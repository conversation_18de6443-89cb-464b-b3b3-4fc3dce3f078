<?php
/**
 * معالج التثبيت الأولي للنظام
 * System Installation Setup
 */

session_start();
define('SYSTEM_INIT', true);

// Check if system is already installed
if (file_exists('../config/installed.lock')) {
    die('System is already installed. Delete the installed.lock file to reinstall.');
}

$step = intval($_GET['step'] ?? 1);
$error_message = '';
$success_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // Requirements check
            $step = 2;
            break;
            
        case 2:
            // Database configuration
            $result = handleDatabaseSetup();
            if ($result['success']) {
                $step = 3;
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
            break;
            
        case 3:
            // Admin user creation
            $result = handleAdminSetup();
            if ($result['success']) {
                $step = 4;
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
            break;
            
        case 4:
            // School configuration
            $result = handleSchoolSetup();
            if ($result['success']) {
                $step = 5;
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
            break;
            
        case 5:
            // Finalize installation
            $result = finalizeInstallation();
            if ($result['success']) {
                $step = 6;
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
            break;
    }
}

/**
 * Handle database setup
 */
function handleDatabaseSetup() {
    $db_host = trim($_POST['db_host'] ?? '');
    $db_name = trim($_POST['db_name'] ?? '');
    $db_user = trim($_POST['db_user'] ?? '');
    $db_pass = $_POST['db_pass'] ?? '';
    
    // تنظيف قاعدة البيانات إذا كان المستخدم يريد إعادة التثبيت
    $cleanup = isset($_POST['cleanup']) && $_POST['cleanup'] == '1';
    
    // Validate inputs
    if (empty($db_host) || empty($db_name) || empty($db_user)) {
        return ['success' => false, 'message' => 'All database fields are required except password.'];
    }
    
    try {
        // Test database connection
        $conn = new mysqli($db_host, $db_user, $db_pass);
        
        if ($conn->connect_error) {
            return ['success' => false, 'message' => 'Database connection failed: ' . $conn->connect_error];
        }
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $conn->query("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $conn->select_db($db_name);
        
        // تنظيف قاعدة البيانات إذا طلب المستخدم ذلك
        if ($cleanup) {
            // تعطيل فحص المفاتيح الأجنبية مؤقتاً
            $conn->query('SET FOREIGN_KEY_CHECKS = 0');
            
            // الحصول على قائمة جميع الجداول الموجودة في قاعدة البيانات
            $tables_result = $conn->query("SHOW TABLES");
            while ($row = $tables_result->fetch_array()) {
                $table = $row[0];
                // حذف الجدول
                $conn->query("DROP TABLE IF EXISTS `$table`");
            }
            
            // حذف جميع الـ Triggers
            $triggers = $conn->query("SHOW TRIGGERS");
            if ($triggers) {
                while ($trigger = $triggers->fetch_assoc()) {
                    $conn->query("DROP TRIGGER IF EXISTS `" . $trigger['Trigger'] . "`");
                }
            }
            
            // إعادة تفعيل فحص المفاتيح الأجنبية
            $conn->query('SET FOREIGN_KEY_CHECKS = 1');
        }
        
        // Create config file
        $config_content = generateConfigFile($db_host, $db_name, $db_user, $db_pass);
        file_put_contents('../config/database.php', $config_content);
        
        // Import database schema
        $sql_file = '../database/production_database.sql';
        $response = ['success' => true, 'message' => ''];
        
        if (file_exists($sql_file)) {
            try {
                $import_result = importSQLFile($sql_file, $conn);

                if ($import_result['errors'] > 0) {
                    $error_message = "فشل استيراد قاعدة البيانات مع {$import_result['errors']} أخطاء من أصل {$import_result['total']} استعلام.\n\n";
                    $error_message .= "تفاصيل الأخطاء:\n";
                    foreach ($import_result['error_details'] as $i => $error) {
                        $error_message .= "- الخطأ " . ($i + 1) . ": " . $error . "\n";
                    }
                    return [
                        'success' => false, 
                        'message' => $error_message,
                        'details' => $import_result['error_details']
                    ];
                }

                // Verify tables were created
                $table_check = checkTablesExist();
                if (!$table_check['all_exist']) {
                    return [
                        'success' => false,
                        'message' => "Database tables missing: " . implode(', ', $table_check['missing'])
                    ];
                }

                $response['message'] = "Database imported successfully. {$import_result['success']} queries executed.";

            } catch (Exception $e) {
                return [
                    'success' => false,
                    'message' => "Database import failed: " . $e->getMessage()
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => "Database file not found: " . $sql_file
            ];
        }
        
        $conn->close();
        
        return ['success' => true, 'message' => 'Database configured successfully.'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Database setup failed: ' . $e->getMessage()];
    }
}

/**
 * Handle admin user setup
 */
function handleAdminSetup() {
    require_once '../config/database.php';
    require_once '../includes/functions.php';
    
    $full_name = trim($_POST['admin_name'] ?? '');
    $email = trim($_POST['admin_email'] ?? '');
    $password = $_POST['admin_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validate inputs
    if (empty($full_name) || empty($email) || empty($password)) {
        return ['success' => false, 'message' => 'All admin fields are required.'];
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Invalid email format.'];
    }
    
    if ($password !== $confirm_password) {
        return ['success' => false, 'message' => 'Passwords do not match.'];
    }
    
    if (strlen($password) < 8) {
        return ['success' => false, 'message' => 'Password must be at least 8 characters long.'];
    }
    
    try {
        global $conn;
        
        // Check if admin already exists
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ? OR role = 'admin'");
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        
        if ($check_stmt->get_result()->num_rows > 0) {
            return ['success' => false, 'message' => 'Admin user already exists.'];
        }
        
        // Create admin user
        $hashed_password = password_hash($password, PASSWORD_ARGON2ID);
        $user_stmt = $conn->prepare("
            INSERT INTO users (full_name, email, password, role, status, created_at, updated_at)
            VALUES (?, ?, ?, 'admin', 'active', NOW(), NOW())
        ");
        $user_stmt->bind_param("sss", $full_name, $email, $hashed_password);
        $user_stmt->execute();
        
        return ['success' => true, 'message' => 'Admin user created successfully.'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Admin setup failed: ' . $e->getMessage()];
    }
}

/**
 * Handle school configuration
 */
function handleSchoolSetup() {
    require_once '../config/database.php';
    
    $school_name = trim($_POST['school_name'] ?? '');
    $school_name_en = trim($_POST['school_name_en'] ?? '');
    $school_address = trim($_POST['school_address'] ?? '');
    $school_phone = trim($_POST['school_phone'] ?? '');
    $school_email = trim($_POST['school_email'] ?? '');
    $academic_year = trim($_POST['academic_year'] ?? '');
    $currency_symbol = trim($_POST['currency_symbol'] ?? 'ر.س');
    
    if (empty($school_name) || empty($academic_year)) {
        return ['success' => false, 'message' => 'School name and academic year are required.'];
    }
    
    try {
        global $conn;
        
        // Insert school settings
        $settings = [
            'school_name' => $school_name,
            'school_name_en' => $school_name_en,
            'school_address' => $school_address,
            'school_phone' => $school_phone,
            'school_email' => $school_email,
            'academic_year' => $academic_year,
            'currency_symbol' => $currency_symbol,
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh',
            'items_per_page' => '20'
        ];
        
        foreach ($settings as $key => $value) {
            $stmt = $conn->prepare("
                INSERT INTO system_settings (setting_key, setting_value, updated_at)
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
            ");
            $stmt->bind_param("ss", $key, $value);
            $stmt->execute();
        }
        
        return ['success' => true, 'message' => 'School configuration saved successfully.'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'School setup failed: ' . $e->getMessage()];
    }
}

/**
 * Finalize installation
 */
function finalizeInstallation() {
    try {
        // Create necessary directories
        $directories = [
            '../uploads',
            '../uploads/profiles',
            '../uploads/documents',
            '../uploads/temp',
            '../logs',
            '../cache',
            '../backups'
        ];
        
        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        // Create .htaccess files for security
        $htaccess_content = "Order Allow,Deny\nDeny from all";
        file_put_contents('../logs/.htaccess', $htaccess_content);
        file_put_contents('../cache/.htaccess', $htaccess_content);
        file_put_contents('../backups/.htaccess', $htaccess_content);
        
        // Create installation lock file
        file_put_contents('../config/installed.lock', date('Y-m-d H:i:s'));
        
        // Create sample data (optional)
        createSampleData();
        
        return ['success' => true, 'message' => 'Installation completed successfully!'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Installation finalization failed: ' . $e->getMessage()];
    }
}

/**
 * Create sample data
 */
function createSampleData() {
    require_once '../config/database.php';
    
    global $conn;
    
    try {
        // Create sample fee types
        $fee_types = [
            ['tuition_fees', 'Tuition Fees', 'رسوم دراسية'],
            ['registration_fees', 'Registration Fees', 'رسوم تسجيل'],
            ['book_fees', 'Book Fees', 'رسوم كتب'],
            ['transport_fees', 'Transport Fees', 'رسوم نقل'],
            ['activity_fees', 'Activity Fees', 'رسوم أنشطة']
        ];
        
        foreach ($fee_types as $fee_type) {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO fee_types (type_key, type_name_en, type_name, status, created_at)
                VALUES (?, ?, ?, 'active', NOW())
            ");
            $stmt->bind_param("sss", $fee_type[0], $fee_type[1], $fee_type[2]);
            $stmt->execute();
        }
        
        // Create sample subjects
        $subjects = [
            ['Mathematics', 'الرياضيات'],
            ['Science', 'العلوم'],
            ['English', 'اللغة الإنجليزية'],
            ['Arabic', 'اللغة العربية'],
            ['History', 'التاريخ'],
            ['Geography', 'الجغرافيا'],
            ['Art', 'الفنون'],
            ['Physical Education', 'التربية البدنية']
        ];
        
        foreach ($subjects as $subject) {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO subjects (subject_name_en, subject_name, status, created_at)
                VALUES (?, ?, 'active', NOW())
            ");
            $stmt->bind_param("ss", $subject[0], $subject[1]);
            $stmt->execute();
        }
        
        // Create sample classes
        $classes = [
            ['Grade 1', 'الصف الأول', 'grade1'],
            ['Grade 2', 'الصف الثاني', 'grade2'],
            ['Grade 3', 'الصف الثالث', 'grade3'],
            ['Grade 4', 'الصف الرابع', 'grade4'],
            ['Grade 5', 'الصف الخامس', 'grade5'],
            ['Grade 6', 'الصف السادس', 'grade6']
        ];
        
        foreach ($classes as $class) {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO classes (class_name_en, class_name, grade_level, status, created_at)
                VALUES (?, ?, ?, 'active', NOW())
            ");
            $stmt->bind_param("sss", $class[0], $class[1], $class[2]);
            $stmt->execute();
        }
        
    } catch (Exception $e) {
        // Sample data creation is optional, don't fail installation
        error_log("Sample data creation failed: " . $e->getMessage());
    }
}

/**
 * Import SQL file
 */
function importSQLFile($file, $connection = null) {
    if ($connection === null) {
        // إنشاء اتصال جديد بقاعدة البيانات إذا لم يتم تمرير اتصال
        $connection = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
        if ($connection->connect_error) {
            throw new Exception('فشل الاتصال بقاعدة البيانات: ' . $connection->connect_error);
        }
        $connection->set_charset('utf8mb4');
    }
    
    $result = [
        'success' => 0,
        'errors' => 0,
        'total' => 0,
        'error_details' => [],
        'failed_queries' => []
    ];

    try {
        // تعطيل التحقق من المفاتيح الأجنبية مؤقتاً
        $connection->query('SET FOREIGN_KEY_CHECKS = 0');
        
        $sql = file_get_contents($file);
        
        // تقسيم الملف إلى استعلامات فردية مع مراعاة الـ delimiter
        $queries = [];
        $query = '';
        $delimiter = ';';
        $in_string = false;
        $string_char = '';
        
        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];
            
            // تعامل مع النصوص المقتبسة
            if (($char == "'" || $char == '"') && $sql[$i-1] != '\\') {
                if (!$in_string) {
                    $in_string = true;
                    $string_char = $char;
                } elseif ($char == $string_char) {
                    $in_string = false;
                }
            }
            
            // تحقق من DELIMITER
            if (!$in_string && strtoupper(substr($sql, $i, 9)) == 'DELIMITER ') {
                $i += 9;
                $delimiter = '';
                while ($i < strlen($sql) && !in_array($sql[$i], ["\r", "\n", "\t", ' '])) {
                    $delimiter .= $sql[$i];
                    $i++;
                }
                continue;
            }
            
            // تحقق من نهاية الاستعلام
            if (!$in_string && substr($sql, $i, strlen($delimiter)) == $delimiter) {
                if (trim($query)) {
                    $queries[] = trim($query);
                }
                $query = '';
                $i += strlen($delimiter) - 1;
                continue;
            }
            
            $query .= $char;
        }
        
        // إضافة آخر استعلام إذا كان موجوداً
        if (trim($query)) {
            $queries[] = trim($query);
        }
        
        // تنفيذ الاستعلامات
        $current_query = '';
        
        // تقسيم الاستعلامات إلى مجموعتين: إنشاء الجداول والـ Triggers
        $table_queries = [];
        $trigger_queries = [];
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $result['total']++;
                
                if (stripos($query, 'CREATE TRIGGER') !== false) {
                    $trigger_queries[] = $query;
                } else {
                    $table_queries[] = $query;
                }
            }
        }
        
        // تنفيذ استعلامات إنشاء الجداول أولاً
        foreach ($table_queries as $query) {
            if (!empty($query)) {
                
                // تجاهل التعليقات والأسطر الفارغة
                if (substr($query, 0, 2) == '--' || substr($query, 0, 1) == '#' || $query == '') {
                    continue;
                }
                
                // تحقق إذا كان الاستعلام يحتوي على DELIMITER
                if (stripos($query, 'DELIMITER') === 0) {
                    // نتجاهل أوامر DELIMITER لأننا سنتعامل مع كل trigger بشكل منفصل
                    continue;
                }
                
                // إذا كان الاستعلام يحتوي على TRIGGER
                if (stripos($query, 'CREATE TRIGGER') !== false) {
                    // تنظيف وتعديل صيغة الـ Trigger
                    $query = preg_replace('/\s*DELIMITER\s+[\$\;]\$/', '', $query);
                    $query = preg_replace('/END\s*(\$\$|\;)\s*/', 'END; ', $query);
                    $query = str_replace(['END$$', 'END IF$$'], ['END;', 'END IF;'], $query);
                    
                    // تأكد من وجود BEGIN بعد FOR EACH ROW
                    if (stripos($query, 'FOR EACH ROW BEGIN') === false) {
                        $query = preg_replace('/FOR\s+EACH\s+ROW\s+/i', 'FOR EACH ROW BEGIN ', $query);
                    }
                    
                    // إضافة تعريف المتغيرات في بداية الـ Trigger بعد BEGIN مباشرة
                    if (stripos($query, 'exam_total') !== false || 
                        stripos($query, 'fee_amount') !== false ||
                        stripos($query, 'total_paid') !== false) {
                        
                        $query = preg_replace(
                            '/FOR\s+EACH\s+ROW\s+BEGIN\s+/i',
                            "FOR EACH ROW BEGIN\n    DECLARE exam_total DECIMAL(10,2) DEFAULT 0;\n    DECLARE fee_amount DECIMAL(10,2) DEFAULT 0;\n    DECLARE total_paid DECIMAL(10,2) DEFAULT 0;\n    ",
                            $query
                        );
                    }
                    
                    // تصحيح مشكلة NEW.student_fee_id
                    $query = str_replace('NEW.student_fee_id', 'NEW.id', $query);
                }
                
                try {
                    if (stripos($query, 'CREATE TRIGGER') !== false) {
                        // للـ Triggers نستخدم query عادي بدون DELIMITER
                        if ($connection->query($query)) {
                            $result['success']++;
                        } else {
                            // تجاهل بعض الأخطاء
                            if (stripos($connection->error, 'already exists') !== false ||
                                stripos($connection->error, 'Duplicate key') !== false) {
                                $result['success']++;
                            } else {
                                throw new Exception($connection->error);
                            }
                        }
                    } else {
                        // للاستعلامات العادية نستخدم query
                        if ($connection->query($query)) {
                            $result['success']++;
                        } else {
                            // تجاهل بعض الأخطاء
                            if (stripos($connection->error, 'already exists') !== false ||
                                stripos($connection->error, 'Duplicate key') !== false) {
                                $result['success']++;
                            } else {
                                throw new Exception($connection->error);
                            }
                        }
                    }
                } catch (Exception $e) {
                    $result['errors']++;
                    $result['error_details'][] = $e->getMessage();
                    $result['failed_queries'][] = $query;
                }
            }
        }
        
        // بعد إنشاء الجداول، نقوم بتنفيذ الـ Triggers
        foreach ($trigger_queries as $query) {
            try {
                if ($connection->multi_query($query)) {
                    do {
                        $result['success']++;
                        while ($connection->more_results() && $connection->next_result()) {
                            if ($res = $connection->store_result()) {
                                $res->free();
                            }
                        }
                    } while ($connection->more_results() && $connection->next_result());
                } else {
                    throw new Exception($connection->error);
                }
            } catch (Exception $e) {
                $result['errors']++;
                $result['error_details'][] = $e->getMessage();
                $result['failed_queries'][] = $query;
            }
        }
    } catch (Exception $e) {
        $result['errors']++;
        $result['error_details'][] = $e->getMessage();
    }
    
    return $result;
}

/**
 * Check if required tables exist
 */
function checkTablesExist() {
    global $conn;
    $required_tables = ['users', 'students', 'teachers', 'classes', 'settings'];
    $missing = [];
    
    foreach ($required_tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows == 0) {
            $missing[] = $table;
        }
    }
    
    return [
        'all_exist' => empty($missing),
        'missing' => $missing
    ];
}

/**
 * Generate database config file content
 */
function generateConfigFile($host, $name, $user, $pass) {
    return "<?php
/**
 * Database Configuration
 * Generated by installation script
 */

if (!defined('SYSTEM_INIT')) {
    die('Direct access not allowed');
}

// Database Configuration
define('DB_HOST', '" . addslashes($host) . "');
define('DB_NAME', '" . addslashes($name) . "');
define('DB_USER', '" . addslashes($user) . "');
define('DB_PASSWORD', '" . addslashes($pass) . "');

// Create database connection
try {
    \$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    
    if (\$conn->connect_error) {
        throw new Exception('Database connection failed: ' . \$conn->connect_error);
    }
    
    // Set charset
    \$conn->set_charset('utf8mb4');
    
} catch (Exception \$e) {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        die('Database Error: ' . \$e->getMessage());
    } else {
        die('Database connection failed. Please check your configuration.');
    }
}
?>";
}

/**
 * Check system requirements
 */
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'MySQL Extension' => extension_loaded('mysqli'),
        'JSON Extension' => extension_loaded('json'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'cURL Extension' => extension_loaded('curl'),
        'GD Extension' => extension_loaded('gd'),
        'Config Directory Writable' => is_writable('../config'),
        'Uploads Directory Writable' => is_writable('../uploads') || mkdir('../uploads', 0755, true),
        'Logs Directory Writable' => is_writable('../logs') || mkdir('../logs', 0755, true)
    ];
    
    return $requirements;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Management System - Installation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { max-width: 800px; margin: 50px auto; }
        .install-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .step-indicator { background: #f8f9fa; padding: 20px; border-radius: 15px 15px 0 0; }
        .step { display: inline-block; width: 40px; height: 40px; line-height: 40px; text-align: center; border-radius: 50%; margin: 0 10px; }
        .step.active { background: #667eea; color: white; }
        .step.completed { background: #28a745; color: white; }
        .step.pending { background: #e9ecef; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-card">
                <!-- Step Indicator -->
                <div class="step-indicator text-center">
                    <h2 class="mb-4">School Management System Installation</h2>
                    <div class="steps">
                        <?php for ($i = 1; $i <= 6; $i++): ?>
                            <span class="step <?php 
                                echo $i < $step ? 'completed' : ($i == $step ? 'active' : 'pending'); 
                            ?>"><?php echo $i; ?></span>
                        <?php endfor; ?>
                    </div>
                </div>
                
                <div class="card-body p-5">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php
                    switch ($step) {
                        case 1:
                            include 'steps/step1_requirements.php';
                            break;
                        case 2:
                            include 'steps/step2_database.php';
                            break;
                        case 3:
                            include 'steps/step3_admin.php';
                            break;
                        case 4:
                            include 'steps/step4_school.php';
                            break;
                        case 5:
                            include 'steps/step5_finalize.php';
                            break;
                        case 6:
                            include 'steps/step6_complete.php';
                            break;
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
