<?php
/**
 * تقارير حضور الموظفين الاحترافية
 * Professional Staff Attendance Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات - فقط الإداريين
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// معالجة الفلاتر
$report_type = clean_input($_GET['type'] ?? 'summary');
$year = intval($_GET['year'] ?? date('Y'));
$month = intval($_GET['month'] ?? date('n'));
$user_filter = clean_input($_GET['user_id'] ?? '');
$date_from = clean_input($_GET['date_from'] ?? date('Y-m-01'));
$date_to = clean_input($_GET['date_to'] ?? date('Y-m-t'));

// بناء شروط الاستعلام
$where_conditions = ["DATE(attendance_date) BETWEEN ? AND ?"];
$params = [$date_from, $date_to];
$types = 'ss';

if ($user_filter) {
    $where_conditions[] = "user_id = ?";
    $params[] = $user_filter;
    $types .= 'i';
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// جلب قائمة المستخدمين للفلتر (باستثناء المديرين)
$users_query = "
    SELECT u.id, u.full_name, u.role
    FROM users u
    WHERE u.role IN ('teacher', 'staff', 'student')
    AND u.role NOT IN ('admin', 'system_admin')
    AND u.status = 'active'
    ORDER BY u.full_name
";
$users_list = $conn->query($users_query);

include_once '../includes/header.php';
?>

<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar me-2 text-primary"></i><?php echo __('staff_attendance_reports'); ?></h2>
                <div>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_attendance'); ?>
                    </a>
                    <button class="btn btn-success" onclick="exportReport()">
                        <i class="fas fa-file-excel me-2"></i><?php echo __('export_excel'); ?>
                    </button>
                    <button class="btn btn-danger" onclick="printReport()">
                        <i class="fas fa-print me-2"></i><?php echo __('print'); ?>
                    </button>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i><?php echo __('report_filters'); ?></h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-2">
                            <label for="type" class="form-label"><?php echo __('report_type'); ?></label>
                            <select class="form-select" id="type" name="type">
                                <option value="summary" <?php echo ($report_type == 'summary') ? 'selected' : ''; ?>>
                                    <?php echo __('summary_report'); ?>
                                </option>
                                <option value="detailed" <?php echo ($report_type == 'detailed') ? 'selected' : ''; ?>>
                                    <?php echo __('detailed_report'); ?>
                                </option>
                                <option value="leaves" <?php echo ($report_type == 'leaves') ? 'selected' : ''; ?>>
                                    <?php echo __('leaves_report'); ?>
                                </option>
                                <option value="statistics" <?php echo ($report_type == 'statistics') ? 'selected' : ''; ?>>
                                    <?php echo __('statistics_report'); ?>
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label"><?php echo __('from_date'); ?></label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label"><?php echo __('to_date'); ?></label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="user_id" class="form-label"><?php echo __('staff_member'); ?></label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value=""><?php echo __('all_staff'); ?></option>
                                <?php while ($user = $users_list->fetch_assoc()): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo ($user_filter == $user['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']) . ' (' . __($user['role']) . ')'; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i><?php echo __('generate_report'); ?>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- محتوى التقرير -->
            <div id="reportContent">
                <?php
                switch ($report_type) {
                    case 'summary':
                        include 'reports/staff_summary.php';
                        break;
                    case 'detailed':
                        include 'reports/staff_detailed.php';
                        break;
                    case 'leaves':
                        include 'reports/staff_leaves.php';
                        break;
                    case 'statistics':
                        include 'reports/staff_statistics.php';
                        break;
                    default:
                        include 'reports/staff_summary.php';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = 'export_staff.php?' + params.toString();
}

function printReport() {
    const printContent = document.getElementById('reportContent').innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div style="direction: rtl; font-family: Arial, sans-serif;">
            <h2 style="text-align: center; margin-bottom: 20px;">تقرير حضور الموظفين</h2>
            <p style="text-align: center; color: #666; margin-bottom: 30px;">
                تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}
            </p>
            ${printContent}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    window.location.reload();
}

// تحديث الصفحة عند تغيير الفلاتر
document.querySelectorAll('select, input[type="date"]').forEach(element => {
    element.addEventListener('change', function() {
        if (this.form) {
            this.form.submit();
        }
    });
});
</script>

<?php include_once '../includes/footer.php'; ?>
