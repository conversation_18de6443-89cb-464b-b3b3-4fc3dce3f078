<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

$id = intval($_GET['id'] ?? 0);
if ($id) {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $user = $stmt->get_result()->fetch_assoc();
    if (!$user) {
        die('المستخدم غير موجود');
    }
} else {
    die('معرف المستخدم غير صحيح');
}
?>
<div class="container">
    <h2>بيانات المستخدم</h2>
    <table class="table table-bordered">
        <tr><th>الاسم الكامل</th><td><?= htmlspecialchars($user['full_name']) ?></td></tr>
        <tr><th>البريد الإلكتروني</th><td><?= htmlspecialchars($user['email']) ?></td></tr>
        <tr><th>الدور</th><td><?= htmlspecialchars($user['role']) ?></td></tr>
        <tr><th>الحالة</th><td><?= htmlspecialchars($user['status']) ?></td></tr>
        <tr><th>تاريخ الإنشاء</th><td><?= htmlspecialchars($user['created_at']) ?></td></tr>
        <tr><th>آخر دخول</th><td><?= htmlspecialchars($user['last_login']) ?></td></tr>
    </table>
    <a href="edit.php?id=<?= $user['id'] ?>" class="btn btn-primary">تعديل</a>
    <a href="index.php" class="btn btn-secondary">رجوع</a>
</div>
<?php require_once '../includes/footer.php'; ?> 