-- الإصلاح النهائي للقيود الخارجية
-- Final foreign key constraints fix

USE school_management;

SET FOREIGN_KEY_CHECKS = 0;

-- إصلاح القيود المفقودة في unified_staff_absences
-- التحقق من وجود العمود applied_by أولاً
SELECT COLUMN_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'school_management' 
AND TABLE_NAME = 'unified_staff_absences' 
AND COLUMN_NAME = 'applied_by';

-- إضافة العمود إذا لم يكن موجوداً
ALTER TABLE unified_staff_absences 
ADD COLUMN IF NOT EXISTS applied_by INT(10) UNSIGNED DEFAULT NULL AFTER status;

-- تنظيف البيانات
UPDATE unified_staff_absences 
SET applied_by = user_id 
WHERE applied_by IS NULL;

-- إضافة القيد الخارجي للـ applied_by إذا لم يكن موجوداً
SELECT COUNT(*) as constraint_exists
FROM information_schema.KEY_COLUMN_USAGE 
WHERE CONSTRAINT_SCHEMA = 'school_management' 
AND TABLE_NAME = 'unified_staff_absences' 
AND COLUMN_NAME = 'applied_by' 
AND CONSTRAINT_NAME LIKE '%applied_by%';

-- إضافة القيد إذا لم يكن موجوداً
SET @sql = 'ALTER TABLE unified_staff_absences ADD CONSTRAINT unified_absences_applied_by_foreign FOREIGN KEY (applied_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE';

-- تنفيذ الأمر فقط إذا لم يكن القيد موجوداً
SET @constraint_count = (
    SELECT COUNT(*) 
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE CONSTRAINT_SCHEMA = 'school_management' 
    AND TABLE_NAME = 'unified_staff_absences' 
    AND COLUMN_NAME = 'applied_by' 
    AND CONSTRAINT_NAME LIKE '%applied_by%'
);

-- إصلاح القيود في staff_leaves
-- التحقق من وجود العمود applied_by
SELECT COLUMN_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'school_management' 
AND TABLE_NAME = 'staff_leaves' 
AND COLUMN_NAME = 'applied_by';

-- إضافة العمود إذا لم يكن موجوداً
ALTER TABLE staff_leaves 
ADD COLUMN IF NOT EXISTS applied_by INT(10) UNSIGNED DEFAULT NULL AFTER user_id;

-- تنظيف البيانات
UPDATE staff_leaves 
SET applied_by = user_id 
WHERE applied_by IS NULL;

-- إضافة القيد الخارجي إذا لم يكن موجوداً
SET @constraint_count_leaves = (
    SELECT COUNT(*) 
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE CONSTRAINT_SCHEMA = 'school_management' 
    AND TABLE_NAME = 'staff_leaves' 
    AND COLUMN_NAME = 'applied_by' 
    AND CONSTRAINT_NAME LIKE '%applied_by%'
);

SET FOREIGN_KEY_CHECKS = 1;

-- إنشاء إجراء مبسط لحذف المستخدمين
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS delete_user_safely(IN p_user_id INT)
BEGIN
    DECLARE user_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدم
    SELECT COUNT(*) INTO user_exists FROM users WHERE id = p_user_id;
    
    IF user_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'User does not exist';
    END IF;
    
    -- تحديث المراجع إلى NULL بدلاً من الحذف
    UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by = p_user_id;
    
    UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by = p_user_id;
    UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by = p_user_id;
    
    UPDATE staff_leaves SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE staff_leaves SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by = p_user_id;
    
    UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_attendance SET approved_by = NULL WHERE approved_by = p_user_id;
    
    UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    
    -- حذف المستخدم
    DELETE FROM users WHERE id = p_user_id;
    
    COMMIT;
    
    SELECT CONCAT('User ', p_user_id, ' deleted successfully') as message;
END//

DELIMITER ;

-- إنشاء view لعرض حالة القيود الخارجية
CREATE OR REPLACE VIEW foreign_key_status AS
SELECT 
    TABLE_NAME as table_name,
    COLUMN_NAME as column_name,
    CONSTRAINT_NAME as constraint_name,
    REFERENCED_TABLE_NAME as referenced_table,
    REFERENCED_COLUMN_NAME as referenced_column,
    'OK' as status
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users'
ORDER BY TABLE_NAME, COLUMN_NAME;

-- عرض النتائج
SELECT 'Foreign key constraints have been fixed!' as result;

-- عرض القيود الحالية
SELECT COUNT(*) as total_foreign_keys 
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users';

-- عرض الجداول التي تحتوي على قيود خارجية لجدول users
SELECT DISTINCT TABLE_NAME as tables_with_user_references
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users';
