<?php
/**
 * نظام معالجة الأخطاء والتسجيل المتقدم
 * Advanced Error Handling and Logging System
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

/**
 * فئة معالجة الأخطاء
 * Error Handler Class
 */
class ErrorHandler {
    
    private static $instance = null;
    private $log_file;
    private $error_levels;
    
    private function __construct() {
        $this->log_file = LOGS_PATH . '/errors.log';
        $this->error_levels = [
            E_ERROR => 'FATAL',
            E_WARNING => 'WARNING',
            E_PARSE => 'PARSE',
            E_NOTICE => 'NOTICE',
            E_CORE_ERROR => 'CORE_ERROR',
            E_CORE_WARNING => 'CORE_WARNING',
            E_COMPILE_ERROR => 'COMPILE_ERROR',
            E_COMPILE_WARNING => 'COMPILE_WARNING',
            E_USER_ERROR => 'USER_ERROR',
            E_USER_WARNING => 'USER_WARNING',
            E_USER_NOTICE => 'USER_NOTICE',
            E_STRICT => 'STRICT',
            E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
            E_DEPRECATED => 'DEPRECATED',
            E_USER_DEPRECATED => 'USER_DEPRECATED'
        ];
        
        $this->setupErrorHandling();
        $this->createLogDirectory();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * إعداد معالجة الأخطاء
     * Setup error handling
     */
    private function setupErrorHandling() {
        // Set custom error handler
        set_error_handler([$this, 'handleError']);
        
        // Set custom exception handler
        set_exception_handler([$this, 'handleException']);
        
        // Set shutdown function to catch fatal errors
        register_shutdown_function([$this, 'handleShutdown']);
        
        // Configure error reporting
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);
            ini_set('display_errors', 0);
        }
        
        ini_set('log_errors', 1);
        ini_set('error_log', $this->log_file);
    }
    
    /**
     * إنشاء مجلد السجلات
     * Create logs directory
     */
    private function createLogDirectory() {
        if (!file_exists(LOGS_PATH)) {
            mkdir(LOGS_PATH, 0755, true);
        }
        
        // Create .htaccess to protect logs
        $htaccess_file = LOGS_PATH . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            file_put_contents($htaccess_file, "Order Allow,Deny\nDeny from all");
        }
    }
    
    /**
     * معالجة الأخطاء
     * Handle errors
     */
    public function handleError($errno, $errstr, $errfile, $errline) {
        // Don't handle errors that are suppressed with @
        if (!(error_reporting() & $errno)) {
            return false;
        }
        
        $error_type = $this->error_levels[$errno] ?? 'UNKNOWN';
        
        $error_data = [
            'type' => $error_type,
            'message' => $errstr,
            'file' => $errfile,
            'line' => $errline,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'user_id' => $_SESSION['user_id'] ?? null
        ];
        
        $this->logError($error_data);
        
        // For fatal errors, show error page
        if (in_array($errno, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            $this->showErrorPage($error_data);
            exit();
        }
        
        return true;
    }
    
    /**
     * معالجة الاستثناءات
     * Handle exceptions
     */
    public function handleException($exception) {
        $error_data = [
            'type' => 'EXCEPTION',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'user_id' => $_SESSION['user_id'] ?? null
        ];
        
        $this->logError($error_data);
        $this->showErrorPage($error_data);
    }
    
    /**
     * معالجة الأخطاء القاتلة عند الإغلاق
     * Handle fatal errors on shutdown
     */
    public function handleShutdown() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $error_data = [
                'type' => 'FATAL',
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'timestamp' => date('Y-m-d H:i:s'),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
                'user_id' => $_SESSION['user_id'] ?? null
            ];
            
            $this->logError($error_data);
            $this->showErrorPage($error_data);
        }
    }
    
    /**
     * تسجيل الخطأ
     * Log error
     */
    private function logError($error_data) {
        $log_entry = sprintf(
            "[%s] %s: %s in %s on line %d (IP: %s, User: %s, URL: %s)\n",
            $error_data['timestamp'],
            $error_data['type'],
            $error_data['message'],
            $error_data['file'],
            $error_data['line'],
            $error_data['ip'],
            $error_data['user_id'] ?? 'guest',
            $error_data['url']
        );
        
        // Add stack trace for exceptions
        if (isset($error_data['trace'])) {
            $log_entry .= "Stack trace:\n" . $error_data['trace'] . "\n";
        }
        
        $log_entry .= str_repeat('-', 80) . "\n";
        
        // Write to log file
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
        
        // Rotate log if too large
        $this->rotateLogIfNeeded();
        
        // Store in database if available
        $this->storeErrorInDatabase($error_data);
        
        // Send email notification for critical errors
        if (in_array($error_data['type'], ['FATAL', 'EXCEPTION', 'USER_ERROR'])) {
            $this->sendErrorNotification($error_data);
        }
    }
    
    /**
     * تدوير ملف السجل
     * Rotate log file if needed
     */
    private function rotateLogIfNeeded() {
        if (!file_exists($this->log_file)) {
            return;
        }
        
        $max_size = defined('LOG_MAX_SIZE') ? LOG_MAX_SIZE : 10 * 1024 * 1024; // 10MB
        
        if (filesize($this->log_file) > $max_size) {
            $backup_file = $this->log_file . '.' . date('Y-m-d-H-i-s');
            rename($this->log_file, $backup_file);
            
            // Compress old log
            if (function_exists('gzopen')) {
                $this->compressLogFile($backup_file);
            }
            
            // Clean old logs
            $this->cleanOldLogs();
        }
    }
    
    /**
     * ضغط ملف السجل
     * Compress log file
     */
    private function compressLogFile($file) {
        $gz_file = $file . '.gz';
        $fp_in = fopen($file, 'rb');
        $fp_out = gzopen($gz_file, 'wb9');
        
        if ($fp_in && $fp_out) {
            while (!feof($fp_in)) {
                gzwrite($fp_out, fread($fp_in, 8192));
            }
            fclose($fp_in);
            gzclose($fp_out);
            unlink($file);
        }
    }
    
    /**
     * تنظيف السجلات القديمة
     * Clean old logs
     */
    private function cleanOldLogs() {
        $max_files = defined('LOG_MAX_FILES') ? LOG_MAX_FILES : 10;
        $log_files = glob(LOGS_PATH . '/errors.log.*');
        
        if (count($log_files) > $max_files) {
            // Sort by modification time
            usort($log_files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // Remove oldest files
            $files_to_remove = array_slice($log_files, 0, count($log_files) - $max_files);
            foreach ($files_to_remove as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * حفظ الخطأ في قاعدة البيانات
     * Store error in database
     */
    private function storeErrorInDatabase($error_data) {
        global $conn;
        
        if (!$conn) {
            return;
        }
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO error_logs (
                    error_type, error_message, file_path, line_number, 
                    stack_trace, ip_address, user_agent, request_url, 
                    user_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->bind_param("sssissssi",
                $error_data['type'],
                $error_data['message'],
                $error_data['file'],
                $error_data['line'],
                $error_data['trace'] ?? null,
                $error_data['ip'],
                $error_data['user_agent'],
                $error_data['url'],
                $error_data['user_id']
            );
            
            $stmt->execute();
        } catch (Exception $e) {
            // Can't log database errors to database, just write to file
            error_log("Failed to store error in database: " . $e->getMessage());
        }
    }
    
    /**
     * إرسال إشعار بالخطأ
     * Send error notification
     */
    private function sendErrorNotification($error_data) {
        // Only send notifications in production
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            return;
        }
        
        $admin_email = get_system_setting('admin_email');
        if (!$admin_email) {
            return;
        }
        
        $subject = "Critical Error in School Management System";
        $message = sprintf(
            "A critical error occurred in the School Management System:\n\n" .
            "Type: %s\n" .
            "Message: %s\n" .
            "File: %s\n" .
            "Line: %d\n" .
            "Time: %s\n" .
            "IP: %s\n" .
            "URL: %s\n" .
            "User: %s\n\n" .
            "Please check the error logs for more details.",
            $error_data['type'],
            $error_data['message'],
            $error_data['file'],
            $error_data['line'],
            $error_data['timestamp'],
            $error_data['ip'],
            $error_data['url'],
            $error_data['user_id'] ?? 'guest'
        );
        
        $headers = [
            'From: ' . get_system_setting('system_email', '<EMAIL>'),
            'Content-Type: text/plain; charset=UTF-8'
        ];
        
        mail($admin_email, $subject, $message, implode("\r\n", $headers));
    }
    
    /**
     * عرض صفحة الخطأ
     * Show error page
     */
    private function showErrorPage($error_data) {
        // Don't show detailed errors in production
        $show_details = defined('DEBUG_MODE') && DEBUG_MODE;
        
        // Check if it's an AJAX request
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => $show_details ? $error_data['message'] : 'An error occurred',
                'type' => $error_data['type']
            ]);
            return;
        }
        
        // Set appropriate HTTP status code
        http_response_code(500);
        
        // Include error page template
        $error_message = $show_details ? $error_data['message'] : 'An internal error occurred';
        $error_file = $show_details ? $error_data['file'] : '';
        $error_line = $show_details ? $error_data['line'] : '';
        
        include ROOT_PATH . '/errors/500.php';
    }
    
    /**
     * تسجيل خطأ مخصص
     * Log custom error
     */
    public function logCustomError($message, $type = 'CUSTOM', $file = '', $line = 0) {
        $error_data = [
            'type' => $type,
            'message' => $message,
            'file' => $file ?: debug_backtrace()[0]['file'],
            'line' => $line ?: debug_backtrace()[0]['line'],
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'user_id' => $_SESSION['user_id'] ?? null
        ];
        
        $this->logError($error_data);
    }
    
    /**
     * الحصول على سجلات الأخطاء الأخيرة
     * Get recent error logs
     */
    public function getRecentErrors($limit = 50) {
        global $conn;
        
        if (!$conn) {
            return [];
        }
        
        try {
            $stmt = $conn->prepare("
                SELECT * FROM error_logs 
                ORDER BY created_at DESC 
                LIMIT ?
            ");
            $stmt->bind_param("i", $limit);
            $stmt->execute();
            
            return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * تنظيف سجلات الأخطاء القديمة
     * Clean old error logs
     */
    public function cleanOldErrorLogs($days = 30) {
        global $conn;
        
        if (!$conn) {
            return false;
        }
        
        try {
            $stmt = $conn->prepare("
                DELETE FROM error_logs 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->bind_param("i", $days);
            return $stmt->execute();
        } catch (Exception $e) {
            return false;
        }
    }
}

/**
 * فئة التسجيل المتقدم
 * Advanced Logger Class
 */
class Logger {
    
    private static $instance = null;
    private $log_levels = [
        'DEBUG' => 0,
        'INFO' => 1,
        'WARNING' => 2,
        'ERROR' => 3,
        'CRITICAL' => 4
    ];
    
    private function __construct() {}
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تسجيل رسالة
     * Log message
     */
    public function log($level, $message, $context = []) {
        $current_level = defined('LOG_LEVEL') ? LOG_LEVEL : 'INFO';
        
        if ($this->log_levels[$level] < $this->log_levels[$current_level]) {
            return;
        }
        
        $log_entry = sprintf(
            "[%s] %s: %s",
            date('Y-m-d H:i:s'),
            $level,
            $message
        );
        
        if (!empty($context)) {
            $log_entry .= " Context: " . json_encode($context);
        }
        
        $log_entry .= "\n";
        
        $log_file = LOGS_PATH . '/system.log';
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    public function debug($message, $context = []) {
        $this->log('DEBUG', $message, $context);
    }
    
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    public function critical($message, $context = []) {
        $this->log('CRITICAL', $message, $context);
    }
}

// ===================================
// HELPER FUNCTIONS
// ===================================

/**
 * Initialize error handling
 */
function init_error_handling() {
    ErrorHandler::getInstance();
}

/**
 * Log error (shorthand)
 */
function log_error($message, $context = []) {
    Logger::getInstance()->error($message, $context);
}

/**
 * Log info (shorthand)
 */
function log_info($message, $context = []) {
    Logger::getInstance()->info($message, $context);
}

/**
 * Log warning (shorthand)
 */
function log_warning($message, $context = []) {
    Logger::getInstance()->warning($message, $context);
}

/**
 * Log debug (shorthand)
 */
function log_debug($message, $context = []) {
    Logger::getInstance()->debug($message, $context);
}

/**
 * Log custom error
 */
function log_custom_error($message, $type = 'CUSTOM') {
    ErrorHandler::getInstance()->logCustomError($message, $type);
}

// Initialize error handling
init_error_handling();
?>
