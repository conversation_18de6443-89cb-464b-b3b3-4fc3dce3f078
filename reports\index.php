<?php
/**
 * صفحة التقارير العامة
 * General Reports Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة تغيير اللغة من URL
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $_GET['lang'];
}

// التحقق من الجلسة قبل أي إخراج
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات
if (!in_array($_SESSION['role'], ['student', 'teacher', 'admin'])) {
    header('Location: ../dashboard/');
    exit();
}
include_once '../includes/header.php';

// --- منطق عرض التقارير المخصصة مباشرة بعد الصلاحيات ---
$type = $_GET['type'] ?? '';
if ($type === 'students') {
    // جلب بيانات المراحل الدراسية
    $stages = $conn->query("SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order");
    // جلب بيانات الفصول
    $classes = $conn->query("SELECT id, class_name FROM classes ORDER BY class_name");
    // جلب بيانات الطلاب مع فلترة
    $student_name = clean_input($_GET['student_name'] ?? '');
    $class_id = intval($_GET['class_id'] ?? 0);
    $stage_id = intval($_GET['stage_id'] ?? 0);
    $status = clean_input($_GET['status'] ?? '');
    $where = [];
    $params = [];
    $types = '';
    if ($student_name) {
        $where[] = 'u.full_name LIKE ?';
        $params[] = "%$student_name%";
        $types .= 's';
    }
    if ($class_id) {
        $where[] = 's.class_id = ?';
        $params[] = $class_id;
        $types .= 'i';
    }
    if ($stage_id) {
        $where[] = 'c.stage_id = ?';
        $params[] = $stage_id;
        $types .= 'i';
    }
    if ($status) {
        $where[] = 'u.status = ?';
        $params[] = $status;
        $types .= 's';
    }
    $where_sql = $where ? 'WHERE ' . implode(' AND ', $where) : '';
    $query = "SELECT s.id, u.full_name, c.class_name, es.stage_name, es.stage_code, u.status
              FROM students s
              JOIN users u ON s.user_id = u.id
              JOIN classes c ON s.class_id = c.id
              LEFT JOIN educational_stages es ON c.stage_id = es.id
              $where_sql
              ORDER BY es.sort_order, c.class_name, u.full_name";
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $students = $stmt->get_result();
    ?>
    <div class="container-fluid my-4">
        <h3 class="mb-4 d-flex justify-content-between align-items-center">
            <span><i class="fas fa-users me-2 text-primary"></i><?php echo __('students_report'); ?></span>
            <a href="export_students_report.php?student_name=<?php echo urlencode($student_name); ?>&class_id=<?php echo $class_id; ?>&status=<?php echo $status; ?>" class="btn btn-success">
                <i class="fas fa-file-excel me-2"></i><?php echo __('export'); ?>
            </a>
        </h3>
        <form method="get" class="row g-3 mb-3">
            <input type="hidden" name="type" value="students">
            <div class="col-md-3">
                <input type="text" name="student_name" class="form-control" placeholder="<?php echo __('search_student'); ?>" value="<?php echo htmlspecialchars($student_name); ?>">
            </div>
            <div class="col-md-2">
                <select name="stage_id" class="form-select">
                    <option value=""><?php echo __('all_stages'); ?></option>
                    <?php while ($row = $stages->fetch_assoc()): ?>
                        <option value="<?php echo $row['id']; ?>" <?php if ($stage_id == $row['id']) echo 'selected'; ?>><?php echo htmlspecialchars($row['stage_name']); ?></option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="col-md-3">
                <select name="class_id" class="form-select">
                    <option value=""><?php echo __('all_classes'); ?></option>
                    <?php while ($row = $classes->fetch_assoc()): ?>
                        <option value="<?php echo $row['id']; ?>" <?php if ($class_id == $row['id']) echo 'selected'; ?>><?php echo htmlspecialchars($row['class_name']); ?></option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="col-md-2">
                <select name="status" class="form-select">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="active" <?php if ($status == 'active') echo 'selected'; ?>><?php echo __('active'); ?></option>
                    <option value="inactive" <?php if ($status == 'inactive') echo 'selected'; ?>><?php echo __('inactive'); ?></option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100"><i class="fas fa-search me-2"></i><?php echo __('search'); ?></button>
            </div>
        </form>
        <div class="table-responsive">
            <table class="table table-striped table-bordered align-middle">
                <thead>
                    <tr>
                        <th>#</th>
                        <th><?php echo __('full_name'); ?></th>
                        <th><?php echo __('educational_stage'); ?></th>
                        <th><?php echo __('class'); ?></th>
                        <th><?php echo __('status'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $i = 1; while ($student = $students->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $i++; ?></td>
                            <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                            <td>
                                <?php if (!empty($student['stage_name'])): ?>
                                    <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($student['stage_code']); ?></span>
                                    <?php echo htmlspecialchars($student['stage_name']); ?>
                                <?php else: ?>
                                    <span class="text-muted"><?php echo __('not_assigned'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($student['class_name']); ?></td>
                            <td><?php echo __($student['status']); ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php
    include_once '../includes/footer.php';
    return;
}
if ($type === 'grades') {
    // تم تعطيل تقرير الدرجات لأن وظائف إدارة الدرجات تم حذفها من النظام
    ?>
    <div class="container-fluid my-4">
        <div class="alert alert-info">
            <h4><i class="fas fa-info-circle me-2"></i>تقرير الدرجات غير متاح</h4>
            <p>تم إزالة وظائف إدارة الدرجات من النظام. يرجى استخدام التقارير الأخرى المتاحة.</p>
            <a href="?type=attendance" class="btn btn-primary">عرض تقرير الحضور</a>
            <a href="?type=exams" class="btn btn-secondary">عرض تقرير الامتحانات</a>
        </div>
    </div>
    <?php
    include_once '../includes/footer.php';
    return;
}

// الحصول على معرف المعلم إذا كان المستخدم معلماً
$teacher_id = null;
if ($_SESSION['role'] === 'teacher') {
    $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
    $teacher_stmt->bind_param("i", $_SESSION['user_id']);
    $teacher_stmt->execute();
    $teacher_result = $teacher_stmt->get_result();
    $teacher_data = $teacher_result->fetch_assoc();
    $teacher_id = $teacher_data['id'] ?? 0;
}

// جلب معرف السنة الدراسية الحالي
$current_year = get_current_academic_year();
$current_year_id = null;
$stmt = $conn->prepare("SELECT id FROM academic_years WHERE year_name = ?");
$stmt->bind_param("s", $current_year);
$stmt->execute();
$result = $stmt->get_result();
if ($row = $result->fetch_assoc()) {
    $current_year_id = $row['id'];
} else {
    $current_year_id = 1; // افتراضي إذا لم توجد السنة
}

// إحصائيات عامة
$students_stats_query = "
    SELECT 
        COUNT(*) as total_students,
        COUNT(CASE WHEN u.status = 'active' THEN 1 END) as active_students,
        COUNT(CASE WHEN u.gender = 'male' THEN 1 END) as male_students,
        COUNT(CASE WHEN u.gender = 'female' THEN 1 END) as female_students
    FROM students s
    JOIN users u ON s.user_id = u.id
";

if ($teacher_id) {
    $students_stats_query .= " 
        JOIN teacher_assignments ta ON s.class_id = ta.class_id 
        WHERE ta.teacher_id = ? AND ta.status = 'active'
    ";
    $students_stats_stmt = $conn->prepare($students_stats_query);
    $students_stats_stmt->bind_param("i", $teacher_id);
} else {
    $students_stats_stmt = $conn->prepare($students_stats_query);
}
if (!$students_stats_stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($students_stats_query));
}
$students_stats_stmt->execute();
$students_stats = $students_stats_stmt->get_result()->fetch_assoc();

// إحصائيات الطلاب
$students_stats_query = "
    SELECT 
        COUNT(*) as total_students,
        COUNT(CASE WHEN u.status = 'active' THEN 1 END) as active_students,
        COUNT(CASE WHEN u.gender = 'male' THEN 1 END) as male_students,
        COUNT(CASE WHEN u.gender = 'female' THEN 1 END) as female_students
    FROM students s
    JOIN users u ON s.user_id = u.id
";

if ($teacher_id) {
    $students_stats_query .= " 
        JOIN teacher_assignments ta ON s.class_id = ta.class_id 
        WHERE ta.teacher_id = ? AND ta.status = 'active'
    ";
    $students_stats_stmt = $conn->prepare($students_stats_query);
    $students_stats_stmt->bind_param("i", $teacher_id);
} else {
    $students_stats_stmt = $conn->prepare($students_stats_query);
}
if (!$students_stats_stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($students_stats_query));
}
$students_stats_stmt->execute();
$students_stats = $students_stats_stmt->get_result()->fetch_assoc();

// إحصائيات الحضور
$attendance_stats_query = "
    SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
        COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
        COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count
    FROM attendance a
    JOIN students s ON a.student_id = s.id
    WHERE DATE(a.attendance_date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
";

if ($teacher_id) {
    $attendance_stats_query .= " 
        AND EXISTS (
            SELECT 1 FROM teacher_assignments ta 
            WHERE ta.teacher_id = ? AND ta.class_id = s.class_id AND ta.status = 'active'
        )
    ";
    $attendance_stats_stmt = $conn->prepare($attendance_stats_query);
    $attendance_stats_stmt->bind_param("i", $teacher_id);
} else {
    $attendance_stats_stmt = $conn->prepare($attendance_stats_query);
}
$attendance_stats_stmt->execute();
$attendance_stats = $attendance_stats_stmt->get_result()->fetch_assoc();

// إحصائيات الدرجات - تم تعطيل هذا القسم لأن وظائف إدارة الدرجات تم حذفها من النظام
$grades_stats = [
    'total_grades' => 0,
    'avg_percentage' => 0,
    'excellent_grades' => 0,
    'very_good_grades' => 0,
    'good_grades' => 0,
    'acceptable_grades' => 0,
    'failing_grades' => 0
];

// إحصائيات الامتحانات
$exams_stats_query = "
    SELECT 
        COUNT(*) as total_exams,
        COUNT(CASE WHEN e.status = 'published' THEN 1 END) as published_exams,
        COUNT(CASE WHEN e.status = 'active' THEN 1 END) as active_exams,
        COUNT(CASE WHEN e.status = 'completed' THEN 1 END) as completed_exams,
        AVG(e.total_marks) as avg_total_marks
    FROM exams e
    WHERE e.academic_year_id = ?
";

if ($teacher_id) {
    $exams_stats_query .= " AND e.teacher_id = ?";
    $exams_stats_stmt = $conn->prepare($exams_stats_query);
    if (!$exams_stats_stmt) {
        die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($exams_stats_query));
    }
    $exams_stats_stmt->bind_param("si", $current_year_id, $teacher_id);
} else {
    $exams_stats_stmt = $conn->prepare($exams_stats_query);
    if (!$exams_stats_stmt) {
        die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($exams_stats_query));
    }
    $exams_stats_stmt->bind_param("s", $current_year_id);
}
$exams_stats_stmt->execute();
$exams_stats = $exams_stats_stmt->get_result()->fetch_assoc();

// إحصائيات مالية (للمدير فقط)
$financial_stats = null;
if (check_permission('admin')) {
    $financial_stats_query = "
        SELECT 
            COUNT(DISTINCT sf.id) as total_fees,
            SUM(sf.final_amount) as total_amount,
            SUM(CASE WHEN sf.status = 'paid' THEN sf.final_amount ELSE 0 END) as paid_amount,
            COUNT(DISTINCT sp.id) as total_payments,
            SUM(sp.amount) as payments_amount
        FROM student_fees sf
        LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
        WHERE sf.academic_year_id = ?
    ";
    $financial_stats_stmt = $conn->prepare($financial_stats_query);
    $financial_stats_stmt->bind_param("s", $current_year_id);
    $financial_stats_stmt->execute();
    $financial_stats = $financial_stats_stmt->get_result()->fetch_assoc();
}

// حساب النسب المئوية
$attendance_rate = $attendance_stats['total_records'] > 0 ? 
    ($attendance_stats['present_count'] / $attendance_stats['total_records']) * 100 : 0;

$collection_rate = $financial_stats && $financial_stats['total_amount'] > 0 ? 
    ($financial_stats['paid_amount'] / $financial_stats['total_amount']) * 100 : 0;

// تعديل منطق جلب التقارير حسب الدور
if ($user_role === 'student') {
    $reports = [];
}
?>

<?php if ($user_role === 'student'): ?>
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0"><?php echo __('reports'); ?></h1>
        </div>
        <?php if (count($reports) > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-bordered align-middle">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th><?php echo __('date'); ?></th>
                            <th><?php echo __('status'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($reports as $i => $report): ?>
                            <tr>
                                <td><?php echo $i + 1; ?></td>
                                <td><?php echo htmlspecialchars($report['date']); ?></td>
                                <td><?php echo htmlspecialchars($report['status']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i><?php echo __('no_reports_found'); ?>
            </div>
        <?php endif; ?>
    </div>
<?php else: ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('reports'); ?></h1>
            <p class="text-muted"><?php echo __('comprehensive_system_reports'); ?></p>
        </div>
        <div>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i><?php echo __('export_reports'); ?>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="export.php?type=students">
                        <i class="fas fa-users me-2"></i><?php echo __('students_report'); ?>
                    </a></li>
                    <li><a class="dropdown-item" href="export.php?type=attendance">
                        <i class="fas fa-calendar-check me-2"></i><?php echo __('attendance_report'); ?>
                    </a></li>
                    <!-- تم إزالة تقرير الدرجات لأن وظائف إدارة الدرجات تم حذفها من النظام -->
                    <?php if (check_permission('admin')): ?>
                    <li><a class="dropdown-item" href="export.php?type=financial">
                        <i class="fas fa-dollar-sign me-2"></i><?php echo __('financial_report'); ?>
                    </a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- Overview Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-users text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($students_stats['total_students']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_students'); ?></p>
                            <small class="text-success">
                                <?php echo number_format($students_stats['active_students']); ?> <?php echo __('active'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-calendar-check text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($attendance_rate, 1); ?>%</h3>
                            <p class="text-muted mb-0"><?php echo __('attendance_rate'); ?></p>
                            <small class="text-info">
                                <?php echo __('last_30_days'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient p-3 rounded-3">
                                <i class="fas fa-chart-line text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($grades_stats['avg_percentage'] ?? 0, 1); ?>%</h3>
                            <p class="text-muted mb-0"><?php echo __('average_grade'); ?></p>
                            <small class="text-warning">
                                <?php echo number_format($grades_stats['total_grades']); ?> <?php echo __('grades'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if ($financial_stats): ?>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-dollar-sign text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($collection_rate, 1); ?>%</h3>
                            <p class="text-muted mb-0"><?php echo __('collection_rate'); ?></p>
                            <small class="text-success">
                                <?php echo number_format($financial_stats['paid_amount'], 0); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="row">
        <!-- Students Distribution -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i><?php echo __('students_distribution'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="studentsChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Grades Distribution -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i><?php echo __('grades_distribution'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="gradesChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Attendance Trends -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i><?php echo __('attendance_trends'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceChart" height="150"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Reports -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i><?php echo __('quick_reports'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="index.php?type=students" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-users me-2 text-primary"></i>
                                    <?php echo __('students_report'); ?>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                        
                        <a href="index.php?type=attendance" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-calendar-check me-2 text-success"></i>
                                    <?php echo __('attendance_report'); ?>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                        
                        <!-- تم إزالة تقرير الدرجات لأن وظائف إدارة الدرجات تم حذفها من النظام -->
                        
                        <a href="index.php?type=exams" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file-alt me-2 text-warning"></i>
                                    <?php echo __('exams_report'); ?>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                        
                        <?php if (check_permission('admin')): ?>
                        <a href="index.php?type=financial" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-dollar-sign me-2 text-danger"></i>
                                    <?php echo __('financial_reports'); ?>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                        <?php endif; ?>
                        
                        <a href="index.php?type=custom" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-cogs me-2 text-secondary"></i>
                                    <?php echo __('custom_report'); ?>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i><?php echo __('recent_activities'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    // جلب الأنشطة الأخيرة
                    $activities_query = "
                        SELECT 
                            al.*,
                            u.full_name as user_name
                        FROM activity_logs al
                        JOIN users u ON al.user_id = u.id
                        ORDER BY al.created_at DESC
                        LIMIT 10
                    ";
                    $activities = $conn->query($activities_query);
                    ?>
                    
                    <?php if ($activities && $activities->num_rows > 0): ?>
                        <div class="timeline">
                            <?php while ($activity = $activities->fetch_assoc()): ?>
                                <div class="timeline-item">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">
                                            <?php echo htmlspecialchars($activity['user_name']); ?>
                                            <small class="text-muted">
                                                <?php echo time_ago($activity['created_at']); ?>
                                            </small>
                                        </h6>
                                        <p class="timeline-text">
                                            <?php echo get_activity_description($activity['action'], $activity['table_name']); ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo __('no_recent_activities'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Students Distribution Chart
    const studentsCtx = document.getElementById('studentsChart').getContext('2d');
    new Chart(studentsCtx, {
        type: 'doughnut',
        data: {
            labels: ['<?php echo __('male'); ?>', '<?php echo __('female'); ?>'],
            datasets: [{
                data: [<?php echo $students_stats['male_students']; ?>, <?php echo $students_stats['female_students']; ?>],
                backgroundColor: ['#667eea', '#764ba2'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Grades Distribution Chart
    const gradesCtx = document.getElementById('gradesChart').getContext('2d');
    new Chart(gradesCtx, {
        type: 'bar',
        data: {
            labels: ['<?php echo __('excellent'); ?>', '<?php echo __('very_good'); ?>', '<?php echo __('good'); ?>', '<?php echo __('acceptable'); ?>', '<?php echo __('failing'); ?>'],
            datasets: [{
                data: [
                    <?php echo $grades_stats['excellent_grades'] ?? 0; ?>,
                    <?php echo $grades_stats['very_good_grades'] ?? 0; ?>,
                    <?php echo $grades_stats['good_grades'] ?? 0; ?>,
                    <?php echo $grades_stats['acceptable_grades'] ?? 0; ?>,
                    <?php echo $grades_stats['failing_grades'] ?? 0; ?>
                ],
                backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#fd7e14', '#dc3545'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Attendance Trends Chart (placeholder - would need actual data)
    const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
    new Chart(attendanceCtx, {
        type: 'line',
        data: {
            labels: ['<?php echo __('week_1'); ?>', '<?php echo __('week_2'); ?>', '<?php echo __('week_3'); ?>', '<?php echo __('week_4'); ?>'],
            datasets: [{
                label: '<?php echo __('attendance_rate'); ?>',
                data: [85, 88, 92, 87],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
</script>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -23px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #667eea;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
    }
    
    .timeline-title {
        margin-bottom: 5px;
        font-weight: 600;
    }
    
    .timeline-text {
        margin-bottom: 0;
        color: #6c757d;
    }
</style>

<?php
function get_activity_description($action, $table_name) {
    $descriptions = [
        'login' => __('logged_into_system'),
        'logout' => __('logged_out_of_system'),
        'add_student' => __('added_new_student'),
        'edit_student' => __('updated_student_info'),
        'delete_student' => __('deleted_student'),
        'add_teacher' => __('added_new_teacher'),
        'edit_teacher' => __('updated_teacher_info'),
        'add_grade' => __('added_new_grade'),
        'take_attendance' => __('recorded_attendance'),
        'add_exam' => __('created_new_exam'),
        'add_payment' => __('recorded_payment'),
        'add_student_fee' => __('added_student_fee')
    ];
    
    return $descriptions[$action] ?? __('performed_action') . ': ' . $action;
}

function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return __('just_now');
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' ' . __('minutes_ago');
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' ' . __('hours_ago');
    } else {
        $days = floor($time / 86400);
        return $days . ' ' . __('days_ago');
    }
}
?>

<?php include_once '../includes/footer.php'; ?>
