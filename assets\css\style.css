/**
 * ملف الأنماط الأساسي
 * Main Stylesheet
 */

/* متغيرات CSS */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* الخطوط */
body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
}

/* العناصر الأساسية */
.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* الشريط الجانبي */
.sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    min-height: calc(100vh - 100px);
}

.sidebar-nav .nav-link {
    color: var(--dark-color);
    padding: 12px 20px;
    margin: 5px 0;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    transform: translateX(5px);
}

.sidebar-nav .nav-link i {
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    min-height: calc(100vh - 100px);
    transition: var(--transition);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 10px 0;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

/* الشارات */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
}

/* النماذج */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.input-group-text {
    background: transparent;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.input-group:focus-within .input-group-text {
    border-color: var(--primary-color);
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* شريط التقدم */
.progress {
    border-radius: var(--border-radius);
    height: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
}

/* الترقيم */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
    transition: var(--transition);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

/* الرسوم المتحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* التحميل البطيء للصور */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* التحديد المخصص */
::selection {
    background: var(--primary-color);
    color: white;
}

::-moz-selection {
    background: var(--primary-color);
    color: white;
}

/* الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .stats-card h3 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* أنماط الطباعة */
@media print {
    .navbar, .sidebar, .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        box-shadow: none !important;
        background: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .btn {
        display: none !important;
    }
    
    .table {
        box-shadow: none !important;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* أنماط إضافية للمكونات */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.language-switcher, .theme-switcher {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    background: transparent;
    border: none;
}

.language-switcher:hover, .theme-switcher:hover {
    background: rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

/* أنماط خاصة بالامتحانات */
.exam-card {
    border-left: 4px solid var(--primary-color);
}

.exam-card.active {
    border-left-color: var(--success-color);
}

.exam-card.completed {
    border-left-color: var(--warning-color);
}

.exam-card.draft {
    border-left-color: var(--secondary);
}

/* أنماط خاصة بالدرجات */
.grade-excellent {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.grade-good {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

.grade-average {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.grade-poor {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

/* أنماط خاصة بالحضور */
.attendance-present {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.attendance-absent {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.attendance-late {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.attendance-excused {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* إصلاح مشاكل المودال والطبقة الخلفية (تكرار في style.css لضمان عدم التعارض) */
.modal-backdrop {
    opacity: 0.5 !important;
    z-index: 2050 !important;
}
.modal.show, .modal.fade.show {
    opacity: 1 !important;
    z-index: 2100 !important;
    pointer-events: auto !important;
}
.modal-dialog {
    pointer-events: auto !important;
}
