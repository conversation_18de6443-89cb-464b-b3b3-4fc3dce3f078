<?php
/**
 * صفحة إضافة صف دراسي جديد
 * Add New School Grade Page
 */

require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('add_grade');

// الحصول على المراحل الدراسية
$stages_query = "SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = [];
if ($stages_result) {
    while ($row = $stages_result->fetch_assoc()) {
        $stages[] = $row;
    }
}

// الحصول على stage_id من URL إذا كان موجوداً
$selected_stage_id = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;

// متغيرات الرسائل والأخطاء
$success_message = '';
$error_message = '';
$validation_errors = [];

// متغيرات النموذج
$grade_name = '';
$grade_name_en = '';
$grade_code = '';
$stage_id = $selected_stage_id;
$description = '';
$sort_order = '';
$min_age = '';
$max_age = '';
$status = 'active';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // تنظيف البيانات
    $grade_name = trim($_POST['grade_name'] ?? '');
    $grade_name_en = trim($_POST['grade_name_en'] ?? '');
    $grade_code = trim($_POST['grade_code'] ?? '');
    $stage_id = intval($_POST['stage_id'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $sort_order = intval($_POST['sort_order'] ?? 0);
    $min_age = !empty($_POST['min_age']) ? intval($_POST['min_age']) : null;
    $max_age = !empty($_POST['max_age']) ? intval($_POST['max_age']) : null;
    $status = $_POST['status'] ?? 'active';

    // التحقق من صحة البيانات
    if (empty($grade_name)) {
        $validation_errors['grade_name'] = __('grade_name_required');
    } elseif (strlen($grade_name) > 100) {
        $validation_errors['grade_name'] = __('grade_name_too_long');
    }

    if (empty($grade_code)) {
        $validation_errors['grade_code'] = __('grade_code_required');
    } elseif (strlen($grade_code) > 20) {
        $validation_errors['grade_code'] = __('grade_code_too_long');
    } elseif (!preg_match('/^[A-Z0-9_]+$/', $grade_code)) {
        $validation_errors['grade_code'] = __('grade_code_invalid_format');
    }

    if (!empty($grade_name_en) && strlen($grade_name_en) > 100) {
        $validation_errors['grade_name_en'] = __('grade_name_en_too_long');
    }

    if ($stage_id <= 0) {
        $validation_errors['stage_id'] = __('educational_stage') . ' ' . __('required_field');
    }

    if ($sort_order <= 0) {
        $validation_errors['sort_order'] = __('grade_sort_order_required');
    }

    if ($min_age !== null && ($min_age < 0 || $min_age > 25)) {
        $validation_errors['min_age'] = __('min_age_invalid');
    }

    if ($max_age !== null && ($max_age < 0 || $max_age > 25)) {
        $validation_errors['max_age'] = __('max_age_invalid');
    }

    if ($min_age !== null && $max_age !== null && $min_age >= $max_age) {
        $validation_errors['age_range'] = __('age_range_invalid');
    }

    // التحقق من عدم تكرار رمز الصف
    if (empty($validation_errors['grade_code'])) {
        $check_query = "SELECT id FROM grades WHERE grade_code = ?";
        $stmt = $conn->prepare($check_query);
        if ($stmt) {
            $stmt->bind_param('s', $grade_code);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $validation_errors['grade_code'] = __('grade_code_exists');
            }
            $stmt->close();
        }
    }

    // التحقق من عدم تكرار ترتيب الصف في نفس المرحلة
    if (empty($validation_errors['sort_order']) && $stage_id > 0) {
        $check_query = "SELECT id FROM grades WHERE stage_id = ? AND sort_order = ?";
        $stmt = $conn->prepare($check_query);
        if ($stmt) {
            $stmt->bind_param('ii', $stage_id, $sort_order);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $validation_errors['sort_order'] = __('grade_sort_order_exists');
            }
            $stmt->close();
        }
    }

    // إذا لم توجد أخطاء، قم بإدراج البيانات
    if (empty($validation_errors)) {
        $insert_query = "
            INSERT INTO grades 
            (grade_name, grade_name_en, grade_code, stage_id, description, sort_order, min_age, max_age, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";
        
        $stmt = $conn->prepare($insert_query);
        if ($stmt) {
            $stmt->bind_param('sssisiiss', 
                $grade_name, $grade_name_en, $grade_code, $stage_id, 
                $description, $sort_order, $min_age, $max_age, $status
            );
            
            if ($stmt->execute()) {
                $grade_id = $conn->insert_id;
                
                // تسجيل النشاط (يمكن إضافة هذا لاحقاً)
                // log_activity($_SESSION['user_id'], 'add_grade', 'grades', $grade_id);
                
                $success_message = __('grade_added_successfully');
                
                // إعادة تعيين المتغيرات
                $grade_name = $grade_name_en = $grade_code = $description = '';
                $sort_order = $min_age = $max_age = '';
                $stage_id = $selected_stage_id;
                $status = 'active';
                
                // إعادة توجيه بعد 2 ثانية
                echo "<script>
                    setTimeout(function() {
                        window.location.href = 'index.php';
                    }, 2000);
                </script>";
                
            } else {
                $error_message = __('database_error') . ': ' . $conn->error;
            }
            $stmt->close();
        } else {
            $error_message = __('database_error') . ': ' . $conn->error;
        }
    }
}

// الحصول على أعلى ترتيب موجود في المرحلة المحددة
$suggested_order = 1;
if ($stage_id > 0) {
    $max_order_query = "SELECT MAX(sort_order) as max_order FROM grades WHERE stage_id = ?";
    $stmt = $conn->prepare($max_order_query);
    if ($stmt) {
        $stmt->bind_param('i', $stage_id);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $suggested_order = ($row['max_order'] ?? 0) + 1;
        }
        $stmt->close();
    }
}
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-plus text-primary me-2"></i>
                <?php echo __('add_grade'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('add_new_grade'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_grades'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Form Section -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('grade_information'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        <div class="row">
                            <!-- اسم الصف بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label for="grade_name" class="form-label">
                                    <?php echo __('grade_name'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?php echo isset($validation_errors['grade_name']) ? 'is-invalid' : ''; ?>" 
                                       id="grade_name" 
                                       name="grade_name" 
                                       value="<?php echo htmlspecialchars($grade_name); ?>"
                                       placeholder="<?php echo __('enter_grade_name'); ?>"
                                       maxlength="100"
                                       required>
                                <?php if (isset($validation_errors['grade_name'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['grade_name']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- اسم الصف بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label for="grade_name_en" class="form-label">
                                    <?php echo __('grade_name_en'); ?>
                                </label>
                                <input type="text" 
                                       class="form-control <?php echo isset($validation_errors['grade_name_en']) ? 'is-invalid' : ''; ?>" 
                                       id="grade_name_en" 
                                       name="grade_name_en" 
                                       value="<?php echo htmlspecialchars($grade_name_en); ?>"
                                       placeholder="<?php echo __('enter_grade_name_en'); ?>"
                                       maxlength="100">
                                <?php if (isset($validation_errors['grade_name_en'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['grade_name_en']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رمز الصف -->
                            <div class="col-md-4 mb-3">
                                <label for="grade_code" class="form-label">
                                    <?php echo __('grade_code'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?php echo isset($validation_errors['grade_code']) ? 'is-invalid' : ''; ?>" 
                                       id="grade_code" 
                                       name="grade_code" 
                                       value="<?php echo htmlspecialchars($grade_code); ?>"
                                       placeholder="<?php echo __('enter_grade_code'); ?>"
                                       maxlength="20"
                                       style="text-transform: uppercase;"
                                       pattern="[A-Z0-9_]+"
                                       required>
                                <div class="form-text"><?php echo __('grade_code_help'); ?></div>
                                <?php if (isset($validation_errors['grade_code'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['grade_code']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- المرحلة الدراسية -->
                            <div class="col-md-4 mb-3">
                                <label for="stage_id" class="form-label">
                                    <?php echo __('educational_stage'); ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?php echo isset($validation_errors['stage_id']) ? 'is-invalid' : ''; ?>" 
                                        id="stage_id" name="stage_id" required onchange="updateSortOrder()">
                                    <option value=""><?php echo __('select_stage'); ?></option>
                                    <?php foreach ($stages as $stage): ?>
                                        <option value="<?php echo $stage['id']; ?>" 
                                                <?php echo $stage_id == $stage['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($stage['stage_name']); ?> (<?php echo htmlspecialchars($stage['stage_code']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text"><?php echo __('grade_stage_help'); ?></div>
                                <?php if (isset($validation_errors['stage_id'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['stage_id']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- ترتيب الصف -->
                            <div class="col-md-4 mb-3">
                                <label for="sort_order" class="form-label">
                                    <?php echo __('sort_order'); ?> <span class="text-danger">*</span>
                                </label>
                                <input type="number" 
                                       class="form-control <?php echo isset($validation_errors['sort_order']) ? 'is-invalid' : ''; ?>" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="<?php echo $sort_order ?: $suggested_order; ?>"
                                       min="1"
                                       max="20"
                                       required>
                                <div class="form-text"><?php echo __('grade_sort_order_help'); ?></div>
                                <?php if (isset($validation_errors['sort_order'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['sort_order']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <!-- العمر الأدنى -->
                            <div class="col-md-4 mb-3">
                                <label for="min_age" class="form-label">
                                    <?php echo __('min_age'); ?>
                                </label>
                                <input type="number" 
                                       class="form-control <?php echo isset($validation_errors['min_age']) ? 'is-invalid' : ''; ?>" 
                                       id="min_age" 
                                       name="min_age" 
                                       value="<?php echo $min_age; ?>"
                                       min="0"
                                       max="25"
                                       placeholder="<?php echo __('optional'); ?>">
                                <div class="form-text"><?php echo __('grade_age_range_help'); ?></div>
                                <?php if (isset($validation_errors['min_age'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['min_age']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- العمر الأقصى -->
                            <div class="col-md-4 mb-3">
                                <label for="max_age" class="form-label">
                                    <?php echo __('max_age'); ?>
                                </label>
                                <input type="number" 
                                       class="form-control <?php echo isset($validation_errors['max_age']) ? 'is-invalid' : ''; ?>" 
                                       id="max_age" 
                                       name="max_age" 
                                       value="<?php echo $max_age; ?>"
                                       min="0"
                                       max="25"
                                       placeholder="<?php echo __('optional'); ?>">
                                <?php if (isset($validation_errors['max_age'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['max_age']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- حالة الصف -->
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">
                                    <?php echo __('status'); ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>
                                        <?php echo __('active'); ?>
                                    </option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>
                                        <?php echo __('inactive'); ?>
                                    </option>
                                </select>
                            </div>
                        </div>

                        <!-- رسالة خطأ النطاق العمري -->
                        <?php if (isset($validation_errors['age_range'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $validation_errors['age_range']; ?>
                            </div>
                        <?php endif; ?>

                        <!-- الوصف -->
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <?php echo __('description'); ?>
                            </label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="4"
                                      placeholder="<?php echo __('enter_grade_description'); ?>"><?php echo htmlspecialchars($description); ?></textarea>
                            <div class="form-text"><?php echo __('description_help'); ?></div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('save_grade'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar with Help -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        <?php echo __('help_and_tips'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary"><?php echo __('grade_name'); ?></h6>
                        <p class="small text-muted"><?php echo __('grade_name_tip'); ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary"><?php echo __('grade_code'); ?></h6>
                        <p class="small text-muted"><?php echo __('grade_code_tip'); ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary"><?php echo __('educational_stage'); ?></h6>
                        <p class="small text-muted"><?php echo __('grade_stage_help'); ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary"><?php echo __('sort_order'); ?></h6>
                        <p class="small text-muted"><?php echo __('grade_sort_order_tip'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Examples Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        <?php echo __('examples'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="mb-2">
                            <strong><?php echo __('kindergarten'); ?>:</strong><br>
                            <span class="text-muted">PRE1, PRE2</span>
                        </div>
                        <div class="mb-2">
                            <strong><?php echo __('primary'); ?>:</strong><br>
                            <span class="text-muted">G1, G2, G3, G4, G5, G6</span>
                        </div>
                        <div class="mb-2">
                            <strong><?php echo __('middle'); ?>:</strong><br>
                            <span class="text-muted">G7, G8, G9</span>
                        </div>
                        <div>
                            <strong><?php echo __('high'); ?>:</strong><br>
                            <span class="text-muted">G10, G11, G12</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحويل رمز الصف إلى أحرف كبيرة
document.getElementById('grade_code').addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9_]/g, '');
});

// التحقق من النطاق العمري
document.getElementById('min_age').addEventListener('change', validateAgeRange);
document.getElementById('max_age').addEventListener('change', validateAgeRange);

function validateAgeRange() {
    const minAge = parseInt(document.getElementById('min_age').value);
    const maxAge = parseInt(document.getElementById('max_age').value);
    
    if (minAge && maxAge && minAge >= maxAge) {
        document.getElementById('max_age').setCustomValidity('<?php echo __('max_age_must_be_greater'); ?>');
    } else {
        document.getElementById('max_age').setCustomValidity('');
    }
}

// تحديث ترتيب الصف عند تغيير المرحلة
function updateSortOrder() {
    const stageId = document.getElementById('stage_id').value;
    if (stageId) {
        // يمكن إضافة AJAX هنا لجلب الترتيب المقترح
        // لكن سنتركه بسيط الآن
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
