<h3 class="mb-4">
    <i class="fas fa-cogs text-primary me-2"></i>
    Finalize Installation
</h3>

<p class="text-muted mb-4">
    The system is ready to be installed. Click the button below to complete the installation process.
</p>

<div class="row">
    <div class="col-md-6">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>What will be installed:
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Database tables and structure
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        System configuration files
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Required directories and permissions
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Sample data (fee types, subjects, classes)
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Security configurations
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        Installation lock file
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Installation Summary:
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless mb-0">
                    <tr>
                        <td><strong>Database:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['db_name'] ?? 'Not configured'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Admin Email:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['admin_email'] ?? 'Not configured'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>School Name:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['school_name'] ?? 'Not configured'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Academic Year:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['academic_year'] ?? 'Not configured'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Currency:</strong></td>
                        <td><?php echo htmlspecialchars($_SESSION['currency_symbol'] ?? 'Not configured'); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="alert alert-warning mt-4">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Important:</strong> 
    <ul class="mb-0 mt-2">
        <li>Make sure you have a backup of your database if you're reinstalling</li>
        <li>The installation process may take a few moments to complete</li>
        <li>Do not close this page or navigate away during installation</li>
        <li>After installation, the install directory should be removed for security</li>
    </ul>
</div>

<form method="POST" action="?step=5" id="installForm">
    <div class="d-flex justify-content-between">
        <a href="?step=4" class="btn btn-secondary btn-lg">
            <i class="fas fa-arrow-left me-2"></i>Back
        </a>
        <button type="submit" class="btn btn-success btn-lg" id="installBtn">
            <i class="fas fa-download me-2"></i>Install System
        </button>
    </div>
</form>

<div id="installProgress" class="mt-4" style="display: none;">
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">
                <i class="fas fa-spinner fa-spin me-2"></i>Installing...
            </h5>
            <div class="progress mb-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" 
                     style="width: 0%" 
                     id="progressBar">
                </div>
            </div>
            <p class="mb-0" id="progressText">Preparing installation...</p>
        </div>
    </div>
</div>

<script>
document.getElementById('installForm').addEventListener('submit', function(e) {
    // Show progress
    document.getElementById('installProgress').style.display = 'block';
    document.getElementById('installBtn').disabled = true;
    
    // Simulate progress (in real implementation, this would be actual progress)
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    const steps = [
        'Creating database tables...',
        'Inserting sample data...',
        'Creating directories...',
        'Setting up security...',
        'Finalizing installation...'
    ];
    
    const interval = setInterval(() => {
        progress += 20;
        progressBar.style.width = progress + '%';
        
        if (progress <= 100) {
            const stepIndex = Math.floor((progress - 1) / 20);
            if (steps[stepIndex]) {
                progressText.textContent = steps[stepIndex];
            }
        }
        
        if (progress >= 100) {
            clearInterval(interval);
            progressText.textContent = 'Installation completed!';
        }
    }, 1000);
});
</script>
