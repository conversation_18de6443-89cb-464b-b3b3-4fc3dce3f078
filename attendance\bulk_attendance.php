<?php
require_once '../includes/header.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_bulk_attendance'])) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $class_id = intval($_POST['class_id'] ?? 0);
        $subject_id = intval($_POST['subject_id'] ?? 0);
        $attendance_date = clean_input($_POST['attendance_date'] ?? '');
        $status = clean_input($_POST['status'] ?? 'absent');
        $notes = clean_input($_POST['notes'] ?? '');

        $errors = [];
        if (empty($class_id)) {
            $errors[] = __('class') . ' ' . __('required_field');
        }
        if (!validate_date($attendance_date)) {
            $errors[] = __('invalid_date');
        }
        if (empty($status)) {
            $errors[] = __('status') . ' ' . __('required_field');
        }

        $user_role = $_SESSION['role'];
        $user_id = $_SESSION['user_id'];
        if ($user_role === 'teacher') {
            $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
            $teacher_stmt->bind_param("i", $user_id);
            $teacher_stmt->execute();
            $teacher_result = $teacher_stmt->get_result();
            $teacher_data = $teacher_result->fetch_assoc();
            $teacher_id = $teacher_data['id'] ?? 0;
            $assignment_check = $conn->prepare("
                SELECT id FROM teacher_assignments 
                WHERE teacher_id = ? AND class_id = ? AND status = 'active'
            ");
            $assignment_check->bind_param("ii", $teacher_id, $class_id);
            $assignment_check->execute();
            if ($assignment_check->get_result()->num_rows === 0) {
                $errors[] = __('not_assigned_to_class');
            }
        }

        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();
            try {
                $students_stmt = $conn->prepare("SELECT id FROM students WHERE class_id = ?");
                $students_stmt->bind_param("i", $class_id);
                $students_stmt->execute();
                $students = $students_stmt->get_result();
                $count = 0;
                while ($student = $students->fetch_assoc()) {
                    $student_id = $student['id'];
                    $existing_stmt = $conn->prepare("
                        SELECT id FROM attendance 
                        WHERE student_id = ? AND DATE(attendance_date) = ? AND subject_id = ?
                    ");
                    $existing_stmt->bind_param("isi", $student_id, $attendance_date, $subject_id);
                    $existing_stmt->execute();
                    $existing_result = $existing_stmt->get_result();
                    if ($existing_result->num_rows > 0) {
                        $existing_record = $existing_result->fetch_assoc();
                        $update_stmt = $conn->prepare("
                            UPDATE attendance 
                            SET status = ?, notes = ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        $update_stmt->bind_param("ssi", $status, $notes, $existing_record['id']);
                        $update_stmt->execute();
                    } else {
                        $insert_stmt = $conn->prepare("
                            INSERT INTO attendance (
                                student_id, subject_id, attendance_date, status, notes, recorded_by, created_at
                            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                        ");
                        $insert_stmt->bind_param("iisssi", $student_id, $subject_id, $attendance_date, $status, $notes, $_SESSION['user_id']);
                        $insert_stmt->execute();
                    }
                    $count++;
                }
                $conn->commit();
                $success_message = sprintf(__('attendance_saved_successfully'), $count, 0);
            } catch (Exception $e) {
                $conn->rollback();
                log_error("Error saving bulk attendance: " . $e->getMessage());
                $error_message = __('error_occurred');
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

$teacher_id = null;
if ($_SESSION['role'] === 'teacher') {
    $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
    $teacher_stmt->bind_param("i", $_SESSION['user_id']);
    $teacher_stmt->execute();
    $teacher_result = $teacher_stmt->get_result();
    $teacher_data = $teacher_result->fetch_assoc();
    $teacher_id = $teacher_data['id'] ?? 0;
}
if ($teacher_id) {
    $classes_query = "
        SELECT DISTINCT c.id, c.class_name, c.grade_level 
        FROM classes c 
        JOIN teacher_assignments ta ON c.id = ta.class_id 
        WHERE ta.teacher_id = ? AND ta.status = 'active'
        ORDER BY c.grade_level, c.class_name
    ";
    $classes_stmt = $conn->prepare($classes_query);
    $classes_stmt->bind_param("i", $teacher_id);
    $classes_stmt->execute();
    $classes = $classes_stmt->get_result();
} else {
    $classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");
}
if ($teacher_id) {
    $subjects_query = "
        SELECT DISTINCT s.id, s.subject_name 
        FROM subjects s 
        JOIN teacher_assignments ta ON s.id = ta.subject_id 
        WHERE ta.teacher_id = ? AND ta.status = 'active'
        ORDER BY s.subject_name
    ";
    $subjects_stmt = $conn->prepare($subjects_query);
    $subjects_stmt->bind_param("i", $teacher_id);
    $subjects_stmt->execute();
    $subjects = $subjects_stmt->get_result();
} else {
    $subjects = $conn->query("SELECT id, subject_name FROM subjects WHERE status = 'active' ORDER BY subject_name");
}
$selected_class_id = intval($_GET['class_id'] ?? $_POST['class_id'] ?? 0);
$selected_subject_id = intval($_GET['subject_id'] ?? $_POST['subject_id'] ?? 0);
$selected_date = clean_input($_GET['date'] ?? $_POST['attendance_date'] ?? date('Y-m-d'));
?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('bulk_attendance'); ?></h1>
            <p class="text-muted"><?php echo __('record_bulk_attendance'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <form method="POST" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
        <div class="row mb-3">
            <div class="col-md-4">
                <label for="class_id" class="form-label"><?php echo __('class'); ?></label>
                <select class="form-select" id="class_id" name="class_id" required>
                    <option value=""><?php echo __('select_class'); ?></option>
                    <?php while ($class = $classes->fetch_assoc()): ?>
                        <option value="<?php echo $class['id']; ?>" <?php echo ($selected_class_id == $class['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($class['class_name']) . ' - ' . htmlspecialchars($class['grade_level']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="subject_id" class="form-label"><?php echo __('subject'); ?></label>
                <select class="form-select" id="subject_id" name="subject_id">
                    <option value="0"><?php echo __('general'); ?></option>
                    <?php while ($subject = $subjects->fetch_assoc()): ?>
                        <option value="<?php echo $subject['id']; ?>" <?php echo ($selected_subject_id == $subject['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="attendance_date" class="form-label"><?php echo __('date'); ?></label>
                <input type="date" class="form-control" id="attendance_date" name="attendance_date" value="<?php echo $selected_date; ?>" required>
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-4">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status" required>
                    <option value="present"><?php echo __('present'); ?></option>
                    <option value="absent"><?php echo __('absent'); ?></option>
                    <option value="late"><?php echo __('late'); ?></option>
                    <option value="excused"><?php echo __('excused'); ?></option>
                </select>
            </div>
            <div class="col-md-8">
                <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                <input type="text" class="form-control" id="notes" name="notes" placeholder="<?php echo __('optional_notes'); ?>">
            </div>
        </div>
        <div class="d-grid">
            <button type="submit" name="save_bulk_attendance" class="btn btn-success">
                <i class="fas fa-users me-2"></i><?php echo __('save_bulk_attendance'); ?>
            </button>
        </div>
    </form>
</div>
<?php require_once '../includes/footer.php'; ?> 