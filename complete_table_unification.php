<?php
/**
 * إكمال عملية توحيد الجداول بشكل صحيح
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

echo "<h1>🔧 إكمال عملية توحيد الجداول</h1>";

echo "<div class='alert alert-warning'>";
echo "<h4>🎯 المشكلة المكتشفة:</h4>";
echo "<p>عملية توحيد الجداول لم تكتمل بشكل صحيح، مما يسبب تضارب في المفاتيح الخارجية</p>";
echo "</div>";

try {
    if ($_POST['action'] ?? '' === 'complete_unification') {
        echo "<h3>🚀 بدء إكمال عملية التوحيد:</h3>";
        
        // الخطوة 1: تعطيل فحص المفاتيح الخارجية
        echo "<h4>1️⃣ تعطيل فحص المفاتيح الخارجية:</h4>";
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");
        echo "<p>✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً</p>";
        
        // الخطوة 2: نقل البيانات من الجداول القديمة إلى الجدول الموحد
        echo "<h4>2️⃣ نقل البيانات إلى الجدول الموحد:</h4>";
        
        // نقل بيانات teacher_attendance
        $teacher_migration = $conn->query("
            INSERT IGNORE INTO staff_attendance 
            (user_id, attendance_date, status, check_in_time, check_out_time, notes, created_at, updated_at)
            SELECT 
                t.user_id,
                ta.attendance_date,
                ta.status,
                ta.check_in_time,
                ta.check_out_time,
                ta.notes,
                ta.created_at,
                ta.updated_at
            FROM teacher_attendance ta
            JOIN teachers t ON ta.teacher_id = t.id
            WHERE t.user_id IS NOT NULL
        ");
        
        if ($teacher_migration) {
            $teacher_rows = $conn->affected_rows;
            echo "<p>✅ تم نقل $teacher_rows سجل من teacher_attendance</p>";
        } else {
            echo "<p>⚠️ لم يتم نقل بيانات من teacher_attendance: " . $conn->error . "</p>";
        }
        
        // نقل بيانات admin_attendance
        $admin_migration = $conn->query("
            INSERT IGNORE INTO staff_attendance 
            (user_id, attendance_date, status, check_in_time, check_out_time, notes, created_at, updated_at)
            SELECT 
                aa.admin_id as user_id,
                aa.attendance_date,
                aa.status,
                aa.check_in_time,
                aa.check_out_time,
                aa.notes,
                aa.created_at,
                aa.updated_at
            FROM admin_attendance aa
            WHERE aa.admin_id IS NOT NULL
        ");
        
        if ($admin_migration) {
            $admin_rows = $conn->affected_rows;
            echo "<p>✅ تم نقل $admin_rows سجل من admin_attendance</p>";
        } else {
            echo "<p>⚠️ لم يتم نقل بيانات من admin_attendance: " . $conn->error . "</p>";
        }
        
        // الخطوة 3: إنشاء فصول افتراضية إذا لم تكن موجودة
        echo "<h4>3️⃣ التأكد من وجود الفصول:</h4>";
        
        $classes_count = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
        if ($classes_count == 0) {
            $default_classes = [
                "INSERT INTO classes (id, name, grade, section, status, created_at) VALUES (1, 'الصف الأول الابتدائي', 1, 'أ', 'active', NOW())",
                "INSERT INTO classes (id, name, grade, section, status, created_at) VALUES (2, 'الصف الثاني الابتدائي', 2, 'أ', 'active', NOW())",
                "INSERT INTO classes (id, name, grade, section, status, created_at) VALUES (3, 'الصف الثالث الابتدائي', 3, 'أ', 'active', NOW())",
                "INSERT INTO classes (id, name, grade, section, status, created_at) VALUES (4, 'الصف الرابع الابتدائي', 4, 'أ', 'active', NOW())",
                "INSERT INTO classes (id, name, grade, section, status, created_at) VALUES (5, 'الصف الخامس الابتدائي', 5, 'أ', 'active', NOW())",
                "INSERT INTO classes (id, name, grade, section, status, created_at) VALUES (6, 'الصف السادس الابتدائي', 6, 'أ', 'active', NOW())"
            ];
            
            foreach ($default_classes as $query) {
                $conn->query($query);
            }
            echo "<p>✅ تم إنشاء 6 فصول افتراضية</p>";
        } else {
            echo "<p>✅ يوجد $classes_count فصل في النظام</p>";
        }
        
        // الخطوة 4: إصلاح بيانات الطلاب
        echo "<h4>4️⃣ إصلاح بيانات الطلاب:</h4>";
        
        // تعيين الطلاب بدون فصول إلى الفصل الأول
        $fix_students = $conn->query("
            UPDATE students 
            SET class_id = 1 
            WHERE class_id IS NULL 
            OR class_id NOT IN (SELECT id FROM classes)
        ");
        
        if ($fix_students) {
            $fixed_students = $conn->affected_rows;
            echo "<p>✅ تم إصلاح $fixed_students طالب</p>";
        }
        
        // الخطوة 5: حذف المفاتيح الخارجية القديمة المتضاربة
        echo "<h4>5️⃣ تنظيف المفاتيح الخارجية القديمة:</h4>";
        
        $drop_constraints = [
            "ALTER TABLE students DROP FOREIGN KEY IF EXISTS students_ibfk_1",
            "ALTER TABLE students DROP FOREIGN KEY IF EXISTS students_ibfk_2",
            "ALTER TABLE students DROP FOREIGN KEY IF EXISTS fk_students_users",
            "ALTER TABLE students DROP FOREIGN KEY IF EXISTS fk_students_classes"
        ];
        
        foreach ($drop_constraints as $query) {
            $conn->query($query);
        }
        echo "<p>✅ تم حذف المفاتيح الخارجية القديمة</p>";
        
        // الخطوة 6: إعادة إنشاء المفاتيح الخارجية الصحيحة
        echo "<h4>6️⃣ إعادة إنشاء المفاتيح الخارجية:</h4>";
        
        $create_constraints = [
            "ALTER TABLE students ADD CONSTRAINT fk_students_users FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE",
            "ALTER TABLE students ADD CONSTRAINT fk_students_classes FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL ON UPDATE CASCADE"
        ];
        
        foreach ($create_constraints as $query) {
            if ($conn->query($query)) {
                echo "<p>✅ تم إنشاء مفتاح خارجي</p>";
            } else {
                echo "<p>⚠️ تحذير: " . $conn->error . "</p>";
            }
        }
        
        // الخطوة 7: إعادة تفعيل فحص المفاتيح الخارجية
        echo "<h4>7️⃣ إعادة تفعيل فحص المفاتيح الخارجية:</h4>";
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        echo "<p>✅ تم إعادة تفعيل فحص المفاتيح الخارجية</p>";
        
        // الخطوة 8: اختبار نهائي
        echo "<h4>8️⃣ اختبار نهائي:</h4>";
        
        // محاولة إدراج طالب تجريبي
        $test_insert = $conn->prepare("
            INSERT INTO students (student_id, class_id, full_name, status, created_at) 
            VALUES ('TEST_UNIFY_001', 1, 'طالب اختبار التوحيد', 'active', NOW())
        ");
        
        if ($test_insert->execute()) {
            echo "<p>✅ اختبار الإدراج نجح - المشكلة محلولة!</p>";
            
            // حذف الطالب التجريبي
            $conn->query("DELETE FROM students WHERE student_id = 'TEST_UNIFY_001'");
            echo "<p>ℹ️ تم حذف البيانات التجريبية</p>";
            
            echo "<div class='alert alert-success mt-4'>";
            echo "<h4>🎉 تم إكمال التوحيد بنجاح!</h4>";
            echo "<p>المشكلة محلولة نهائياً. يمكنك الآن:</p>";
            echo "<ul>";
            echo "<li>إضافة وتعديل الطلاب بدون مشاكل</li>";
            echo "<li>استخدام النظام الموحد بشكل طبيعي</li>";
            echo "<li>الاستفادة من جميع المزايا الجديدة</li>";
            echo "</ul>";
            echo "</div>";
            
        } else {
            echo "<p>❌ ما زالت هناك مشكلة: " . $conn->error . "</p>";
        }
        
        // الخطوة 9: تنظيف اختياري للجداول القديمة
        echo "<h4>9️⃣ تنظيف الجداول القديمة (اختياري):</h4>";
        
        echo "<div class='alert alert-warning'>";
        echo "<h5>⚠️ تحذير مهم:</h5>";
        echo "<p>يمكنك الآن حذف الجداول القديمة بأمان، لكن تأكد من عمل نسخة احتياطية أولاً</p>";
        echo "<form method='POST' class='mt-3'>";
        echo "<input type='hidden' name='action' value='cleanup_old_tables'>";
        echo "<button type='submit' class='btn btn-warning' onclick='return confirm(\"هل أنت متأكد من حذف الجداول القديمة؟\\n\\nتأكد من عمل نسخة احتياطية أولاً!\")'>🗑️ حذف الجداول القديمة</button>";
        echo "</form>";
        echo "</div>";
        
    } elseif ($_POST['action'] ?? '' === 'cleanup_old_tables') {
        echo "<h3>🗑️ حذف الجداول القديمة:</h3>";
        
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");
        
        $old_tables = ['teacher_attendance', 'admin_attendance'];
        foreach ($old_tables as $table) {
            if ($conn->query("DROP TABLE IF EXISTS $table")) {
                echo "<p>✅ تم حذف جدول $table</p>";
            } else {
                echo "<p>❌ فشل في حذف جدول $table</p>";
            }
        }
        
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        
        echo "<div class='alert alert-success'>";
        echo "<h4>🎉 تم التنظيف بنجاح!</h4>";
        echo "<p>النظام الآن يستخدم الجداول الموحدة فقط</p>";
        echo "</div>";
        
    } else {
        // عرض معلومات المشكلة والحل
        echo "<h3>📋 ملخص المشكلة:</h3>";
        
        echo "<div class='row'>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card border-danger'>";
        echo "<div class='card-header bg-danger text-white'>";
        echo "<h6>❌ المشاكل الحالية:</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<ul>";
        echo "<li>الجداول القديمة ما زالت موجودة</li>";
        echo "<li>البيانات مبعثرة بين الجداول القديمة والجديدة</li>";
        echo "<li>مفاتيح خارجية متضاربة</li>";
        echo "<li>مشكلة في إضافة/تعديل الطلاب</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card border-success'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h6>✅ الحل الشامل:</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<ul>";
        echo "<li>نقل جميع البيانات للجدول الموحد</li>";
        echo "<li>إصلاح المفاتيح الخارجية</li>";
        echo "<li>تنظيف البيانات المتضاربة</li>";
        echo "<li>اختبار شامل للتأكد من الحل</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "</div>";
        
        echo "<div class='text-center mt-4'>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='action' value='complete_unification'>";
        echo "<button type='submit' class='btn btn-primary btn-lg' onclick='return confirm(\"هل تريد إكمال عملية توحيد الجداول؟\\n\\nهذا سيحل مشكلة المفاتيح الخارجية نهائياً.\")'>🔧 إكمال التوحيد وحل المشكلة</button>";
        echo "</form>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ في العملية:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
    
    // التأكد من إعادة تفعيل المفاتيح الخارجية
    try {
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
    } catch (Exception $e2) {
        echo "<p>⚠️ تحذير: قد تحتاج لإعادة تفعيل المفاتيح الخارجية يدوياً</p>";
    }
}

echo "<hr>";
echo "<div class='alert alert-info'>";
echo "<h4>📋 ما سيحدث عند الإكمال:</h4>";
echo "<ol>";
echo "<li><strong>نقل البيانات:</strong> من الجداول القديمة إلى الجدول الموحد</li>";
echo "<li><strong>إصلاح المفاتيح:</strong> حذف القديمة وإنشاء جديدة صحيحة</li>";
echo "<li><strong>تنظيف البيانات:</strong> إصلاح أي تضارب في البيانات</li>";
echo "<li><strong>اختبار شامل:</strong> للتأكد من حل المشكلة نهائياً</li>";
echo "</ol>";
echo "</div>";

// حذف الملف بعد 5 دقائق
echo "<script>
setTimeout(function() {
    if (confirm('هل تريد حذف ملف إكمال التوحيد؟')) {
        fetch('cleanup_complete_unification.php');
    }
}, 300000);
</script>";
?>
