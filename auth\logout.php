<?php
/**
 * صفحة تسجيل الخروج
 * Logout Page
 */

define('SYSTEM_INIT', true);
require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

session_start();

// تسجيل النشاط إذا كان المستخدم مسجل دخوله
if (is_logged_in()) {
    $user_id = $_SESSION['user_id'];
    
    // تسجيل نشاط تسجيل الخروج
    log_user_activity($user_id, 'logout', 'User logged out');
    
    // تسجيل محاولة تسجيل خروج ناجحة
    $ip_address = get_client_ip();
    security()->logLoginAttempt($_SESSION['email'] ?? '', $ip_address, $_SERVER['HTTP_USER_AGENT'] ?? '', true, 'Logout');
}

// حذف كوكيز "تذكرني"
if (isset($_COOKIE['remember_token'])) {
    // حذف الرمز من قاعدة البيانات
    if (isset($conn)) {
        $stmt = $conn->prepare("DELETE FROM remember_tokens WHERE token = ?");
        $stmt->bind_param("s", $_COOKIE['remember_token']);
        $stmt->execute();
    }
    
    // حذف الكوكيز
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
}

// تدمير الجلسة
session_destroy();

// حذف كوكيز الجلسة
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?logout=1');
exit();
?>
