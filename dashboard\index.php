<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة تغيير اللغة من URL
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $_GET['lang'];
}

$page_title = __('dashboard');
require_once '../includes/header.php';

// التحقق من تسجيل الدخول
check_session();

// الحصول على الإحصائيات السريعة
$stats = get_quick_stats();

// الحصول على معلومات المستخدم الحالي
$user_role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];

// إحصائيات خاصة بكل نوع مستخدم
$user_stats = [];

switch ($user_role) {
    case 'admin':
        // إحصائيات المدير
        $user_stats = [
            'total_users' => $stats['students'] + $stats['teachers'],
            'educational_stages' => $stats['stages'],
            'school_grades' => $stats['grades'],
            'active_classes' => $stats['classes'],
            'total_subjects' => $stats['subjects'],
            'active_exams' => $stats['active_exams']
        ];
        break;
        
    case 'teacher':
        // إحصائيات المعلم
        global $conn;
        
        // الحصول على معرف المعلم
        $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
        $teacher_stmt->bind_param("i", $user_id);
        $teacher_stmt->execute();
        $teacher_result = $teacher_stmt->get_result();
        $teacher_data = $teacher_result->fetch_assoc();
        $teacher_id = $teacher_data['id'] ?? 0;
        
        // عدد الفصول التي يدرسها
        $classes_stmt = $conn->prepare("SELECT COUNT(DISTINCT class_id) as count FROM teacher_assignments WHERE teacher_id = ? AND status = 'active'");
        $classes_stmt->bind_param("i", $teacher_id);
        $classes_stmt->execute();
        $my_classes = $classes_stmt->get_result()->fetch_assoc()['count'] ?? 0;
        
        // عدد المواد التي يدرسها
        $subjects_stmt = $conn->prepare("SELECT COUNT(DISTINCT subject_id) as count FROM teacher_assignments WHERE teacher_id = ? AND status = 'active'");
        $subjects_stmt->bind_param("i", $teacher_id);
        $subjects_stmt->execute();
        $my_subjects = $subjects_stmt->get_result()->fetch_assoc()['count'] ?? 0;
        
        // عدد الطلاب
        $students_stmt = $conn->prepare("
            SELECT COUNT(DISTINCT s.id) as count 
            FROM students s 
            JOIN teacher_assignments ta ON s.class_id = ta.class_id 
            WHERE ta.teacher_id = ? AND ta.status = 'active'
        ");
        $students_stmt->bind_param("i", $teacher_id);
        $students_stmt->execute();
        $my_students = $students_stmt->get_result()->fetch_assoc()['count'] ?? 0;
        
        // عدد الامتحانات
        $exams_stmt = $conn->prepare("SELECT COUNT(*) as count FROM exams WHERE teacher_id = ?");
        $exams_stmt->bind_param("i", $teacher_id);
        $exams_stmt->execute();
        $my_exams = $exams_stmt->get_result()->fetch_assoc()['count'] ?? 0;
        
        $user_stats = [
            'my_classes' => $my_classes,
            'my_subjects' => $my_subjects,
            'my_students' => $my_students,
            'my_exams' => $my_exams
        ];
        break;
        
    case 'student':
        // إحصائيات الطالب
        global $conn;
        
        // الحصول على معرف الطالب
        $student_stmt = $conn->prepare("SELECT id, class_id FROM students WHERE user_id = ?");
        $student_stmt->bind_param("i", $user_id);
        $student_stmt->execute();
        $student_result = $student_stmt->get_result();
        $student_data = $student_result->fetch_assoc();
        $student_id = $student_data['id'] ?? 0;
        $class_id = $student_data['class_id'] ?? 0;
        
        // عدد المواد
        $subjects_stmt = $conn->prepare("
            SELECT COUNT(DISTINCT ta.subject_id) as count 
            FROM teacher_assignments ta 
            WHERE ta.class_id = ? AND ta.status = 'active'
        ");
        $subjects_stmt->bind_param("i", $class_id);
        $subjects_stmt->execute();
        $my_subjects = $subjects_stmt->get_result()->fetch_assoc()['count'] ?? 0;
        
        // عدد الامتحانات المتاحة
        $exams_stmt = $conn->prepare("
            SELECT COUNT(*) as count 
            FROM exams e 
            WHERE e.class_id = ? AND e.status IN ('published', 'active')
        ");
        $exams_stmt->bind_param("i", $class_id);
        $exams_stmt->execute();
        $available_exams = $exams_stmt->get_result()->fetch_assoc()['count'] ?? 0;
        
        // متوسط الدرجات - تم إزالة هذه الميزة
        $avg_grade = 0;
        
        // معدل الحضور
        $attendance_stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_days,
                SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days
            FROM attendance 
            WHERE student_id = ?
        ");
        $attendance_stmt->bind_param("i", $student_id);
        $attendance_stmt->execute();
        $attendance_data = $attendance_stmt->get_result()->fetch_assoc();
        $attendance_rate = $attendance_data['total_days'] > 0 ? 
            ($attendance_data['present_days'] / $attendance_data['total_days']) * 100 : 0;
        
        $user_stats = [
            'my_subjects' => $my_subjects,
            'available_exams' => $available_exams,
            'avg_grade' => round($avg_grade, 1),
            'attendance_rate' => round($attendance_rate, 1)
        ];
        break;
}

// الحصول على الأنشطة الأخيرة
$recent_activities = [];
global $conn;
$activities_stmt = $conn->prepare("
    SELECT al.*, u.full_name 
    FROM activity_logs al 
    LEFT JOIN users u ON al.user_id = u.id 
    ORDER BY al.created_at DESC 
    LIMIT 10
");
$activities_stmt->execute();
$recent_activities = $activities_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('dashboard'); ?></h1>
            <p class="text-muted"><?php echo __('welcome_back') . ', ' . htmlspecialchars($_SESSION['full_name']); ?></p>
        </div>
        <div>
            <span class="badge bg-primary"><?php echo __(strtolower($user_role)); ?></span>
            <span class="text-muted ms-2"><?php echo format_datetime(date('Y-m-d H:i:s'), 'Y/m/d H:i'); ?></span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <?php if ($user_role === 'admin'): ?>
            <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($stats['students']); ?></h3>
                            <p class="mb-0 small"><?php echo __('students'); ?></p>
                        </div>
                        <div class="fs-2">
                            <i class="fas fa-user-graduate text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($stats['teachers']); ?></h3>
                            <p class="mb-0 small"><?php echo __('teachers'); ?></p>
                        </div>
                        <div class="fs-2">
                            <i class="fas fa-chalkboard-teacher text-success"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($stats['stages']); ?></h3>
                            <p class="mb-0 small"><?php echo __('stages'); ?></p>
                        </div>
                        <div class="fs-2">
                            <i class="fas fa-layer-group text-info"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($stats['grades']); ?></h3>
                            <p class="mb-0 small"><?php echo __('school_grades'); ?></p>
                        </div>
                        <div class="fs-2">
                            <i class="fas fa-graduation-cap text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($stats['classes']); ?></h3>
                            <p class="mb-0 small"><?php echo __('classes'); ?></p>
                        </div>
                        <div class="fs-2">
                            <i class="fas fa-school text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($stats['subjects']); ?></h3>
                            <p class="mb-0 small"><?php echo __('subjects'); ?></p>
                        </div>
                        <div class="fs-2">
                            <i class="fas fa-book text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        <?php elseif ($user_role === 'teacher'): ?>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($user_stats['my_classes']); ?></h3>
                            <p class="mb-0"><?php echo __('my_classes'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-school"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($user_stats['my_subjects']); ?></h3>
                            <p class="mb-0"><?php echo __('my_subjects'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-book"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($user_stats['my_students']); ?></h3>
                            <p class="mb-0"><?php echo __('my_students'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($user_stats['my_exams']); ?></h3>
                            <p class="mb-0"><?php echo __('my_exams'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        <?php elseif ($user_role === 'student'): ?>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($user_stats['my_subjects']); ?></h3>
                            <p class="mb-0"><?php echo __('my_subjects'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-book"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo number_format($user_stats['available_exams']); ?></h3>
                            <p class="mb-0"><?php echo __('available_exams'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo $user_stats['avg_grade']; ?>%</h3>
                            <p class="mb-0"><?php echo __('average_grade'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo $user_stats['attendance_rate']; ?>%</h3>
                            <p class="mb-0"><?php echo __('attendance_rate'); ?></p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i><?php echo __('quick_actions'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php if ($user_role === 'admin'): ?>
                            <div class="col-md-3 mb-3">
                                <a href="../students/add.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-plus d-block mb-2 fs-4"></i>
                                    <?php echo __('add_student'); ?>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../teachers/add.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-chalkboard-teacher d-block mb-2 fs-4"></i>
                                    <?php echo __('add_teacher'); ?>
                                </a>
                            </div>
                            <!-- 1. المراحل الدراسية -->
                            <div class="col-md-3 mb-3">
                                <a href="../stages/" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-layer-group d-block mb-2 fs-4"></i>
                                    <?php echo __('educational_stages'); ?>
                                </a>
                            </div>
                            <!-- 2. الصفوف الدراسية -->
                            <div class="col-md-3 mb-3">
                                <a href="../school_grades/" class="btn btn-outline-success w-100">
                                    <i class="fas fa-graduation-cap d-block mb-2 fs-4"></i>
                                    <?php echo __('school_grades'); ?>
                                </a>
                            </div>
                            <!-- 3. الفصول -->
                            <div class="col-md-3 mb-3">
                                <a href="../classes/" class="btn btn-outline-info w-100">
                                    <i class="fas fa-school d-block mb-2 fs-4"></i>
                                    <?php echo __('classes'); ?>
                                </a>
                            </div>
                            <!-- 4. المواد -->
                            <div class="col-md-3 mb-3">
                                <a href="../subjects/" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-book d-block mb-2 fs-4"></i>
                                    <?php echo __('subjects'); ?>
                                </a>
                            </div>
                            <!-- 5. المصروفات اليومية -->
                            <div class="col-md-3 mb-3">
                                <a href="../finance/expenses/" class="btn btn-outline-danger w-100">
                                    <i class="fas fa-money-bill-wave d-block mb-2 fs-4"></i>
                                    المصروفات اليومية
                                    <small class="d-block text-muted">جديد</small>
                                </a>
                            </div>
                            <!-- 6. النظام المالي -->
                            <div class="col-md-3 mb-3">
                                <a href="../finance/" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-chart-pie d-block mb-2 fs-4"></i>
                                    النظام المالي
                                </a>
                            </div>
                        <?php elseif ($user_role === 'teacher'): ?>
                            <div class="col-md-3 mb-3">
                                <a href="../exams/add.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-plus d-block mb-2 fs-4"></i>
                                    <?php echo __('create_exam'); ?>
                                </a>
                            </div>

                            <div class="col-md-3 mb-3">
                                <a href="../attendance/" class="btn btn-outline-info w-100">
                                    <i class="fas fa-calendar-check d-block mb-2 fs-4"></i>
                                    <?php echo __('take_attendance'); ?>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../reports/" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-chart-bar d-block mb-2 fs-4"></i>
                                    <?php echo __('view_reports'); ?>
                                </a>
                            </div>
                        <?php elseif ($user_role === 'student'): ?>
                            <div class="col-md-3 mb-3">
                                <a href="../exams/" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-file-alt d-block mb-2 fs-4"></i>
                                    <?php echo __('take_exams'); ?>
                                </a>
                            </div>

                            <div class="col-md-3 mb-3">
                                <a href="../attendance/" class="btn btn-outline-info w-100">
                                    <i class="fas fa-calendar-check d-block mb-2 fs-4"></i>
                                    <?php echo __('view_attendance'); ?>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../profile/" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-user d-block mb-2 fs-4"></i>
                                    <?php echo __('edit_profile'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i><?php echo __('recent_activities'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_activities)): ?>
                        <p class="text-muted text-center"><?php echo __('no_recent_activities'); ?></p>
                    <?php else: ?>
                        <div class="activity-list">
                            <?php foreach (array_slice($recent_activities, 0, 5) as $activity): ?>
                                <div class="activity-item d-flex align-items-start mb-3">
                                    <div class="activity-icon me-3">
                                        <i class="fas fa-circle text-primary" style="font-size: 8px;"></i>
                                    </div>
                                    <div class="activity-content flex-grow-1">
                                        <p class="mb-1">
                                            <strong><?php echo htmlspecialchars($activity['full_name'] ?? __('system')); ?></strong>
                                            <?php echo htmlspecialchars($activity['action']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo format_datetime($activity['created_at'], 'Y/m/d H:i'); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center">
                            <a href="../activities/" class="btn btn-sm btn-outline-primary">
                                <?php echo __('view_all_activities'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i><?php echo __('statistics_overview'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="overviewChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i><?php echo __('monthly_progress'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="progressChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Overview Chart
    const overviewCtx = document.getElementById('overviewChart').getContext('2d');
    const overviewChart = new Chart(overviewCtx, {
        type: 'doughnut',
        data: {
            labels: ['<?php echo __('students'); ?>', '<?php echo __('teachers'); ?>', '<?php echo __('classes'); ?>', '<?php echo __('subjects'); ?>'],
            datasets: [{
                data: [<?php echo $stats['students']; ?>, <?php echo $stats['teachers']; ?>, <?php echo $stats['classes']; ?>, <?php echo $stats['subjects']; ?>],
                backgroundColor: [
                    'rgba(102, 126, 234, 0.8)',
                    'rgba(118, 75, 162, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Progress Chart
    const progressCtx = document.getElementById('progressChart').getContext('2d');
    const progressChart = new Chart(progressCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: '<?php echo __('students'); ?>',
                data: [65, 70, 75, 80, 85, <?php echo $stats['students']; ?>],
                borderColor: 'rgba(102, 126, 234, 1)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
            }, {
                label: '<?php echo __('teachers'); ?>',
                data: [10, 12, 15, 18, 20, <?php echo $stats['teachers']; ?>],
                borderColor: 'rgba(118, 75, 162, 1)',
                backgroundColor: 'rgba(118, 75, 162, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>

<?php require_once '../includes/footer.php'; ?>
