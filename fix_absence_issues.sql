-- إصلاح مشاكل النظام الموحد للغياب
-- Fix unified absence system issues

USE school_management;

-- 1. إضافة المستخدمين المفقودين
INSERT IGNORE INTO users (id, username, email, password, full_name, role, status) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 'active'),
(3, 'staff1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف إداري', 'staff', 'active');

-- تحديث المستخدم الموجود
UPDATE users SET full_name = 'معلم أول' WHERE id = 2;

-- 2. تنظيف البيانات المكررة في unified_staff_absences
-- حذف السجلات المكررة (الاحتفاظ بالأحدث)
DELETE t1 FROM unified_staff_absences t1
INNER JOIN unified_staff_absences t2 
WHERE t1.id < t2.id 
AND t1.user_id = t2.user_id 
AND t1.absence_date = t2.absence_date;

-- 3. إصلاح القيود الخارجية
SET FOREIGN_KEY_CHECKS = 0;

-- تنظيف البيانات غير الصحيحة
UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);
UPDATE unified_staff_absences SET applied_by = 1 WHERE applied_by NOT IN (SELECT id FROM users);

SET FOREIGN_KEY_CHECKS = 1;

-- 4. تحديث الإجراء المخزن لتجنب التكرار
DROP PROCEDURE IF EXISTS record_unified_absence;

DELIMITER //

CREATE PROCEDURE record_unified_absence(
    IN p_user_id INT,
    IN p_absence_date DATE,
    IN p_end_date DATE,
    IN p_absence_type VARCHAR(50),
    IN p_absence_category VARCHAR(20),
    IN p_days_count INT,
    IN p_reason TEXT,
    IN p_has_deduction TINYINT,
    IN p_applied_by INT
)
BEGIN
    DECLARE v_deduction_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_deduction_type VARCHAR(20) DEFAULT 'daily_rate';
    DECLARE v_absence_id INT DEFAULT NULL;
    DECLARE v_existing_count INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود تسجيل مسبق
    SELECT COUNT(*) INTO v_existing_count
    FROM unified_staff_absences 
    WHERE user_id = p_user_id AND absence_date = p_absence_date;
    
    IF v_existing_count > 0 THEN
        -- تحديث السجل الموجود بدلاً من إنشاء جديد
        UPDATE unified_staff_absences 
        SET absence_type = p_absence_type,
            absence_category = p_absence_category,
            days_count = p_days_count,
            reason = p_reason,
            has_deduction = p_has_deduction,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = p_user_id AND absence_date = p_absence_date;
        
        SELECT id INTO v_absence_id 
        FROM unified_staff_absences 
        WHERE user_id = p_user_id AND absence_date = p_absence_date;
        
        SELECT v_absence_id as absence_id, 'تم تحديث السجل الموجود بنجاح' as message;
    ELSE
        -- حساب مبلغ الخصم إذا كان مطلوباً
        IF p_has_deduction = 1 THEN
            SELECT ds.deduction_value, ds.deduction_type 
            INTO v_deduction_amount, v_deduction_type
            FROM deduction_settings ds 
            WHERE ds.absence_type = p_absence_type AND ds.is_active = 1 
            LIMIT 1;
            
            IF v_deduction_type = 'daily_rate' THEN
                SET v_deduction_amount = v_deduction_amount * p_days_count;
            END IF;
        END IF;
        
        -- إدراج سجل جديد
        INSERT INTO unified_staff_absences (
            user_id, absence_date, end_date, absence_type, absence_category,
            days_count, reason, has_deduction, deduction_type, deduction_amount,
            status, applied_by
        ) VALUES (
            p_user_id, p_absence_date, p_end_date, p_absence_type, p_absence_category,
            p_days_count, p_reason, p_has_deduction, v_deduction_type, v_deduction_amount,
            'pending', p_applied_by
        );
        
        SET v_absence_id = LAST_INSERT_ID();
        
        SELECT v_absence_id as absence_id, 'تم تسجيل الغياب/الإجازة بنجاح' as message;
    END IF;
    
    -- تحديث/إدراج في جدول الحضور
    INSERT INTO staff_attendance (
        user_id, attendance_date, status, absence_id, notes, recorded_by
    ) VALUES (
        p_user_id, p_absence_date, 
        CASE WHEN p_has_deduction = 1 THEN 'absent' ELSE p_absence_type END,
        v_absence_id, p_reason, p_applied_by
    )
    ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        absence_id = VALUES(absence_id),
        notes = VALUES(notes),
        check_in_time = CASE WHEN p_has_deduction = 1 THEN NULL ELSE check_in_time END,
        check_out_time = CASE WHEN p_has_deduction = 1 THEN NULL ELSE check_out_time END,
        updated_at = CURRENT_TIMESTAMP;
    
    COMMIT;
END//

DELIMITER ;

-- 5. تحديث إجراء الموافقة ليتعامل مع المستخدمين الموجودين
DROP PROCEDURE IF EXISTS approve_absence;

DELIMITER //

CREATE PROCEDURE approve_absence(
    IN p_absence_id INT,
    IN p_approved_by INT,
    IN p_notes TEXT
)
BEGIN
    DECLARE v_user_exists INT DEFAULT 0;
    DECLARE v_final_approved_by INT DEFAULT 1;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المُوافق
    SELECT COUNT(*) INTO v_user_exists FROM users WHERE id = p_approved_by;
    
    IF v_user_exists > 0 THEN
        SET v_final_approved_by = p_approved_by;
    ELSE
        SET v_final_approved_by = 1; -- استخدام admin كافتراضي
    END IF;
    
    -- تحديث حالة الغياب
    UPDATE unified_staff_absences 
    SET status = 'approved',
        approved_by = v_final_approved_by,
        approved_at = CURRENT_TIMESTAMP,
        manager_notes = p_notes
    WHERE id = p_absence_id;
    
    -- تحديث سجل الحضور
    UPDATE staff_attendance sa
    JOIN unified_staff_absences usa ON sa.absence_id = usa.id
    SET sa.status = CASE 
            WHEN usa.has_deduction = 1 THEN 'absent'
            ELSE usa.absence_type
        END,
        sa.check_in_time = CASE WHEN usa.has_deduction = 1 THEN NULL ELSE sa.check_in_time END,
        sa.check_out_time = CASE WHEN usa.has_deduction = 1 THEN NULL ELSE sa.check_out_time END
    WHERE usa.id = p_absence_id;
    
    COMMIT;
    
    SELECT 'تم اعتماد الغياب/الإجازة بنجاح' as message;
END//

DELIMITER ;

-- 6. إنشاء إجراء لتنظيف البيانات المكررة
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS clean_duplicate_absences()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_user_id INT;
    DECLARE v_absence_date DATE;
    DECLARE v_count INT;
    
    DECLARE duplicate_cursor CURSOR FOR
        SELECT user_id, absence_date, COUNT(*) as count
        FROM unified_staff_absences
        GROUP BY user_id, absence_date
        HAVING COUNT(*) > 1;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN duplicate_cursor;
    
    read_loop: LOOP
        FETCH duplicate_cursor INTO v_user_id, v_absence_date, v_count;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- حذف السجلات المكررة (الاحتفاظ بالأحدث)
        DELETE FROM unified_staff_absences 
        WHERE user_id = v_user_id 
        AND absence_date = v_absence_date 
        AND id NOT IN (
            SELECT * FROM (
                SELECT MAX(id) 
                FROM unified_staff_absences 
                WHERE user_id = v_user_id AND absence_date = v_absence_date
            ) as temp
        );
        
    END LOOP;
    
    CLOSE duplicate_cursor;
    
    SELECT 'تم تنظيف البيانات المكررة بنجاح' as message;
END//

DELIMITER ;

-- 7. تشغيل تنظيف البيانات المكررة
CALL clean_duplicate_absences();

-- 8. إضافة فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_unified_absences_user_status ON unified_staff_absences(user_id, status);
CREATE INDEX IF NOT EXISTS idx_unified_absences_date_status ON unified_staff_absences(absence_date, status);
CREATE INDEX IF NOT EXISTS idx_unified_absences_type_category ON unified_staff_absences(absence_type, absence_category);

-- 9. تحديث البيانات الموجودة لضمان الاتساق
UPDATE unified_staff_absences 
SET applied_by = 1 
WHERE applied_by IS NULL OR applied_by NOT IN (SELECT id FROM users);

UPDATE unified_staff_absences 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

-- 10. إنشاء view محسنة للاختبار
CREATE OR REPLACE VIEW test_absence_view AS
SELECT 
    usa.id,
    usa.user_id,
    u.full_name as user_name,
    usa.absence_date,
    usa.absence_type,
    usa.absence_category,
    usa.days_count,
    usa.has_deduction,
    usa.deduction_amount,
    usa.status,
    usa.reason,
    applier.full_name as applied_by_name,
    approver.full_name as approved_by_name
FROM unified_staff_absences usa
LEFT JOIN users u ON usa.user_id = u.id
LEFT JOIN users applier ON usa.applied_by = applier.id
LEFT JOIN users approver ON usa.approved_by = approver.id
ORDER BY usa.absence_date DESC, usa.id DESC;

SELECT 'تم إصلاح جميع مشاكل النظام الموحد بنجاح!' as message;
