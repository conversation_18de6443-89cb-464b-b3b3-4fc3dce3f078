<?php
/**
 * صفحة المواد الدراسية المحسنة
 * Enhanced Subjects Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// معالجة الفلاتر
$stage_filter = isset($_GET['stage']) ? intval($_GET['stage']) : 0;
$grade_filter = isset($_GET['grade']) ? intval($_GET['grade']) : 0;
$department_filter = isset($_GET['department']) ? trim($_GET['department']) : '';
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';

// بناء استعلام المواد مع الفلاتر
$where_conditions = [];
$params = [];
$param_types = '';

if ($stage_filter > 0) {
    $where_conditions[] = "s.stage_id = ?";
    $params[] = $stage_filter;
    $param_types .= 'i';

if ($grade_filter > 0) {
    $where_conditions[] = "s.grade_id = ?";
    $params[] = $grade_filter;
    $param_types .= 'i';

if (!empty($department_filter)) {
    $where_conditions[] = "s.department = ?";
    $params[] = $department_filter;
    $param_types .= 's';

if (!empty($status_filter)) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';

if (!empty($search_query)) {
    $where_conditions[] = "(s.subject_name LIKE ? OR s.subject_code LIKE ? OR s.description LIKE ?)";
    $search_param = "%{$search_query}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// استعلام المواد مع المعلومات الإضافية
$subjects_query = "
    SELECT
        s.*,
        es.stage_name,
        g.grade_name,
        COUNT(DISTINCT ta.teacher_id) as teacher_count,
        COUNT(DISTINCT st.id) as student_count
    FROM subjects s
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN teacher_assignments ta ON s.id = ta.subject_id
    LEFT JOIN students st ON s.grade_id = st.class_id
    {$where_clause}
    GROUP BY s.id
    ORDER BY es.sort_order, g.sort_order, s.subject_name
";

$subjects_stmt = $conn->prepare($subjects_query);
if (!empty($params)) {
    $subjects_stmt->bind_param($param_types, ...$params);
}
$subjects_stmt->execute();
$subjects_result = $subjects_stmt->get_result();
$subjects = $subjects_result->fetch_all(MYSQLI_ASSOC);

// جلب الإحصائيات
$stats_query = "
    SELECT
        COUNT(*) as total_subjects,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subjects,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_subjects,
        COUNT(DISTINCT department) as total_departments,
        COUNT(DISTINCT stage_id) as total_stages
    FROM subjects
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// جلب المراحل الدراسية للفلاتر
$stages_query = "SELECT id, stage_name FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = $stages_result->fetch_all(MYSQLI_ASSOC);

// جلب الصفوف الدراسية للفلاتر
$grades_query = "
    SELECT g.id, g.grade_name, g.stage_id
    FROM grades g
    INNER JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order, g.sort_order
";
$grades_result = $conn->query($grades_query);
$grades = $grades_result->fetch_all(MYSQLI_ASSOC);

// جلب الأقسام المتاحة
$departments_query = "SELECT DISTINCT department FROM subjects WHERE department IS NOT NULL AND department != '' ORDER BY department";
$departments_result = $conn->query($departments_query);
$departments = $departments_result->fetch_all(MYSQLI_ASSOC);

// توزيع المواد حسب المراحل
$stage_distribution_query = "
    SELECT
        es.stage_name,
        COUNT(s.id) as subject_count,
        es.id as stage_id
    FROM educational_stages es
    LEFT JOIN subjects s ON es.id = s.stage_id
    WHERE es.status = 'active'
    GROUP BY es.id, es.stage_name
    ORDER BY es.sort_order
";
$stage_distribution_result = $conn->query($stage_distribution_query);
$stage_distribution = $stage_distribution_result->fetch_all(MYSQLI_ASSOC);

// توزيع المواد حسب الأقسام
$department_distribution_query = "
    SELECT
        department,
        COUNT(*) as subject_count
    FROM subjects
    WHERE department IS NOT NULL AND department != ''
    GROUP BY department
    ORDER BY subject_count DESC
";
$department_distribution_result = $conn->query($department_distribution_query);
$department_distribution = $department_distribution_result->fetch_all(MYSQLI_ASSOC);

// التأكد من وجود بيانات للرسوم البيانية
if (empty($stage_distribution)) {
    if (!empty($subjects)) {
        // إنشاء توزيع افتراضي إذا لم تكن هناك مراحل محددة
        $stage_distribution = [
            ['stage_name' => 'مواد بدون مرحلة محددة', 'subject_count' => count($subjects)]
        ];
    } else {
        // بيانات تجريبية للعرض
        $stage_distribution = [
            ['stage_name' => 'المرحلة الابتدائية', 'subject_count' => 8],
            ['stage_name' => 'المرحلة الإعدادية', 'subject_count' => 6],
            ['stage_name' => 'المرحلة الثانوية', 'subject_count' => 10]
        ];
    }
}

if (empty($department_distribution)) {
    if (!empty($subjects)) {
        // إنشاء توزيع افتراضي إذا لم تكن هناك أقسام محددة
        $department_distribution = [
            ['department' => 'مواد بدون قسم محدد', 'subject_count' => count($subjects)]
        ];
    } else {
        // بيانات تجريبية للعرض
        $department_distribution = [
            ['department' => 'رياضيات', 'subject_count' => 5],
            ['department' => 'علوم', 'subject_count' => 4],
            ['department' => 'لغة عربية', 'subject_count' => 3],
            ['department' => 'لغة إنجليزية', 'subject_count' => 3],
            ['department' => 'تاريخ', 'subject_count' => 2],
            ['department' => 'جغرافيا', 'subject_count' => 2],
            ['department' => 'فيزياء', 'subject_count' => 2],
            ['department' => 'كيمياء', 'subject_count' => 2],
            ['department' => 'أحياء', 'subject_count' => 1]
        ];
    }
}

// دالة لتحديد أيقونة ولون المادة حسب القسم
function getSubjectIcon($department, $subject_name) {
    $department = strtolower($department ?? '');
    $subject_name = strtolower($subject_name ?? '');

    // أيقونات حسب القسم
    $department_icons = [
        'رياضيات' => 'fas fa-calculator',
        'علوم' => 'fas fa-flask',
        'لغة عربية' => 'fas fa-book-open',
        'لغة إنجليزية' => 'fas fa-language',
        'تاريخ' => 'fas fa-landmark',
        'جغرافيا' => 'fas fa-globe',
        'فيزياء' => 'fas fa-atom',
        'كيمياء' => 'fas fa-vial',
        'أحياء' => 'fas fa-dna',
        'حاسوب' => 'fas fa-laptop-code',
        'فنون' => 'fas fa-palette',
        'موسيقى' => 'fas fa-music',
        'رياضة' => 'fas fa-running',
        'دين' => 'fas fa-mosque'
    ];

    // البحث في القسم أولاً
    foreach ($department_icons as $dept => $icon) {
        if (strpos($department, $dept) !== false) {
            return $icon;
        }
    }

    // البحث في اسم المادة
    foreach ($department_icons as $dept => $icon) {
        if (strpos($subject_name, $dept) !== false) {
            return $icon;
        }
    }

    return 'fas fa-book'; // أيقونة افتراضية
}

function getSubjectColor($department, $subject_name) {
    $department = strtolower($department ?? '');
    $subject_name = strtolower($subject_name ?? '');

    $department_colors = [
        'رياضيات' => 'primary',
        'علوم' => 'success',
        'لغة عربية' => 'info',
        'لغة إنجليزية' => 'warning',
        'تاريخ' => 'secondary',
        'جغرافيا' => 'success',
        'فيزياء' => 'primary',
        'كيمياء' => 'danger',
        'أحياء' => 'success',
        'حاسوب' => 'dark',
        'فنون' => 'warning',
        'موسيقى' => 'info',
        'رياضة' => 'success',
        'دين' => 'secondary'
    ];

    // البحث في القسم أولاً
    foreach ($department_colors as $dept => $color) {
        if (strpos($department, $dept) !== false) {
            return $color;
        }
    }

    // البحث في اسم المادة
    foreach ($department_colors as $dept => $color) {
        if (strpos($subject_name, $dept) !== false) {
            return $color;
        }
    }

    return 'primary'; // لون افتراضي
}

$page_title = 'المواد الدراسية';
include_once '../includes/header.php';
?>

<!-- إضافة SweetAlert2 للحذف -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-book-open me-2 text-primary"></i>المواد الدراسية
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item active">المواد الدراسية</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مادة
            </a>
            <a href="import.php" class="btn btn-outline-primary">
                <i class="fas fa-upload me-2"></i>استيراد CSV
            </a>
            <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-2"></i>تصدير Excel
            </button>
            <button type="button" class="btn btn-outline-info" onclick="exportToPDF()">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </button>

        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المواد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_subjects']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المواد النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['active_subjects']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الأقسام
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_departments']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المراحل الدراسية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_stages']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نظام الفلترة المتقدم -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>البحث والفلترة
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" id="filterForm">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">البحث في المواد</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($search_query); ?>"
                                   placeholder="اسم المادة أو الكود...">
                        </div>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="stage" class="form-label">المرحلة الدراسية</label>
                        <select class="form-select" id="stage" name="stage" onchange="loadGrades()">
                            <option value="">جميع المراحل</option>
                            <?php foreach ($stages as $stage): ?>
                                <option value="<?php echo $stage['id']; ?>"
                                        <?php echo ($stage_filter == $stage['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($stage['stage_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="grade" class="form-label">الصف الدراسي</label>
                        <select class="form-select" id="grade" name="grade">
                            <option value="">جميع الصفوف</option>
                            <?php foreach ($grades as $grade): ?>
                                <option value="<?php echo $grade['id']; ?>"
                                        data-stage="<?php echo $grade['stage_id']; ?>"
                                        <?php echo ($grade_filter == $grade['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($grade['grade_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="department" class="form-label">القسم</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">جميع الأقسام</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo htmlspecialchars($dept['department']); ?>"
                                        <?php echo ($department_filter == $dept['department']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($dept['department']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo ($status_filter == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>

                    <div class="col-md-1 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </button>
                        <span class="text-muted ms-3">
                            <i class="fas fa-info-circle me-1"></i>
                            عرض <?php echo count($subjects); ?> من أصل <?php echo $stats['total_subjects']; ?> مادة
                        </span>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض المواد -->
    <?php if (empty($subjects)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مواد دراسية</h5>
                <p class="text-muted">لم يتم العثور على أي مواد تطابق معايير البحث المحددة.</p>
                <a href="add.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة مادة جديدة
                </a>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($subjects as $subject):
                $icon = getSubjectIcon($subject['department'], $subject['subject_name']);
                $color = getSubjectColor($subject['department'], $subject['subject_name']);
            ?>
                <div class="col-xl-4 col-lg-6 col-md-6 mb-4">
                    <div class="card border-left-<?php echo $color; ?> shadow h-100">
                        <div class="card-header bg-<?php echo $color; ?> text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="<?php echo $icon; ?> fa-lg me-2"></i>
                                    <h6 class="mb-0 font-weight-bold">
                                        <?php echo htmlspecialchars($subject['subject_name']); ?>
                                    </h6>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-light" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="view.php?id=<?php echo $subject['id']; ?>">
                                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="edit.php?id=<?php echo $subject['id']; ?>">
                                                <i class="fas fa-edit me-2"></i>تعديل
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="copy.php?id=<?php echo $subject['id']; ?>">
                                                <i class="fas fa-copy me-2"></i>نسخ
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#"
                                               onclick="confirmDelete(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['subject_name'], ENT_QUOTES); ?>')">
                                                <i class="fas fa-trash me-2"></i>حذف
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">كود المادة</small>
                                    <div class="font-weight-bold">
                                        <?php echo htmlspecialchars($subject['subject_code'] ?: 'غير محدد'); ?>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الساعات المعتمدة</small>
                                    <div class="font-weight-bold">
                                        <?php echo $subject['credit_hours'] ?: 1; ?> ساعة
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">القسم</small>
                                    <div class="font-weight-bold">
                                        <?php echo htmlspecialchars($subject['department'] ?: 'غير محدد'); ?>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الحالة</small>
                                    <div>
                                        <?php if ($subject['status'] == 'active'): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">المرحلة</small>
                                    <div class="font-weight-bold">
                                        <?php echo htmlspecialchars($subject['stage_name'] ?: 'غير محدد'); ?>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الصف</small>
                                    <div class="font-weight-bold">
                                        <?php echo htmlspecialchars($subject['grade_name'] ?: 'غير محدد'); ?>
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($subject['description'])): ?>
                                <div class="mb-3">
                                    <small class="text-muted">الوصف</small>
                                    <div class="text-truncate" title="<?php echo htmlspecialchars($subject['description']); ?>">
                                        <?php echo htmlspecialchars(substr($subject['description'], 0, 100)); ?>
                                        <?php if (strlen($subject['description']) > 100): ?>...<?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <div class="h5 mb-0 text-<?php echo $color; ?>">
                                            <?php echo $subject['teacher_count']; ?>
                                        </div>
                                        <small class="text-muted">معلم</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="h5 mb-0 text-<?php echo $color; ?>">
                                        <?php echo $subject['student_count']; ?>
                                    </div>
                                    <small class="text-muted">طالب</small>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php echo date('d/m/Y', strtotime($subject['created_at'])); ?>
                                </small>
                                <div class="btn-group btn-group-sm">
                                    <a href="view.php?id=<?php echo $subject['id']; ?>"
                                       class="btn btn-outline-<?php echo $color; ?>" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?php echo $subject['id']; ?>"
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-outline-danger"
                                            title="حذف"
                                            onclick="confirmDelete(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['subject_name'], ENT_QUOTES); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- الرسوم البيانية -->
    <div class="row mt-4">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>توزيع المواد حسب المراحل
                    </h6>
                </div>
                <div class="card-body" style="height: 300px;">
                    <canvas id="stageChart"></canvas>
                    <div id="stageChartFallback" class="text-center py-4" style="display: none;">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <p class="text-muted">جاري تحميل الرسم البياني...</p>
                        <small class="text-muted">إذا لم يظهر الرسم، تحقق من الاتصال بالإنترنت</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>توزيع المواد حسب الأقسام
                    </h6>
                </div>
                <div class="card-body" style="height: 300px;">
                    <canvas id="departmentChart"></canvas>
                    <div id="departmentChartFallback" class="text-center py-4" style="display: none;">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <p class="text-muted">جاري تحميل الرسم البياني...</p>
                        <small class="text-muted">إذا لم يظهر الرسم، تحقق من الاتصال بالإنترنت</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تضمين مكتبات الرسوم البيانية -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
// سيتم تحميل الرسوم البيانية عبر دالة initCharts() في نهاية الصفحة

// وظائف الفلترة
function loadGrades() {
    const stageSelect = document.getElementById('stage');
    const gradeSelect = document.getElementById('grade');
    const selectedStage = stageSelect.value;

    // إخفاء جميع الصفوف
    Array.from(gradeSelect.options).forEach(option => {
        if (option.value === '') return; // تجاهل الخيار الافتراضي

        if (selectedStage === '' || option.dataset.stage === selectedStage) {
            option.style.display = 'block';
         else {
            option.style.display = 'none';
        
    );

    // إعادة تعيين الصف المحدد إذا لم يعد متاحاً
    if (gradeSelect.value && gradeSelect.selectedOptions[0].style.display === 'none') {
        gradeSelect.value = '';

function clearFilters() {
    document.getElementById('search').value = '';
    document.getElementById('stage').value = '';
    document.getElementById('grade').value = '';
    document.getElementById('department').value = '';
    document.getElementById('status').value = '';
    document.getElementById('filterForm').submit();

// وظائف التصدير
function exportToExcel() {
    const table = document.createElement('table');
    const thead = table.createTHead();
    const tbody = table.createTBody();

    // إضافة الرؤوس
    const headerRow = thead.insertRow();
    ['اسم المادة', 'كود المادة', 'القسم', 'المرحلة', 'الصف', 'الساعات المعتمدة', 'الحالة'].forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
    );

    // إضافة البيانات
    <?php foreach ($subjects as $subject): ?>
    const row = tbody.insertRow();
    row.insertCell().textContent = '<?php echo addslashes($subject['subject_name']); ?>';
    row.insertCell().textContent = '<?php echo addslashes($subject['subject_code']); ?>';
    row.insertCell().textContent = '<?php echo addslashes($subject['department']); ?>';
    row.insertCell().textContent = '<?php echo addslashes($subject['stage_name']); ?>';
    row.insertCell().textContent = '<?php echo addslashes($subject['grade_name']); ?>';
    row.insertCell().textContent = '<?php echo $subject['credit_hours']; ?>';
    row.insertCell().textContent = '<?php echo $subject['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>';
    <?php endforeach; ?>

    const wb = XLSX.utils.table_to_book(table);
    XLSX.writeFile(wb, 'subjects_<?php echo date('Y-m-d'); ?>.xlsx');

function exportToPDF() {
    const { jsPDF  = window.jspdf;
    const doc = new jsPDF();

    // إضافة العنوان
    doc.setFontSize(16);
    doc.text('تقرير المواد الدراسية', 105, 20, { align: 'center' );

    // إضافة التاريخ
    doc.setFontSize(10);
    doc.text('تاريخ التقرير: <?php echo date('Y-m-d H:i'); ?>', 20, 30);

    let yPosition = 50;
    doc.setFontSize(12);

    <?php foreach ($subjects as $index => $subject): ?>
    if (yPosition > 270) {
        doc.addPage();
        yPosition = 20;

    doc.text('<?php echo ($index1) . '. ' . addslashes($subject['subject_name']); ?>', 20, yPosition);
    yPosition += 7;
    doc.setFontSize(10);
    doc.text('الكود: <?php echo addslashes($subject['subject_code']); ?> | القسم: <?php echo addslashes($subject['department']); ?>', 25, yPosition);
    yPosition += 5;
    doc.text('المرحلة: <?php echo addslashes($subject['stage_name']); ?> | الصف: <?php echo addslashes($subject['grade_name']); ?>', 25, yPosition);
    yPosition += 10;
    doc.setFontSize(12);
    <?php endforeach; ?>

    doc.save('subjects_<?php echo date('Y-m-d'); ?>.pdf');

// تأكيد الحذف
function submitDeleteForm(subjectId) {
    try {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'delete.php?id='subjectId;

        const confirmInput = document.createElement('input');
        confirmInput.type = 'hidden';
        confirmInput.name = 'confirm_delete';
        confirmInput.value = '1';

        form.appendChild(confirmInput);
        document.body.appendChild(form);
        form.submit();
     catch (error) {
        console.error('Error submitting form:', error);
        if (typeof Swal !== 'undefined') {
            Swal.fire('خطأ!', 'حدث خطأ أثناء الحذف', 'error');
         else {
            alert('حدث خطأ أثناء الحذف');

// تحميل الصفوف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadGrades();
);

// تحميل الرسوم البيانية بعد تحميل الصفحة بالكامل
window.addEventListener('load', function() {
    // تأخير قصير للتأكد من تحميل Chart.js
    setTimeout(function() {
        initCharts();
    , 100);
);

function initCharts() {
    console.log('Starting chart initialization...');

    // التحقق من وجود Chart.js
    if (typeof Chart === 'undefined') {
        console.error('Chart.js not loaded');
        showChartError();
        return;

    console.log('Chart.js is available, version:', Chart.version);

    // بيانات الرسوم البيانية
    const stageData = [
        {stage_name: 'المرحلة الابتدائية', subject_count: 8,
        {stage_name: 'المرحلة الإعدادية', subject_count: 6,
        {stage_name: 'المرحلة الثانوية', subject_count: 10
    ];

    const departmentData = [
        {department: 'رياضيات', subject_count: 5,
        {department: 'علوم', subject_count: 4,
        {department: 'لغة عربية', subject_count: 3,
        {department: 'لغة إنجليزية', subject_count: 3,
        {department: 'تاريخ', subject_count: 2,
        {department: 'جغرافيا', subject_count: 2,
        {department: 'فيزياء', subject_count: 2,
        {department: 'كيمياء', subject_count: 2,
        {department: 'أحياء', subject_count: 1
    ];

    // رسم بياني للمراحل
    const stageCanvas = document.getElementById('stageChart');
    if (stageCanvas) {
        const stageCtx = stageCanvas.getContext('2d');
        new Chart(stageCtx, {
            type: 'doughnut',
            data: {
                labels: stageData.map(item => item.stage_name),
                datasets: [{
                    data: stageData.map(item => item.subject_count),
                    backgroundColor: [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                ]
            ,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true
                        
                    ,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => ab, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return ""label: "value مادة ("percentage%)";

        );
        console.log('Stage chart created');

    // رسم بياني للأقسام
    const departmentCanvas = document.getElementById('departmentChart');
    if (departmentCanvas) {
        const departmentCtx = departmentCanvas.getContext('2d');
        new Chart(departmentCtx, {
            type: 'bar',
            data: {
                labels: departmentData.map(item => item.department),
                datasets: [{
                    label: 'عدد المواد',
                    data: departmentData.map(item => item.subject_count),
                    backgroundColor: [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                        '#858796', '#5a5c69', '#6f42c1', '#e83e8c', '#fd7e14'
                    ],
                    borderWidth: 1
                ]
            ,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1

                ,
                plugins: {
                    legend: {
                        display: false
                    ,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return ""context.label: "context.parsed.y مادة";

        );
        console.log('Department chart created');

function showChartError() {
    // إخفاء canvas وإظهار رسالة الخطأ
    const stageCanvas = document.getElementById('stageChart');
    const departmentCanvas = document.getElementById('departmentChart');
    const stageFallback = document.getElementById('stageChartFallback');
    const departmentFallback = document.getElementById('departmentChartFallback');

    if (stageCanvas && stageFallback) {
        stageCanvas.style.display = 'none';
        stageFallback.style.display = 'block';
        stageFallback.innerHTML = "
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <p class="text-muted">فشل في تحميل مكتبة الرسوم البيانية</p>
            <small class="text-muted">تحقق من الاتصال بالإنترنت وأعد تحميل الصفحة</small>
        ";

    if (departmentCanvas && departmentFallback) {
        departmentCanvas.style.display = 'none';
        departmentFallback.style.display = 'block';
        departmentFallback.innerHTML = "
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <p class="text-muted">فشل في تحميل مكتبة الرسوم البيانية</p>
            <small class="text-muted">تحقق من الاتصال بالإنترنت وأعد تحميل الصفحة</small>
        ";

</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;

.text-xs {
    font-size: 0.7rem;

.font-weight-bold {
    font-weight: 700 !important;

.text-gray-800 {
    color: #5a5c69 !important;

.text-gray-300 {
    color: #dddfeb !important;

/* تحسين مظهر الرسوم البيانية */
.card-body {
    position: relative;

.card-body canvas {
    width: 100% !important;
    height: 100% !important;

/* تحسين مظهر البطاقات */
.card.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;

/* تحسين الألوان */
.text-primary {
    color: #5a5c69 !important;

/* تحسين التوزيع */
.stats-card {
    transition: transform 0.2s;

.stats-card:hover {
    transform: translateY(-2px);

/* تحسين الرسوم البيانية للهواتف */
@media (max-width: 768px) {
    .card-body canvas {
        max-height: 250px;

    .chart-container {
        height: 250px;

</style>

<?php include_once '../includes/footer.php'; ?>
<script>
// دالة تأكيد الحذف - محسنة ومبسطة
function confirmDelete(subjectId, subjectName) {
    console.log("confirmDelete called:", subjectId, subjectName);
    
    // استخدام confirm بسيط وموثوق
    var message = "هل أنت متأكد من حذف المادة: "subjectName؟\n\n";
    message += "لا يمكن التراجع عن هذا الإجراء.";
    
    if (confirm(message)) {
        console.log("User confirmed deletion");
        
        // إنشاء نموذج للحذف
        var form = document.createElement("form");
        form.method = "POST";
        form.action = "delete.php?id="subjectId;
        
        var confirmInput = document.createElement("input");
        confirmInput.type = "hidden";
        confirmInput.name = "confirm_delete";
        confirmInput.value = "1";
        
        form.appendChild(confirmInput);
        document.body.appendChild(form);
        
        console.log("Submitting form to:", form.action);
        form.submit();
    } else {
        console.log("User cancelled deletion");
    }
}

// دالة بديلة مع SweetAlert إذا كانت متاحة
function confirmDeleteSweet(subjectId, subjectName) {
    if (typeof Swal === "undefined") {
        confirmDelete(subjectId, subjectName);
        return;
    }
    
    Swal.fire({
        title: "تأكيد الحذف",
        text: "هل أنت متأكد من حذف المادة: "subjectName؟",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#dc3545",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "حذف",
        cancelButtonText: "إلغاء"
    }).then((result) => {
        if (result.isConfirmed) {
            var form = document.createElement("form");
            form.method = "POST";
            form.action = "delete.php?id="subjectId;
            
            var confirmInput = document.createElement("input");
            confirmInput.type = "hidden";
            confirmInput.name = "confirm_delete";
            confirmInput.value = "1";
            
            form.appendChild(confirmInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>