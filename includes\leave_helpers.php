<?php
/**
 * دوال مساعدة للتعامل مع أنواع الإجازات الجديدة
 * Helper functions for new leave types structure
 */

function get_leave_type_id_helper($leave_type, $conn) {
    // البحث بالكود أولاً
    $stmt = $conn->prepare("SELECT id FROM leave_types WHERE code = ? AND is_active = 1 LIMIT 1");
    $stmt->bind_param("s", $leave_type);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['id'];
    }
    
    // البحث بالاسم
    $stmt = $conn->prepare("SELECT id FROM leave_types WHERE (name = ? OR name_en = ?) AND is_active = 1 LIMIT 1");
    $stmt->bind_param("ss", $leave_type, $leave_type);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['id'];
    }
    
    // افتراضي: إجازة طارئة
    return 3;
}

function get_staff_leaves_with_types_query() {
    return "
    SELECT 
        sl.*,
        lt.name as leave_type_name,
        lt.code as leave_type_code,
        lt.color_code,
        lt.icon,
        u.full_name as user_name,
        u.role as user_role
    FROM staff_leaves sl
    LEFT JOIN leave_types lt ON sl.leave_type_id = lt.id
    LEFT JOIN users u ON sl.user_id = u.id
    ";
}
?>