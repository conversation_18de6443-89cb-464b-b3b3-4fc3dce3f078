<?php
/**
 * صفحة المواد الدراسية مع الفلاتر الديناميكية
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// جلب البيانات الأساسية للفلاتر
$stages_query = "SELECT id, stage_name FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = $stages_result->fetch_all(MYSQLI_ASSOC);

$grades_query = "SELECT g.id, g.grade_name, g.stage_id FROM grades g INNER JOIN educational_stages es ON g.stage_id = es.id WHERE g.status = 'active' ORDER BY es.sort_order, g.sort_order";
$grades_result = $conn->query($grades_query);
$grades = $grades_result->fetch_all(MYSQLI_ASSOC);

$classes_query = "SELECT c.id, c.class_name, c.grade_id FROM classes c INNER JOIN grades g ON c.grade_id = g.id WHERE c.status = 'active' ORDER BY g.sort_order, c.class_name";
$classes_result = $conn->query($classes_query);
$classes = $classes_result->fetch_all(MYSQLI_ASSOC);

$departments_query = "SELECT DISTINCT department FROM subjects WHERE department IS NOT NULL AND department != '' ORDER BY department";
$departments_result = $conn->query($departments_query);
$departments = $departments_result->fetch_all(MYSQLI_ASSOC);

// جلب الإحصائيات
$stats_query = "
    SELECT
        COUNT(*) as total_subjects,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subjects,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_subjects,
        COUNT(DISTINCT department) as total_departments,
        COUNT(DISTINCT stage_id) as total_stages
    FROM subjects
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// جلب المواد الأولية (أول 20 مادة)
$initial_subjects_query = "
    SELECT
        s.*,
        es.stage_name,
        g.grade_name,
        c.class_name,
        COUNT(DISTINCT ta.teacher_id) as teacher_count,
        COUNT(DISTINCT st.id) as student_count
    FROM subjects s
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN teacher_assignments ta ON s.id = ta.subject_id
    LEFT JOIN students st ON s.class_id = st.class_id
    GROUP BY s.id
    ORDER BY es.sort_order, g.sort_order, c.class_name, s.subject_name
    LIMIT 20
";
$initial_subjects_result = $conn->query($initial_subjects_query);
$initial_subjects = $initial_subjects_result->fetch_all(MYSQLI_ASSOC);

$page_title = 'المواد الدراسية - الفلاتر الديناميكية';
include_once '../includes/header.php';
?>

<!-- SweetAlert2 للحذف -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-book-open me-2 text-primary"></i>المواد الدراسية
                <span class="badge bg-info ms-2">فلاتر ديناميكية</span>
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item active">المواد الدراسية</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مادة
            </a>
            <a href="import.php" class="btn btn-outline-primary">
                <i class="fas fa-upload me-2"></i>استيراد CSV
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المواد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_subjects']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المواد النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['active_subjects']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الأقسام
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_departments']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المراحل الدراسية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_stages']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نظام الفلترة الديناميكي -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>البحث والفلترة الديناميكية
                <span class="badge bg-success ms-2">محدث</span>
            </h6>
        </div>
        <div class="card-body">
            <form id="filterForm">
                <div class="row">
                    <!-- البحث -->
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">
                            <i class="fas fa-search me-1"></i>البحث في المواد
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="اسم المادة أو الكود...">
                        </div>
                    </div>

                    <!-- المرحلة الدراسية -->
                    <div class="col-md-2 mb-3">
                        <label for="stage" class="form-label">
                            <i class="fas fa-layer-group me-1"></i>المرحلة الدراسية
                        </label>
                        <select class="form-select" id="stage" name="stage">
                            <option value="">جميع المراحل</option>
                            <?php foreach ($stages as $stage): ?>
                                <option value="<?php echo $stage['id']; ?>">
                                    <?php echo htmlspecialchars($stage['stage_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- الصف الدراسي -->
                    <div class="col-md-2 mb-3">
                        <label for="grade" class="form-label">
                            <i class="fas fa-graduation-cap me-1"></i>الصف الدراسي
                        </label>
                        <select class="form-select" id="grade" name="grade">
                            <option value="">جميع الصفوف</option>
                        </select>
                    </div>

                    <!-- الفصل -->
                    <div class="col-md-2 mb-3">
                        <label for="class" class="form-label">
                            <i class="fas fa-users me-1"></i>الفصل
                        </label>
                        <select class="form-select" id="class" name="class">
                            <option value="">جميع الفصول</option>
                        </select>
                    </div>

                    <!-- القسم -->
                    <div class="col-md-2 mb-3">
                        <label for="department" class="form-label">
                            <i class="fas fa-sitemap me-1"></i>القسم
                        </label>
                        <select class="form-select" id="department" name="department">
                            <option value="">جميع الأقسام</option>
                        </select>
                    </div>

                    <!-- الحالة -->
                    <div class="col-md-1 mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-1"></i>الحالة
                        </label>
                        <select class="form-select" id="status" name="status">
                            <option value="">الكل</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="button" id="searchBtn" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                                <button type="button" id="clearBtn" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </button>
                            </div>
                            <div>
                                <span class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <span id="resultsCount">عرض <?php echo count($initial_subjects); ?> مادة</span>
                                </span>
                                <div id="loadingIndicator" class="spinner-border spinner-border-sm ms-2" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض المواد -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table me-2"></i>قائمة المواد الدراسية
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="subjectsTable">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم المادة</th>
                            <th>كود المادة</th>
                            <th>القسم</th>
                            <th>المرحلة</th>
                            <th>الصف</th>
                            <th>الفصل</th>
                            <th>الساعات</th>
                            <th>الحالة</th>
                            <th>المعلمين</th>
                            <th>الطلاب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($initial_subjects)): ?>
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <i class="fas fa-book fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مواد دراسية</p>
                                <a href="add.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>إضافة مادة جديدة
                                </a>
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($initial_subjects as $subject): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-book me-2 text-primary"></i>
                                        <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($subject['subject_code'] ?: 'غير محدد'); ?></code>
                                </td>
                                <td><?php echo htmlspecialchars($subject['department'] ?: 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($subject['stage_name'] ?: 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($subject['grade_name'] ?: 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($subject['class_name'] ?: 'غير محدد'); ?></td>
                                <td>
                                    <span class="badge bg-info"><?php echo $subject['credit_hours'] ?: 1; ?> ساعة</span>
                                </td>
                                <td>
                                    <?php if ($subject['status'] == 'active'): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $subject['teacher_count']; ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-warning"><?php echo $subject['student_count']; ?></span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="view.php?id=<?php echo $subject['id']; ?>"
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit.php?id=<?php echo $subject['id']; ?>"
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" title="حذف"
                                                onclick="confirmDelete(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['subject_name'], ENT_QUOTES); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- تضمين ملف JavaScript للفلاتر الديناميكية -->
<script src="js/dynamic-filters.js"></script>

<script>
// دالة تأكيد الحذف
function confirmDelete(subjectId, subjectName) {
    console.log('confirmDelete called:', subjectId, subjectName);

    // التحقق من وجود SweetAlert2
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'تأكيد الحذف',
            text: 'هل أنت متأكد من حذف المادة: ' + subjectName + '؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'حذف',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                submitDeleteForm(subjectId);
            }
        });
    } else {
        // استخدام confirm عادي إذا لم تكن SweetAlert متاحة
        if (confirm('هل أنت متأكد من حذف المادة: ' + subjectName + '؟\n\nلا يمكن التراجع عن هذا الإجراء.')) {
            submitDeleteForm(subjectId);
        }
    }
}

// دالة إرسال نموذج الحذف
function submitDeleteForm(subjectId) {
    try {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = 'delete.php?id=' + subjectId;

        var confirmInput = document.createElement('input');
        confirmInput.type = 'hidden';
        confirmInput.name = 'confirm_delete';
        confirmInput.value = '1';

        form.appendChild(confirmInput);
        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error('Error submitting form:', error);
        alert('حدث خطأ أثناء الحذف: ' + error.message);
    }
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.text-xs {
    font-size: 0.7rem;
}
.font-weight-bold {
    font-weight: 700 !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}

/* تحسينات للفلاتر الديناميكية */
.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.table th {
    background-color: #5a5c69;
    color: white;
    font-weight: 600;
    border: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.1);
}

#loadingIndicator {
    color: #4e73df;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}
</style>

<?php include_once '../includes/footer.php'; ?>
