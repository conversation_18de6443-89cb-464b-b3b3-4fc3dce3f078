<?php
/**
 * مراجعة شاملة للجداول القديمة وإصلاحها
 */

echo "<h1>🔍 مراجعة شاملة للجداول القديمة</h1>";

// البحث عن الجداول القديمة
$old_tables = ['staff_attendance', 'staff_attendance'];
$new_table = 'staff_attendance';

// قائمة الملفات للفحص
$files_to_check = [];

// البحث في جميع ملفات PHP
function scanDirectory($dir, &$files) {
    if (is_dir($dir)) {
        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item != '.' && $item != '..') {
                $path = $dir . '/' . $item;
                if (is_dir($path) && !in_array($item, ['.git', 'vendor', 'node_modules'])) {
                    scanDirectory($path, $files);
                } elseif (is_file($path) && pathinfo($path, PATHINFO_EXTENSION) === 'php') {
                    $files[] = $path;
                }
            }
        }
    }
}

scanDirectory('.', $files_to_check);

$found_issues = [];
$total_issues = 0;

echo "<div class='alert alert-info'>";
echo "<h4>🎯 البحث عن الجداول القديمة:</h4>";
echo "<ul>";
foreach ($old_tables as $table) {
    echo "<li><code class='text-danger'>$table</code> → <code class='text-success'>$new_table</code></li>";
}
echo "</ul>";
echo "<p><strong>الملفات المفحوصة:</strong> " . count($files_to_check) . " ملف</p>";
echo "</div>";

// فحص كل ملف
foreach ($files_to_check as $file) {
    $content = file_get_contents($file);
    $file_issues = [];
    
    foreach ($old_tables as $old_table) {
        if (stripos($content, $old_table) !== false) {
            // العثور على جميع المواضع
            $lines = explode("\n", $content);
            foreach ($lines as $line_num => $line) {
                if (stripos($line, $old_table) !== false) {
                    $file_issues[] = [
                        'table' => $old_table,
                        'line' => $line_num + 1,
                        'content' => trim($line)
                    ];
                    $total_issues++;
                }
            }
        }
    }
    
    if (!empty($file_issues)) {
        $found_issues[$file] = $file_issues;
    }
}

// عرض النتائج
if (!empty($found_issues)) {
    echo "<div class='alert alert-warning'>";
    echo "<h4>⚠️ وُجدت $total_issues مشكلة في " . count($found_issues) . " ملف</h4>";
    echo "</div>";
    
    foreach ($found_issues as $file => $issues) {
        echo "<div class='card mb-3'>";
        echo "<div class='card-header bg-warning text-dark'>";
        echo "<h6><i class='fas fa-file-code me-2'></i>" . htmlspecialchars($file) . " (" . count($issues) . " مشكلة)</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<table class='table table-sm'>";
        echo "<tr><th>السطر</th><th>الجدول القديم</th><th>المحتوى</th></tr>";
        
        foreach ($issues as $issue) {
            echo "<tr>";
            echo "<td><span class='badge bg-info'>" . $issue['line'] . "</span></td>";
            echo "<td><code class='text-danger'>" . $issue['table'] . "</code></td>";
            echo "<td><code>" . htmlspecialchars(substr($issue['content'], 0, 100)) . "...</code></td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        echo "</div>";
    }
    
    // زر الإصلاح
    if ($_POST['action'] ?? '' === 'fix_all') {
        echo "<hr>";
        echo "<h3>🔧 تطبيق الإصلاح الشامل:</h3>";
        
        $fixed_files = 0;
        $total_fixes = 0;
        
        foreach ($found_issues as $file => $issues) {
            echo "<h4>📝 إصلاح: " . htmlspecialchars($file) . "</h4>";
            
            $content = file_get_contents($file);
            $original_content = $content;
            
            foreach ($old_tables as $old_table) {
                // استبدال شامل مع الحفاظ على السياق
                $patterns = [
                    // استعلامات SQL
                    "/\bFROM\s+$old_table\b/i" => "FROM $new_table",
                    "/\bJOIN\s+$old_table\b/i" => "JOIN $new_table",
                    "/\bLEFT\s+JOIN\s+$old_table\b/i" => "LEFT JOIN $new_table",
                    "/\bRIGHT\s+JOIN\s+$old_table\b/i" => "RIGHT JOIN $new_table",
                    "/\bINNER\s+JOIN\s+$old_table\b/i" => "INNER JOIN $new_table",
                    "/\bINSERT\s+INTO\s+$old_table\b/i" => "INSERT INTO $new_table",
                    "/\bUPDATE\s+$old_table\b/i" => "UPDATE $new_table",
                    "/\bDELETE\s+FROM\s+$old_table\b/i" => "DELETE FROM $new_table",
                    
                    // مراجع الجداول في التعليقات والنصوص
                    "/\b$old_table\b/" => $new_table
                ];
                
                foreach ($patterns as $pattern => $replacement) {
                    $new_content = preg_replace($pattern, $replacement, $content);
                    if ($new_content !== $content) {
                        $content = $new_content;
                        $total_fixes++;
                    }
                }
            }
            
            // حفظ الملف إذا تم تعديله
            if ($content !== $original_content) {
                if (file_put_contents($file, $content)) {
                    echo "<p class='text-success'>✅ تم إصلاح الملف</p>";
                    $fixed_files++;
                } else {
                    echo "<p class='text-danger'>❌ فشل في حفظ الملف</p>";
                }
            } else {
                echo "<p class='text-info'>ℹ️ لا توجد تغييرات</p>";
            }
        }
        
        echo "<div class='alert alert-success mt-4'>";
        echo "<h4>🎉 اكتمل الإصلاح!</h4>";
        echo "<ul>";
        echo "<li><strong>الملفات المُصلحة:</strong> $fixed_files</li>";
        echo "<li><strong>إجمالي الإصلاحات:</strong> $total_fixes</li>";
        echo "</ul>";
        echo "</div>";
        
        // إعادة فحص
        echo "<div class='mt-3'>";
        echo '<a href="comprehensive_table_audit.php" class="btn btn-primary">🔄 إعادة الفحص</a>';
        echo "</div>";
        
    } else {
        echo "<div class='mt-4 text-center'>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='action' value='fix_all'>";
        echo "<button type='submit' class='btn btn-danger btn-lg' onclick='return confirm(\"هل أنت متأكد من إصلاح جميع الملفات؟\\n\\nسيتم استبدال جميع مراجع الجداول القديمة.\")'>🔧 إصلاح جميع الملفات</button>";
        echo "</form>";
        echo "</div>";
    }
    
} else {
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ ممتاز! النظام نظيف</h4>";
    echo "<p>لا توجد مراجع للجداول القديمة <code>staff_attendance</code> أو <code>staff_attendance</code></p>";
    echo "<p>جميع الملفات تستخدم الجدول الموحد <code>staff_attendance</code></p>";
    echo "</div>";
}

// فحص إضافي للتأكد من وجود الجدول الموحد
echo "<hr>";
echo "<h3>🔍 فحص الجدول الموحد:</h3>";

try {
    require_once 'includes/config.php';
    require_once 'includes/database.php';
    
    // فحص بنية الجدول
    $table_check = $conn->query("SHOW TABLES LIKE 'staff_attendance'");
    if ($table_check->num_rows > 0) {
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ الجدول الموحد موجود</h5>";
        
        // فحص البيانات
        $data_check = $conn->query("SELECT COUNT(*) as count FROM staff_attendance");
        $count = $data_check->fetch_assoc()['count'];
        echo "<p><strong>عدد السجلات:</strong> $count</p>";
        
        // فحص الحالات
        $status_check = $conn->query("
            SELECT status, COUNT(*) as count 
            FROM staff_attendance 
            GROUP BY status 
            ORDER BY status
        ");
        
        if ($status_check->num_rows > 0) {
            echo "<p><strong>توزيع الحالات:</strong></p>";
            echo "<ul>";
            while ($row = $status_check->fetch_assoc()) {
                echo "<li><code>" . $row['status'] . "</code>: " . $row['count'] . " سجل</li>";
            }
            echo "</ul>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ الجدول الموحد غير موجود!</h5>";
        echo "<p>يجب إنشاء الجدول <code>staff_attendance</code> أولاً</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-warning'>";
    echo "<h5>⚠️ لا يمكن الاتصال بقاعدة البيانات</h5>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<div class='alert alert-info'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>تأكد من إصلاح جميع المراجع للجداول القديمة</li>";
echo "<li>اختبر النظام للتأكد من عمله بشكل صحيح</li>";
echo "<li>احذف الجداول القديمة من قاعدة البيانات (اختياري)</li>";
echo "<li>اختبر التقارير والإحصائيات</li>";
echo "</ol>";
echo "</div>";

// حذف الملف بعد 5 دقائق
echo "<script>
setTimeout(function() {
    if (confirm('هل تريد حذف ملف المراجعة؟')) {
        fetch('cleanup_audit.php');
    }
}, 300000);
</script>";
?>
