<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

check_session();

// تحقق من الصلاحيات (يفضل أن يكون Admin فقط)
if (!check_permission('admin')) {
    header('Location: index.php');
    exit();
}

$id = intval($_GET['id'] ?? 0);
if ($id > 0) {
    try {
        delete_user_with_relations($id, 'user');
        $_SESSION['success_message'] = 'تم حذف المستخدم بنجاح';
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء الحذف: ' . $e->getMessage();
        log_error("Error deleting user: " . $e->getMessage());
    }
} else {
    $_SESSION['error_message'] = 'معرف المستخدم غير صحيح';
}
header('Location: index.php');
exit();