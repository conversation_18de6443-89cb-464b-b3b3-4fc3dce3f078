<?php
/**
 * إعدادات الخصم
 * Deduction Settings
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        try {
            $conn->begin_transaction();

            foreach ($_POST as $key => $value) {
                if (strpos($key, 'setting_') === 0 && is_numeric($value)) {
                    $setting_name = str_replace('setting_', '', $key);
                    $setting_value = floatval($value);
                    
                    $stmt = $conn->prepare("
                        INSERT INTO deduction_settings (setting_name, setting_value) 
                        VALUES (?, ?)
                        ON DUPLICATE KEY UPDATE setting_value = ?
                    ");
                    $stmt->bind_param("sdd", $setting_name, $setting_value, $setting_value);
                    $stmt->execute();
                }
            }

            $conn->commit();
            $success_message = 'تم تحديث إعدادات الخصم بنجاح';

            // تسجيل النشاط
            log_activity($user_id, 'update_deduction_settings', 'deduction_settings', 0);

        } catch (Exception $e) {
            $conn->rollback();
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// جلب الإعدادات الحالية
$settings_query = "SELECT * FROM deduction_settings ORDER BY id";
$settings_result = $conn->query($settings_query);
$settings = [];
if ($settings_result) {
    while ($row = $settings_result->fetch_assoc()) {
        $settings[$row['setting_name']] = $row;
    }
}

// الإعدادات الافتراضية إذا لم تكن موجودة
$default_settings = [
    'daily_absence_deduction' => ['value' => 100.00, 'description' => 'مبلغ الخصم للغياب اليومي'],
    'hourly_absence_deduction' => ['value' => 15.00, 'description' => 'مبلغ الخصم للساعة الواحدة'],
    'late_arrival_deduction' => ['value' => 10.00, 'description' => 'مبلغ الخصم للتأخير'],
    'early_departure_deduction' => ['value' => 20.00, 'description' => 'مبلغ الخصم للانصراف المبكر']
];
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            إعدادات الخصم
                        </h5>
                        <a href="manage_absences.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>معلومة:</strong> هذه الإعدادات تحدد المبالغ الافتراضية للخصم. يمكن تعديل المبلغ عند تسجيل كل غياب حسب الحاجة.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row">
                            <?php foreach ($default_settings as $setting_name => $default_data): ?>
                                <?php 
                                $current_value = $settings[$setting_name]['setting_value'] ?? $default_data['value'];
                                $description = $settings[$setting_name]['description'] ?? $default_data['description'];
                                ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-money-bill-wave me-2 text-danger"></i>
                                                <?php echo $description; ?>
                                            </h6>
                                            <div class="input-group">
                                                <input type="number" 
                                                       class="form-control" 
                                                       name="setting_<?php echo $setting_name; ?>" 
                                                       value="<?php echo $current_value; ?>" 
                                                       step="0.01" 
                                                       min="0" 
                                                       required>
                                                <span class="input-group-text">ج.م</span>
                                            </div>
                                            <small class="text-muted">
                                                <?php
                                                switch($setting_name) {
                                                    case 'daily_absence_deduction':
                                                        echo 'يُستخدم عند اختيار "خصم يومي كامل"';
                                                        break;
                                                    case 'hourly_absence_deduction':
                                                        echo 'يُستخدم عند اختيار "خصم بالساعة"';
                                                        break;
                                                    case 'late_arrival_deduction':
                                                        echo 'للاستخدام المستقبلي في نظام التأخير';
                                                        break;
                                                    case 'early_departure_deduction':
                                                        echo 'للاستخدام المستقبلي في نظام الانصراف المبكر';
                                                        break;
                                                }
                                                ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="manage_absences.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- معاينة الإعدادات الحالية -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        معاينة الإعدادات الحالية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>نوع الخصم</th>
                                    <th>المبلغ الحالي</th>
                                    <th>الوصف</th>
                                    <th>آخر تحديث</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($default_settings as $setting_name => $default_data): ?>
                                    <?php 
                                    $current_setting = $settings[$setting_name] ?? null;
                                    $current_value = $current_setting['setting_value'] ?? $default_data['value'];
                                    $description = $current_setting['description'] ?? $default_data['description'];
                                    $updated_at = $current_setting['updated_at'] ?? 'لم يتم التحديث';
                                    ?>
                                    <tr>
                                        <td><strong><?php echo $description; ?></strong></td>
                                        <td>
                                            <span class="badge bg-danger">
                                                <?php echo number_format($current_value, 2); ?> ج.م
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php
                                                switch($setting_name) {
                                                    case 'daily_absence_deduction':
                                                        echo 'خصم كامل لليوم الواحد';
                                                        break;
                                                    case 'hourly_absence_deduction':
                                                        echo 'خصم للساعة الواحدة';
                                                        break;
                                                    case 'late_arrival_deduction':
                                                        echo 'خصم التأخير (مستقبلي)';
                                                        break;
                                                    case 'early_departure_deduction':
                                                        echo 'خصم الانصراف المبكر (مستقبلي)';
                                                        break;
                                                }
                                                ?>
                                            </small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo $updated_at !== 'لم يتم التحديث' ? date('Y-m-d H:i', strtotime($updated_at)) : $updated_at; ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
