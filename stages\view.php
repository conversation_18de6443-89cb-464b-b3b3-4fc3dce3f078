<?php
/**
 * صفحة عرض تفاصيل المرحلة الدراسية
 * View Educational Stage Details Page
 */

require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('view_stage');

// الحصول على معرف المرحلة
$stage_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($stage_id <= 0) {
    redirect_to('index.php');
}

// جلب بيانات المرحلة
$stage_query = "SELECT * FROM educational_stages WHERE id = ?";
$stmt = $conn->prepare($stage_query);
if (!$stmt) {
    die(__('database_error') . ': ' . $conn->error);
}

$stmt->bind_param('i', $stage_id);
$stmt->execute();
$result = $stmt->get_result();
$stage = $result->fetch_assoc();
$stmt->close();

if (!$stage) {
    redirect_to('index.php');
}

// جلب الفصول المرتبطة بالمرحلة
$classes_query = "
    SELECT 
        c.*,
        COUNT(DISTINCT s.id) as students_count,
        u.full_name as teacher_name
    FROM classes c
    LEFT JOIN students s ON c.id = s.class_id
    LEFT JOIN teachers t ON c.class_teacher_id = t.id
    LEFT JOIN users u ON t.user_id = u.id
    WHERE c.stage_id = ?
    GROUP BY c.id
    ORDER BY c.grade_level, c.section
";
$stmt = $conn->prepare($classes_query);
$classes = [];
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $classes[] = $row;
    }
    $stmt->close();
}

// جلب الصفوف الدراسية المرتبطة بالمرحلة
$grades_query = "
    SELECT g.*,
           COUNT(DISTINCT c.id) as classes_count,
           COUNT(DISTINCT s.id) as subjects_count,
           COALESCE(SUM(students_data.students_count), 0) as total_students
    FROM grades g
    LEFT JOIN classes c ON g.id = c.grade_id
    LEFT JOIN subjects s ON g.id = s.grade_id
    LEFT JOIN (
        SELECT class_id, COUNT(*) as students_count
        FROM students
        GROUP BY class_id
    ) students_data ON c.id = students_data.class_id
    WHERE g.stage_id = ?
    GROUP BY g.id
    ORDER BY g.sort_order
";
$stmt = $conn->prepare($grades_query);
$grades = [];
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $grades[] = $row;
    }
    $stmt->close();
}

// جلب المواد المرتبطة بالمرحلة
$subjects_query = "
    SELECT
        s.*,
        g.grade_name,
        g.grade_code,
        COUNT(DISTINCT ta.teacher_id) as teachers_count
    FROM subjects s
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN teacher_assignments ta ON s.id = ta.subject_id
    WHERE s.stage_id = ?
    GROUP BY s.id
    ORDER BY g.sort_order, s.subject_name
";
$stmt = $conn->prepare($subjects_query);
$subjects = [];
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $subjects[] = $row;
    }
    $stmt->close();
}

// حساب الإحصائيات
$total_grades = count($grades);
$total_classes = count($classes);
$total_subjects = count($subjects);
$total_students = array_sum(array_column($classes, 'students_count'));
$active_grades = count(array_filter($grades, function($g) { return $g['status'] === 'active'; }));
$active_classes = count(array_filter($classes, function($c) { return $c['status'] === 'active'; }));
$active_subjects = count(array_filter($subjects, function($s) { return $s['status'] === 'active'; }));
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-eye text-primary me-2"></i>
                <?php echo __('view_stage'); ?>
            </h2>
            <p class="text-muted mb-0">
                <?php echo __('stage_details'); ?>: <strong><?php echo htmlspecialchars($stage['stage_name']); ?></strong>
            </p>
        </div>
        <div>
            <a href="edit.php?id=<?php echo $stage_id; ?>" class="btn btn-primary me-2">
                <i class="fas fa-edit me-2"></i><?php echo __('edit_stage'); ?>
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_stages'); ?>
            </a>
        </div>
    </div>

    <!-- Stage Information Card -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('stage_information'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('stage_name'); ?>:</td>
                                    <td><?php echo htmlspecialchars($stage['stage_name']); ?></td>
                                </tr>
                                <?php if (!empty($stage['stage_name_en'])): ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('stage_name_en'); ?>:</td>
                                    <td><?php echo htmlspecialchars($stage['stage_name_en']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('stage_code'); ?>:</td>
                                    <td><code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($stage['stage_code']); ?></code></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('sort_order'); ?>:</td>
                                    <td><span class="badge bg-primary"><?php echo $stage['sort_order']; ?></span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('duration_years'); ?>:</td>
                                    <td><?php echo $stage['duration_years']; ?> <?php echo __('years'); ?></td>
                                </tr>
                                <?php if ($stage['min_age'] || $stage['max_age']): ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('age_range'); ?>:</td>
                                    <td>
                                        <?php if ($stage['min_age'] && $stage['max_age']): ?>
                                            <?php echo $stage['min_age']; ?> - <?php echo $stage['max_age']; ?> <?php echo __('years'); ?>
                                        <?php elseif ($stage['min_age']): ?>
                                            <?php echo __('min_age'); ?>: <?php echo $stage['min_age']; ?> <?php echo __('years'); ?>
                                        <?php elseif ($stage['max_age']): ?>
                                            <?php echo __('max_age'); ?>: <?php echo $stage['max_age']; ?> <?php echo __('years'); ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('status'); ?>:</td>
                                    <td>
                                        <?php if ($stage['status'] === 'active'): ?>
                                            <span class="badge bg-success"><?php echo __('active'); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('created_at'); ?>:</td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($stage['created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if (!empty($stage['description'])): ?>
                        <hr>
                        <div>
                            <h6 class="text-muted mb-2"><?php echo __('description'); ?>:</h6>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($stage['description'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo __('stage_statistics'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4 mb-3">
                            <div class="border-end">
                                <h3 class="text-primary mb-0"><?php echo $total_grades; ?></h3>
                                <small class="text-muted"><?php echo __('total_grades'); ?></small>
                                <br><small class="text-success"><?php echo $active_grades; ?> <?php echo __('active'); ?></small>
                            </div>
                        </div>
                        <div class="col-4 mb-3">
                            <div class="border-end">
                                <h3 class="text-info mb-0"><?php echo $total_classes; ?></h3>
                                <small class="text-muted"><?php echo __('total_classes'); ?></small>
                                <br><small class="text-success"><?php echo $active_classes; ?> <?php echo __('active'); ?></small>
                            </div>
                        </div>
                        <div class="col-4 mb-3">
                            <h3 class="text-warning mb-0"><?php echo $total_subjects; ?></h3>
                            <small class="text-muted"><?php echo __('subjects'); ?></small>
                            <br><small class="text-success"><?php echo $active_subjects; ?> <?php echo __('active'); ?></small>
                        </div>
                        <div class="col-12">
                            <hr class="my-2">
                            <h3 class="text-success mb-0"><?php echo $total_students; ?></h3>
                            <small class="text-muted"><?php echo __('total_students'); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        <?php echo __('quick_actions'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="../classes/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-plus me-2"></i><?php echo __('add_class'); ?>
                        </a>
                        <a href="../subjects/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-plus me-2"></i><?php echo __('add_subject'); ?>
                        </a>
                        <a href="edit.php?id=<?php echo $stage_id; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i><?php echo __('edit_stage'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grades Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        <?php echo __('school_grades'); ?>
                        <span class="badge bg-primary"><?php echo $total_grades; ?></span>
                    </h5>
                    <a href="../grades/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_grade'); ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (!empty($grades)): ?>
                        <div class="row">
                            <?php foreach ($grades as $grade): ?>
                                <div class="col-lg-6 col-md-6 mb-3">
                                    <div class="card border-start border-primary border-4">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="card-title mb-1">
                                                        <i class="fas fa-graduation-cap me-2 text-primary"></i>
                                                        <?php echo htmlspecialchars($grade['grade_name']); ?>
                                                    </h6>
                                                    <p class="text-muted small mb-2">
                                                        <strong><?php echo __('grade_code'); ?>:</strong>
                                                        <code><?php echo htmlspecialchars($grade['grade_code']); ?></code>
                                                    </p>
                                                </div>
                                                <span class="badge bg-<?php echo $grade['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo __($grade['status']); ?>
                                                </span>
                                            </div>

                                            <div class="row text-center mt-3">
                                                <div class="col-4">
                                                    <div class="border-end">
                                                        <h6 class="text-info mb-0"><?php echo $grade['classes_count']; ?></h6>
                                                        <small class="text-muted"><?php echo __('classes'); ?></small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="border-end">
                                                        <h6 class="text-warning mb-0"><?php echo $grade['subjects_count']; ?></h6>
                                                        <small class="text-muted"><?php echo __('subjects'); ?></small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <h6 class="text-success mb-0"><?php echo $grade['total_students']; ?></h6>
                                                    <small class="text-muted"><?php echo __('students'); ?></small>
                                                </div>
                                            </div>

                                            <div class="mt-3">
                                                <a href="../grades/view.php?id=<?php echo $grade['id']; ?>" class="btn btn-outline-info btn-sm">
                                                    <i class="fas fa-eye me-1"></i><?php echo __('view'); ?>
                                                </a>
                                                <a href="../grades/edit.php?id=<?php echo $grade['id']; ?>" class="btn btn-outline-warning btn-sm">
                                                    <i class="fas fa-edit me-1"></i><?php echo __('edit'); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted"><?php echo __('no_grades_found'); ?></h5>
                            <p class="text-muted"><?php echo __('add_first_grade'); ?></p>
                            <a href="../grades/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i><?php echo __('add_grade'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Classes Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-school me-2"></i>
                        <?php echo __('classes'); ?> 
                        <span class="badge bg-info"><?php echo $total_classes; ?></span>
                    </h5>
                    <a href="../classes/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_class'); ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($classes)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-school fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted"><?php echo __('no_classes_found'); ?></h6>
                            <p class="text-muted"><?php echo __('no_classes_in_stage'); ?></p>
                            <a href="../classes/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i><?php echo __('add_first_class'); ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th><?php echo __('class_name'); ?></th>
                                        <th><?php echo __('grade_level'); ?></th>
                                        <th><?php echo __('section'); ?></th>
                                        <th><?php echo __('capacity'); ?></th>
                                        <th><?php echo __('students'); ?></th>
                                        <th><?php echo __('class_teacher'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($classes as $class): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($class['class_name']); ?></strong>
                                                <?php if (!empty($class['room_number'])): ?>
                                                    <br><small class="text-muted"><?php echo __('room'); ?>: <?php echo htmlspecialchars($class['room_number']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($class['grade_level']); ?></td>
                                            <td><?php echo htmlspecialchars($class['section'] ?: '-'); ?></td>
                                            <td><?php echo $class['capacity']; ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $class['students_count']; ?></span>
                                                <?php if ($class['students_count'] > $class['capacity']): ?>
                                                    <i class="fas fa-exclamation-triangle text-warning ms-1" title="<?php echo __('over_capacity'); ?>"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($class['teacher_name'] ?: __('not_assigned')); ?></td>
                                            <td>
                                                <?php if ($class['status'] === 'active'): ?>
                                                    <span class="badge bg-success"><?php echo __('active'); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="../classes/view.php?id=<?php echo $class['id']; ?>" 
                                                       class="btn btn-outline-info" title="<?php echo __('view'); ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="../classes/edit.php?id=<?php echo $class['id']; ?>" 
                                                       class="btn btn-outline-primary" title="<?php echo __('edit'); ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Subjects Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        <?php echo __('subjects'); ?> 
                        <span class="badge bg-warning"><?php echo $total_subjects; ?></span>
                    </h5>
                    <a href="../subjects/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_subject'); ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($subjects)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted"><?php echo __('no_subjects_found'); ?></h6>
                            <p class="text-muted"><?php echo __('no_subjects_in_stage'); ?></p>
                            <a href="../subjects/add.php?stage_id=<?php echo $stage_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i><?php echo __('add_first_subject'); ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th><?php echo __('subject_name'); ?></th>
                                        <th><?php echo __('subject_code'); ?></th>
                                        <th><?php echo __('school_grade'); ?></th>
                                        <th><?php echo __('credit_hours'); ?></th>
                                        <th><?php echo __('department'); ?></th>
                                        <th><?php echo __('teachers'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subjects as $subject): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                                <?php if (!empty($subject['subject_name_en'])): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($subject['subject_name_en']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><code><?php echo htmlspecialchars($subject['subject_code']); ?></code></td>
                                            <td>
                                                <?php if (!empty($subject['grade_name'])): ?>
                                                    <span class="badge bg-primary"><?php echo htmlspecialchars($subject['grade_code']); ?></span>
                                                    <?php echo htmlspecialchars($subject['grade_name']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $subject['credit_hours']; ?></td>
                                            <td><?php echo htmlspecialchars($subject['department'] ?: '-'); ?></td>
                                            <td><span class="badge bg-secondary"><?php echo $subject['teachers_count']; ?></span></td>
                                            <td>
                                                <?php if ($subject['status'] === 'active'): ?>
                                                    <span class="badge bg-success"><?php echo __('active'); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="../subjects/view.php?id=<?php echo $subject['id']; ?>" 
                                                       class="btn btn-outline-info" title="<?php echo __('view'); ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="../subjects/edit.php?id=<?php echo $subject['id']; ?>" 
                                                       class="btn btn-outline-primary" title="<?php echo __('edit'); ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(stageId, stageName) {
    <?php if ($total_classes > 0 || $total_subjects > 0): ?>
        // إذا كانت هناك فصول أو مواد مرتبطة، عرض تحذير فقط
        Swal.fire({
            title: '<?php echo __('cannot_delete'); ?>',
            html: `
                <div class="text-center mb-3">
                    <i class="fas fa-ban fa-3x text-danger"></i>
                </div>
                <p><?php echo __('cannot_delete_stage_has_dependencies'); ?></p>
                <div class="alert alert-warning">
                    <strong><?php echo __('stage_name'); ?>:</strong> ${stageName}
                </div>
                <p class="text-muted small">
                    <?php echo __('total_classes'); ?>: <?php echo $total_classes; ?><br>
                    <?php echo __('total_subjects'); ?>: <?php echo $total_subjects; ?>
                </p>
            `,
            icon: 'error',
            confirmButtonText: '<?php echo __('ok'); ?>',
            customClass: {
                confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
        });
        return;
    <?php endif; ?>

    // استخدام SweetAlert للتأكيد
    Swal.fire({
        title: '<?php echo __('confirm_delete'); ?>',
        html: `
            <div class="text-center mb-3">
                <i class="fas fa-trash-alt fa-3x text-danger"></i>
            </div>
            <p><?php echo __('delete_stage_confirmation'); ?></p>
            <div class="alert alert-warning">
                <strong><?php echo __('stage_name'); ?>:</strong> ${stageName}
            </div>
            <p class="text-muted small"><?php echo __('delete_stage_warning'); ?></p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>',
        cancelButtonText: '<i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار رسالة تحميل
            Swal.fire({
                title: '<?php echo __('deleting'); ?>...',
                text: '<?php echo __('please_wait'); ?>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // الانتقال لصفحة الحذف
            window.location.href = 'delete.php?id=' + stageId;
        }
    });
}
</script>

<?php require_once '../includes/footer.php'; ?>
