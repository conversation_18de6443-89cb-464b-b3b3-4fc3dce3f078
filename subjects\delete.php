<?php
/**
 * صفحة حذف المادة الدراسية
 * Delete Subject Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// الحصول على معرف المادة
$subject_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($subject_id <= 0) {
    $_SESSION['error_message'] = 'معرف المادة غير صحيح';
    header('Location: index.php');
    exit();
}

// جلب بيانات المادة
$subject_query = "SELECT * FROM subjects WHERE id = ?";
$stmt = $conn->prepare($subject_query);
if (!$stmt) {
    $_SESSION['error_message'] = 'خطأ في قاعدة البيانات';
    header('Location: index.php');
    exit();
}

$stmt->bind_param('i', $subject_id);
$stmt->execute();
$result = $stmt->get_result();
$subject = $result->fetch_assoc();
$stmt->close();

if (!$subject) {
    $_SESSION['error_message'] = 'المادة غير موجودة';
    header('Location: index.php');
    exit();
}

// التحقق من وجود تبعيات (يمكن إضافة فحص للطلاب المسجلين في المادة لاحقاً)
$dependencies_count = 0;

// معالجة طلب الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من وجود تأكيد الحذف
    if (!isset($_POST['confirm_delete'])) {
        $_SESSION['error_message'] = 'لم يتم تأكيد الحذف';
        header('Location: index.php');
        exit();
    }

    // بدء المعاملة
    $conn->begin_transaction();

    try {
        // حذف المادة
        $delete_query = "DELETE FROM subjects WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        if (!$stmt) {
            throw new Exception('خطأ في قاعدة البيانات: ' . $conn->error);
        }

        $stmt->bind_param('i', $subject_id);
        if (!$stmt->execute()) {
            throw new Exception('خطأ في قاعدة البيانات: ' . $stmt->error);
        }
        $stmt->close();

        // تسجيل النشاط (يمكن إضافة هذا لاحقاً)
        // log_activity($_SESSION['user_id'], 'delete_subject', 'subjects', $subject_id);

        // تأكيد المعاملة
        $conn->commit();

        // رسالة نجاح وإعادة توجيه
        $_SESSION['success_message'] = 'تم حذف المادة بنجاح';
        header('Location: index.php');
        exit();

    } catch (Exception $e) {
        // إلغاء المعاملة في حالة الخطأ
        $conn->rollback();
        
        // تسجيل الخطأ
        error_log("Error deleting subject {$subject_id}: " . $e->getMessage());
        
        $_SESSION['error_message'] = 'خطأ في حذف المادة: ' . $e->getMessage();
        header('Location: index.php');
        exit();
    }
}

$page_title = 'حذف المادة';

// إذا لم يكن طلب POST، عرض صفحة التأكيد
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-trash text-danger me-2"></i>
                حذف المادة
            </h2>
            <p class="text-muted mb-0">تأكيد حذف المادة الدراسية</p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للمواد
            </a>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحذير
                        </h6>
                        <p class="mb-0">هل أنت متأكد من حذف هذه المادة؟</p>
                    </div>

                    <!-- Subject Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">المادة المراد حذفها</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold text-muted">اسم المادة:</td>
                                            <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">رمز المادة:</td>
                                            <td><code><?php echo htmlspecialchars($subject['subject_code']); ?></code></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold text-muted">الساعات المعتمدة:</td>
                                            <td><?php echo $subject['credit_hours']; ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">الحالة:</td>
                                            <td>
                                                <?php if ($subject['status'] === 'active'): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Consequences Information -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            عواقب الحذف
                        </h6>
                        <ul class="mb-0">
                            <li>سيتم حذف المادة نهائياً من النظام</li>
                            <li>لا يمكن التراجع عن هذا الإجراء</li>
                            <li>ستتم إزالة جميع المراجع لهذه المادة</li>
                        </ul>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="POST" class="mt-4">
                        <input type="hidden" name="confirm_delete" value="1">
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                أفهم عواقب حذف هذه المادة وأريد المتابعة
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                <i class="fas fa-trash me-2"></i>حذف المادة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الحذف عند تأكيد الفهم
document.getElementById('confirmDelete').addEventListener('change', function() {
    document.getElementById('deleteBtn').disabled = !this.checked;
});

// تأكيد إضافي عند الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد تماماً من حذف هذه المادة؟')) {
        e.preventDefault();
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
