<?php
// api/set_language.php
session_start();
header('Content-Type: application/json');

$data = json_decode(file_get_contents('php://input'), true);

if (isset($data['language']) && in_array($data['language'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $data['language'];
    echo json_encode(['status' => 'success']);
    exit;
}

echo json_encode(['status' => 'error', 'message' => 'Invalid language']);
exit; 