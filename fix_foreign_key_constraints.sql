-- إصلاح مشاكل القيود الخارجية
-- Fix foreign key constraints issues

USE school_management;

-- 1. تعطيل فحص القيود الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- 2. التحقق من الجداول التي تحتوي على قيود خارجية
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users'
ORDER BY TABLE_NAME;

-- 3. إصلاح جدول admin_attendance
-- التحقق من وجود الجدول أولاً
SELECT COUNT(*) as table_exists 
FROM information_schema.tables 
WHERE table_schema = 'school_management' 
AND table_name = 'admin_attendance';

-- حذ<PERSON> القيد الخارجي المُشكل
ALTER TABLE admin_attendance DROP FOREIGN KEY IF EXISTS admin_attendance_ibfk_1;

-- تنظيف البيانات غير الصحيحة في admin_attendance
DELETE FROM admin_attendance 
WHERE admin_id NOT IN (SELECT id FROM users);

-- إعادة إضافة القيد الخارجي بشكل صحيح
ALTER TABLE admin_attendance
ADD CONSTRAINT admin_attendance_user_id_foreign 
FOREIGN KEY (admin_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 4. إصلاح جداول أخرى قد تحتوي على قيود مشابهة
-- جدول staff_attendance
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_ibfk_1;
ALTER TABLE staff_attendance DROP FOREIGN KEY IF EXISTS staff_attendance_user_id_foreign;

-- تنظيف البيانات
DELETE FROM staff_attendance 
WHERE user_id NOT IN (SELECT id FROM users);

-- إعادة إضافة القيد
ALTER TABLE staff_attendance
ADD CONSTRAINT staff_attendance_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 5. إصلاح جدول unified_staff_absences
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_user_id_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_applied_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_approved_by_foreign;
ALTER TABLE unified_staff_absences DROP FOREIGN KEY IF EXISTS unified_absences_rejected_by_foreign;

-- تنظيف البيانات
DELETE FROM unified_staff_absences 
WHERE user_id NOT IN (SELECT id FROM users);

UPDATE unified_staff_absences 
SET applied_by = 1 
WHERE applied_by NOT IN (SELECT id FROM users);

UPDATE unified_staff_absences 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

UPDATE unified_staff_absences 
SET rejected_by = NULL 
WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود
ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_applied_by_foreign 
FOREIGN KEY (applied_by) REFERENCES users (id) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_approved_by_foreign 
FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE unified_staff_absences
ADD CONSTRAINT unified_absences_rejected_by_foreign 
FOREIGN KEY (rejected_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- 6. إصلاح جداول أخرى محتملة
-- جدول staff_leaves
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_ibfk_1;
ALTER TABLE staff_leaves DROP FOREIGN KEY IF EXISTS staff_leaves_user_id_foreign;

-- تنظيف البيانات
DELETE FROM staff_leaves 
WHERE user_id NOT IN (SELECT id FROM users);

UPDATE staff_leaves 
SET applied_by = 1 
WHERE applied_by IS NOT NULL AND applied_by NOT IN (SELECT id FROM users);

UPDATE staff_leaves 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود
ALTER TABLE staff_leaves
ADD CONSTRAINT staff_leaves_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 7. إصلاح جدول staff_absences_with_deduction
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_user_id_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_approved_by_foreign;
ALTER TABLE staff_absences_with_deduction DROP FOREIGN KEY IF EXISTS staff_absences_recorded_by_foreign;

-- تنظيف البيانات
DELETE FROM staff_absences_with_deduction 
WHERE user_id NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET recorded_by = 1 
WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

UPDATE staff_absences_with_deduction 
SET approved_by = NULL 
WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);

-- إعادة إضافة القيود
ALTER TABLE staff_absences_with_deduction
ADD CONSTRAINT staff_absences_user_id_foreign 
FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 8. إنشاء إجراء لحذف المستخدمين بأمان
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS safe_delete_user(IN p_user_id INT)
BEGIN
    DECLARE user_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدم
    SELECT COUNT(*) INTO user_exists FROM users WHERE id = p_user_id;
    
    IF user_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'User does not exist';
    END IF;
    
    -- حذف البيانات المرتبطة أولاً (إذا لزم الأمر)
    -- يمكن إضافة المزيد من الجداول هنا حسب الحاجة
    
    -- حذف المستخدم (القيود الخارجية ستتولى باقي البيانات)
    DELETE FROM users WHERE id = p_user_id;
    
    COMMIT;
    
    SELECT CONCAT('User ', p_user_id, ' deleted successfully') as message;
END//

DELIMITER ;

-- 9. إنشاء إجراء لتحديث معرف المستخدم بأمان
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS safe_update_user_id(
    IN p_old_user_id INT, 
    IN p_new_user_id INT
)
BEGIN
    DECLARE old_user_exists INT DEFAULT 0;
    DECLARE new_user_exists INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدمين
    SELECT COUNT(*) INTO old_user_exists FROM users WHERE id = p_old_user_id;
    SELECT COUNT(*) INTO new_user_exists FROM users WHERE id = p_new_user_id;
    
    IF old_user_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Old user does not exist';
    END IF;
    
    IF new_user_exists > 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'New user ID already exists';
    END IF;
    
    -- تحديث معرف المستخدم
    UPDATE users SET id = p_new_user_id WHERE id = p_old_user_id;
    
    COMMIT;
    
    SELECT CONCAT('User ID updated from ', p_old_user_id, ' to ', p_new_user_id) as message;
END//

DELIMITER ;

-- 10. إعادة تفعيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- 11. إنشاء view لعرض جميع القيود الخارجية
CREATE OR REPLACE VIEW foreign_key_constraints AS
SELECT 
    TABLE_NAME as 'جدول',
    COLUMN_NAME as 'عمود',
    CONSTRAINT_NAME as 'اسم القيد',
    REFERENCED_TABLE_NAME as 'الجدول المرجعي',
    REFERENCED_COLUMN_NAME as 'العمود المرجعي'
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 12. إنشاء دالة للتحقق من سلامة القيود الخارجية
DELIMITER //

CREATE FUNCTION IF NOT EXISTS check_foreign_key_integrity() 
RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE integrity_issues TEXT DEFAULT '';
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(64);
    DECLARE column_name VARCHAR(64);
    DECLARE ref_table VARCHAR(64);
    DECLARE ref_column VARCHAR(64);
    DECLARE issue_count INT;
    
    DECLARE fk_cursor CURSOR FOR
        SELECT TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
        AND REFERENCED_TABLE_NAME IS NOT NULL;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN fk_cursor;
    
    read_loop: LOOP
        FETCH fk_cursor INTO table_name, column_name, ref_table, ref_column;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- فحص سلامة القيد الخارجي
        SET @sql = CONCAT(
            'SELECT COUNT(*) INTO @issue_count FROM ', table_name, ' t1 ',
            'LEFT JOIN ', ref_table, ' t2 ON t1.', column_name, ' = t2.', ref_column, ' ',
            'WHERE t1.', column_name, ' IS NOT NULL AND t2.', ref_column, ' IS NULL'
        );
        
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        IF @issue_count > 0 THEN
            SET integrity_issues = CONCAT(
                integrity_issues, 
                table_name, '.', column_name, ' -> ', ref_table, '.', ref_column, 
                ' (', @issue_count, ' issues); '
            );
        END IF;
        
    END LOOP;
    
    CLOSE fk_cursor;
    
    IF integrity_issues = '' THEN
        RETURN 'All foreign key constraints are valid';
    ELSE
        RETURN CONCAT('Issues found: ', integrity_issues);
    END IF;
END//

DELIMITER ;

-- اختبار سلامة القيود الخارجية
SELECT check_foreign_key_integrity() as integrity_status;

-- عرض جميع القيود الخارجية الحالية
SELECT * FROM foreign_key_constraints;

SELECT 'Foreign key constraints fixed successfully!' as message;
