<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// الحصول على معرف الإداري
$staff_id = intval($_GET['id'] ?? 0);
if ($staff_id <= 0) {
    $_SESSION['error_message'] = 'معرف الإداري غير صالح';
    header('Location: index.php');
    exit();
}

// جلب بيانات الإداري
$query = "
    SELECT 
        s.*,
        u.username,
        u.email,
        u.status as user_status,
        u.full_name,
        u.phone as user_phone,
        u.gender as user_gender
    FROM staff s
    JOIN users u ON s.user_id = u.id
    WHERE s.id = ?
";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error_message'] = 'الإداري غير موجود';
    header('Location: index.php');
    exit();
}

$staff = $result->fetch_assoc();

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        // جمع البيانات وتنظيفها
        $full_name = clean_input($_POST['full_name'] ?? '');
        $email = clean_input($_POST['email'] ?? '');
        $username = clean_input($_POST['username'] ?? '');
        $phone = clean_input($_POST['phone'] ?? '');
        $national_id = clean_input($_POST['national_id'] ?? '');
        $date_of_birth = clean_input($_POST['date_of_birth'] ?? '');
        $gender = clean_input($_POST['gender'] ?? '');
        $address = clean_input($_POST['address'] ?? '');
        $employee_id = clean_input($_POST['employee_id'] ?? '');
        $hire_date = clean_input($_POST['hire_date'] ?? '');
        $department = clean_input($_POST['department'] ?? '');
        $position = clean_input($_POST['position'] ?? '');
        $salary = floatval($_POST['salary'] ?? 0);
        $user_status = clean_input($_POST['user_status'] ?? 'active');
        $staff_status = clean_input($_POST['staff_status'] ?? 'active');

        // التحقق من صحة البيانات
        $errors = [];
        if (empty($full_name)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        if (empty($username)) {
            $errors[] = 'اسم المستخدم مطلوب';
        } else {
            // التحقق من عدم تكرار اسم المستخدم
            $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->bind_param("si", $username, $staff['user_id']);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $errors[] = 'اسم المستخدم موجود بالفعل';
            }
        }
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!validate_email($email)) {
            $errors[] = 'البريد الإلكتروني غير صالح';
        } else {
            // التحقق من عدم تكرار البريد الإلكتروني
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->bind_param("si", $email, $staff['user_id']);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $errors[] = 'البريد الإلكتروني موجود بالفعل';
            }
        }
        if (empty($employee_id)) {
            $errors[] = 'رقم الموظف مطلوب';
        } else {
            // التحقق من عدم تكرار رقم الموظف
            $stmt = $conn->prepare("SELECT id FROM staff WHERE employee_id = ? AND id != ?");
            $stmt->bind_param("si", $employee_id, $staff_id);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $errors[] = 'رقم الموظف موجود بالفعل';
            }
        }
        if (!empty($national_id) && !validate_national_id($national_id)) {
            $errors[] = 'الرقم القومي غير صالح';
        }
        if (!empty($phone) && !validate_phone($phone)) {
            $errors[] = 'رقم الهاتف غير صالح';
        }
        if (!empty($date_of_birth) && !validate_date($date_of_birth)) {
            $errors[] = 'تاريخ الميلاد غير صالح';
        }
        if (!empty($hire_date) && !validate_date($hire_date)) {
            $errors[] = 'تاريخ التوظيف غير صالح';
        }
        if (empty($gender) || !in_array($gender, ['male', 'female'])) {
            $errors[] = 'الجنس مطلوب';
        }

        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();
            try {
                // تحديث بيانات المستخدم
                $stmt = $conn->prepare("
                    UPDATE users 
                    SET full_name = ?, username = ?, email = ?, status = ?, phone = ?, gender = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->bind_param("ssssssi", $full_name, $username, $email, $user_status, $phone, $gender, $staff['user_id']);
                $stmt->execute();
                $stmt->close();

                // تحديث بيانات الإداري
                $stmt = $conn->prepare("
                    UPDATE staff 
                    SET employee_id = ?, phone = ?, address = ?, date_of_birth = ?, gender = ?, 
                        national_id = ?, hire_date = ?, department = ?, position = ?, 
                        salary = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->bind_param("sssssssssdsi", 
                    $employee_id, $phone, $address, $date_of_birth, $gender,
                    $national_id, $hire_date, $department, $position, $salary, $staff_status, $staff_id
                );
                $stmt->execute();
                $stmt->close();

                $conn->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'edit_staff', 'staff', $staff_id, null, [
                    'staff_name' => $full_name,
                    'employee_id' => $employee_id
                ]);

                $_SESSION['success_message'] = 'تم تحديث بيانات الإداري بنجاح';
                header('Location: view.php?id=' . $staff_id);
                exit();
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = 'حدث خطأ أثناء تحديث البيانات';
                log_error("Error updating staff: " . $e->getMessage());
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// تحميل ملف اللغة
load_language();
$page_title = 'تعديل بيانات الإداري - ' . $staff['full_name'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">تعديل بيانات الإداري</h1>
                    <p class="text-muted"><?php echo htmlspecialchars($staff['full_name']); ?></p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                    <a href="view.php?id=<?php echo $staff_id; ?>" class="btn btn-info">
                        <i class="fas fa-eye me-2"></i>عرض
                    </a>
                </div>
            </div>

            <!-- Error/Success Messages -->
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Edit Staff Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>تعديل بيانات الإداري
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- Personal Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2"></i>المعلومات الشخصية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? $staff['full_name']); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="male" <?php echo ($_POST['gender'] ?? $staff['gender']) === 'male' ? 'selected' : ''; ?>>ذكر</option>
                                    <option value="female" <?php echo ($_POST['gender'] ?? $staff['gender']) === 'female' ? 'selected' : ''; ?>>أنثى</option>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار الجنس</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                       value="<?php echo htmlspecialchars($_POST['date_of_birth'] ?? $staff['date_of_birth']); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="national_id" class="form-label">الرقم القومي</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" 
                                       value="<?php echo htmlspecialchars($_POST['national_id'] ?? $staff['national_id']); ?>" 
                                       pattern="[0-9]{14}" maxlength="14">
                                <div class="form-text">14 رقم</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? $staff['phone']); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="2"><?php echo htmlspecialchars($_POST['address'] ?? $staff['address']); ?></textarea>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-key me-2"></i>معلومات الحساب
                                </h6>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? $staff['username']); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? $staff['email']); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صالح</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="user_status" class="form-label">حالة الحساب</label>
                                <select class="form-select" id="user_status" name="user_status">
                                    <option value="active" <?php echo ($_POST['user_status'] ?? $staff['user_status']) === 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($_POST['user_status'] ?? $staff['user_status']) === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                    <option value="suspended" <?php echo ($_POST['user_status'] ?? $staff['user_status']) === 'suspended' ? 'selected' : ''; ?>>معلق</option>
                                </select>
                            </div>
                        </div>

                        <!-- Job Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-briefcase me-2"></i>المعلومات الوظيفية
                                </h6>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="employee_id" class="form-label">رقم الموظف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employee_id" name="employee_id"
                                       value="<?php echo htmlspecialchars($_POST['employee_id'] ?? $staff['employee_id']); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال رقم الموظف</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="hire_date" class="form-label">تاريخ التوظيف</label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date"
                                       value="<?php echo htmlspecialchars($_POST['hire_date'] ?? $staff['hire_date']); ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="department" name="department"
                                       value="<?php echo htmlspecialchars($_POST['department'] ?? $staff['department']); ?>"
                                       placeholder="مثل: الشؤون الإدارية، المحاسبة، الموارد البشرية">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">المنصب</label>
                                <input type="text" class="form-control" id="position" name="position"
                                       value="<?php echo htmlspecialchars($_POST['position'] ?? $staff['position']); ?>"
                                       placeholder="مثل: مدير إداري، محاسب، سكرتير">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="salary" class="form-label">الراتب</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="salary" name="salary"
                                           value="<?php echo htmlspecialchars($_POST['salary'] ?? $staff['salary']); ?>"
                                           min="0" step="0.01">
                                    <span class="input-group-text">جنيه</span>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="staff_status" class="form-label">حالة الوظيفة</label>
                                <select class="form-select" id="staff_status" name="staff_status">
                                    <option value="active" <?php echo ($_POST['staff_status'] ?? $staff['status']) === 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($_POST['staff_status'] ?? $staff['status']) === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                    <option value="on_leave" <?php echo ($_POST['staff_status'] ?? $staff['status']) === 'on_leave' ? 'selected' : ''; ?>>في إجازة</option>
                                    <option value="terminated" <?php echo ($_POST['staff_status'] ?? $staff['status']) === 'terminated' ? 'selected' : ''; ?>>منتهي الخدمة</option>
                                </select>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="view.php?id=<?php echo $staff_id; ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// National ID validation
document.getElementById('national_id').addEventListener('input', function() {
    const value = this.value;
    if (value && !/^[0-9]{14}$/.test(value)) {
        this.setCustomValidity('الرقم القومي يجب أن يكون 14 رقم');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
