<?php
/**
 * صفحة تكليفات المعلم
 * Teacher Assignments Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

// دالة للتحقق من صحة البيانات
function validate_assignment_data($conn, $teacher_id, $subject_id) {
    $errors = [];

    // التحقق من وجود المعلم
    $teacher_check = $conn->prepare("SELECT id FROM teachers WHERE id = ?");
    if ($teacher_check) {
        $teacher_check->bind_param('i', $teacher_id);
        $teacher_check->execute();
        if (!$teacher_check->get_result()->fetch_assoc()) {
            $errors[] = 'المعلم المحدد غير موجود';
        }
        $teacher_check->close();
    }

    // التحقق من وجود المادة
    $subject_check = $conn->prepare("SELECT id FROM subjects WHERE id = ? AND status = 'active'");
    if ($subject_check) {
        $subject_check->bind_param('i', $subject_id);
        $subject_check->execute();
        if (!$subject_check->get_result()->fetch_assoc()) {
            $errors[] = 'المادة المحددة غير موجودة أو غير نشطة';
        }
        $subject_check->close();
    }

    // التحقق من عدم وجود التكليف مسبقاً
    $existing_check = $conn->prepare("SELECT id FROM teacher_assignments WHERE teacher_id = ? AND subject_id = ?");
    if ($existing_check) {
        $existing_check->bind_param('ii', $teacher_id, $subject_id);
        $existing_check->execute();
        if ($existing_check->get_result()->fetch_assoc()) {
            $errors[] = 'هذا التكليف موجود مسبقاً';
        }
        $existing_check->close();
    }

    return $errors;
}

// الحصول على معرف المعلم
$teacher_id = isset($_GET['teacher_id']) ? intval($_GET['teacher_id']) : 0;

if ($teacher_id <= 0) {
    $_SESSION['error_message'] = 'معرف المعلم غير صحيح';
    redirect_to('index.php');
}

// التحقق من وجود جدول teacher_assignments وإنشاؤه إذا لم يكن موجوداً
$check_table = $conn->query("SHOW TABLES LIKE 'teacher_assignments'");
if ($check_table && $check_table->num_rows == 0) {
    $create_table_sql = "
        CREATE TABLE IF NOT EXISTS `teacher_assignments` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `teacher_id` int(10) UNSIGNED NOT NULL,
            `subject_id` int(10) UNSIGNED NOT NULL,
            `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_teacher_subject` (`teacher_id`, `subject_id`),
            KEY `idx_teacher_assignments_teacher` (`teacher_id`),
            KEY `idx_teacher_assignments_subject` (`subject_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $result = $conn->query($create_table_sql);
    if (!$result) {
        // إذا فشل إنشاء الجدول، نحاول بدون UNIQUE KEY
        $simple_create_sql = "
            CREATE TABLE IF NOT EXISTS `teacher_assignments` (
                `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                `teacher_id` int(10) UNSIGNED NOT NULL,
                `subject_id` int(10) UNSIGNED NOT NULL,
                `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        if (!$conn->query($simple_create_sql)) {
            $_SESSION['error_message'] = 'خطأ في إنشاء جدول التكليفات: ' . $conn->error;
            redirect_to('index.php');
        }
    }
} else {
    // إذا كان الجدول موجود، تحقق من وجود مفاتيح أجنبية وحذفها
    $fk_check = $conn->query("
        SELECT CONSTRAINT_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'teacher_assignments'
    ");

    if ($fk_check && $fk_check->num_rows > 0) {
        while ($fk = $fk_check->fetch_assoc()) {
            $drop_fk_sql = "ALTER TABLE teacher_assignments DROP FOREIGN KEY " . $fk['CONSTRAINT_NAME'];
            $conn->query($drop_fk_sql); // تجاهل الأخطاء
        }
    }
}

// جلب بيانات المعلم
$teacher_query = "
    SELECT t.*, u.username, u.email, COALESCE(u.full_name, u.username) as full_name
    FROM teachers t
    INNER JOIN users u ON t.user_id = u.id
    WHERE t.id = ?
";

$stmt = $conn->prepare($teacher_query);
if (!$stmt) {
    $_SESSION['error_message'] = 'خطأ في قاعدة البيانات';
    redirect_to('index.php');
}

$stmt->bind_param('i', $teacher_id);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();
$stmt->close();

if (!$teacher) {
    $_SESSION['error_message'] = 'المعلم غير موجود';
    redirect_to('index.php');
}

// معالجة إضافة تكليف جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_assignment'])) {
    $subject_id = intval($_POST['subject_id']);

    if ($subject_id > 0) {
        // التحقق من صحة البيانات
        $validation_errors = validate_assignment_data($conn, $teacher_id, $subject_id);

        if (!empty($validation_errors)) {
            $_SESSION['error_message'] = implode('<br>', $validation_errors);
        } else {
            // إضافة التكليف الجديد
            $insert_query = "INSERT INTO teacher_assignments (teacher_id, subject_id, assigned_at) VALUES (?, ?, NOW())";
            $stmt = $conn->prepare($insert_query);

            if ($stmt) {
                $stmt->bind_param('ii', $teacher_id, $subject_id);

                if ($stmt->execute()) {
                    $_SESSION['success_message'] = 'تم إضافة التكليف بنجاح';
                } else {
                    $_SESSION['error_message'] = 'خطأ في إضافة التكليف: ' . $conn->error;
                    // إضافة معلومات إضافية للتشخيص
                    error_log("Teacher ID: $teacher_id, Subject ID: $subject_id, Error: " . $conn->error);
                }
                $stmt->close();
            } else {
                $_SESSION['error_message'] = 'خطأ في تحضير الاستعلام: ' . $conn->error;
            }
        }
    } else {
        $_SESSION['error_message'] = 'يرجى اختيار مادة صحيحة';
    }
    
    header('Location: assignments.php?teacher_id=' . $teacher_id);
    exit();
}

// معالجة حذف تكليف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['remove_assignment'])) {
    $assignment_id = intval($_POST['assignment_id']);

    if ($assignment_id > 0) {
        // التحقق من وجود التكليف أولاً
        $check_query = "SELECT id FROM teacher_assignments WHERE id = ? AND teacher_id = ?";
        $stmt = $conn->prepare($check_query);
        $stmt->bind_param('ii', $assignment_id, $teacher_id);
        $stmt->execute();
        $exists = $stmt->get_result()->fetch_assoc();
        $stmt->close();

        if ($exists) {
            $delete_query = "DELETE FROM teacher_assignments WHERE id = ? AND teacher_id = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param('ii', $assignment_id, $teacher_id);

            if ($stmt->execute()) {
                $_SESSION['success_message'] = 'تم حذف التكليف بنجاح';
            } else {
                $_SESSION['error_message'] = 'خطأ في حذف التكليف: ' . $conn->error;
            }
            $stmt->close();
        } else {
            $_SESSION['error_message'] = 'التكليف غير موجود';
        }
    } else {
        $_SESSION['error_message'] = 'معرف التكليف غير صحيح';
    }

    header('Location: assignments.php?teacher_id=' . $teacher_id);
    exit();
}

// جلب التكليفات الحالية
$assignments_query = "
    SELECT 
        ta.id as assignment_id,
        ta.assigned_at,
        s.subject_name,
        s.subject_code,
        s.credit_hours,
        g.grade_name,
        es.stage_name
    FROM teacher_assignments ta
    INNER JOIN subjects s ON ta.subject_id = s.id
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    WHERE ta.teacher_id = ?
    ORDER BY s.subject_name
";

$stmt = $conn->prepare($assignments_query);
$assignments = [];
if ($stmt) {
    $stmt->bind_param('i', $teacher_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $assignments[] = $row;
    }
    $stmt->close();
}

// جلب المواد المتاحة للتكليف
$available_subjects_query = "
    SELECT 
        s.id,
        s.subject_name,
        s.subject_code,
        g.grade_name,
        es.stage_name
    FROM subjects s
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    WHERE s.status = 'active'
    AND s.id NOT IN (
        SELECT subject_id FROM teacher_assignments WHERE teacher_id = ?
    )
    ORDER BY es.sort_order, g.sort_order, s.subject_name
";

$stmt = $conn->prepare($available_subjects_query);
$available_subjects = [];
if ($stmt) {
    $stmt->bind_param('i', $teacher_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $available_subjects[] = $row;
    }
    $stmt->close();
}

$page_title = 'تكليفات المعلم: ' . ($teacher['full_name'] ?? $teacher['username']);
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-tasks text-primary me-2"></i>
                تكليفات المعلم
            </h2>
            <p class="text-muted mb-0"><?php echo htmlspecialchars($teacher['full_name'] ?? $teacher['username']); ?></p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $teacher_id; ?>" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i>عرض المعلم
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للمعلمين
            </a>
        </div>
    </div>

    <div class="row">
        <!-- إضافة تكليف جديد -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>إضافة تكليف جديد
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($available_subjects)): ?>
                        <form method="POST">
                            <div class="mb-3">
                                <label for="subject_id" class="form-label">اختر المادة</label>
                                <select class="form-select" id="subject_id" name="subject_id" required>
                                    <option value="">-- اختر المادة --</option>
                                    <?php foreach ($available_subjects as $subject): ?>
                                        <option value="<?php echo $subject['id']; ?>">
                                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                                            (<?php echo htmlspecialchars($subject['subject_code']); ?>)
                                            <?php if ($subject['grade_name']): ?>
                                                - <?php echo htmlspecialchars($subject['grade_name']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <button type="submit" name="add_assignment" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة التكليف
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">جميع المواد مُكلف بها هذا المعلم</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- التكليفات الحالية -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>التكليفات الحالية
                        <span class="badge bg-primary"><?php echo count($assignments); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($assignments)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تكليفات</h5>
                            <p class="text-muted">لم يتم تكليف هذا المعلم بأي مواد بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المادة</th>
                                        <th>رمز المادة</th>
                                        <th>الصف</th>
                                        <th>المرحلة</th>
                                        <th>الساعات</th>
                                        <th>تاريخ التكليف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assignments as $assignment): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($assignment['subject_name']); ?></strong>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($assignment['subject_code']); ?></code>
                                            </td>
                                            <td><?php echo htmlspecialchars($assignment['grade_name'] ?: '-'); ?></td>
                                            <td><?php echo htmlspecialchars($assignment['stage_name'] ?: '-'); ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $assignment['credit_hours']; ?></span>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($assignment['assigned_at'])); ?></td>
                                            <td>
                                                <form method="POST" style="display: inline;" 
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا التكليف؟')">
                                                    <input type="hidden" name="assignment_id" 
                                                           value="<?php echo $assignment['assignment_id']; ?>">
                                                    <button type="submit" name="remove_assignment" 
                                                            class="btn btn-outline-danger btn-sm"
                                                            title="حذف التكليف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
