-- إصلاح جدول deduction_settings وإضافة البيانات الأساسية
-- Fix deduction_settings table and add basic data

USE school_management;

-- حذف البيانات الموجودة إذا كانت هناك أي بيانات خاطئة
DELETE FROM deduction_settings;

-- إدراج إعدادات الخصم الأساسية
INSERT INTO deduction_settings (
    absence_type, 
    deduction_type, 
    deduction_value, 
    max_allowed_per_month, 
    requires_approval, 
    description, 
    is_active
) VALUES 
-- إجازة مرضية - بدون خصم عادة
('sick', 'fixed', 0.00, 30, 1, 'إجازة مرضية - عادة بدون خصم مع تقرير طبي', 1),

-- إجازة شخصية - خصم جزئي
('personal', 'daily_rate', 50.00, 5, 1, 'إجازة شخصية - خصم 50 ريال لليوم الواحد', 1),

-- إجازة طارئة - خصم أقل
('emergency', 'daily_rate', 25.00, 3, 1, 'إجازة طارئة - خصم 25 ريال لليوم الواحد', 1),

-- غياب غير مبرر - خصم كامل
('unauthorized', 'daily_rate', 100.00, 0, 0, 'غياب غير مبرر - خصم 100 ريال لليوم الواحد', 1);

-- إنشاء جدول إعدادات عامة للنظام إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS system_settings (
    id int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    setting_key varchar(100) NOT NULL,
    setting_value text,
    setting_type enum('string','number','boolean','json') DEFAULT 'string',
    description text,
    category varchar(50) DEFAULT 'general',
    is_public tinyint(1) DEFAULT 0,
    created_at timestamp NOT NULL DEFAULT current_timestamp(),
    updated_at timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (id),
    UNIQUE KEY setting_key (setting_key),
    KEY idx_category (category),
    KEY idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج إعدادات الحضور والغياب في جدول system_settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, category) VALUES
-- إعدادات الخصم
('daily_absence_deduction', '100', 'number', 'مبلغ الخصم اليومي للغياب غير المبرر', 'attendance'),
('hourly_absence_deduction', '15', 'number', 'مبلغ الخصم للساعة الواحدة', 'attendance'),
('sick_leave_deduction', '0', 'number', 'مبلغ الخصم للإجازة المرضية', 'attendance'),
('personal_leave_deduction', '50', 'number', 'مبلغ الخصم للإجازة الشخصية', 'attendance'),
('emergency_leave_deduction', '25', 'number', 'مبلغ الخصم للإجازة الطارئة', 'attendance'),

-- إعدادات أوقات العمل
('work_start_time', '08:00', 'string', 'وقت بداية العمل', 'attendance'),
('work_end_time', '16:00', 'string', 'وقت نهاية العمل', 'attendance'),
('break_start_time', '12:00', 'string', 'وقت بداية الاستراحة', 'attendance'),
('break_end_time', '13:00', 'string', 'وقت نهاية الاستراحة', 'attendance'),
('late_tolerance_minutes', '15', 'number', 'عدد الدقائق المسموحة للتأخير', 'attendance'),

-- إعدادات الإجازات
('max_sick_days_per_month', '7', 'number', 'الحد الأقصى لأيام الإجازة المرضية شهرياً', 'attendance'),
('max_personal_days_per_month', '3', 'number', 'الحد الأقصى لأيام الإجازة الشخصية شهرياً', 'attendance'),
('max_emergency_days_per_month', '2', 'number', 'الحد الأقصى لأيام الإجازة الطارئة شهرياً', 'attendance'),

-- إعدادات التنبيهات
('attendance_notification_enabled', '1', 'boolean', 'تفعيل تنبيهات الحضور', 'attendance'),
('late_notification_enabled', '1', 'boolean', 'تفعيل تنبيهات التأخير', 'attendance'),
('absence_notification_enabled', '1', 'boolean', 'تفعيل تنبيهات الغياب', 'attendance'),

-- إعدادات التقارير
('attendance_report_period', 'monthly', 'string', 'فترة تقارير الحضور (daily/weekly/monthly)', 'attendance'),
('auto_generate_reports', '1', 'boolean', 'إنشاء التقارير تلقائياً', 'attendance');

-- إنشاء view لتسهيل الوصول لإعدادات الخصم
CREATE OR REPLACE VIEW deduction_settings_view AS
SELECT 
    ds.*,
    CASE 
        WHEN ds.absence_type = 'sick' THEN 'إجازة مرضية'
        WHEN ds.absence_type = 'personal' THEN 'إجازة شخصية'
        WHEN ds.absence_type = 'emergency' THEN 'إجازة طارئة'
        WHEN ds.absence_type = 'unauthorized' THEN 'غياب غير مبرر'
        ELSE ds.absence_type
    END as absence_type_ar,
    CASE 
        WHEN ds.deduction_type = 'fixed' THEN 'مبلغ ثابت'
        WHEN ds.deduction_type = 'percentage' THEN 'نسبة مئوية'
        WHEN ds.deduction_type = 'daily_rate' THEN 'معدل يومي'
        ELSE ds.deduction_type
    END as deduction_type_ar
FROM deduction_settings ds
WHERE ds.is_active = 1;

-- إنشاء دالة للحصول على قيمة الخصم حسب نوع الغياب
DELIMITER //

CREATE FUNCTION IF NOT EXISTS get_deduction_amount(
    absence_type_param VARCHAR(50),
    days_count INT
) RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE deduction_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE deduction_value DECIMAL(10,2) DEFAULT 0.00;
    DECLARE deduction_type VARCHAR(20) DEFAULT 'fixed';
    
    -- جلب إعدادات الخصم
    SELECT ds.deduction_value, ds.deduction_type 
    INTO deduction_value, deduction_type
    FROM deduction_settings ds 
    WHERE ds.absence_type = absence_type_param 
    AND ds.is_active = 1 
    LIMIT 1;
    
    -- حساب مبلغ الخصم
    IF deduction_type = 'fixed' THEN
        SET deduction_amount = deduction_value;
    ELSEIF deduction_type = 'daily_rate' THEN
        SET deduction_amount = deduction_value * days_count;
    ELSEIF deduction_type = 'percentage' THEN
        -- يمكن تطوير هذا لاحقاً لحساب النسبة من الراتب
        SET deduction_amount = deduction_value;
    END IF;
    
    RETURN deduction_amount;
END//

DELIMITER ;

-- إنشاء إجراء مخزن لتسجيل غياب مع خصم
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS record_absence_with_deduction(
    IN p_user_id INT,
    IN p_absence_date DATE,
    IN p_absence_type VARCHAR(50),
    IN p_days_count INT,
    IN p_reason TEXT,
    IN p_recorded_by INT
)
BEGIN
    DECLARE deduction_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE absence_id INT;
    
    -- حساب مبلغ الخصم
    SET deduction_amount = get_deduction_amount(p_absence_type, p_days_count);
    
    -- تسجيل الغياب في جدول staff_attendance
    INSERT INTO staff_attendance (
        user_id, attendance_date, status, notes, recorded_by
    ) VALUES (
        p_user_id, p_absence_date, p_absence_type, p_reason, p_recorded_by
    )
    ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        notes = VALUES(notes),
        updated_at = CURRENT_TIMESTAMP;
    
    -- تسجيل الخصم إذا كان هناك مبلغ خصم
    IF deduction_amount > 0 THEN
        INSERT INTO staff_absences_with_deduction (
            user_id, absence_date, absence_type, days_count, 
            deduction_amount, reason, recorded_by
        ) VALUES (
            p_user_id, p_absence_date, p_absence_type, p_days_count,
            deduction_amount, p_reason, p_recorded_by
        )
        ON DUPLICATE KEY UPDATE
            deduction_amount = VALUES(deduction_amount),
            reason = VALUES(reason),
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
END//

DELIMITER ;

-- إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_deduction_settings_type_active ON deduction_settings(absence_type, is_active);
CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category);

SELECT 'Deduction settings fixed successfully!' as message;
