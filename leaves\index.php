<?php
/**
 * صفحة إدارة الإجازات الرئيسية
 * Main Leave Management Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];

// جلب إحصائيات الإجازات
$stats_query = "
    SELECT 
        COUNT(*) as total_leaves,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_leaves,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_leaves,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_leaves,
        SUM(CASE WHEN status = 'approved' THEN total_days ELSE 0 END) as total_approved_days
    FROM staff_leaves
    WHERE YEAR(created_at) = YEAR(CURDATE())
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// جلب الإجازات الحديثة
$recent_leaves_query = "
    SELECT sl.*, u.full_name, u.role
    FROM staff_leaves sl
    JOIN users u ON sl.user_id = u.id
    ORDER BY sl.created_at DESC
    LIMIT 10
";
$recent_leaves_result = $conn->query($recent_leaves_query);
?>

<div class="container-fluid">
    <!-- إحصائيات الإجازات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['total_leaves']; ?></h4>
                            <p class="mb-0">إجمالي الإجازات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['pending_leaves']; ?></h4>
                            <p class="mb-0">في الانتظار</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['approved_leaves']; ?></h4>
                            <p class="mb-0">موافق عليها</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['total_approved_days']; ?></h4>
                            <p class="mb-0">إجمالي الأيام</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>
                            إدارة الإجازات
                        </h5>
                        <div>
                            <a href="add.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-2"></i>طلب إجازة جديد
                            </a>
                            <a href="reports.php" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-bar me-2"></i>التقارير
                            </a>
                            <a href="../attendance/smart_attendance.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>رجوع للحضور
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h6>الإجازات الحديثة</h6>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($recent_leaves_result->num_rows > 0): ?>
                                    <?php while ($leave = $recent_leaves_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($leave['full_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo $leave['role']; ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $leave_types = [
                                                    'sick' => 'إجازة مرضية',
                                                    'regular' => 'إجازة اعتيادية',
                                                    'emergency' => 'إجازة طارئة'
                                                ];
                                                echo $leave_types[$leave['leave_type']] ?? $leave['leave_type'];
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['start_date'])); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['end_date'])); ?></td>
                                            <td><?php echo $leave['total_days']; ?> يوم</td>
                                            <td>
                                                <?php
                                                $status_badges = [
                                                    'pending' => 'warning',
                                                    'approved' => 'success',
                                                    'rejected' => 'danger'
                                                ];
                                                $status_text = [
                                                    'pending' => 'في الانتظار',
                                                    'approved' => 'موافق عليها',
                                                    'rejected' => 'مرفوضة'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_badges[$leave['status']]; ?>">
                                                    <?php echo $status_text[$leave['status']]; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <!-- زر العرض متاح للجميع -->
                                                    <a href="../attendance/view_leave.php?id=<?php echo $leave['id']; ?>" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>

                                                    <?php if (check_permission('admin')): ?>
                                                        <!-- زر التعديل -->
                                                        <a href="../attendance/edit_leave.php?id=<?php echo $leave['id']; ?>" class="btn btn-warning btn-sm" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>

                                                        <!-- زر الحذف -->
                                                        <a href="../attendance/delete_leave.php?id=<?php echo $leave['id']; ?>"
                                                           class="btn btn-danger btn-sm"
                                                           title="حذف"
                                                           onclick="return confirm('هل أنت متأكد من حذف هذه الإجازة؟\n\nالموظف: <?php echo htmlspecialchars($leave['full_name']); ?>\nالنوع: <?php echo $leave_types[$leave['leave_type']] ?? $leave['leave_type']; ?>\nالفترة: <?php echo date('Y-m-d', strtotime($leave['start_date'])) . ' إلى ' . date('Y-m-d', strtotime($leave['end_date'])); ?>\n\nهذا الإجراء لا يمكن التراجع عنه.')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center">لا توجد إجازات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="text-center mt-3">
                        <a href="../attendance/manage_leaves.php" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>عرض جميع الإجازات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
