<?php
/**
 * عرض تفاصيل الإجازة
 * View Leave Details
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$leave_id = intval($_GET['id'] ?? 0);

if (!$leave_id) {
    header('Location: manage_leaves.php');
    exit();
}

// جلب تفاصيل الإجازة
$query = "
    SELECT sl.*, u.full_name, u.role,
           approver.full_name as approved_by_name,
           applier.full_name as applied_by_name
    FROM staff_leaves sl
    JOIN users u ON sl.user_id = u.id
    LEFT JOIN users approver ON sl.approved_by = approver.id
    LEFT JOIN users applier ON sl.applied_by = applier.id
    WHERE sl.id = ?
";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $leave_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: manage_leaves.php');
    exit();
}

$leave = $result->fetch_assoc();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            تفاصيل الإجازة
                        </h5>
                        <a href="manage_leaves.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>رجوع
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">الموظف:</th>
                                    <td><?php echo htmlspecialchars($leave['full_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>المنصب:</th>
                                    <td><?php echo $leave['role']; ?></td>
                                </tr>
                                <tr>
                                    <th>نوع الإجازة:</th>
                                    <td>
                                        <?php
                                        $leave_types = [
                                            'sick' => 'إجازة مرضية',
                                            'regular' => 'إجازة اعتيادية',
                                            'emergency' => 'إجازة طارئة'
                                        ];
                                        echo $leave_types[$leave['leave_type']] ?? $leave['leave_type'];
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ البداية:</th>
                                    <td><?php echo date('Y-m-d', strtotime($leave['start_date'])); ?></td>
                                </tr>
                                <tr>
                                    <th>تاريخ النهاية:</th>
                                    <td><?php echo date('Y-m-d', strtotime($leave['end_date'])); ?></td>
                                </tr>
                                <tr>
                                    <th>عدد الأيام:</th>
                                    <td><?php echo $leave['total_days']; ?> يوم</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">الحالة:</th>
                                    <td>
                                        <?php
                                        $status_badges = [
                                            'pending' => 'warning',
                                            'approved' => 'success',
                                            'rejected' => 'danger'
                                        ];
                                        $status_text = [
                                            'pending' => 'في الانتظار',
                                            'approved' => 'موافق عليها',
                                            'rejected' => 'مرفوضة'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $status_badges[$leave['status']]; ?>">
                                            <?php echo $status_text[$leave['status']]; ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ الطلب:</th>
                                    <td><?php echo date('Y-m-d H:i', strtotime($leave['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <th>مقدم الطلب:</th>
                                    <td><?php echo htmlspecialchars($leave['applied_by_name'] ?? 'غير محدد'); ?></td>
                                </tr>
                                <?php if ($leave['approved_by_name']): ?>
                                <tr>
                                    <th>تمت الموافقة بواسطة:</th>
                                    <td><?php echo htmlspecialchars($leave['approved_by_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>تاريخ الموافقة:</th>
                                    <td><?php echo $leave['approved_at'] ? date('Y-m-d H:i', strtotime($leave['approved_at'])) : '-'; ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($leave['reason'])): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>السبب:</h6>
                            <div class="alert alert-light">
                                <?php echo nl2br(htmlspecialchars($leave['reason'])); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (check_permission('admin')): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <hr>
                            <h6>إجراءات الإدارة:</h6>
                            <div class="d-flex flex-wrap gap-2">
                                <?php if ($leave['status'] === 'pending'): ?>
                                    <!-- أزرار الموافقة والرفض للإجازات المعلقة -->
                                    <form method="POST" action="manage_leaves.php" class="d-inline">
                                        <?php echo csrf_token_field(); ?>
                                        <input type="hidden" name="leave_id" value="<?php echo $leave['id']; ?>">
                                        <button type="submit" name="action" value="approve" class="btn btn-success">
                                            <i class="fas fa-check me-2"></i>الموافقة على الإجازة
                                        </button>
                                        <button type="submit" name="action" value="reject" class="btn btn-outline-danger">
                                            <i class="fas fa-times me-2"></i>رفض الإجازة
                                        </button>
                                    </form>
                                <?php endif; ?>

                                <!-- زر التعديل -->
                                <a href="edit_leave.php?id=<?php echo $leave['id']; ?>" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>تعديل الإجازة
                                </a>

                                <!-- زر الحذف -->
                                <a href="delete_leave.php?id=<?php echo $leave['id']; ?>"
                                   class="btn btn-danger"
                                   onclick="return confirm('هل أنت متأكد من حذف هذه الإجازة؟\n\nهذا الإجراء لا يمكن التراجع عنه.')">
                                    <i class="fas fa-trash me-2"></i>حذف الإجازة
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
