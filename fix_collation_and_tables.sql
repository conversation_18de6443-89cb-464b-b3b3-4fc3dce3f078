-- إصلا<PERSON> مشاكل collation والجداول المفقودة
-- Fix collation issues and missing tables

USE school_management;

-- إصلاح collation لجدول deduction_settings
ALTER TABLE deduction_settings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح collation لجدول staff_absences_with_deduction
ALTER TABLE staff_absences_with_deduction CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إضافة العمود المفقود days_count إلى جدول staff_absences_with_deduction
ALTER TABLE staff_absences_with_deduction 
ADD COLUMN IF NOT EXISTS days_count INT(10) UNSIGNED DEFAULT 1 AFTER absence_type;

-- إضافة عمود deduction_type إذا لم يكن موجوداً
ALTER TABLE staff_absences_with_deduction 
ADD COLUMN IF NOT EXISTS deduction_type ENUM('fixed','percentage','daily_rate') DEFAULT 'daily_rate' AFTER days_count;

-- تحديث البيانات الموجودة
UPDATE staff_absences_with_deduction 
SET days_count = 1 
WHERE days_count IS NULL;

UPDATE staff_absences_with_deduction 
SET deduction_type = 'daily_rate' 
WHERE deduction_type IS NULL;

-- حذف الدالة القديمة وإعادة إنشائها بـ collation صحيح
DROP FUNCTION IF EXISTS get_deduction_amount;

DELIMITER //

CREATE FUNCTION get_deduction_amount(
    absence_type_param VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    days_count INT
) RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE deduction_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE deduction_value DECIMAL(10,2) DEFAULT 0.00;
    DECLARE deduction_type VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'fixed';
    
    -- جلب إعدادات الخصم
    SELECT ds.deduction_value, ds.deduction_type 
    INTO deduction_value, deduction_type
    FROM deduction_settings ds 
    WHERE ds.absence_type COLLATE utf8mb4_unicode_ci = absence_type_param COLLATE utf8mb4_unicode_ci
    AND ds.is_active = 1 
    LIMIT 1;
    
    -- حساب مبلغ الخصم
    IF deduction_type = 'fixed' THEN
        SET deduction_amount = deduction_value;
    ELSEIF deduction_type = 'daily_rate' THEN
        SET deduction_amount = deduction_value * days_count;
    ELSEIF deduction_type = 'percentage' THEN
        -- يمكن تطوير هذا لاحقاً لحساب النسبة من الراتب
        SET deduction_amount = deduction_value;
    END IF;
    
    RETURN deduction_amount;
END//

DELIMITER ;

-- حذف الإجراء القديم وإعادة إنشائه
DROP PROCEDURE IF EXISTS record_absence_with_deduction;

DELIMITER //

CREATE PROCEDURE record_absence_with_deduction(
    IN p_user_id INT,
    IN p_absence_date DATE,
    IN p_absence_type VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    IN p_days_count INT,
    IN p_reason TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    IN p_recorded_by INT
)
BEGIN
    DECLARE deduction_amount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE deduction_type_val VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'daily_rate';
    DECLARE absence_id INT;
    
    -- حساب مبلغ الخصم
    SET deduction_amount = get_deduction_amount(p_absence_type, p_days_count);
    
    -- جلب نوع الخصم
    SELECT ds.deduction_type INTO deduction_type_val
    FROM deduction_settings ds 
    WHERE ds.absence_type COLLATE utf8mb4_unicode_ci = p_absence_type COLLATE utf8mb4_unicode_ci
    AND ds.is_active = 1 
    LIMIT 1;
    
    -- تسجيل الغياب في جدول staff_attendance
    INSERT INTO staff_attendance (
        user_id, attendance_date, status, notes, recorded_by
    ) VALUES (
        p_user_id, p_absence_date, p_absence_type, p_reason, p_recorded_by
    )
    ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        notes = VALUES(notes),
        updated_at = CURRENT_TIMESTAMP;
    
    -- تسجيل الخصم
    INSERT INTO staff_absences_with_deduction (
        user_id, absence_date, absence_type, days_count, 
        deduction_type, deduction_amount, reason, recorded_by
    ) VALUES (
        p_user_id, p_absence_date, p_absence_type, p_days_count,
        deduction_type_val, deduction_amount, p_reason, p_recorded_by
    )
    ON DUPLICATE KEY UPDATE
        days_count = VALUES(days_count),
        deduction_type = VALUES(deduction_type),
        deduction_amount = VALUES(deduction_amount),
        reason = VALUES(reason),
        updated_at = CURRENT_TIMESTAMP;
    
END//

DELIMITER ;

-- تحديث الـ view لتتضمن الأعمدة الجديدة
DROP VIEW IF EXISTS deduction_settings_view;

CREATE VIEW deduction_settings_view AS
SELECT 
    ds.*,
    CASE 
        WHEN ds.absence_type = 'sick' THEN 'إجازة مرضية'
        WHEN ds.absence_type = 'personal' THEN 'إجازة شخصية'
        WHEN ds.absence_type = 'emergency' THEN 'إجازة طارئة'
        WHEN ds.absence_type = 'unauthorized' THEN 'غياب غير مبرر'
        ELSE ds.absence_type
    END as absence_type_ar,
    CASE 
        WHEN ds.deduction_type = 'fixed' THEN 'مبلغ ثابت'
        WHEN ds.deduction_type = 'percentage' THEN 'نسبة مئوية'
        WHEN ds.deduction_type = 'daily_rate' THEN 'معدل يومي'
        ELSE ds.deduction_type
    END as deduction_type_ar
FROM deduction_settings ds
WHERE ds.is_active = 1;

-- إنشاء view شاملة للغيابات مع الخصومات
CREATE OR REPLACE VIEW staff_absences_detailed AS
SELECT 
    sad.*,
    u.full_name as user_name,
    u.role as user_role,
    u.email as user_email,
    recorder.full_name as recorded_by_name,
    approver.full_name as approved_by_name,
    processor.full_name as processed_by_name,
    ds.description as absence_type_description,
    CASE 
        WHEN sad.absence_type = 'sick' THEN 'إجازة مرضية'
        WHEN sad.absence_type = 'personal' THEN 'إجازة شخصية'
        WHEN sad.absence_type = 'emergency' THEN 'إجازة طارئة'
        WHEN sad.absence_type = 'unauthorized' THEN 'غياب غير مبرر'
        ELSE sad.absence_type
    END as absence_type_ar,
    CASE 
        WHEN sad.deduction_type = 'fixed' THEN 'مبلغ ثابت'
        WHEN sad.deduction_type = 'percentage' THEN 'نسبة مئوية'
        WHEN sad.deduction_type = 'daily_rate' THEN 'معدل يومي'
        ELSE sad.deduction_type
    END as deduction_type_ar
FROM staff_absences_with_deduction sad
LEFT JOIN users u ON sad.user_id = u.id
LEFT JOIN users recorder ON sad.recorded_by = recorder.id
LEFT JOIN users approver ON sad.approved_by = approver.id
LEFT JOIN users processor ON sad.processed_by = processor.id
LEFT JOIN deduction_settings ds ON sad.absence_type = ds.absence_type AND ds.is_active = 1;

-- إضافة فهارس جديدة
CREATE INDEX IF NOT EXISTS idx_staff_absences_user_date ON staff_absences_with_deduction(user_id, absence_date);
CREATE INDEX IF NOT EXISTS idx_staff_absences_type_status ON staff_absences_with_deduction(absence_type, status);
CREATE INDEX IF NOT EXISTS idx_staff_absences_date_range ON staff_absences_with_deduction(absence_date);

-- إنشاء دالة لحساب إجمالي الخصومات لفترة معينة
DELIMITER //

CREATE FUNCTION IF NOT EXISTS get_total_deductions(
    p_user_id INT,
    p_start_date DATE,
    p_end_date DATE
) RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE total_deductions DECIMAL(10,2) DEFAULT 0.00;
    
    SELECT COALESCE(SUM(deduction_amount), 0.00) INTO total_deductions
    FROM staff_absences_with_deduction
    WHERE user_id = p_user_id
    AND absence_date BETWEEN p_start_date AND p_end_date
    AND status = 'approved';
    
    RETURN total_deductions;
END//

DELIMITER ;

-- إنشاء دالة لحساب عدد أيام الغياب حسب النوع
DELIMITER //

CREATE FUNCTION IF NOT EXISTS get_absence_days_count(
    p_user_id INT,
    p_absence_type VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    p_start_date DATE,
    p_end_date DATE
) RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE days_count INT DEFAULT 0;
    
    SELECT COALESCE(SUM(days_count), 0) INTO days_count
    FROM staff_absences_with_deduction
    WHERE user_id = p_user_id
    AND absence_type COLLATE utf8mb4_unicode_ci = p_absence_type COLLATE utf8mb4_unicode_ci
    AND absence_date BETWEEN p_start_date AND p_end_date
    AND status IN ('approved', 'pending');
    
    RETURN days_count;
END//

DELIMITER ;

-- تحديث البيانات الموجودة لتتوافق مع البنية الجديدة
UPDATE staff_absences_with_deduction sad
JOIN deduction_settings ds ON sad.absence_type = ds.absence_type
SET sad.deduction_type = ds.deduction_type,
    sad.deduction_amount = CASE 
        WHEN ds.deduction_type = 'daily_rate' THEN ds.deduction_value * sad.days_count
        ELSE ds.deduction_value
    END
WHERE sad.deduction_type IS NULL OR sad.deduction_amount = 0;

SELECT 'Collation and tables fixed successfully!' as message;
