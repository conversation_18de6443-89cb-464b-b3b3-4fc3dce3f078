<?php
/**
 * صفحة النظام المالي الرئيسية
 * Financial Management Main Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// الحصول على الإحصائيات المالية
global $conn;

// إحصائيات الرسوم
$fees_stats_query = "
    SELECT
        COUNT(*) as total_fees,
        SUM(final_amount) as total_amount,
        SUM(CASE WHEN status = 'paid' THEN final_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN status = 'pending' THEN final_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'overdue' THEN final_amount ELSE 0 END) as overdue_amount
    FROM student_fees
    WHERE YEAR(created_at) = YEAR(CURDATE())
";

$fees_result = $conn->query($fees_stats_query);
$fees_stats = $fees_result->fetch_assoc();

// التأكد من أن القيم ليست null
$fees_stats = [
    'total_fees' => $fees_stats['total_fees'] ?? 0,
    'total_amount' => $fees_stats['total_amount'] ?? 0,
    'paid_amount' => $fees_stats['paid_amount'] ?? 0,
    'pending_amount' => $fees_stats['pending_amount'] ?? 0,
    'overdue_amount' => $fees_stats['overdue_amount'] ?? 0
];

// إحصائيات المدفوعات
$payments_stats_query = "
    SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount,
        COUNT(CASE WHEN payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as recent_payments,
        SUM(CASE WHEN payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN amount ELSE 0 END) as recent_amount
    FROM student_payments 
    WHERE status = 'confirmed'
    AND YEAR(payment_date) = YEAR(CURDATE())
";

$payments_result = $conn->query($payments_stats_query);
$payments_stats = $payments_result->fetch_assoc();

// التأكد من أن القيم ليست null
$payments_stats = [
    'total_payments' => $payments_stats['total_payments'] ?? 0,
    'total_amount' => $payments_stats['total_amount'] ?? 0,
    'recent_payments' => $payments_stats['recent_payments'] ?? 0,
    'recent_amount' => $payments_stats['recent_amount'] ?? 0
];

// إحصائيات الكتب
$books_stats_query = "
    SELECT
        COUNT(DISTINCT sbo.id) as total_orders,
        SUM(sbo.final_amount) as total_amount,
        COUNT(CASE WHEN sbo.status = 'delivered' THEN 1 END) as delivered_orders,
        COUNT(CASE WHEN sbo.status = 'pending' THEN 1 END) as pending_orders
    FROM student_book_orders sbo
    WHERE YEAR(sbo.created_at) = YEAR(CURDATE())
";

$books_result = $conn->query($books_stats_query);
$books_stats = $books_result ? $books_result->fetch_assoc() : [];

// التأكد من أن القيم ليست null
$books_stats = [
    'total_orders' => $books_stats['total_orders'] ?? 0,
    'total_amount' => $books_stats['total_amount'] ?? 0,
    'delivered_orders' => $books_stats['delivered_orders'] ?? 0,
    'pending_orders' => $books_stats['pending_orders'] ?? 0
];

// إحصائيات الأقساط
$installments_stats_query = "
    SELECT 
        COUNT(*) as total_installments,
        SUM(amount) as total_amount,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_installments,
        COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_installments,
        SUM(CASE WHEN status = 'overdue' THEN amount ELSE 0 END) as overdue_amount
    FROM student_installments
    WHERE YEAR(due_date) = YEAR(CURDATE())
";

$installments_result = $conn->query($installments_stats_query);
$installments_stats = $installments_result ? $installments_result->fetch_assoc() : [];

// التأكد من أن القيم ليست null
$installments_stats = [
    'total_installments' => $installments_stats['total_installments'] ?? 0,
    'total_amount' => $installments_stats['total_amount'] ?? 0,
    'paid_installments' => $installments_stats['paid_installments'] ?? 0,
    'overdue_installments' => $installments_stats['overdue_installments'] ?? 0,
    'overdue_amount' => $installments_stats['overdue_amount'] ?? 0
];

// الحصول على آخر المعاملات
$recent_transactions_query = "
    SELECT
        sp.id,
        sp.payment_reference,
        sp.amount,
        sp.payment_date,
        sp.payment_method,
        u.full_name as student_name,
        s.student_id as student_number,
        s.id as student_id
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    WHERE sp.status = 'confirmed'
    ORDER BY sp.payment_date DESC, sp.id DESC
    LIMIT 10
";

$recent_transactions = $conn->query($recent_transactions_query);

// الحصول على الرسوم المتأخرة
$overdue_fees_query = "
    SELECT
        sf.id,
        sf.final_amount,
        sf.created_at as fee_date,
        DATEDIFF(CURDATE(), sf.created_at) as days_since_created,
        u.full_name as student_name,
        s.id as student_id,
        ft.type_name
    FROM student_fees sf
    JOIN students s ON sf.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    WHERE sf.status IN ('pending', 'partial')
    ORDER BY sf.created_at ASC
    LIMIT 10
";

$overdue_fees = $conn->query($overdue_fees_query);

// حساب النسب المئوية
$collection_rate = $fees_stats['total_amount'] > 0 ? 
    ($fees_stats['paid_amount'] / $fees_stats['total_amount']) * 100 : 0;

$installment_payment_rate = $installments_stats['total_installments'] > 0 ?
    ($installments_stats['paid_installments'] / $installments_stats['total_installments']) * 100 : 0;

include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('financial_management'); ?></h1>
            <p class="text-muted"><?php echo __('comprehensive_financial_overview'); ?></p>
        </div>
        <div>
            <div class="btn-group">
                <a href="fee_types/" class="btn btn-primary">
                    <i class="fas fa-tags me-2"></i>إدارة أنواع الرسوم
                </a>
                <a href="installments/" class="btn btn-success">
                    <i class="fas fa-calendar-alt me-2"></i>إدارة الأقساط
                </a>
                <a href="expenses/" class="btn btn-warning">
                    <i class="fas fa-money-bill-wave me-2"></i>المصروفات اليومية
                </a>
                <a href="../reports/financial.php" class="btn btn-info">
                    <i class="fas fa-chart-bar me-2"></i><?php echo __('financial_reports'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Financial Overview Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-money-bill-wave text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($fees_stats['total_amount'], 2); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_fees'); ?></p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>
                                <?php echo number_format($collection_rate, 1); ?>% <?php echo __('collected'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-check-circle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($fees_stats['paid_amount'], 2); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('collected_amount'); ?></p>
                            <small class="text-info">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo __('this_year'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-clock text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($fees_stats['pending_amount'], 2); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('pending_amount'); ?></p>
                            <small class="text-warning">
                                <i class="fas fa-hourglass-half me-1"></i>
                                <?php echo __('awaiting_payment'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-gradient p-3 rounded-3">
                                <i class="fas fa-exclamation-triangle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($fees_stats['overdue_amount'], 2); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('overdue_amount'); ?></p>
                            <small class="text-danger">
                                <i class="fas fa-exclamation-circle me-1"></i>
                                <?php echo __('requires_attention'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-primary mb-2">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <h4 class="mb-0"><?php echo number_format($payments_stats['total_payments']); ?></h4>
                    <p class="text-muted mb-0"><?php echo __('total_payments'); ?></p>
                    <small class="text-success">
                        <?php echo number_format($payments_stats['recent_payments']); ?> <?php echo __('this_month'); ?>
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-info mb-2">
                        <i class="fas fa-book"></i>
                    </div>
                    <h4 class="mb-0"><?php echo number_format($books_stats['total_orders']); ?></h4>
                    <p class="text-muted mb-0"><?php echo __('book_orders'); ?></p>
                    <small class="text-info">
                        <?php echo number_format($books_stats['delivered_orders']); ?> <?php echo __('delivered'); ?>
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-warning mb-2">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h4 class="mb-0"><?php echo number_format($installment_payment_rate, 1); ?>%</h4>
                    <p class="text-muted mb-0"><?php echo __('installment_rate'); ?></p>
                    <small class="text-warning">
                        <?php echo number_format($installments_stats['overdue_installments']); ?> <?php echo __('overdue'); ?>
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-success mb-2">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="mb-0"><?php echo number_format($payments_stats['recent_amount'], 2); ?></h4>
                    <p class="text-muted mb-0"><?php echo __('monthly_revenue'); ?></p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i><?php echo __('last_30_days'); ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Transactions -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i><?php echo __('recent_transactions'); ?>
                    </h5>
                    <a href="payments/detailed_list.php" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($recent_transactions->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th><?php echo __('reference'); ?></th>
                                        <th><?php echo __('student'); ?></th>
                                        <th><?php echo __('amount'); ?></th>
                                        <th><?php echo __('method'); ?></th>
                                        <th><?php echo __('date'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo htmlspecialchars($transaction['payment_reference']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($transaction['student_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($transaction['student_number']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">
                                                    <?php echo number_format($transaction['amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $method_icons = [
                                                    'cash' => 'fas fa-money-bill',
                                                    'bank_transfer' => 'fas fa-university',
                                                    'check' => 'fas fa-money-check',
                                                    'card' => 'fas fa-credit-card',
                                                    'online' => 'fas fa-globe'
                                                ];
                                                $icon = $method_icons[$transaction['payment_method']] ?? 'fas fa-question';
                                                ?>
                                                <i class="<?php echo $icon; ?> me-1"></i>
                                                <?php echo __(strtolower($transaction['payment_method'])); ?>
                                            </td>
                                            <td>
                                                <?php echo format_date($transaction['payment_date']); ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo __('no_recent_transactions'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Overdue Fees -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i><?php echo __('overdue_fees'); ?>
                    </h5>
                    <a href="student_finance.php" class="btn btn-sm btn-outline-danger">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($overdue_fees->num_rows > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php while ($fee = $overdue_fees->fetch_assoc()): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($fee['student_name']); ?></h6>
                                            <p class="mb-1 small text-muted">
                                                <?php echo htmlspecialchars($fee['type_name']); ?>
                                            </p>
                                            <small class="text-danger">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo $fee['days_overdue']; ?> <?php echo __('days_overdue'); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="fw-bold text-danger">
                                                <?php echo number_format($fee['final_amount'], 2); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted"><?php echo __('no_overdue_fees'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i><?php echo __('quick_actions'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-4 col-6 mb-3">
                            <a href="fee_types/" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-tags fa-2x mb-2"></i>
                                <span>إدارة أنواع الرسوم</span>
                                <small class="text-white-50">أساسي</small>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-4 col-6 mb-3">
                            <a href="payments/detailed_list.php" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-list-alt fa-2x mb-2"></i>
                                <span>قائمة المدفوعات</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-4 col-6 mb-3">
                            <a href="installments/" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                <span><?php echo __('installments'); ?></span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-4 col-6 mb-3">
                            <a href="expenses/" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <span>المصروفات اليومية</span>
                                <small class="text-white-50">جديد</small>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-4 col-6 mb-3">
                            <a href="settings/" class="btn btn-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-cog fa-2x mb-2"></i>
                                <span><?php echo __('settings'); ?></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for future charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Auto-refresh statistics every 5 minutes
    setInterval(function() {
        location.reload();
    }, 300000);
    
    // Add tooltips to all elements with title attribute
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
</script>

<?php require_once '../includes/footer.php'; ?>
