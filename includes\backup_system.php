<?php
/**
 * نظام النسخ الاحتياطي المتقدم
 * Advanced Backup System
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

/**
 * فئة النسخ الاحتياطي
 * Backup System Class
 */
class BackupSystem {
    
    private static $instance = null;
    private $backup_path;
    private $conn;
    
    private function __construct() {
        global $conn;
        $this->conn = $conn;
        $this->backup_path = BACKUPS_PATH;
        $this->createBackupDirectory();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * إنشاء مجلد النسخ الاحتياطية
     * Create backup directory
     */
    private function createBackupDirectory() {
        if (!file_exists($this->backup_path)) {
            mkdir($this->backup_path, 0755, true);
        }
        
        // Create .htaccess for security
        $htaccess_file = $this->backup_path . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            file_put_contents($htaccess_file, "Order Allow,Deny\nDeny from all");
        }
    }
    
    /**
     * إنشاء نسخة احتياطية كاملة
     * Create full backup
     */
    public function createFullBackup($include_files = true) {
        try {
            $backup_name = 'full_backup_' . date('Y-m-d_H-i-s');
            $backup_dir = $this->backup_path . '/' . $backup_name;
            
            if (!mkdir($backup_dir, 0755, true)) {
                throw new Exception('Failed to create backup directory');
            }
            
            $backup_info = [
                'type' => 'full',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => $_SESSION['user_id'] ?? null,
                'database_backup' => null,
                'files_backup' => null,
                'size' => 0,
                'status' => 'in_progress'
            ];
            
            // Create database backup
            log_info("Starting database backup");
            $db_backup_result = $this->createDatabaseBackup($backup_dir);
            if (!$db_backup_result['success']) {
                throw new Exception('Database backup failed: ' . $db_backup_result['message']);
            }
            $backup_info['database_backup'] = $db_backup_result['filename'];
            
            // Create files backup if requested
            if ($include_files) {
                log_info("Starting files backup");
                $files_backup_result = $this->createFilesBackup($backup_dir);
                if (!$files_backup_result['success']) {
                    log_warning('Files backup failed: ' . $files_backup_result['message']);
                } else {
                    $backup_info['files_backup'] = $files_backup_result['filename'];
                }
            }
            
            // Calculate total size
            $backup_info['size'] = $this->calculateDirectorySize($backup_dir);
            
            // Create backup info file
            $info_file = $backup_dir . '/backup_info.json';
            file_put_contents($info_file, json_encode($backup_info, JSON_PRETTY_PRINT));
            
            // Compress backup if enabled
            if (BACKUP_COMPRESSION) {
                $compressed_file = $this->compressBackup($backup_dir);
                if ($compressed_file) {
                    // Remove uncompressed directory
                    $this->removeDirectory($backup_dir);
                    $backup_info['compressed'] = true;
                    $backup_info['filename'] = basename($compressed_file);
                }
            }
            
            $backup_info['status'] = 'completed';
            
            // Store backup info in database
            $this->storeBackupInfo($backup_info);
            
            // Clean old backups
            $this->cleanOldBackups();
            
            log_info("Full backup completed successfully: " . $backup_name);
            
            return [
                'success' => true,
                'backup_name' => $backup_name,
                'backup_info' => $backup_info
            ];
            
        } catch (Exception $e) {
            log_error("Full backup failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية لقاعدة البيانات
     * Create database backup
     */
    public function createDatabaseBackup($backup_dir = null) {
        try {
            if (!$backup_dir) {
                $backup_dir = $this->backup_path;
            }
            
            $filename = 'database_backup_' . date('Y-m-d_H-i-s') . '.sql';
            $file_path = $backup_dir . '/' . $filename;
            
            // Get all tables
            $tables = [];
            $result = $this->conn->query("SHOW TABLES");
            while ($row = $result->fetch_array()) {
                $tables[] = $row[0];
            }
            
            $sql_content = "-- Database Backup\n";
            $sql_content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
            $sql_content .= "-- Database: " . DB_NAME . "\n\n";
            
            $sql_content .= "SET FOREIGN_KEY_CHECKS = 0;\n";
            $sql_content .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
            $sql_content .= "SET AUTOCOMMIT = 0;\n";
            $sql_content .= "START TRANSACTION;\n\n";
            
            foreach ($tables as $table) {
                // Get table structure
                $sql_content .= "-- Table structure for `$table`\n";
                $sql_content .= "DROP TABLE IF EXISTS `$table`;\n";
                
                $create_table = $this->conn->query("SHOW CREATE TABLE `$table`");
                $row = $create_table->fetch_array();
                $sql_content .= $row[1] . ";\n\n";
                
                // Get table data
                $sql_content .= "-- Dumping data for table `$table`\n";
                $data_result = $this->conn->query("SELECT * FROM `$table`");
                
                if ($data_result->num_rows > 0) {
                    $columns = [];
                    $fields = $data_result->fetch_fields();
                    foreach ($fields as $field) {
                        $columns[] = "`{$field->name}`";
                    }
                    
                    $sql_content .= "INSERT INTO `$table` (" . implode(', ', $columns) . ") VALUES\n";
                    
                    $rows = [];
                    while ($row = $data_result->fetch_array(MYSQLI_NUM)) {
                        $escaped_row = [];
                        foreach ($row as $value) {
                            if ($value === null) {
                                $escaped_row[] = 'NULL';
                            } else {
                                $escaped_row[] = "'" . $this->conn->real_escape_string($value) . "'";
                            }
                        }
                        $rows[] = '(' . implode(', ', $escaped_row) . ')';
                    }
                    
                    $sql_content .= implode(",\n", $rows) . ";\n\n";
                } else {
                    $sql_content .= "-- No data for table `$table`\n\n";
                }
            }
            
            $sql_content .= "SET FOREIGN_KEY_CHECKS = 1;\n";
            $sql_content .= "COMMIT;\n";
            
            // Write to file
            if (file_put_contents($file_path, $sql_content) === false) {
                throw new Exception('Failed to write database backup file');
            }
            
            return [
                'success' => true,
                'filename' => $filename,
                'file_path' => $file_path,
                'size' => filesize($file_path)
            ];
            
        } catch (Exception $e) {
            log_error("Database backup failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية للملفات
     * Create files backup
     */
    public function createFilesBackup($backup_dir = null) {
        try {
            if (!$backup_dir) {
                $backup_dir = $this->backup_path;
            }
            
            $filename = 'files_backup_' . date('Y-m-d_H-i-s') . '.tar.gz';
            $file_path = $backup_dir . '/' . $filename;
            
            // Directories to backup
            $directories_to_backup = [
                UPLOADS_PATH,
                INCLUDES_PATH,
                ROOT_PATH . '/assets',
                ROOT_PATH . '/config'
            ];
            
            // Create tar archive
            $tar_command = "tar -czf '$file_path'";
            foreach ($directories_to_backup as $dir) {
                if (file_exists($dir)) {
                    $tar_command .= " -C '" . dirname($dir) . "' '" . basename($dir) . "'";
                }
            }
            
            // Execute tar command
            $output = [];
            $return_code = 0;
            exec($tar_command . ' 2>&1', $output, $return_code);
            
            if ($return_code !== 0) {
                // Fallback to PHP-based archive creation
                return $this->createFilesBackupPHP($backup_dir);
            }
            
            if (!file_exists($file_path)) {
                throw new Exception('Files backup archive was not created');
            }
            
            return [
                'success' => true,
                'filename' => $filename,
                'file_path' => $file_path,
                'size' => filesize($file_path)
            ];
            
        } catch (Exception $e) {
            log_error("Files backup failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية للملفات باستخدام PHP
     * Create files backup using PHP
     */
    private function createFilesBackupPHP($backup_dir) {
        if (!class_exists('ZipArchive')) {
            return [
                'success' => false,
                'message' => 'ZipArchive class not available'
            ];
        }
        
        $filename = 'files_backup_' . date('Y-m-d_H-i-s') . '.zip';
        $file_path = $backup_dir . '/' . $filename;
        
        $zip = new ZipArchive();
        if ($zip->open($file_path, ZipArchive::CREATE) !== TRUE) {
            return [
                'success' => false,
                'message' => 'Cannot create zip file'
            ];
        }
        
        // Directories to backup
        $directories_to_backup = [
            UPLOADS_PATH => 'uploads',
            INCLUDES_PATH => 'includes',
            ROOT_PATH . '/assets' => 'assets',
            ROOT_PATH . '/config' => 'config'
        ];
        
        foreach ($directories_to_backup as $source_dir => $archive_dir) {
            if (file_exists($source_dir)) {
                $this->addDirectoryToZip($zip, $source_dir, $archive_dir);
            }
        }
        
        $zip->close();
        
        return [
            'success' => true,
            'filename' => $filename,
            'file_path' => $file_path,
            'size' => filesize($file_path)
        ];
    }
    
    /**
     * إضافة مجلد إلى ملف ZIP
     * Add directory to ZIP
     */
    private function addDirectoryToZip($zip, $source_dir, $archive_dir) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source_dir),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                $zip->addEmptyDir($archive_dir . '/' . $iterator->getSubPathName());
            } elseif ($file->isFile()) {
                $zip->addFile($file, $archive_dir . '/' . $iterator->getSubPathName());
            }
        }
    }
    
    /**
     * ضغط النسخة الاحتياطية
     * Compress backup
     */
    private function compressBackup($backup_dir) {
        $compressed_file = $backup_dir . '.tar.gz';
        
        // Use tar command if available
        $tar_command = "tar -czf '$compressed_file' -C '" . dirname($backup_dir) . "' '" . basename($backup_dir) . "'";
        $output = [];
        $return_code = 0;
        exec($tar_command . ' 2>&1', $output, $return_code);
        
        if ($return_code === 0 && file_exists($compressed_file)) {
            return $compressed_file;
        }
        
        // Fallback to ZIP
        if (class_exists('ZipArchive')) {
            $compressed_file = $backup_dir . '.zip';
            $zip = new ZipArchive();
            
            if ($zip->open($compressed_file, ZipArchive::CREATE) === TRUE) {
                $this->addDirectoryToZip($zip, $backup_dir, basename($backup_dir));
                $zip->close();
                return $compressed_file;
            }
        }
        
        return false;
    }
    
    /**
     * استعادة نسخة احتياطية
     * Restore backup
     */
    public function restoreBackup($backup_name) {
        try {
            $backup_path = $this->backup_path . '/' . $backup_name;
            
            // Check if backup exists
            if (!file_exists($backup_path) && !file_exists($backup_path . '.tar.gz') && !file_exists($backup_path . '.zip')) {
                throw new Exception('Backup not found');
            }
            
            // Extract compressed backup if needed
            if (file_exists($backup_path . '.tar.gz')) {
                $this->extractTarGz($backup_path . '.tar.gz', dirname($backup_path));
            } elseif (file_exists($backup_path . '.zip')) {
                $this->extractZip($backup_path . '.zip', dirname($backup_path));
            }
            
            // Read backup info
            $info_file = $backup_path . '/backup_info.json';
            if (!file_exists($info_file)) {
                throw new Exception('Backup info file not found');
            }
            
            $backup_info = json_decode(file_get_contents($info_file), true);
            
            // Restore database
            if (isset($backup_info['database_backup'])) {
                $db_file = $backup_path . '/' . $backup_info['database_backup'];
                $restore_result = $this->restoreDatabase($db_file);
                if (!$restore_result['success']) {
                    throw new Exception('Database restore failed: ' . $restore_result['message']);
                }
            }
            
            // Restore files
            if (isset($backup_info['files_backup'])) {
                $files_file = $backup_path . '/' . $backup_info['files_backup'];
                $restore_result = $this->restoreFiles($files_file);
                if (!$restore_result['success']) {
                    log_warning('Files restore failed: ' . $restore_result['message']);
                }
            }
            
            log_info("Backup restored successfully: " . $backup_name);
            
            return [
                'success' => true,
                'message' => 'Backup restored successfully'
            ];
            
        } catch (Exception $e) {
            log_error("Backup restore failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * استعادة قاعدة البيانات
     * Restore database
     */
    private function restoreDatabase($sql_file) {
        try {
            if (!file_exists($sql_file)) {
                throw new Exception('SQL file not found');
            }
            
            $sql_content = file_get_contents($sql_file);
            if ($sql_content === false) {
                throw new Exception('Failed to read SQL file');
            }
            
            // Split SQL into individual queries
            $queries = explode(';', $sql_content);
            
            $this->conn->autocommit(false);
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query)) {
                    if (!$this->conn->query($query)) {
                        throw new Exception('SQL query failed: ' . $this->conn->error);
                    }
                }
            }
            
            $this->conn->commit();
            $this->conn->autocommit(true);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->conn->rollback();
            $this->conn->autocommit(true);
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * حساب حجم المجلد
     * Calculate directory size
     */
    private function calculateDirectorySize($directory) {
        $size = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }
        
        return $size;
    }
    
    /**
     * حفظ معلومات النسخة الاحتياطية
     * Store backup information
     */
    private function storeBackupInfo($backup_info) {
        if (!$this->conn) {
            return false;
        }
        
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO backups (
                    backup_name, backup_type, file_size, created_by, 
                    database_included, files_included, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $backup_name = $backup_info['filename'] ?? 'unknown';
            $database_included = !empty($backup_info['database_backup']);
            $files_included = !empty($backup_info['files_backup']);
            
            $stmt->bind_param("ssiibbs",
                $backup_name,
                $backup_info['type'],
                $backup_info['size'],
                $backup_info['created_by'],
                $database_included,
                $files_included,
                $backup_info['status']
            );
            
            return $stmt->execute();
            
        } catch (Exception $e) {
            log_error("Error storing backup info: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنظيف النسخ الاحتياطية القديمة
     * Clean old backups
     */
    private function cleanOldBackups() {
        if (!BACKUP_ENABLED) {
            return;
        }
        
        $retention_days = BACKUP_RETENTION_DAYS;
        $cutoff_date = date('Y-m-d', strtotime("-{$retention_days} days"));
        
        // Get old backups from database
        if ($this->conn) {
            $stmt = $this->conn->prepare("
                SELECT backup_name FROM backups 
                WHERE DATE(created_at) < ? 
                ORDER BY created_at ASC
            ");
            $stmt->bind_param("s", $cutoff_date);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $this->deleteBackup($row['backup_name']);
            }
            
            // Remove from database
            $delete_stmt = $this->conn->prepare("DELETE FROM backups WHERE DATE(created_at) < ?");
            $delete_stmt->bind_param("s", $cutoff_date);
            $delete_stmt->execute();
        }
    }
    
    /**
     * حذف نسخة احتياطية
     * Delete backup
     */
    public function deleteBackup($backup_name) {
        try {
            $backup_files = [
                $this->backup_path . '/' . $backup_name,
                $this->backup_path . '/' . $backup_name . '.tar.gz',
                $this->backup_path . '/' . $backup_name . '.zip'
            ];
            
            foreach ($backup_files as $file) {
                if (file_exists($file)) {
                    if (is_dir($file)) {
                        $this->removeDirectory($file);
                    } else {
                        unlink($file);
                    }
                }
            }
            
            return ['success' => true];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إزالة مجلد بالكامل
     * Remove directory recursively
     */
    private function removeDirectory($dir) {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        return rmdir($dir);
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     * Get backups list
     */
    public function getBackupsList() {
        if (!$this->conn) {
            return [];
        }
        
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM backups 
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
            
        } catch (Exception $e) {
            log_error("Error getting backups list: " . $e->getMessage());
            return [];
        }
    }
}

// ===================================
// HELPER FUNCTIONS
// ===================================

/**
 * Get backup system instance
 */
function backup_system() {
    return BackupSystem::getInstance();
}

/**
 * Create full backup (shorthand)
 */
function create_full_backup($include_files = true) {
    return backup_system()->createFullBackup($include_files);
}

/**
 * Create database backup (shorthand)
 */
function create_database_backup() {
    return backup_system()->createDatabaseBackup();
}

/**
 * Restore backup (shorthand)
 */
function restore_backup($backup_name) {
    return backup_system()->restoreBackup($backup_name);
}
?>
