-- إصلا<PERSON> القيود الخارجية للجداول الموجودة فقط
-- Fix foreign keys for existing tables only

USE school_management;

-- تعطيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 0;

-- 1. إص<PERSON><PERSON><PERSON> جدول student_grades
-- حذف القيد الموجود
ALTER TABLE student_grades DROP FOREIGN KEY IF EXISTS fk_student_grades_graded_by;
ALTER TABLE student_grades DROP FOREIGN KEY IF EXISTS student_grades_graded_by_foreign;

-- تنظيف البيانات
UPDATE student_grades SET graded_by = NULL WHERE graded_by IS NOT NULL AND graded_by NOT IN (SELECT id FROM users);

-- إضافة القيد الجديد
ALTER TABLE student_grades
ADD CONSTRAINT student_grades_graded_by_foreign 
FOREIGN KEY (graded_by) REFERENCES users (id) ON DELETE SET NULL ON UPDATE CASCADE;

-- 2. ت<PERSON><PERSON><PERSON><PERSON> جميع المراجع في الجداول الأخرى
-- activity_logs
UPDATE activity_logs SET user_id = NULL WHERE user_id IS NOT NULL AND user_id NOT IN (SELECT id FROM users);

-- approval_workflow
UPDATE approval_workflow SET approver_id = NULL WHERE approver_id IS NOT NULL AND approver_id NOT IN (SELECT id FROM users);

-- attendance
UPDATE attendance SET marked_by = NULL WHERE marked_by IS NOT NULL AND marked_by NOT IN (SELECT id FROM users);

-- backups
UPDATE backups SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);

-- daily_expenses
UPDATE daily_expenses SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE daily_expenses SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);

-- discounts
UPDATE discounts SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);

-- employee_leave_balances
DELETE FROM employee_leave_balances WHERE user_id NOT IN (SELECT id FROM users);

-- error_logs
UPDATE error_logs SET resolved_by = NULL WHERE resolved_by IS NOT NULL AND resolved_by NOT IN (SELECT id FROM users);
UPDATE error_logs SET user_id = NULL WHERE user_id IS NOT NULL AND user_id NOT IN (SELECT id FROM users);

-- expenses
UPDATE expenses SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE expenses SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);

-- leave_notifications
DELETE FROM leave_notifications WHERE user_id NOT IN (SELECT id FROM users);

-- notifications
DELETE FROM notifications WHERE user_id NOT IN (SELECT id FROM users);

-- payments
UPDATE payments SET processed_by = NULL WHERE processed_by IS NOT NULL AND processed_by NOT IN (SELECT id FROM users);

-- remember_tokens
DELETE FROM remember_tokens WHERE user_id NOT IN (SELECT id FROM users);

-- staff
DELETE FROM staff WHERE user_id NOT IN (SELECT id FROM users);

-- staff_permissions
DELETE FROM staff_permissions WHERE user_id NOT IN (SELECT id FROM users);
UPDATE staff_permissions SET applied_by = NULL WHERE applied_by IS NOT NULL AND applied_by NOT IN (SELECT id FROM users);
UPDATE staff_permissions SET approved_by = NULL WHERE approved_by IS NOT NULL AND approved_by NOT IN (SELECT id FROM users);
UPDATE staff_permissions SET cancelled_by = NULL WHERE cancelled_by IS NOT NULL AND cancelled_by NOT IN (SELECT id FROM users);
UPDATE staff_permissions SET rejected_by = NULL WHERE rejected_by IS NOT NULL AND rejected_by NOT IN (SELECT id FROM users);
UPDATE staff_permissions SET replacement_user_id = NULL WHERE replacement_user_id IS NOT NULL AND replacement_user_id NOT IN (SELECT id FROM users);

-- students
DELETE FROM students WHERE user_id NOT IN (SELECT id FROM users);

-- student_fees
UPDATE student_fees SET created_by = NULL WHERE created_by IS NOT NULL AND created_by NOT IN (SELECT id FROM users);

-- student_payments
UPDATE student_payments SET processed_by = NULL WHERE processed_by IS NOT NULL AND processed_by NOT IN (SELECT id FROM users);

-- system_settings
UPDATE system_settings SET updated_by = NULL WHERE updated_by IS NOT NULL AND updated_by NOT IN (SELECT id FROM users);

-- teachers
DELETE FROM teachers WHERE user_id NOT IN (SELECT id FROM users);

-- teacher_attendance
UPDATE teacher_attendance SET recorded_by = NULL WHERE recorded_by IS NOT NULL AND recorded_by NOT IN (SELECT id FROM users);

-- uploaded_files
UPDATE uploaded_files SET uploaded_by = NULL WHERE uploaded_by IS NOT NULL AND uploaded_by NOT IN (SELECT id FROM users);

-- إعادة تفعيل فحص القيود الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- إنشاء إجراء مبسط لحذف المستخدمين
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS safe_delete_user_final(IN p_user_id INT)
BEGIN
    DECLARE user_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المستخدم
    SELECT COUNT(*) INTO user_exists FROM users WHERE id = p_user_id;
    
    IF user_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'User does not exist';
    END IF;
    
    -- تنظيف جميع المراجع
    UPDATE student_grades SET graded_by = NULL WHERE graded_by = p_user_id;
    UPDATE activity_logs SET user_id = NULL WHERE user_id = p_user_id;
    UPDATE approval_workflow SET approver_id = NULL WHERE approver_id = p_user_id;
    UPDATE attendance SET marked_by = NULL WHERE marked_by = p_user_id;
    UPDATE backups SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE daily_expenses SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE daily_expenses SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE discounts SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE error_logs SET resolved_by = NULL WHERE resolved_by = p_user_id;
    UPDATE error_logs SET user_id = NULL WHERE user_id = p_user_id;
    UPDATE expenses SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE expenses SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE payments SET processed_by = NULL WHERE processed_by = p_user_id;
    UPDATE staff_permissions SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE staff_permissions SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_permissions SET cancelled_by = NULL WHERE cancelled_by = p_user_id;
    UPDATE staff_permissions SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE staff_permissions SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    UPDATE student_fees SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE student_payments SET processed_by = NULL WHERE processed_by = p_user_id;
    UPDATE system_settings SET updated_by = NULL WHERE updated_by = p_user_id;
    UPDATE teacher_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE uploaded_files SET uploaded_by = NULL WHERE uploaded_by = p_user_id;
    
    -- تنظيف مراجع الغياب والإجازات
    UPDATE unified_staff_absences SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE unified_staff_absences SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE unified_staff_absences SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE unified_staff_absences SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_absences_with_deduction SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_absences_with_deduction SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_absences_with_deduction SET created_by = NULL WHERE created_by = p_user_id;
    UPDATE staff_absences_with_deduction SET processed_by = NULL WHERE processed_by = p_user_id;
    UPDATE staff_absences_with_deduction SET rejected_by = NULL WHERE rejected_by = p_user_id;
    
    UPDATE staff_leaves SET applied_by = NULL WHERE applied_by = p_user_id;
    UPDATE staff_leaves SET approved_by = NULL WHERE approved_by = p_user_id;
    UPDATE staff_leaves SET rejected_by = NULL WHERE rejected_by = p_user_id;
    UPDATE staff_leaves SET cancelled_by = NULL WHERE cancelled_by = p_user_id;
    UPDATE staff_leaves SET replacement_user_id = NULL WHERE replacement_user_id = p_user_id;
    
    UPDATE staff_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    UPDATE staff_attendance SET approved_by = NULL WHERE approved_by = p_user_id;
    
    UPDATE admin_attendance SET recorded_by = NULL WHERE recorded_by = p_user_id;
    
    -- حذف المستخدم (سيحذف السجلات المرتبطة تلقائياً بسبب CASCADE)
    DELETE FROM users WHERE id = p_user_id;
    
    COMMIT;
    
    SELECT CONCAT('User ', p_user_id, ' deleted successfully with all references cleaned') as message;
END//

DELIMITER ;

SELECT 'Foreign key constraints fixed for existing tables!' as status;

-- عرض القيود الخارجية المتبقية
SELECT COUNT(*) as remaining_foreign_keys
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'school_management' 
AND REFERENCED_TABLE_NAME = 'users';
