<?php
/**
 * API لجلب رسوم الطالب
 * Get Student Fees API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

define('SYSTEM_INIT', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// بدء الجلسة والتحقق من الصلاحيات
session_start();
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$student_id = intval($_GET['student_id'] ?? 0);

if (empty($student_id)) {
    http_response_code(400);
    echo json_encode(['error' => 'Student ID is required']);
    exit();
}

try {
    global $conn;
    
    // جلب معلومات الطالب
    $student_stmt = $conn->prepare("
        SELECT 
            s.id,
            u.full_name,
            s.student_number,
            c.class_name,
            c.grade_level,
            u.phone,
            u.email
        FROM students s
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        WHERE s.id = ? AND u.status = 'active'
    ");
    $student_stmt->bind_param("i", $student_id);
    $student_stmt->execute();
    $student_info = $student_stmt->get_result()->fetch_assoc();
    
    if (!$student_info) {
        http_response_code(404);
        echo json_encode(['error' => 'Student not found']);
        exit();
    }
    
    // جلب الرسوم المستحقة
    $fees_stmt = $conn->prepare("
        SELECT 
            sf.id,
            sf.final_amount,
            sf.due_date,
            sf.status,
            sf.academic_year,
            sf.semester,
            ft.type_name,
            ft.type_name_en,
            COALESCE(SUM(sp.amount), 0) as paid_amount,
            (sf.final_amount - COALESCE(SUM(sp.amount), 0)) as remaining_amount,
            CASE 
                WHEN sf.due_date < CURDATE() AND sf.status != 'paid' THEN 1
                ELSE 0
            END as is_overdue,
            DATEDIFF(CURDATE(), sf.due_date) as days_overdue
        FROM student_fees sf
        JOIN fee_types ft ON sf.fee_type_id = ft.id
        LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
        WHERE sf.student_id = ? AND sf.status IN ('pending', 'partial')
        GROUP BY sf.id
        HAVING remaining_amount > 0
        ORDER BY sf.due_date ASC, sf.created_at DESC
    ");
    $fees_stmt->bind_param("i", $student_id);
    $fees_stmt->execute();
    $fees_result = $fees_stmt->get_result();
    
    $fees = [];
    $total_outstanding = 0;
    $overdue_count = 0;
    
    while ($fee = $fees_result->fetch_assoc()) {
        $fee['type_name_display'] = get_current_language() === 'en' && !empty($fee['type_name_en']) 
            ? $fee['type_name_en'] 
            : $fee['type_name'];
        
        $fee['semester_display'] = __($fee['semester'] . '_semester');
        $fee['status_display'] = __($fee['status']);
        $fee['due_date_formatted'] = $fee['due_date'] ? format_date($fee['due_date']) : '';
        $fee['remaining_amount_formatted'] = number_format($fee['remaining_amount'], 2);
        
        if ($fee['is_overdue']) {
            $overdue_count++;
        }
        
        $total_outstanding += $fee['remaining_amount'];
        $fees[] = $fee;
    }
    
    // جلب الأقساط المستحقة
    $installments_stmt = $conn->prepare("
        SELECT 
            si.id,
            si.installment_number,
            si.amount,
            si.due_date,
            si.paid_amount,
            si.status,
            (si.amount - si.paid_amount) as remaining_amount,
            sf.id as fee_id,
            ft.type_name,
            CASE 
                WHEN si.due_date < CURDATE() AND si.status != 'paid' THEN 1
                ELSE 0
            END as is_overdue
        FROM student_installments si
        JOIN student_fees sf ON si.student_fee_id = sf.id
        JOIN fee_types ft ON sf.fee_type_id = ft.id
        WHERE si.student_id = ? AND si.status IN ('pending')
        ORDER BY si.due_date ASC
    ");
    $installments_stmt->bind_param("i", $student_id);
    $installments_stmt->execute();
    $installments_result = $installments_stmt->get_result();
    
    $installments = [];
    while ($installment = $installments_result->fetch_assoc()) {
        $installment['due_date_formatted'] = format_date($installment['due_date']);
        $installment['remaining_amount_formatted'] = number_format($installment['remaining_amount'], 2);
        $installments[] = $installment;
    }
    
    // جلب آخر المدفوعات
    $recent_payments_stmt = $conn->prepare("
        SELECT 
            sp.id,
            sp.amount,
            sp.payment_date,
            sp.payment_method,
            sp.payment_reference,
            ft.type_name
        FROM student_payments sp
        LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
        LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
        WHERE sp.student_id = ? AND sp.status = 'confirmed'
        ORDER BY sp.payment_date DESC
        LIMIT 5
    ");
    $recent_payments_stmt->bind_param("i", $student_id);
    $recent_payments_stmt->execute();
    $recent_payments_result = $recent_payments_stmt->get_result();
    
    $recent_payments = [];
    while ($payment = $recent_payments_result->fetch_assoc()) {
        $payment['payment_date_formatted'] = format_date($payment['payment_date']);
        $payment['amount_formatted'] = number_format($payment['amount'], 2);
        $payment['payment_method_display'] = __($payment['payment_method']);
        $recent_payments[] = $payment;
    }
    
    // إحصائيات مالية للطالب
    $financial_stats_stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT sf.id) as total_fees,
            SUM(sf.final_amount) as total_amount,
            COUNT(DISTINCT sp.id) as total_payments,
            SUM(sp.amount) as total_paid,
            (SUM(sf.final_amount) - COALESCE(SUM(sp.amount), 0)) as total_outstanding
        FROM student_fees sf
        LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
        WHERE sf.student_id = ?
    ");
    $financial_stats_stmt->bind_param("i", $student_id);
    $financial_stats_stmt->execute();
    $financial_stats = $financial_stats_stmt->get_result()->fetch_assoc();
    
    // تحضير الاستجابة
    $response = [
        'success' => true,
        'student_info' => [
            'id' => $student_info['id'],
            'full_name' => $student_info['full_name'],
            'student_number' => $student_info['student_number'],
            'class_name' => $student_info['class_name'],
            'grade_level' => $student_info['grade_level'],
            'phone' => $student_info['phone'],
            'email' => $student_info['email']
        ],
        'fees' => $fees,
        'installments' => $installments,
        'recent_payments' => $recent_payments,
        'summary' => [
            'total_fees' => intval($financial_stats['total_fees']),
            'total_amount' => floatval($financial_stats['total_amount']),
            'total_paid' => floatval($financial_stats['total_paid']),
            'total_outstanding' => floatval($financial_stats['total_outstanding']),
            'overdue_count' => $overdue_count,
            'currency_symbol' => get_system_setting('currency_symbol', 'ر.س')
        ],
        'outstanding_fees' => [
            'count' => count($fees),
            'total_amount' => $total_outstanding,
            'overdue_count' => $overdue_count,
            'formatted_total' => number_format($total_outstanding, 2)
        ]
    ];
    
    // تسجيل النشاط
    log_activity($_SESSION['user_id'], 'view_student_fees', 'students', $student_id, null, [
        'fees_count' => count($fees),
        'outstanding_amount' => $total_outstanding
    ]);
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    log_error("Error in get_student_fees API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error'
    ]);
}
?>
