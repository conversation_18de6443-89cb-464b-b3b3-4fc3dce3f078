<h3 class="mb-4">
    <i class="fas fa-user-shield text-primary me-2"></i>
    Create Admin Account
</h3>

<p class="text-muted mb-4">
    Create the main administrator account for your school management system.
</p>

<form method="POST" action="?step=3" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="admin_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control" 
                       id="admin_name" 
                       name="admin_name" 
                       value="<?php echo htmlspecialchars($_POST['admin_name'] ?? ''); ?>"
                       required>
                <div class="invalid-feedback">
                    Please provide the administrator's full name.
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label for="admin_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                <input type="email" 
                       class="form-control" 
                       id="admin_email" 
                       name="admin_email" 
                       value="<?php echo htmlspecialchars($_POST['admin_email'] ?? ''); ?>"
                       required>
                <div class="form-text">This will be used to login to the system</div>
                <div class="invalid-feedback">
                    Please provide a valid email address.
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="admin_password" class="form-label">Password <span class="text-danger">*</span></label>
                <input type="password" 
                       class="form-control" 
                       id="admin_password" 
                       name="admin_password" 
                       minlength="8"
                       required>
                <div class="form-text">Minimum 8 characters</div>
                <div class="invalid-feedback">
                    Password must be at least 8 characters long.
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="mb-3">
                <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                <input type="password" 
                       class="form-control" 
                       id="confirm_password" 
                       name="confirm_password" 
                       minlength="8"
                       required>
                <div class="invalid-feedback">
                    Please confirm your password.
                </div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Important:</strong> Please remember these credentials as they will be needed to access the system after installation.
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-shield-alt me-2"></i>
        <strong>Security Tips:</strong>
        <ul class="mb-0 mt-2">
            <li>Use a strong password with a mix of letters, numbers, and symbols</li>
            <li>Don't use common passwords or personal information</li>
            <li>Consider using a password manager</li>
            <li>You can change these credentials later from the admin panel</li>
        </ul>
    </div>
    
    <div class="d-flex justify-content-between">
        <a href="?step=2" class="btn btn-secondary btn-lg">
            <i class="fas fa-arrow-left me-2"></i>Back
        </a>
        <button type="submit" class="btn btn-primary btn-lg">
            Create Admin & Continue <i class="fas fa-arrow-right ms-2"></i>
        </button>
    </div>
</form>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    var password = document.getElementById('admin_password').value;
    var confirm = this.value;
    
    if (password !== confirm) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
