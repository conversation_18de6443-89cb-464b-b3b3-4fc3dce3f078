/**
 * ملف المظهر الفاتح
 * Light Theme Stylesheet
 */

[data-theme="light"] {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #343a40;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
}

/* الخلفية العامة */
[data-theme="light"] body {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-primary);
}

/* الشريط العلوي */
[data-theme="light"] .navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="light"] .navbar-brand {
    color: var(--text-primary) !important;
}

[data-theme="light"] .navbar .btn {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="light"] .navbar .btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--primary-color);
}

/* الشريط الجانبي */
[data-theme="light"] .sidebar {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--border-color);
}

[data-theme="light"] .sidebar-nav .nav-link {
    color: var(--text-primary);
}

[data-theme="light"] .sidebar-nav .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

[data-theme="light"] .sidebar-nav .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

/* المحتوى الرئيسي */
[data-theme="light"] .main-content {
    background: rgba(255, 255, 255, 0.95);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

/* البطاقات */
[data-theme="light"] .card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .card-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* النماذج */
[data-theme="light"] .form-control,
[data-theme="light"] .form-select {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .form-control:focus,
[data-theme="light"] .form-select:focus {
    background-color: var(--bg-primary);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

[data-theme="light"] .form-control::placeholder {
    color: var(--text-secondary);
}

[data-theme="light"] .form-label {
    color: var(--text-primary);
}

[data-theme="light"] .input-group-text {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* الجداول */
[data-theme="light"] .table {
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

[data-theme="light"] .table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--border-color);
}

[data-theme="light"] .table td {
    border-color: var(--border-color);
}

[data-theme="light"] .table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

[data-theme="light"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

/* الأزرار */
[data-theme="light"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="light"] .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

[data-theme="light"] .btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-color);
}

[data-theme="light"] .btn-outline-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .btn-outline-info {
    color: var(--info-color);
    border-color: var(--info-color);
}

[data-theme="light"] .btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
}

[data-theme="light"] .btn-outline-warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
}

[data-theme="light"] .btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

/* التنبيهات */
[data-theme="light"] .alert {
    border: 1px solid var(--border-color);
}

[data-theme="light"] .alert-primary {
    background-color: rgba(102, 126, 234, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

[data-theme="light"] .alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

[data-theme="light"] .alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

[data-theme="light"] .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

[data-theme="light"] .alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: var(--info-color);
    color: var(--info-color);
}

/* النوافذ المنبثقة */
[data-theme="light"] .modal-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

[data-theme="light"] .modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-bottom: 1px solid var(--border-color);
}

[data-theme="light"] .modal-body {
    color: var(--text-primary);
}

[data-theme="light"] .modal-footer {
    border-top: 1px solid var(--border-color);
}

/* القوائم المنسدلة */
[data-theme="light"] .dropdown-menu {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

[data-theme="light"] .dropdown-item {
    color: var(--text-primary);
}

[data-theme="light"] .dropdown-item:hover,
[data-theme="light"] .dropdown-item:focus {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

[data-theme="light"] .dropdown-divider {
    border-color: var(--border-color);
}

/* الترقيم */
[data-theme="light"] .pagination .page-link {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

[data-theme="light"] .pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--primary-color);
}

[data-theme="light"] .pagination .page-item.disabled .page-link {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-secondary);
}

/* شريط التقدم */
[data-theme="light"] .progress {
    background-color: var(--bg-tertiary);
}

[data-theme="light"] .progress-bar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/* الشارات */
[data-theme="light"] .badge.bg-secondary {
    background-color: var(--text-secondary) !important;
    color: white;
}

[data-theme="light"] .badge.bg-light {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary);
}

/* القوائم */
[data-theme="light"] .list-group-item {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .list-group-item:hover {
    background-color: var(--bg-secondary);
}

[data-theme="light"] .list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* التبويبات */
[data-theme="light"] .nav-tabs {
    border-bottom-color: var(--border-color);
}

[data-theme="light"] .nav-tabs .nav-link {
    color: var(--text-secondary);
    border-color: transparent;
}

[data-theme="light"] .nav-tabs .nav-link:hover {
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .nav-tabs .nav-link.active {
    background-color: var(--bg-primary);
    border-color: var(--border-color) var(--border-color) var(--bg-primary);
    color: var(--text-primary);
}

[data-theme="light"] .tab-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-top: none;
}

/* النصوص */
[data-theme="light"] .text-muted {
    color: var(--text-secondary) !important;
}

[data-theme="light"] .text-dark {
    color: var(--text-primary) !important;
}

[data-theme="light"] .text-light {
    color: var(--text-secondary) !important;
}

/* الحدود */
[data-theme="light"] .border {
    border-color: var(--border-color) !important;
}

[data-theme="light"] .border-top {
    border-top-color: var(--border-color) !important;
}

[data-theme="light"] .border-bottom {
    border-bottom-color: var(--border-color) !important;
}

[data-theme="light"] .border-start {
    border-left-color: var(--border-color) !important;
}

[data-theme="light"] .border-end {
    border-right-color: var(--border-color) !important;
}

/* الخلفيات */
[data-theme="light"] .bg-light {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary);
}

[data-theme="light"] .bg-white {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary);
}

/* أنماط خاصة بالمكونات */
[data-theme="light"] .stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

[data-theme="light"] .exam-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

[data-theme="light"] .attendance-present {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

[data-theme="light"] .attendance-absent {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

[data-theme="light"] .attendance-late {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

[data-theme="light"] .attendance-excused {
    background-color: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}
