<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - Internal Server Error</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            text-align: center;
            color: white;
            max-width: 700px;
            padding: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        .error-description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-home:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .error-details {
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
        .floating-icon {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        .icon-1 { top: 10%; left: 10%; animation-delay: 0s; }
        .icon-2 { top: 20%; right: 10%; animation-delay: 2s; }
        .icon-3 { bottom: 20%; left: 15%; animation-delay: 4s; }
        .icon-4 { bottom: 10%; right: 20%; animation-delay: 1s; }
    </style>
</head>
<body>
    <!-- Floating Icons -->
    <i class="fas fa-exclamation-triangle floating-icon icon-1" style="font-size: 3rem;"></i>
    <i class="fas fa-tools floating-icon icon-2" style="font-size: 2.5rem;"></i>
    <i class="fas fa-bug floating-icon icon-3" style="font-size: 3.5rem;"></i>
    <i class="fas fa-cog floating-icon icon-4" style="font-size: 2rem;"></i>

    <div class="error-container">
        <div class="error-code">500</div>
        <div class="error-message">
            <i class="fas fa-exclamation-triangle me-3"></i>
            خطأ داخلي في الخادم
        </div>
        <div class="error-description">
            عذراً، حدث خطأ غير متوقع في النظام. نحن نعمل على حل هذه المشكلة.
            <br>
            يرجى المحاولة مرة أخرى لاحقاً أو الاتصال بمدير النظام إذا استمرت المشكلة.
        </div>
        
        <?php if (isset($error_message) && defined('DEBUG_MODE') && DEBUG_MODE): ?>
        <div class="error-details">
            <strong>تفاصيل الخطأ (وضع التطوير):</strong><br>
            <?php echo htmlspecialchars($error_message ?? 'Unknown error'); ?><br>
            <?php if (isset($error_file)): ?>
                <strong>الملف:</strong> <?php echo htmlspecialchars($error_file); ?><br>
            <?php endif; ?>
            <?php if (isset($error_line)): ?>
                <strong>السطر:</strong> <?php echo htmlspecialchars($error_line); ?><br>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="/" class="btn-home">
                <i class="fas fa-home me-2"></i>
                الصفحة الرئيسية
            </a>
            <a href="/dashboard/" class="btn-home">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </a>
            <a href="javascript:location.reload()" class="btn-home">
                <i class="fas fa-redo me-2"></i>
                إعادة المحاولة
            </a>
        </div>
        
        <div class="mt-4">
            <small style="opacity: 0.7;">
                رقم الخطأ: <?php echo uniqid(); ?> | الوقت: <?php echo date('Y-m-d H:i:s'); ?>
            </small>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate error code on load
            const errorCode = document.querySelector('.error-code');
            errorCode.style.animation = 'shake 0.5s ease-in-out';
            
            // Add CSS for shake animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
                    20%, 40%, 60%, 80% { transform: translateX(10px); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
