<?php
/**
 * صفحة حذف المرحلة الدراسية
 * Delete Educational Stage Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// الحصول على معرف المرحلة
$stage_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($stage_id <= 0) {
    $_SESSION['error_message'] = __('invalid_stage_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات المرحلة
$stage_query = "SELECT * FROM educational_stages WHERE id = ?";
$stmt = $conn->prepare($stage_query);
if (!$stmt) {
    $_SESSION['error_message'] = __('database_error');
    header('Location: index.php');
    exit();
}

$stmt->bind_param('i', $stage_id);
$stmt->execute();
$result = $stmt->get_result();
$stage = $result->fetch_assoc();
$stmt->close();

if (!$stage) {
    $_SESSION['error_message'] = __('stage_not_found');
    header('Location: index.php');
    exit();
}

// التحقق من وجود بيانات مرتبطة
$dependencies = [];

// التحقق من الفصول
$classes_query = "SELECT COUNT(*) as count FROM classes WHERE stage_id = ?";
$stmt = $conn->prepare($classes_query);
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $classes_count = $result->fetch_assoc()['count'];
    $stmt->close();
    
    if ($classes_count > 0) {
        $dependencies[] = [
            'type' => 'classes',
            'count' => $classes_count,
            'message' => __('cannot_delete_stage_has_classes')
        ];
    }
}

// التحقق من المواد
$subjects_query = "SELECT COUNT(*) as count FROM subjects WHERE stage_id = ?";
$stmt = $conn->prepare($subjects_query);
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $subjects_count = $result->fetch_assoc()['count'];
    $stmt->close();
    
    if ($subjects_count > 0) {
        $dependencies[] = [
            'type' => 'subjects',
            'count' => $subjects_count,
            'message' => __('cannot_delete_stage_has_subjects')
        ];
    }
}

// التحقق من الطلاب (عبر الفصول)
$students_query = "
    SELECT COUNT(DISTINCT s.id) as count 
    FROM students s 
    INNER JOIN classes c ON s.class_id = c.id 
    WHERE c.stage_id = ?
";
$stmt = $conn->prepare($students_query);
if ($stmt) {
    $stmt->bind_param('i', $stage_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $students_count = $result->fetch_assoc()['count'];
    $stmt->close();
    
    if ($students_count > 0) {
        $dependencies[] = [
            'type' => 'students',
            'count' => $students_count,
            'message' => __('cannot_delete_stage_has_students')
        ];
    }
}

$can_delete = empty($dependencies);
$success_message = '';
$error_message = '';

// معالجة طلب الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    if (!$can_delete) {
        $error_message = __('cannot_delete') . ': ' . __('stage_has_dependencies');
    } else {
        // بدء المعاملة
        $conn->begin_transaction();
        
        try {
            // حفظ بيانات المرحلة للسجل
            $stage_data = [
                'stage_name' => $stage['stage_name'],
                'stage_code' => $stage['stage_code']
            ];
            
            // حذف المرحلة
            $delete_query = "DELETE FROM educational_stages WHERE id = ?";
            $stmt = $conn->prepare($delete_query);
            if (!$stmt) {
                throw new Exception(__('database_error') . ': ' . $conn->error);
            }
            
            $stmt->bind_param('i', $stage_id);
            if (!$stmt->execute()) {
                throw new Exception(__('database_error') . ': ' . $stmt->error);
            }
            $stmt->close();
            
            // تسجيل النشاط
            log_activity($_SESSION['user_id'], 'delete_stage', 'educational_stages', $stage_id, $stage_data, null);
            
            // تأكيد المعاملة
            $conn->commit();
            
            // إعادة توجيه مع رسالة نجاح
            $_SESSION['success_message'] = __('stage_deleted_successfully');
            header('Location: index.php');
            exit();
            
        } catch (Exception $e) {
            // إلغاء المعاملة
            $conn->rollback();
            $error_message = $e->getMessage();
        }
    }
}

$page_title = __('delete_stage');

// إذا لم يكن طلب POST، عرض صفحة التأكيد
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-trash text-danger me-2"></i>
                <?php echo __('delete_stage'); ?>
            </h2>
            <p class="text-muted mb-0">
                <?php echo __('confirm_delete'); ?>: <strong><?php echo htmlspecialchars($stage['stage_name']); ?></strong>
            </p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $stage_id; ?>" class="btn btn-outline-info me-2">
                <i class="fas fa-eye me-2"></i><?php echo __('view_stage'); ?>
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_stages'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Stage Information -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('stage_information'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('stage_name'); ?>:</td>
                                    <td><?php echo htmlspecialchars($stage['stage_name']); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('stage_code'); ?>:</td>
                                    <td><code><?php echo htmlspecialchars($stage['stage_code']); ?></code></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('sort_order'); ?>:</td>
                                    <td><span class="badge bg-primary"><?php echo $stage['sort_order']; ?></span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('duration_years'); ?>:</td>
                                    <td><?php echo $stage['duration_years']; ?> <?php echo __('years'); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('status'); ?>:</td>
                                    <td>
                                        <?php if ($stage['status'] === 'active'): ?>
                                            <span class="badge bg-success"><?php echo __('active'); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo __('inactive'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted"><?php echo __('created_at'); ?>:</td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($stage['created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dependencies Check -->
            <?php if (!empty($dependencies)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo __('warning'); ?>: <?php echo __('stage_has_dependencies'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-danger fw-bold"><?php echo __('cannot_delete'); ?></p>
                        <p><?php echo __('stage_dependencies_info'); ?>:</p>
                        
                        <ul class="list-group list-group-flush">
                            <?php foreach ($dependencies as $dependency): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-link me-2 text-warning"></i>
                                        <?php echo $dependency['message']; ?>
                                    </div>
                                    <span class="badge bg-danger rounded-pill"><?php echo $dependency['count']; ?></span>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong><?php echo __('suggestion'); ?>:</strong>
                                <?php echo __('delete_dependencies_first'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Delete Confirmation -->
            <div class="card">
                <div class="card-header <?php echo $can_delete ? 'bg-danger text-white' : 'bg-secondary text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas fa-trash me-2"></i>
                        <?php echo __('delete_confirmation'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($can_delete): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong><?php echo __('warning'); ?>:</strong> <?php echo __('delete_stage_warning'); ?>
                        </div>
                        
                        <p><?php echo __('delete_stage_confirmation'); ?></p>
                        <p class="text-danger fw-bold"><?php echo htmlspecialchars($stage['stage_name']); ?></p>
                        
                        <form method="POST" onsubmit="return confirmDelete()">
                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                                </a>
                                <button type="submit" name="confirm_delete" class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i><?php echo __('delete_stage'); ?>
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-ban me-2"></i>
                            <?php echo __('cannot_delete_stage_has_dependencies'); ?>
                        </div>
                        
                        <p><?php echo __('resolve_dependencies_message'); ?></p>
                        
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_stages'); ?>
                            </a>
                            <div>
                                <a href="view.php?id=<?php echo $stage_id; ?>" class="btn btn-info me-2">
                                    <i class="fas fa-eye me-2"></i><?php echo __('view_dependencies'); ?>
                                </a>
                                <a href="edit.php?id=<?php echo $stage_id; ?>" class="btn btn-primary">
                                    <i class="fas fa-edit me-2"></i><?php echo __('edit_stage'); ?>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    return confirm('<?php echo __('delete_stage_confirmation'); ?>\n\n<?php echo __('delete_stage_warning'); ?>');
}
</script>

<?php require_once '../includes/footer.php'; ?>
