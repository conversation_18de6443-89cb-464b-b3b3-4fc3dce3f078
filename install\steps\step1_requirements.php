<h3 class="mb-4">
    <i class="fas fa-check-circle text-primary me-2"></i>
    System Requirements Check
</h3>

<p class="text-muted mb-4">
    Please ensure your server meets the following requirements before proceeding with the installation.
</p>

<?php $requirements = checkRequirements(); ?>

<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Requirement</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($requirements as $requirement => $status): ?>
                <tr>
                    <td><?php echo htmlspecialchars($requirement); ?></td>
                    <td>
                        <?php if ($status): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Passed
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger">
                                <i class="fas fa-times me-1"></i>Failed
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<?php $all_passed = !in_array(false, $requirements); ?>

<?php if ($all_passed): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        All requirements are met! You can proceed with the installation.
    </div>
    
    <form method="POST" action="?step=2">
        <div class="text-end">
            <button type="submit" class="btn btn-primary btn-lg">
                Continue <i class="fas fa-arrow-right ms-2"></i>
            </button>
        </div>
    </form>
<?php else: ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Some requirements are not met. Please fix the issues above before continuing.
    </div>
    
    <div class="text-end">
        <a href="?step=1" class="btn btn-secondary btn-lg">
            <i class="fas fa-refresh me-2"></i>Check Again
        </a>
    </div>
<?php endif; ?>
