# ===================================
# School Management System .gitignore
# ===================================

# Environment Configuration
.env
.env.local
.env.production
.env.staging
.env.testing

# Configuration Files with Sensitive Data
config/database.php
config/mail.php
config/services.php
config/installed.lock

# Logs
logs/
*.log
error.log
access.log
debug.log

# Cache
cache/
*.cache
.cache/
tmp/
temp/

# Uploads and User Files
uploads/profiles/*
uploads/documents/*
uploads/temp/*
!uploads/profiles/.gitkeep
!uploads/documents/.gitkeep
!uploads/temp/.gitkeep

# Backups
backups/
*.sql.gz
*.tar.gz
*.zip
# استثناء ملف قاعدة البيانات الأساسي
!database/school_management.sql

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# PHP
*.php~
*.php.bak

# Testing Files (temporary)
test_*.php
check_*.php
fix_*.php
update_*.php
debug_*.php
setup_*.php
*_test.php
*_check.php
*_fix.php
*_update.php
*_debug.php
*_setup.php

# Documentation Files (temporary)
*_README.md
*_GUIDE.md
*_SUMMARY.md
*_FIX.md
HOSTING_*.md
INSTALLATION_*.md

# Archive Files
*.tar
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Backup Files
*.bak
*.backup
*.old
*.orig

# Patch Files
*.patch
*.diff
