<?php
/**
 * الصفحة الرئيسية لنظام إدارة المدارس
 * School Management System - Main Index
 */

define('SYSTEM_INIT', true);
require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من وجود ملف التثبيت
if (!file_exists('config/installed.lock')) {
    header('Location: login.php');
    exit();
}

// التحقق من تسجيل الدخول
if (is_logged_in()) {
    // إعادة توجيه المستخدم المسجل دخوله إلى لوحة التحكم
    header('Location: dashboard/');
    exit();
}

// الحصول على إعدادات النظام
$system_name = get_system_setting('school_name', __('system_name'));
$system_description = get_system_setting('school_description', __('system_description'));
// لا داعي لتعريف $current_language أو تحميل ملف الترجمة هنا، لأن الهيدر سيتكفل بذلك
require_once 'includes/header.php';

// تعريف متغيرات إحصائية افتراضية لمنع التحذيرات
$total_teachers = $total_teachers ?? 0;
$total_classes = $total_classes ?? 0;
$total_students = $total_students ?? 0;
$total_subjects = $total_subjects ?? 0;
// أضف أي متغيرات أخرى تظهر في التحذيرات هنا
?>
<!DOCTYPE html>
<html lang="<?php echo get_current_language(); ?>" dir="<?php echo get_current_language() === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_name; ?> - <?php echo __('welcome'); ?></title>
    
    <!-- Meta Tags -->
    <meta name="description" content="<?php echo $system_description; ?>">
    <meta name="keywords" content="school management, education, students, teachers, grades">
    <meta name="author" content="School Management System">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <?php if (get_current_language() === 'ar'): ?>
        <link href="assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .hero-section {
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin: 1rem 0;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 4rem 0;
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 80px;
            height: 80px;
            top: 40%;
            left: 70%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        .btn-hero {
            background: var(--secondary-gradient);
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-hero:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            padding: 10px 15px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .language-switcher:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="dropdown position-fixed" style="top: 20px; right: 20px; z-index: 1000;">
        <button class="language-switcher dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-globe me-1"></i>
            <?php echo get_current_language() === 'ar' ? 'العربية' : 'English'; ?>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
            <li><a class="dropdown-item" href="?lang=en">English</a></li>
        </ul>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <div class="container hero-content">
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <h1 class="display-4 fw-bold text-white mb-4">
                        <?php echo $system_name; ?>
                    </h1>
                    <p class="lead text-white mb-4">
                        <?php echo $system_description; ?>
                    </p>
                    <p class="text-white mb-5">
                        <?php echo __('system_welcome_description'); ?>
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="login.php" class="btn-hero">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            <?php echo __('login'); ?>
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo __('learn_more'); ?>
                        </a>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="text-center">
                        <i class="fas fa-graduation-cap text-white" style="font-size: 15rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section" data-aos="fade-up">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_students); ?></div>
                        <h5><?php echo __('students'); ?></h5>
                        <p class="text-muted"><?php echo __('registered_students'); ?></p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_teachers); ?></div>
                        <h5><?php echo __('teachers'); ?></h5>
                        <p class="text-muted"><?php echo __('qualified_teachers'); ?></p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_classes); ?></div>
                        <h5><?php echo __('classes'); ?></h5>
                        <p class="text-muted"><?php echo __('active_classes'); ?></p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_subjects); ?></div>
                        <h5><?php echo __('subjects'); ?></h5>
                        <p class="text-muted"><?php echo __('available_subjects'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5" style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="display-5 fw-bold"><?php echo __('system_features'); ?></h2>
                    <p class="lead text-muted"><?php echo __('comprehensive_solution'); ?></p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4><?php echo __('student_management'); ?></h4>
                        <p><?php echo __('complete_student_records'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h4><?php echo __('teacher_management'); ?></h4>
                        <p><?php echo __('teacher_profiles_assignments'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h4><?php echo __('exam_system'); ?></h4>
                        <p><?php echo __('advanced_exam_creation'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4><?php echo __('grade_management'); ?></h4>
                        <p><?php echo __('automated_grade_calculation'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h4><?php echo __('attendance_system'); ?></h4>
                        <p><?php echo __('daily_attendance_tracking'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <h4><?php echo __('financial_management'); ?></h4>
                        <p><?php echo __('fees_payments_tracking'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo $system_name; ?></h5>
                    <p><?php echo __('footer_description'); ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo $system_name; ?>. <?php echo __('all_rights_reserved'); ?></p>
                    <p><?php echo __('version'); ?> <?php echo SYSTEM_VERSION; ?></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });
        
        // Language switcher
        document.addEventListener('DOMContentLoaded', function() {
            const langLinks = document.querySelectorAll('.dropdown-item[href*="lang="]');
            langLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.href.split('lang=')[1];
                    
                    // Save language preference
                    fetch('api/save_preference.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            key: 'language',
                            value: lang
                        })
                    }).then(() => {
                        location.reload();
                    });
                });
            });
        });
        
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
