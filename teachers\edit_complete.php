<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$error_message = '';
$success_message = '';

// التحقق من معرف المعلم
$teacher_id = intval($_GET['id'] ?? 0);
if (empty($teacher_id)) {
    $_SESSION['error_message'] = 'معرف المعلم غير صحيح';
    header('Location: index.php');
    exit();
}

// جلب بيانات المعلم
$teacher_stmt = $conn->prepare("
    SELECT t.*, u.full_name, u.username, u.email, u.status as user_status
    FROM teachers t
    INNER JOIN users u ON t.user_id = u.id
    WHERE t.id = ?
");
$teacher_stmt->bind_param("i", $teacher_id);
$teacher_stmt->execute();
$teacher = $teacher_stmt->get_result()->fetch_assoc();

if (!$teacher) {
    $_SESSION['error_message'] = 'المعلم غير موجود';
    header('Location: index.php');
    exit();
}

// جلب قائمة الفصول النشطة
$classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");

// جلب الفصول المرتبطة بالمعلم
$teacher_classes_result = $conn->prepare("SELECT class_id FROM teacher_class_assignments WHERE teacher_id = ?");
$teacher_classes_result->bind_param("i", $teacher_id);
$teacher_classes_result->execute();
$teacher_classes_data = $teacher_classes_result->get_result()->fetch_all(MYSQLI_ASSOC);
$teacher_class_ids = array_column($teacher_classes_data, 'class_id');

// معالجة تحديث المعلم
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صحيح';
    } else {
        // جمع البيانات وتنظيفها
        $full_name = clean_input($_POST['full_name'] ?? '');
        $email = clean_input($_POST['email'] ?? '');
        $username = clean_input($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $phone = clean_input($_POST['phone'] ?? '');
        $national_id = clean_input($_POST['national_id'] ?? '');
        $date_of_birth = clean_input($_POST['date_of_birth'] ?? '');
        $gender = clean_input($_POST['gender'] ?? '');
        $address = clean_input($_POST['address'] ?? '');
        $employee_id = clean_input($_POST['employee_id'] ?? '');
        $hire_date = clean_input($_POST['hire_date'] ?? '');
        $department = clean_input($_POST['department'] ?? '');
        $qualification = clean_input($_POST['qualification'] ?? '');
        $specialization = clean_input($_POST['specialization'] ?? '');
        $experience_years = intval($_POST['experience_years'] ?? 0);
        $salary = floatval($_POST['salary'] ?? 0);
        $notes = clean_input($_POST['notes'] ?? '');
        $status = clean_input($_POST['status'] ?? 'active');

        // التحقق من صحة البيانات
        $errors = [];
        if (empty($full_name)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        if (empty($username)) {
            $errors[] = 'اسم المستخدم مطلوب';
        } else {
            // التحقق من عدم تكرار اسم المستخدم
            $username_check = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $username_check->bind_param("si", $username, $teacher['user_id']);
            $username_check->execute();
            if ($username_check->get_result()->num_rows > 0) {
                $errors[] = 'اسم المستخدم موجود مسبقاً';
            }
        }
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'صيغة البريد الإلكتروني غير صحيحة';
        } else {
            // التحقق من عدم تكرار البريد الإلكتروني
            $email_check = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $email_check->bind_param("si", $email, $teacher['user_id']);
            $email_check->execute();
            if ($email_check->get_result()->num_rows > 0) {
                $errors[] = 'البريد الإلكتروني موجود مسبقاً';
            }
        }
        
        // التحقق من كلمة المرور إذا تم إدخالها
        if (!empty($password)) {
            if (!validate_password($password)) {
                $errors[] = 'كلمة المرور قصيرة جداً';
            } elseif ($password !== $confirm_password) {
                $errors[] = 'كلمات المرور غير متطابقة';
            }
        }
        
        if (empty($employee_id)) {
            $errors[] = 'رقم الموظف مطلوب';
        } else {
            // التحقق من عدم تكرار رقم الموظف
            $employee_check = $conn->prepare("SELECT id FROM teachers WHERE employee_id = ? AND id != ?");
            $employee_check->bind_param("si", $employee_id, $teacher_id);
            $employee_check->execute();
            if ($employee_check->get_result()->num_rows > 0) {
                $errors[] = 'رقم الموظف موجود مسبقاً';
            }
        }
        
        if (!empty($national_id) && !validate_national_id($national_id)) {
            $errors[] = 'رقم الهوية غير صحيح';
        }
        if (!empty($phone) && !validate_phone($phone)) {
            $errors[] = 'رقم الهاتف غير صحيح';
        }
        if (!empty($date_of_birth) && !validate_date($date_of_birth)) {
            $errors[] = 'تاريخ الميلاد غير صحيح';
        }
        if (!empty($hire_date) && !validate_date($hire_date)) {
            $errors[] = 'تاريخ التوظيف غير صحيح';
        }
        if (empty($gender) || !in_array($gender, ['male', 'female'])) {
            $errors[] = 'الجنس مطلوب';
        }
        
        // التحقق من اختيار الفصول
        $class_ids = $_POST['class_ids'] ?? [];
        if (empty($class_ids) || !is_array($class_ids)) {
            $errors[] = 'يجب اختيار فصل واحد على الأقل';
        }

        if (empty($errors)) {
            $conn->begin_transaction();
            try {
                // تحديث بيانات المستخدم
                $update_user_sql = "UPDATE users SET full_name = ?, username = ?, email = ?, status = ?, updated_at = NOW() WHERE id = ?";
                $update_user_stmt = $conn->prepare($update_user_sql);
                $update_user_stmt->bind_param("ssssi", $full_name, $username, $email, $status, $teacher['user_id']);
                $update_user_stmt->execute();

                // تحديث كلمة المرور إذا تم إدخالها
                if (!empty($password)) {
                    $hashed_password = hash_password($password);
                    $update_password_stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $update_password_stmt->bind_param("si", $hashed_password, $teacher['user_id']);
                    $update_password_stmt->execute();
                }

                // تحديث بيانات المعلم
                $update_teacher_sql = "UPDATE teachers SET 
                    employee_id = ?, phone = ?, address = ?, date_of_birth = ?, gender = ?, 
                    nationality = ?, national_id = ?, qualification = ?, specialization = ?, 
                    experience_years = ?, hire_date = ?, department = ?, salary = ?, 
                    status = ?, updated_at = NOW() 
                    WHERE id = ?";
                
                $update_teacher_stmt = $conn->prepare($update_teacher_sql);
                $nationality = 'سعودي'; // قيمة افتراضية
                $update_teacher_stmt->bind_param("ssssssssissdssi", 
                    $employee_id, $phone, $address, $date_of_birth, $gender, 
                    $nationality, $national_id, $qualification, $specialization, 
                    $experience_years, $hire_date, $department, $salary, 
                    $status, $teacher_id
                );
                $update_teacher_stmt->execute();

                // تحديث الفصول المرتبطة
                // حذف الفصول القديمة
                $delete_classes_stmt = $conn->prepare("DELETE FROM teacher_class_assignments WHERE teacher_id = ?");
                $delete_classes_stmt->bind_param("i", $teacher_id);
                $delete_classes_stmt->execute();

                // إضافة الفصول الجديدة
                $insert_class_stmt = $conn->prepare("INSERT INTO teacher_class_assignments (teacher_id, class_id, created_at) VALUES (?, ?, NOW())");
                foreach ($class_ids as $class_id) {
                    $insert_class_stmt->bind_param("ii", $teacher_id, $class_id);
                    $insert_class_stmt->execute();
                }

                $conn->commit();
                $_SESSION['success_message'] = 'تم تحديث بيانات المعلم بنجاح';
                header('Location: view.php?id=' . $teacher_id);
                exit();

            } catch (Exception $e) {
                $conn->rollback();
                $error_message = 'حدث خطأ أثناء التحديث: ' . $e->getMessage();
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

$page_title = 'تعديل معلم';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit me-2"></i>تعديل معلم: <?php echo htmlspecialchars($teacher['full_name']); ?>
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">المعلمون</a></li>
                    <li class="breadcrumb-item active">تعديل معلم</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Error Messages -->
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Success Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <!-- Edit Teacher Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-chalkboard-teacher me-2"></i>معلومات المعلم
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-lg-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user me-2"></i>المعلومات الشخصية
                        </h6>
                        <div class="mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name"
                                   value="<?php echo htmlspecialchars($teacher['full_name']); ?>" required>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="<?php echo htmlspecialchars($teacher['username']); ?>" required>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo htmlspecialchars($teacher['email']); ?>" required>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور الجديدة</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="<?php echo htmlspecialchars($teacher['phone'] ?: ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="national_id" class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id"
                                   value="<?php echo htmlspecialchars($teacher['national_id'] ?: ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth"
                                   value="<?php echo $teacher['date_of_birth'] ?: ''; ?>">
                        </div>
                        <div class="mb-3">
                            <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">اختر</option>
                                <option value="male" <?php echo ($teacher['gender'] === 'male') ? 'selected' : ''; ?>>ذكر</option>
                                <option value="female" <?php echo ($teacher['gender'] === 'female') ? 'selected' : ''; ?>>أنثى</option>
                            </select>
                            <div class="invalid-feedback">هذا الحقل مطلوب</div>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="address" name="address"
                                   value="<?php echo htmlspecialchars($teacher['address'] ?: ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                            <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                            <?php if (!empty($teacher['profile_picture'])): ?>
                                <div class="form-text">الصورة الحالية: <?php echo htmlspecialchars($teacher['profile_picture']); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
